<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول المخزون الرئيسي للصيدليات
        Schema::create('pharmacy_inventory', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->string('batch_number')->nullable();
            $table->date('expiry_date')->nullable();
            $table->date('manufacturing_date')->nullable();
            $table->integer('quantity_in_stock')->default(0);
            $table->integer('reserved_quantity')->default(0); // الكمية المحجوزة
            $table->integer('minimum_stock_level')->default(10); // الحد الأدنى للمخزون
            $table->integer('maximum_stock_level')->default(1000); // الحد الأقصى للمخزون
            $table->integer('reorder_level')->default(20); // مستوى إعادة الطلب
            $table->decimal('cost_price', 10, 2)->nullable(); // سعر التكلفة
            $table->decimal('selling_price', 10, 2)->nullable(); // سعر البيع
            $table->string('supplier_name')->nullable();
            $table->string('storage_location')->nullable(); // موقع التخزين
            $table->enum('storage_condition', ['room_temperature', 'refrigerated', 'frozen', 'controlled'])->default('room_temperature');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_prescription_required')->default(false);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['pharmacy_id', 'product_id', 'batch_number']);
            $table->index(['pharmacy_id', 'product_id']);
            $table->index(['expiry_date']);
            $table->index(['quantity_in_stock']);
        });

        // جدول حركات المخزون
        Schema::create('inventory_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pharmacy_inventory_id')->constrained('pharmacy_inventory')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('movement_number')->unique();
            $table->enum('type', ['in', 'out', 'adjustment', 'transfer', 'return', 'expired', 'damaged']);
            $table->integer('quantity');
            $table->integer('quantity_before');
            $table->integer('quantity_after');
            $table->decimal('unit_cost', 10, 2)->nullable();
            $table->decimal('total_cost', 10, 2)->nullable();
            $table->string('reference_type')->nullable(); // order, prescription, transfer, etc.
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->text('reason')->nullable();
            $table->text('notes')->nullable();
            $table->date('movement_date');
            $table->timestamps();

            $table->index(['pharmacy_inventory_id', 'type']);
            $table->index(['movement_date']);
            $table->index(['reference_type', 'reference_id']);
        });

        // جدول تنبيهات المخزون
        Schema::create('inventory_alerts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('pharmacy_inventory_id')->nullable()->constrained('pharmacy_inventory')->onDelete('cascade');
            $table->enum('type', ['low_stock', 'out_of_stock', 'expiry_warning', 'expired', 'overstock']);
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->string('title');
            $table->text('message');
            $table->integer('current_quantity')->nullable();
            $table->integer('threshold_quantity')->nullable();
            $table->date('expiry_date')->nullable();
            $table->integer('days_to_expiry')->nullable();
            $table->boolean('is_read')->default(false);
            $table->boolean('is_resolved')->default(false);
            $table->timestamp('resolved_at')->nullable();
            $table->foreignId('resolved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('resolution_notes')->nullable();
            $table->timestamps();

            $table->index(['pharmacy_id', 'type', 'is_resolved']);
            $table->index(['expiry_date']);
            $table->index(['is_read', 'priority']);
        });

        // جدول طلبات التوريد
        Schema::create('supply_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->foreignId('supplier_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('supplier_name');
            $table->string('supplier_contact')->nullable();
            $table->enum('status', ['draft', 'pending', 'approved', 'ordered', 'partially_received', 'received', 'cancelled'])->default('draft');
            $table->date('order_date');
            $table->date('expected_delivery_date')->nullable();
            $table->date('actual_delivery_date')->nullable();
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('final_amount', 15, 2)->default(0);
            $table->enum('payment_status', ['pending', 'partial', 'paid'])->default('pending');
            $table->enum('payment_method', ['cash', 'credit', 'bank_transfer', 'check'])->nullable();
            $table->text('notes')->nullable();
            $table->text('delivery_notes')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();

            $table->index(['pharmacy_id', 'status']);
            $table->index(['order_date']);
            $table->index(['expected_delivery_date']);
        });

        // جدول تفاصيل طلبات التوريد
        Schema::create('supply_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supply_order_id')->constrained('supply_orders')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->integer('quantity_ordered');
            $table->integer('quantity_received')->default(0);
            $table->decimal('unit_cost', 10, 2);
            $table->decimal('total_cost', 10, 2);
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->string('batch_number')->nullable();
            $table->date('expiry_date')->nullable();
            $table->date('manufacturing_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['supply_order_id', 'product_id']);
        });

        // جدول استلام البضائع
        Schema::create('inventory_receipts', function (Blueprint $table) {
            $table->id();
            $table->string('receipt_number')->unique();
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->foreignId('supply_order_id')->nullable()->constrained('supply_orders')->onDelete('set null');
            $table->string('supplier_name');
            $table->string('delivery_note_number')->nullable();
            $table->date('receipt_date');
            $table->enum('status', ['pending', 'partial', 'completed', 'rejected'])->default('pending');
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->text('notes')->nullable();
            $table->text('quality_notes')->nullable();
            $table->foreignId('received_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();

            $table->index(['pharmacy_id', 'receipt_date']);
            $table->index(['supply_order_id']);
        });

        // جدول تفاصيل استلام البضائع
        Schema::create('inventory_receipt_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('inventory_receipt_id')->constrained('inventory_receipts')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('supply_order_item_id')->nullable()->constrained('supply_order_items')->onDelete('set null');
            $table->integer('quantity_ordered')->nullable();
            $table->integer('quantity_received');
            $table->integer('quantity_accepted');
            $table->integer('quantity_rejected')->default(0);
            $table->string('rejection_reason')->nullable();
            $table->decimal('unit_cost', 10, 2);
            $table->decimal('total_cost', 10, 2);
            $table->string('batch_number')->nullable();
            $table->date('expiry_date')->nullable();
            $table->date('manufacturing_date')->nullable();
            $table->enum('quality_status', ['good', 'damaged', 'expired', 'defective'])->default('good');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['inventory_receipt_id', 'product_id']);
        });

        // جدول جرد المخزون
        Schema::create('inventory_audits', function (Blueprint $table) {
            $table->id();
            $table->string('audit_number')->unique();
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->enum('type', ['full', 'partial', 'cycle', 'spot'])->default('full');
            $table->enum('status', ['planned', 'in_progress', 'completed', 'cancelled'])->default('planned');
            $table->date('audit_date');
            $table->date('planned_start_date')->nullable();
            $table->date('actual_start_date')->nullable();
            $table->date('completion_date')->nullable();
            $table->text('reason')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('conducted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();

            $table->index(['pharmacy_id', 'audit_date']);
            $table->index(['status']);
        });

        // جدول تفاصيل جرد المخزون
        Schema::create('inventory_audit_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('inventory_audit_id')->constrained('inventory_audits')->onDelete('cascade');
            $table->foreignId('pharmacy_inventory_id')->constrained('pharmacy_inventory')->onDelete('cascade');
            $table->integer('system_quantity'); // الكمية في النظام
            $table->integer('physical_quantity'); // الكمية الفعلية
            $table->integer('variance_quantity'); // الفرق
            $table->decimal('variance_value', 10, 2)->default(0); // قيمة الفرق
            $table->enum('variance_type', ['surplus', 'shortage', 'match'])->default('match');
            $table->text('reason')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_adjusted')->default(false);
            $table->timestamp('adjusted_at')->nullable();
            $table->foreignId('adjusted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['inventory_audit_id', 'variance_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('inventory_audit_items');
        Schema::dropIfExists('inventory_audits');
        Schema::dropIfExists('inventory_receipt_items');
        Schema::dropIfExists('inventory_receipts');
        Schema::dropIfExists('supply_order_items');
        Schema::dropIfExists('supply_orders');
        Schema::dropIfExists('inventory_alerts');
        Schema::dropIfExists('inventory_movements');
        Schema::dropIfExists('pharmacy_inventory');
    }
};

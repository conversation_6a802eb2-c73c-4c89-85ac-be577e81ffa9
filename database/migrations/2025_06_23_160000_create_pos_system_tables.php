<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول المبيعات الرئيسي
        Schema::create('pos_sales', function (Blueprint $table) {
            $table->id();
            $table->string('sale_number')->unique(); // رقم البيع
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->foreignId('cashier_id')->constrained('users')->onDelete('cascade'); // الكاشير
            $table->foreignId('customer_id')->nullable()->constrained('ec_customers')->onDelete('set null'); // العميل (اختياري)
            
            // تفاصيل البيع
            $table->decimal('subtotal', 10, 2); // المجموع الفرعي
            $table->decimal('tax_amount', 10, 2)->default(0); // قيمة الضريبة
            $table->decimal('discount_amount', 10, 2)->default(0); // قيمة الخصم
            $table->decimal('total_amount', 10, 2); // المجموع الكلي
            $table->decimal('paid_amount', 10, 2)->default(0); // المبلغ المدفوع
            $table->decimal('change_amount', 10, 2)->default(0); // الباقي
            
            // معلومات إضافية
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded'])->default('pending');
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'refunded'])->default('pending');
            $table->text('notes')->nullable(); // ملاحظات
            $table->text('cancellation_reason')->nullable(); // سبب الإلغاء
            $table->timestamp('sale_date'); // تاريخ البيع
            $table->timestamp('completed_at')->nullable(); // تاريخ الإكمال
            $table->timestamp('cancelled_at')->nullable(); // تاريخ الإلغاء
            
            $table->timestamps();
            
            // فهارس
            $table->index(['pharmacy_id', 'sale_date']);
            $table->index(['cashier_id', 'sale_date']);
            $table->index(['status', 'sale_date']);
            $table->index('sale_number');
        });

        // جدول عناصر البيع
        Schema::create('pos_sale_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sale_id')->constrained('pos_sales')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('pharmacy_inventory_id')->nullable()->constrained('pharmacy_inventory')->onDelete('set null');
            
            // تفاصيل العنصر
            $table->string('product_name'); // اسم المنتج (نسخة للحفظ)
            $table->string('product_sku')->nullable(); // رمز المنتج
            $table->integer('quantity'); // الكمية
            $table->decimal('unit_price', 10, 2); // سعر الوحدة
            $table->decimal('total_price', 10, 2); // السعر الإجمالي
            $table->decimal('discount_amount', 10, 2)->default(0); // خصم العنصر
            $table->decimal('tax_amount', 10, 2)->default(0); // ضريبة العنصر
            
            // معلومات إضافية
            $table->string('batch_number')->nullable(); // رقم الدفعة
            $table->date('expiry_date')->nullable(); // تاريخ انتهاء الصلاحية
            $table->boolean('is_prescription_required')->default(false); // يتطلب وصفة
            $table->text('notes')->nullable(); // ملاحظات
            
            $table->timestamps();
            
            // فهارس
            $table->index(['sale_id', 'product_id']);
            $table->index('product_id');
        });

        // جدول المدفوعات
        Schema::create('pos_payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_number')->unique(); // رقم الدفعة
            $table->foreignId('sale_id')->constrained('pos_sales')->onDelete('cascade');
            $table->foreignId('cashier_id')->constrained('users')->onDelete('cascade');
            
            // تفاصيل الدفع
            $table->enum('payment_method', ['cash', 'card', 'wallet', 'bank_transfer', 'insurance', 'credit']); // طريقة الدفع
            $table->decimal('amount', 10, 2); // المبلغ
            $table->string('reference_number')->nullable(); // رقم المرجع (للبطاقات/التحويلات)
            $table->string('card_last_four')->nullable(); // آخر 4 أرقام البطاقة
            $table->string('approval_code')->nullable(); // رمز الموافقة
            
            // معلومات إضافية
            $table->enum('status', ['pending', 'approved', 'declined', 'refunded'])->default('pending');
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamp('payment_date'); // تاريخ الدفع
            $table->timestamp('processed_at')->nullable(); // تاريخ المعالجة
            
            $table->timestamps();
            
            // فهارس
            $table->index(['sale_id', 'payment_method']);
            $table->index(['payment_method', 'payment_date']);
            $table->index('payment_number');
        });

        // جدول الفواتير
        Schema::create('pos_invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique(); // رقم الفاتورة
            $table->foreignId('sale_id')->constrained('pos_sales')->onDelete('cascade');
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained('ec_customers')->onDelete('set null');
            
            // تفاصيل الفاتورة
            $table->decimal('subtotal', 10, 2); // المجموع الفرعي
            $table->decimal('tax_amount', 10, 2)->default(0); // قيمة الضريبة
            $table->decimal('discount_amount', 10, 2)->default(0); // قيمة الخصم
            $table->decimal('total_amount', 10, 2); // المجموع الكلي
            
            // معلومات العميل (نسخة للحفظ)
            $table->string('customer_name')->nullable();
            $table->string('customer_phone')->nullable();
            $table->string('customer_email')->nullable();
            $table->text('customer_address')->nullable();
            $table->string('customer_tax_number')->nullable(); // الرقم الضريبي
            
            // حالة الفاتورة
            $table->enum('status', ['draft', 'sent', 'paid', 'cancelled', 'refunded'])->default('draft');
            $table->date('invoice_date'); // تاريخ الفاتورة
            $table->date('due_date')->nullable(); // تاريخ الاستحقاق
            $table->timestamp('sent_at')->nullable(); // تاريخ الإرسال
            $table->timestamp('paid_at')->nullable(); // تاريخ الدفع
            
            // معلومات إضافية
            $table->text('notes')->nullable(); // ملاحظات
            $table->text('terms_conditions')->nullable(); // الشروط والأحكام
            $table->string('pdf_path')->nullable(); // مسار ملف PDF
            
            $table->timestamps();
            
            // فهارس
            $table->index(['pharmacy_id', 'invoice_date']);
            $table->index(['customer_id', 'invoice_date']);
            $table->index(['status', 'invoice_date']);
            $table->index('invoice_number');
        });

        // جدول المرتجعات
        Schema::create('pos_returns', function (Blueprint $table) {
            $table->id();
            $table->string('return_number')->unique(); // رقم المرتجع
            $table->foreignId('sale_id')->constrained('pos_sales')->onDelete('cascade');
            $table->foreignId('cashier_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained('ec_customers')->onDelete('set null');
            
            // تفاصيل المرتجع
            $table->decimal('total_amount', 10, 2); // قيمة المرتجع
            $table->enum('return_type', ['full', 'partial']); // نوع المرتجع
            $table->enum('reason', ['defective', 'expired', 'wrong_item', 'customer_request', 'other']); // سبب المرتجع
            $table->text('reason_details')->nullable(); // تفاصيل السبب
            
            // معلومات المعالجة
            $table->enum('status', ['pending', 'approved', 'rejected', 'processed'])->default('pending');
            $table->enum('refund_method', ['cash', 'card', 'wallet', 'credit'])->nullable(); // طريقة الاسترداد
            $table->decimal('refund_amount', 10, 2)->default(0); // مبلغ الاسترداد
            
            // تواريخ
            $table->timestamp('return_date'); // تاريخ المرتجع
            $table->timestamp('approved_at')->nullable(); // تاريخ الموافقة
            $table->timestamp('processed_at')->nullable(); // تاريخ المعالجة
            
            $table->timestamps();
            
            // فهارس
            $table->index(['sale_id', 'return_date']);
            $table->index(['status', 'return_date']);
            $table->index('return_number');
        });

        // جدول عناصر المرتجعات
        Schema::create('pos_return_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('return_id')->constrained('pos_returns')->onDelete('cascade');
            $table->foreignId('sale_item_id')->constrained('pos_sale_items')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            
            // تفاصيل العنصر المرتجع
            $table->integer('quantity_returned'); // الكمية المرتجعة
            $table->decimal('unit_price', 10, 2); // سعر الوحدة
            $table->decimal('total_amount', 10, 2); // المبلغ الإجمالي
            $table->enum('condition', ['good', 'damaged', 'expired']); // حالة المنتج
            $table->text('notes')->nullable(); // ملاحظات
            
            $table->timestamps();
            
            // فهارس
            $table->index(['return_id', 'product_id']);
            $table->index('sale_item_id');
        });

        // جدول جلسات الكاشير
        Schema::create('pos_cashier_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_number')->unique(); // رقم الجلسة
            $table->foreignId('cashier_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            
            // تفاصيل الجلسة
            $table->decimal('opening_balance', 10, 2)->default(0); // الرصيد الافتتاحي
            $table->decimal('closing_balance', 10, 2)->nullable(); // الرصيد الختامي
            $table->decimal('expected_balance', 10, 2)->nullable(); // الرصيد المتوقع
            $table->decimal('difference', 10, 2)->nullable(); // الفرق
            
            // إحصائيات الجلسة
            $table->integer('total_sales')->default(0); // عدد المبيعات
            $table->decimal('total_sales_amount', 10, 2)->default(0); // قيمة المبيعات
            $table->integer('total_returns')->default(0); // عدد المرتجعات
            $table->decimal('total_returns_amount', 10, 2)->default(0); // قيمة المرتجعات
            
            // تواريخ
            $table->timestamp('opened_at'); // تاريخ الفتح
            $table->timestamp('closed_at')->nullable(); // تاريخ الإغلاق
            $table->enum('status', ['open', 'closed'])->default('open');
            
            // ملاحظات
            $table->text('opening_notes')->nullable(); // ملاحظات الفتح
            $table->text('closing_notes')->nullable(); // ملاحظات الإغلاق
            
            $table->timestamps();
            
            // فهارس
            $table->index(['cashier_id', 'opened_at']);
            $table->index(['pharmacy_id', 'opened_at']);
            $table->index(['status', 'opened_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pos_cashier_sessions');
        Schema::dropIfExists('pos_return_items');
        Schema::dropIfExists('pos_returns');
        Schema::dropIfExists('pos_invoices');
        Schema::dropIfExists('pos_payments');
        Schema::dropIfExists('pos_sale_items');
        Schema::dropIfExists('pos_sales');
    }
};

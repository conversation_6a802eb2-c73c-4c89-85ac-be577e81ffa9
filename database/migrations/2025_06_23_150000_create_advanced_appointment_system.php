<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // تحديث جدول المواعيد الموجود
        Schema::table('appointments', function (Blueprint $table) {
            // إضافة حقول جديدة
            $table->string('appointment_number')->unique()->after('id');
            $table->text('doctor_notes')->nullable()->after('notes');
            $table->timestamp('confirmed_at')->nullable()->after('payment_status');
            $table->timestamp('cancelled_at')->nullable()->after('confirmed_at');
            $table->string('cancellation_reason')->nullable()->after('cancelled_at');
            $table->enum('priority', ['normal', 'urgent', 'emergency'])->default('normal')->after('type');
            $table->string('patient_phone')->nullable()->after('reason');
            $table->boolean('is_online')->default(false)->after('priority');
            $table->string('meeting_link')->nullable()->after('is_online');
        });

        // جدول فترات العمل للأطباء
        Schema::create('doctor_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->enum('day_of_week', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']);
            $table->time('start_time');
            $table->time('end_time');
            $table->integer('slot_duration')->default(30); // بالدقائق
            $table->integer('break_start')->nullable(); // دقائق من بداية اليوم
            $table->integer('break_duration')->default(0); // بالدقائق
            $table->boolean('is_available')->default(true);
            $table->decimal('consultation_fee', 8, 2)->nullable();
            $table->timestamps();

            $table->unique(['doctor_id', 'day_of_week']);
        });

        // جدول فترات الحجز المتاحة
        Schema::create('appointment_slots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->date('date');
            $table->time('start_time');
            $table->time('end_time');
            $table->boolean('is_available')->default(true);
            $table->boolean('is_blocked')->default(false);
            $table->string('block_reason')->nullable();
            $table->foreignId('appointment_id')->nullable()->constrained('appointments')->onDelete('set null');
            $table->timestamps();

            $table->unique(['doctor_id', 'date', 'start_time']);
        });

        // جدول إشعارات المواعيد
        Schema::create('appointment_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('appointment_id')->constrained('appointments')->onDelete('cascade');
            $table->enum('type', ['reminder', 'confirmation', 'cancellation', 'rescheduling', 'follow_up']);
            $table->enum('method', ['email', 'sms', 'push', 'in_app']);
            $table->dateTime('scheduled_at');
            $table->dateTime('sent_at')->nullable();
            $table->enum('status', ['pending', 'sent', 'failed', 'cancelled'])->default('pending');
            $table->text('message')->nullable();
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->integer('retry_count')->default(0);
            $table->timestamps();
        });

        // جدول قوائم الانتظار
        Schema::create('appointment_waitlists', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained('patients')->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->date('preferred_date');
            $table->time('preferred_time_start')->nullable();
            $table->time('preferred_time_end')->nullable();
            $table->enum('priority', ['normal', 'urgent', 'emergency'])->default('normal');
            $table->text('reason')->nullable();
            $table->enum('status', ['active', 'notified', 'booked', 'expired', 'cancelled'])->default('active');
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });

        // جدول تقييم المواعيد
        Schema::create('appointment_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('appointment_id')->constrained('appointments')->onDelete('cascade');
            $table->foreignId('patient_id')->constrained('patients')->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->integer('rating')->comment('من 1 إلى 5');
            $table->text('comment')->nullable();
            $table->json('criteria_ratings')->nullable(); // تقييمات معايير مختلفة
            $table->boolean('is_anonymous')->default(false);
            $table->boolean('is_approved')->default(true);
            $table->timestamps();
        });

        // جدول إعدادات الحجوزات للأطباء
        Schema::create('doctor_booking_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->integer('advance_booking_days')->default(30); // كم يوم مقدماً يمكن الحجز
            $table->integer('cancellation_hours')->default(24); // كم ساعة قبل الموعد يمكن الإلغاء
            $table->boolean('auto_confirm')->default(false);
            $table->boolean('allow_online_consultation')->default(false);
            $table->boolean('send_reminders')->default(true);
            $table->integer('reminder_hours')->default(24); // متى يتم إرسال التذكير
            $table->decimal('cancellation_fee', 8, 2)->default(0);
            $table->text('booking_instructions')->nullable();
            $table->json('blocked_dates')->nullable(); // تواريخ مغلقة
            $table->timestamps();
        });

        // جدول سجل تغييرات المواعيد
        Schema::create('appointment_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('appointment_id')->constrained('appointments')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('action', ['created', 'confirmed', 'rescheduled', 'cancelled', 'completed', 'no_show']);
            $table->text('description');
            $table->json('old_data')->nullable();
            $table->json('new_data')->nullable();
            $table->timestamp('action_at');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('appointment_history');
        Schema::dropIfExists('doctor_booking_settings');
        Schema::dropIfExists('appointment_reviews');
        Schema::dropIfExists('appointment_waitlists');
        Schema::dropIfExists('appointment_notifications');
        Schema::dropIfExists('appointment_slots');
        Schema::dropIfExists('doctor_schedules');
        
        // إزالة الحقول المضافة من جدول appointments
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropColumn([
                'appointment_number',
                'doctor_notes',
                'confirmed_at',
                'cancelled_at',
                'cancellation_reason',
                'priority',
                'patient_phone',
                'is_online',
                'meeting_link'
            ]);
        });
    }
};

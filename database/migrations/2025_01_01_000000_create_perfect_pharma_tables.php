<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول أنواع المستخدمين المتقدم
        Schema::create('user_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // patient, doctor, pharmacy, lab, admin, donor
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->json('permissions')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // جدول المرضى المتقدم
        Schema::create('patients', function (Blueprint $table) {
            $table->id();
            $table->string('patient_code')->unique(); // رقم تعريفي فريد
            $table->foreignId('user_id')->constrained('ec_customers')->onDelete('cascade');
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('blood_type', 5)->nullable();
            $table->text('medical_history')->nullable();
            $table->text('allergies')->nullable();
            $table->text('chronic_diseases')->nullable();
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone')->nullable();
            $table->string('national_id')->nullable();
            $table->boolean('is_chronic_patient')->default(false);
            $table->timestamps();
        });

        // جدول الأطباء
        Schema::create('doctors', function (Blueprint $table) {
            $table->id();
            $table->string('doctor_code')->unique();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('license_number')->unique();
            $table->string('specialization');
            $table->string('qualification')->nullable();
            $table->text('bio')->nullable();
            $table->string('clinic_address')->nullable();
            $table->string('clinic_phone')->nullable();
            $table->time('work_start_time')->nullable();
            $table->time('work_end_time')->nullable();
            $table->json('work_days')->nullable(); // أيام العمل
            $table->decimal('consultation_fee', 8, 2)->nullable();
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // جدول الصيدليات
        Schema::create('pharmacies', function (Blueprint $table) {
            $table->id();
            $table->string('pharmacy_code')->unique();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('license_number')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('address');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->time('opening_time')->nullable();
            $table->time('closing_time')->nullable();
            $table->boolean('is_24_hours')->default(false);
            $table->boolean('accepts_insurance')->default(false);
            $table->boolean('home_delivery')->default(false);
            $table->decimal('delivery_fee', 8, 2)->nullable();
            $table->integer('delivery_radius_km')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // جدول المعامل
        Schema::create('labs', function (Blueprint $table) {
            $table->id();
            $table->string('lab_code')->unique();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('license_number')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('address');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->time('opening_time')->nullable();
            $table->time('closing_time')->nullable();
            $table->boolean('home_collection')->default(false);
            $table->decimal('collection_fee', 8, 2)->nullable();
            $table->json('available_tests')->nullable(); // قائمة التحاليل المتاحة
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // جدول المحفظة الرقمية
        Schema::create('pharma_wallets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('wallet_number')->unique();
            $table->decimal('balance', 15, 2)->default(0);
            $table->decimal('pending_balance', 15, 2)->default(0);
            $table->string('currency', 3)->default('EGP');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_verified')->default(false);
            $table->timestamp('last_transaction_at')->nullable();
            $table->timestamps();
        });

        // جدول معاملات المحفظة
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wallet_id')->constrained('pharma_wallets')->onDelete('cascade');
            $table->string('transaction_id')->unique();
            $table->enum('type', ['credit', 'debit', 'transfer_in', 'transfer_out', 'refund']);
            $table->decimal('amount', 15, 2);
            $table->decimal('balance_before', 15, 2);
            $table->decimal('balance_after', 15, 2);
            $table->string('reference_type')->nullable(); // order, prescription, test, course, donation
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->text('description')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->json('metadata')->nullable();
            $table->timestamps();
        });

        // جدول الوصفات الطبية
        Schema::create('prescriptions', function (Blueprint $table) {
            $table->id();
            $table->string('prescription_number')->unique();
            $table->foreignId('patient_id')->constrained('patients')->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->text('diagnosis')->nullable();
            $table->text('symptoms')->nullable();
            $table->text('notes')->nullable();
            $table->date('prescription_date');
            $table->date('expiry_date')->nullable();
            $table->enum('status', ['active', 'partially_filled', 'completed', 'expired', 'cancelled'])->default('active');
            $table->boolean('is_chronic')->default(false);
            $table->integer('refill_count')->default(0);
            $table->integer('max_refills')->default(1);
            $table->timestamps();
        });

        // جدول أدوية الوصفة
        Schema::create('prescription_medications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prescription_id')->constrained('prescriptions')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->string('medication_name'); // اسم الدواء كما كتبه الطبيب
            $table->string('dosage'); // الجرعة
            $table->string('frequency'); // عدد مرات التناول
            $table->integer('duration_days'); // مدة العلاج بالأيام
            $table->integer('quantity'); // الكمية المطلوبة
            $table->integer('dispensed_quantity')->default(0); // الكمية المصروفة
            $table->text('instructions')->nullable(); // تعليمات الاستخدام
            $table->enum('status', ['pending', 'partially_dispensed', 'completed'])->default('pending');
            $table->timestamps();
        });

        // جدول طلبات التحاليل
        Schema::create('lab_test_requests', function (Blueprint $table) {
            $table->id();
            $table->string('request_number')->unique();
            $table->foreignId('patient_id')->constrained('patients')->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->foreignId('lab_id')->constrained('labs')->nullable();
            $table->text('clinical_data')->nullable(); // البيانات السريرية
            $table->text('notes')->nullable();
            $table->date('request_date');
            $table->date('required_date')->nullable(); // التاريخ المطلوب
            $table->enum('priority', ['normal', 'urgent', 'emergency'])->default('normal');
            $table->enum('status', ['pending', 'accepted', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->decimal('total_cost', 8, 2)->nullable();
            $table->boolean('is_paid')->default(false);
            $table->timestamps();
        });

        // جدول تفاصيل التحاليل المطلوبة
        Schema::create('lab_test_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('request_id')->constrained('lab_test_requests')->onDelete('cascade');
            $table->string('test_name');
            $table->string('test_code')->nullable();
            $table->text('test_description')->nullable();
            $table->decimal('price', 8, 2)->nullable();
            $table->enum('status', ['pending', 'in_progress', 'completed'])->default('pending');
            $table->timestamps();
        });

        // جدول نتائج التحاليل
        Schema::create('lab_test_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('test_item_id')->constrained('lab_test_items')->onDelete('cascade');
            $table->string('parameter_name'); // اسم المعامل
            $table->string('result_value'); // القيمة
            $table->string('unit')->nullable(); // الوحدة
            $table->string('reference_range')->nullable(); // المدى الطبيعي
            $table->enum('status', ['normal', 'abnormal', 'critical'])->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });

        // جدول صرف الأدوية
        Schema::create('medication_dispensings', function (Blueprint $table) {
            $table->id();
            $table->string('dispensing_number')->unique();
            $table->foreignId('prescription_medication_id')->constrained('prescription_medications')->onDelete('cascade');
            $table->foreignId('pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->foreignId('pharmacist_id')->constrained('users')->onDelete('cascade');
            $table->integer('quantity_dispensed');
            $table->decimal('unit_price', 8, 2);
            $table->decimal('total_price', 8, 2);
            $table->decimal('discount_amount', 8, 2)->default(0);
            $table->decimal('final_price', 8, 2);
            $table->date('dispensing_date');
            $table->text('pharmacist_notes')->nullable();
            $table->enum('payment_method', ['cash', 'wallet', 'insurance', 'credit_card']);
            $table->timestamps();
        });

        // جدول الأدوية المزمنة
        Schema::create('chronic_medications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained('patients')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->string('medication_name');
            $table->string('dosage');
            $table->string('frequency');
            $table->integer('refill_interval_days'); // فترة التجديد بالأيام
            $table->date('last_refill_date')->nullable();
            $table->date('next_refill_date')->nullable();
            $table->boolean('auto_reminder')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // جدول التوزيع بين الصيدليات
        Schema::create('inter_pharmacy_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('requesting_pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->foreignId('supplying_pharmacy_id')->constrained('pharmacies')->onDelete('cascade');
            $table->decimal('total_amount', 15, 2);
            $table->enum('status', ['pending', 'accepted', 'rejected', 'shipped', 'delivered', 'completed'])->default('pending');
            $table->text('notes')->nullable();
            $table->date('requested_date');
            $table->date('delivery_date')->nullable();
            $table->enum('payment_method', ['cash', 'wallet', 'credit']);
            $table->boolean('is_paid')->default(false);
            $table->timestamps();
        });

        // جدول تفاصيل طلبات التوزيع
        Schema::create('inter_pharmacy_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('inter_pharmacy_orders')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->integer('quantity');
            $table->decimal('unit_price', 8, 2);
            $table->decimal('total_price', 8, 2);
            $table->date('expiry_date')->nullable();
            $table->string('batch_number')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('inter_pharmacy_order_items');
        Schema::dropIfExists('inter_pharmacy_orders');
        Schema::dropIfExists('chronic_medications');
        Schema::dropIfExists('medication_dispensings');
        Schema::dropIfExists('lab_test_results');
        Schema::dropIfExists('lab_test_items');
        Schema::dropIfExists('lab_test_requests');
        Schema::dropIfExists('prescription_medications');
        Schema::dropIfExists('prescriptions');
        Schema::dropIfExists('wallet_transactions');
        Schema::dropIfExists('pharma_wallets');
        Schema::dropIfExists('labs');
        Schema::dropIfExists('pharmacies');
        Schema::dropIfExists('doctors');
        Schema::dropIfExists('patients');
        Schema::dropIfExists('user_types');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول الأكاديمية التعليمية - الدورات
        Schema::create('academy_courses', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->text('content')->nullable();
            $table->string('category'); // pharmacy, medical_marketing, pharmacy_assistant
            $table->enum('type', ['video', 'pdf', 'mixed', 'live'])->default('mixed');
            $table->string('instructor_name')->nullable();
            $table->text('instructor_bio')->nullable();
            $table->decimal('price', 8, 2)->default(0);
            $table->integer('duration_hours')->nullable();
            $table->string('difficulty_level')->nullable(); // beginner, intermediate, advanced
            $table->string('image')->nullable();
            $table->string('video_url')->nullable();
            $table->string('pdf_url')->nullable();
            $table->json('learning_objectives')->nullable();
            $table->boolean('has_certificate')->default(false);
            $table->integer('passing_score')->default(70);
            $table->boolean('is_featured')->default(false);
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->timestamps();
        });

        // جدول محتوى الدورات
        Schema::create('course_contents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained('academy_courses')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['video', 'pdf', 'text', 'quiz']);
            $table->string('content_url')->nullable();
            $table->text('text_content')->nullable();
            $table->integer('order')->default(0);
            $table->integer('duration_minutes')->nullable();
            $table->boolean('is_free')->default(false);
            $table->timestamps();
        });

        // جدول تقدم الطلاب في الدورات
        Schema::create('course_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('course_id')->constrained('academy_courses')->onDelete('cascade');
            $table->foreignId('content_id')->constrained('course_contents')->onDelete('cascade');
            $table->enum('status', ['not_started', 'in_progress', 'completed'])->default('not_started');
            $table->integer('progress_percentage')->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('time_spent_minutes')->default(0);
            $table->timestamps();

            $table->unique(['user_id', 'course_id', 'content_id']);
        });

        // جدول الاختبارات
        Schema::create('course_quizzes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained('academy_courses')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->integer('passing_score')->default(70);
            $table->integer('time_limit_minutes')->nullable();
            $table->integer('max_attempts')->default(3);
            $table->boolean('randomize_questions')->default(false);
            $table->boolean('show_results_immediately')->default(true);
            $table->timestamps();
        });

        // جدول أسئلة الاختبارات
        Schema::create('quiz_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quiz_id')->constrained('course_quizzes')->onDelete('cascade');
            $table->text('question');
            $table->enum('type', ['multiple_choice', 'true_false', 'text'])->default('multiple_choice');
            $table->json('options')->nullable(); // للأسئلة متعددة الخيارات
            $table->string('correct_answer');
            $table->text('explanation')->nullable();
            $table->integer('points')->default(1);
            $table->integer('order')->default(0);
            $table->timestamps();
        });

        // جدول محاولات الاختبارات
        Schema::create('quiz_attempts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('quiz_id')->constrained('course_quizzes')->onDelete('cascade');
            $table->integer('score')->default(0);
            $table->integer('total_questions');
            $table->integer('correct_answers')->default(0);
            $table->boolean('passed')->default(false);
            $table->json('answers')->nullable(); // إجابات المستخدم
            $table->timestamp('started_at');
            $table->timestamp('completed_at')->nullable();
            $table->integer('time_taken_minutes')->nullable();
            $table->timestamps();
        });

        // جدول الشهادات
        Schema::create('certificates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('course_id')->constrained('academy_courses')->onDelete('cascade');
            $table->string('certificate_number')->unique();
            $table->string('certificate_url')->nullable();
            $table->date('issued_date');
            $table->date('expiry_date')->nullable();
            $table->integer('final_score');
            $table->enum('status', ['active', 'expired', 'revoked'])->default('active');
            $table->timestamps();
        });

        // جدول المستشفيات الخيرية
        Schema::create('charity_hospitals', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('address');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->string('website')->nullable();
            $table->string('license_number')->unique();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('contact_person_name');
            $table->string('contact_person_phone');
            $table->json('needed_specialties')->nullable(); // التخصصات المطلوبة
            $table->json('needed_medications')->nullable(); // الأدوية المطلوبة
            $table->json('needed_equipment')->nullable(); // المعدات المطلوبة
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // جدول التبرعات
        Schema::create('donations', function (Blueprint $table) {
            $table->id();
            $table->string('donation_number')->unique();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('hospital_id')->constrained('charity_hospitals')->onDelete('cascade');
            $table->enum('type', ['money', 'medication', 'equipment', 'service']);
            $table->decimal('amount', 15, 2)->nullable(); // للتبرعات المالية
            $table->text('description'); // وصف التبرع
            $table->json('items')->nullable(); // للتبرعات العينية
            $table->enum('payment_method', ['wallet', 'credit_card', 'bank_transfer', 'cash']);
            $table->enum('status', ['pending', 'approved', 'delivered', 'completed', 'cancelled'])->default('pending');
            $table->date('donation_date');
            $table->date('delivery_date')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_anonymous')->default(false);
            $table->string('receipt_number')->nullable();
            $table->timestamps();
        });

        // جدول الإشعارات
        Schema::create('pharma_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('title');
            $table->text('message');
            $table->enum('type', ['prescription', 'test_result', 'appointment', 'medication_reminder', 'system', 'promotion']);
            $table->string('reference_type')->nullable(); // prescription, test, order, etc.
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->json('data')->nullable(); // بيانات إضافية
            $table->timestamps();
        });

        // جدول تقييمات الصيدليات والمعامل
        Schema::create('service_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('reviewable_type'); // pharmacy, lab, doctor
            $table->unsignedBigInteger('reviewable_id');
            $table->integer('rating'); // من 1 إلى 5
            $table->text('comment')->nullable();
            $table->json('criteria_ratings')->nullable(); // تقييمات معايير مختلفة
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_approved')->default(true);
            $table->timestamps();

            $table->index(['reviewable_type', 'reviewable_id']);
        });

        // جدول المواعيد
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained('patients')->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->datetime('appointment_datetime');
            $table->integer('duration_minutes')->default(30);
            $table->enum('type', ['consultation', 'follow_up', 'emergency']);
            $table->text('reason')->nullable();
            $table->text('notes')->nullable();
            $table->decimal('fee', 8, 2)->nullable();
            $table->enum('status', ['scheduled', 'confirmed', 'completed', 'cancelled', 'no_show'])->default('scheduled');
            $table->enum('payment_status', ['pending', 'paid', 'refunded'])->default('pending');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('appointments');
        Schema::dropIfExists('service_reviews');
        Schema::dropIfExists('pharma_notifications');
        Schema::dropIfExists('donations');
        Schema::dropIfExists('charity_hospitals');
        Schema::dropIfExists('certificates');
        Schema::dropIfExists('quiz_attempts');
        Schema::dropIfExists('quiz_questions');
        Schema::dropIfExists('course_quizzes');
        Schema::dropIfExists('course_progress');
        Schema::dropIfExists('course_contents');
        Schema::dropIfExists('academy_courses');
    }
};

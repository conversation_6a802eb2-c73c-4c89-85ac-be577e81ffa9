<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول تقييمات الدورات
        if (!Schema::hasTable('course_ratings')) {
            Schema::create('course_ratings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('course_id')->constrained('academy_courses')->onDelete('cascade');
                $table->tinyInteger('rating')->unsigned(); // 1-5
                $table->text('review')->nullable();
                $table->boolean('is_approved')->default(true);
                $table->integer('helpful_count')->default(0);
                $table->timestamps();
                
                $table->unique(['user_id', 'course_id']);
                $table->index(['course_id', 'rating']);
                $table->index(['course_id', 'is_approved']);
            });
        }

        // جدول تقييمات المحتوى
        if (!Schema::hasTable('content_ratings')) {
            Schema::create('content_ratings', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('content_id')->constrained('course_contents')->onDelete('cascade');
                $table->tinyInteger('rating')->unsigned(); // 1-5
                $table->text('comment')->nullable();
                $table->boolean('is_helpful')->default(false);
                $table->timestamps();
                
                $table->unique(['user_id', 'content_id']);
                $table->index(['content_id', 'rating']);
                $table->index(['content_id', 'is_helpful']);
            });
        }

        // إضافة عمود average_rating للدورات
        if (!Schema::hasColumn('academy_courses', 'average_rating')) {
            Schema::table('academy_courses', function (Blueprint $table) {
                $table->decimal('average_rating', 3, 2)->default(0)->after('status');
                $table->integer('ratings_count')->default(0)->after('average_rating');
            });
        }

        // إضافة عمود average_rating للمحتوى
        if (!Schema::hasColumn('course_contents', 'average_rating')) {
            Schema::table('course_contents', function (Blueprint $table) {
                $table->decimal('average_rating', 3, 2)->default(0)->after('status');
                $table->integer('ratings_count')->default(0)->after('average_rating');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('content_ratings');
        Schema::dropIfExists('course_ratings');
        
        if (Schema::hasColumn('academy_courses', 'average_rating')) {
            Schema::table('academy_courses', function (Blueprint $table) {
                $table->dropColumn(['average_rating', 'ratings_count']);
            });
        }
        
        if (Schema::hasColumn('course_contents', 'average_rating')) {
            Schema::table('course_contents', function (Blueprint $table) {
                $table->dropColumn(['average_rating', 'ratings_count']);
            });
        }
    }
};

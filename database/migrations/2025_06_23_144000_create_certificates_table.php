<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasTable('certificates')) {
            Schema::create('certificates', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('course_id')->constrained('academy_courses')->onDelete('cascade');
                $table->string('certificate_number')->unique();
                $table->string('verification_code')->unique();
                $table->timestamp('issued_at')->nullable();
                $table->decimal('grade', 5, 2)->default(0);
                $table->enum('status', ['active', 'revoked'])->default('active');
                $table->string('pdf_path')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamps();
                
                $table->index(['user_id', 'course_id']);
                $table->index('certificate_number');
                $table->index('verification_code');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('certificates');
    }
};

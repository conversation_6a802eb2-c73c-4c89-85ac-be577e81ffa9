<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // تحديث جدول أنواع المستخدمين لدعم التسعير المتدرج (فقط الأعمدة غير الموجودة)
        // جميع الأعمدة موجودة بالفعل، لا حاجة لإضافتها

        // جدول ربط العملاء بأنواع المستخدمين
        if (!Schema::hasTable('customer_user_types')) {
            Schema::create('customer_user_types', function (Blueprint $table) {
                $table->id();
                $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
                $table->foreignId('user_type_id')->constrained('user_types')->onDelete('cascade');
                $table->decimal('custom_discount_percentage', 5, 2)->nullable(); // خصم مخصص
                $table->json('custom_features')->nullable(); // ميزات مخصصة
                $table->boolean('is_verified')->default(false); // هل تم التحقق
                $table->timestamp('verified_at')->nullable();
                $table->foreignId('verified_by')->nullable()->constrained('users');
                $table->text('verification_notes')->nullable();
                $table->json('verification_documents')->nullable(); // مستندات التحقق
                $table->timestamps();

                $table->unique(['customer_id', 'user_type_id']);
            });
        }

        // جدول التسعير المتدرج للمنتجات
        if (!Schema::hasTable('product_tiered_pricing')) {
            Schema::create('product_tiered_pricing', function (Blueprint $table) {
                $table->id();
                $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
                $table->foreignId('user_type_id')->constrained('user_types')->onDelete('cascade');
                $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة الخصم
                $table->decimal('fixed_price', 10, 2)->nullable(); // سعر ثابت (اختياري)
                $table->integer('min_quantity')->default(1); // الحد الأدنى للكمية
                $table->integer('max_quantity')->nullable(); // الحد الأقصى للكمية
                $table->date('start_date')->nullable(); // تاريخ البداية
                $table->date('end_date')->nullable(); // تاريخ النهاية
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->unique(['product_id', 'user_type_id']);
            });
        }

        // جدول تسعير الفئات
        if (!Schema::hasTable('category_tiered_pricing')) {
            Schema::create('category_tiered_pricing', function (Blueprint $table) {
                $table->id();
                $table->foreignId('category_id')->constrained('ec_product_categories')->onDelete('cascade');
                $table->foreignId('user_type_id')->constrained('user_types')->onDelete('cascade');
                $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة الخصم
                $table->integer('min_quantity')->default(1); // الحد الأدنى للكمية
                $table->date('start_date')->nullable(); // تاريخ البداية
                $table->date('end_date')->nullable(); // تاريخ النهاية
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->unique(['category_id', 'user_type_id']);
            });
        }

        // جدول سجل عرض الأسعار (للمراقبة والأمان)
        if (!Schema::hasTable('price_view_logs')) {
            Schema::create('price_view_logs', function (Blueprint $table) {
                $table->id();
                $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
                $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
                $table->decimal('viewed_price', 10, 2); // السعر الذي تم عرضه
                $table->decimal('original_price', 10, 2); // السعر الأصلي
                $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة الخصم المطبقة
                $table->string('user_type_slug'); // نوع المستخدم
                $table->string('ip_address')->nullable();
                $table->string('user_agent')->nullable();
                $table->timestamps();
            });
        }

        // جدول طلبات التحقق من نوع المستخدم
        if (!Schema::hasTable('user_type_verification_requests')) {
            Schema::create('user_type_verification_requests', function (Blueprint $table) {
                $table->id();
                $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
                $table->foreignId('user_type_id')->constrained('user_types')->onDelete('cascade');
                $table->json('submitted_documents'); // المستندات المرفوعة
                $table->text('business_description')->nullable(); // وصف النشاط التجاري
                $table->string('license_number')->nullable(); // رقم الترخيص
                $table->string('tax_number')->nullable(); // الرقم الضريبي
                $table->string('contact_person')->nullable(); // الشخص المسؤول
                $table->string('contact_phone')->nullable(); // هاتف التواصل
                $table->enum('status', ['pending', 'under_review', 'approved', 'rejected', 'requires_more_info'])->default('pending');
                $table->text('admin_notes')->nullable(); // ملاحظات الإدارة
                $table->foreignId('reviewed_by')->nullable()->constrained('users');
                $table->timestamp('reviewed_at')->nullable();
                $table->timestamps();
            });
        }

        // تحديث جدول العملاء لإضافة معلومات إضافية (فقط الأعمدة غير الموجودة)
        if (!Schema::hasColumn('ec_customers', 'business_name')) {
            Schema::table('ec_customers', function (Blueprint $table) {
                $table->string('business_name')->nullable()->after('name');
            });
        }

        if (!Schema::hasColumn('ec_customers', 'license_number')) {
            Schema::table('ec_customers', function (Blueprint $table) {
                $table->string('license_number')->nullable()->after('business_name');
            });
        }

        if (!Schema::hasColumn('ec_customers', 'tax_number')) {
            Schema::table('ec_customers', function (Blueprint $table) {
                $table->string('tax_number')->nullable()->after('license_number');
            });
        }

        if (!Schema::hasColumn('ec_customers', 'account_type')) {
            Schema::table('ec_customers', function (Blueprint $table) {
                $table->enum('account_type', ['individual', 'business'])->default('individual')->after('tax_number');
            });
        }

        if (!Schema::hasColumn('ec_customers', 'is_verified_vendor')) {
            Schema::table('ec_customers', function (Blueprint $table) {
                $table->boolean('is_verified_vendor')->default(false)->after('account_type');
            });
        }

        // تحديث جدول المنتجات لدعم التسعير المتدرج
        if (!Schema::hasColumn('ec_products', 'has_tiered_pricing')) {
            Schema::table('ec_products', function (Blueprint $table) {
                $table->boolean('has_tiered_pricing')->default(false)->after('price');
            });
        }

        if (!Schema::hasColumn('ec_products', 'vendor_cost')) {
            Schema::table('ec_products', function (Blueprint $table) {
                $table->decimal('vendor_cost', 10, 2)->nullable()->after('has_tiered_pricing');
            });
        }

        if (!Schema::hasColumn('ec_products', 'suggested_retail_price')) {
            Schema::table('ec_products', function (Blueprint $table) {
                $table->decimal('suggested_retail_price', 10, 2)->nullable()->after('vendor_cost');
            });
        }
    }

    public function down(): void
    {
        Schema::table('ec_products', function (Blueprint $table) {
            $table->dropColumn(['has_tiered_pricing', 'vendor_cost', 'suggested_retail_price']);
        });

        Schema::table('ec_customers', function (Blueprint $table) {
            $table->dropColumn([
                'business_name', 'license_number', 'tax_number', 
                'account_type', 'is_verified_vendor', 'vendor_verified_at'
            ]);
        });

        Schema::dropIfExists('user_type_verification_requests');
        Schema::dropIfExists('price_view_logs');
        Schema::dropIfExists('category_tiered_pricing');
        Schema::dropIfExists('product_tiered_pricing');
        Schema::dropIfExists('customer_user_types');

        Schema::table('user_types', function (Blueprint $table) {
            $table->dropColumn([
                'slug', 'default_discount_percentage', 'allowed_features', 
                'priority', 'can_see_other_prices', 'is_vendor', 'requires_verification'
            ]);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول أسئلة الاختبارات
        Schema::create('quiz_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quiz_id')->constrained('course_quizzes')->onDelete('cascade');
            $table->text('question');
            $table->enum('type', ['multiple_choice', 'true_false', 'text']);
            $table->json('options')->nullable(); // للأسئلة متعددة الخيارات
            $table->text('correct_answer');
            $table->text('explanation')->nullable();
            $table->integer('points')->default(1);
            $table->integer('order')->default(0);
            $table->timestamps();
            
            $table->index(['quiz_id', 'order']);
        });

        // جدول محاولات الاختبارات
        Schema::create('quiz_attempts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quiz_id')->constrained('course_quizzes')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->json('answers')->nullable();
            $table->decimal('score', 5, 2)->default(0);
            $table->boolean('passed')->default(false);
            $table->integer('time_taken_minutes')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            $table->index(['quiz_id', 'user_id']);
            $table->index(['user_id', 'completed_at']);
        });

        // تحديث جدول course_quizzes لإضافة الحقول الجديدة
        Schema::table('course_quizzes', function (Blueprint $table) {
            $table->boolean('randomize_questions')->default(false)->after('max_attempts');
            $table->boolean('show_results_immediately')->default(true)->after('randomize_questions');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('quiz_attempts');
        Schema::dropIfExists('quiz_questions');
        
        Schema::table('course_quizzes', function (Blueprint $table) {
            $table->dropColumn(['randomize_questions', 'show_results_immediately']);
        });
    }
};

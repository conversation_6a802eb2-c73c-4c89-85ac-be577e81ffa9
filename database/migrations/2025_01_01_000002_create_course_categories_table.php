<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('course_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('icon')->nullable();
            $table->string('color')->default('primary');
            $table->foreignId('parent_id')->nullable()->constrained('course_categories')->onDelete('cascade');
            $table->integer('order_index')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->timestamps();
            
            $table->index(['is_active', 'order_index']);
            $table->index('parent_id');
        });

        // إضافة عمود category_id لجدول academy_courses
        Schema::table('academy_courses', function (Blueprint $table) {
            $table->foreignId('category_id')->nullable()->after('category')->constrained('course_categories')->onDelete('set null');
            $table->index('category_id');
        });
    }

    public function down(): void
    {
        Schema::table('academy_courses', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropColumn('category_id');
        });
        
        Schema::dropIfExists('course_categories');
    }
};

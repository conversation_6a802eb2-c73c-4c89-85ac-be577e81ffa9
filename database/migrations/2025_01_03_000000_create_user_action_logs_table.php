<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول تسجيل إجراءات المستخدمين
        if (!Schema::hasTable('user_action_logs')) {
            Schema::create('user_action_logs', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('user_id'); // معرف المستخدم
                $table->enum('user_type', ['admin', 'customer']); // نوع المستخدم
                $table->string('action'); // الإجراء المنفذ
                $table->string('route')->nullable(); // اسم المسار
                $table->string('method'); // طريقة HTTP
                $table->text('url'); // الرابط الكامل
                $table->string('ip_address')->nullable(); // عنوان IP
                $table->text('user_agent')->nullable(); // معلومات المتصفح
                $table->json('request_data')->nullable(); // بيانات الطلب
                $table->integer('response_status')->default(200); // حالة الاستجابة
                $table->text('notes')->nullable(); // ملاحظات إضافية
                $table->timestamps();
                
                // الفهارس
                $table->index(['user_id', 'user_type']);
                $table->index('action');
                $table->index('created_at');
                $table->index('response_status');
                $table->index(['user_type', 'action']);
            });
        }

        // جدول قيود الوصول للأسعار
        if (!Schema::hasTable('price_access_restrictions')) {
            Schema::create('price_access_restrictions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_type_id')->constrained('user_types')->onDelete('cascade');
                $table->json('restricted_user_types'); // أنواع المستخدمين المحظور رؤية أسعارهم
                $table->json('allowed_features')->nullable(); // الميزات المسموحة
                $table->boolean('can_view_all_prices')->default(false); // هل يمكن رؤية جميع الأسعار
                $table->boolean('can_export_prices')->default(false); // هل يمكن تصدير الأسعار
                $table->boolean('can_modify_prices')->default(false); // هل يمكن تعديل الأسعار
                $table->timestamps();
            });
        }

        // جدول محاولات الوصول المرفوضة
        if (!Schema::hasTable('access_denied_logs')) {
            Schema::create('access_denied_logs', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('user_id')->nullable();
                $table->enum('user_type', ['admin', 'customer', 'guest']);
                $table->string('attempted_action'); // الإجراء المحاول
                $table->string('attempted_route')->nullable(); // المسار المحاول
                $table->text('attempted_url'); // الرابط المحاول
                $table->string('denial_reason'); // سبب الرفض
                $table->string('ip_address')->nullable();
                $table->text('user_agent')->nullable();
                $table->json('request_data')->nullable();
                $table->timestamps();
                
                // الفهارس
                $table->index(['user_id', 'user_type']);
                $table->index('attempted_action');
                $table->index('created_at');
                $table->index('ip_address');
            });
        }

        // جدول جلسات المستخدمين النشطة
        if (!Schema::hasTable('active_user_sessions')) {
            Schema::create('active_user_sessions', function (Blueprint $table) {
                $table->id();
                $table->string('session_id')->unique();
                $table->unsignedBigInteger('user_id');
                $table->enum('user_type', ['admin', 'customer']);
                $table->string('ip_address')->nullable();
                $table->text('user_agent')->nullable();
                $table->timestamp('last_activity');
                $table->json('session_data')->nullable(); // بيانات الجلسة
                $table->boolean('is_active')->default(true);
                $table->timestamps();
                
                // الفهارس
                $table->index(['user_id', 'user_type']);
                $table->index('session_id');
                $table->index('last_activity');
                $table->index('is_active');
            });
        }

        // جدول قواعد الأمان
        if (!Schema::hasTable('security_rules')) {
            Schema::create('security_rules', function (Blueprint $table) {
                $table->id();
                $table->string('rule_name'); // اسم القاعدة
                $table->text('description'); // وصف القاعدة
                $table->enum('rule_type', ['access_control', 'rate_limiting', 'data_protection', 'audit']); // نوع القاعدة
                $table->json('rule_config'); // تكوين القاعدة
                $table->json('applies_to_user_types'); // أنواع المستخدمين المطبقة عليهم
                $table->boolean('is_active')->default(true);
                $table->integer('priority')->default(0); // أولوية التطبيق
                $table->timestamps();
                
                // الفهارس
                $table->index('rule_type');
                $table->index('is_active');
                $table->index('priority');
            });
        }

        // جدول انتهاكات الأمان
        if (!Schema::hasTable('security_violations')) {
            Schema::create('security_violations', function (Blueprint $table) {
                $table->id();
                $table->foreignId('security_rule_id')->constrained('security_rules')->onDelete('cascade');
                $table->unsignedBigInteger('user_id')->nullable();
                $table->enum('user_type', ['admin', 'customer', 'guest']);
                $table->string('violation_type'); // نوع الانتهاك
                $table->text('violation_description'); // وصف الانتهاك
                $table->enum('severity', ['low', 'medium', 'high', 'critical']); // مستوى الخطورة
                $table->string('ip_address')->nullable();
                $table->text('user_agent')->nullable();
                $table->json('violation_data')->nullable(); // بيانات الانتهاك
                $table->enum('status', ['pending', 'reviewed', 'resolved', 'ignored'])->default('pending');
                $table->text('admin_notes')->nullable(); // ملاحظات الإدارة
                $table->foreignId('reviewed_by')->nullable()->constrained('users');
                $table->timestamp('reviewed_at')->nullable();
                $table->timestamps();
                
                // الفهارس
                $table->index(['user_id', 'user_type']);
                $table->index('violation_type');
                $table->index('severity');
                $table->index('status');
                $table->index('created_at');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('security_violations');
        Schema::dropIfExists('security_rules');
        Schema::dropIfExists('active_user_sessions');
        Schema::dropIfExists('access_denied_logs');
        Schema::dropIfExists('price_access_restrictions');
        Schema::dropIfExists('user_action_logs');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE course_contents MODIFY COLUMN type ENUM('video', 'pdf', 'text', 'quiz', 'assignment')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE course_contents MODIFY COLUMN type ENUM('video', 'pdf', 'text', 'quiz')");
    }
};

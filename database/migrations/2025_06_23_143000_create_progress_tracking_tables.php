<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول تسجيل الطلاب في الدورات
        Schema::create('course_enrollments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('course_id')->constrained('academy_courses')->onDelete('cascade');
            $table->timestamp('enrollment_date');
            $table->timestamp('completion_date')->nullable();
            $table->enum('status', ['enrolled', 'in_progress', 'completed', 'dropped', 'suspended'])->default('enrolled');
            $table->decimal('progress_percentage', 5, 2)->default(0);
            $table->integer('total_time_spent')->default(0); // بالدقائق
            $table->boolean('certificate_issued')->default(false);
            $table->timestamps();
            
            $table->unique(['user_id', 'course_id']);
            $table->index(['course_id', 'status']);
            $table->index(['user_id', 'status']);
        });

        // جدول تتبع تقدم الطلاب في المحتوى
        Schema::create('student_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('course_id')->constrained('academy_courses')->onDelete('cascade');
            $table->foreignId('content_id')->nullable()->constrained('course_contents')->onDelete('cascade');
            $table->enum('status', ['not_started', 'in_progress', 'completed', 'paused'])->default('not_started');
            $table->decimal('progress_percentage', 5, 2)->default(0);
            $table->integer('time_spent_minutes')->default(0);
            $table->timestamp('last_accessed_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            $table->unique(['user_id', 'course_id', 'content_id']);
            $table->index(['user_id', 'course_id']);
            $table->index(['content_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('student_progress');
        Schema::dropIfExists('course_enrollments');
    }
};

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Botble\PerfectPharma\Models\PharmacyInventory;
use <PERSON><PERSON>ble\PerfectPharma\Models\Pharmacy;
use Botble\Ecommerce\Models\Product;
use Carbon\Carbon;

class InventorySeeder extends Seeder
{
    public function run()
    {
        // الحصول على الصيدليات والمنتجات الموجودة
        $pharmacies = Pharmacy::where('is_active', true)->get();
        $products = Product::where('status', 'published')->take(20)->get();

        if ($pharmacies->isEmpty() || $products->isEmpty()) {
            $this->command->info('لا توجد صيدليات أو منتجات لإنشاء بيانات المخزون');
            return;
        }

        $storageConditions = ['room_temperature', 'refrigerated', 'frozen', 'controlled'];
        $suppliers = ['شركة الدواء المتقدم', 'مؤسسة الصحة الطبية', 'شركة الأدوية الحديثة', 'مختبرات الشفاء'];

        foreach ($pharmacies as $pharmacy) {
            // إضافة 10-15 منتج لكل صيدلية
            $selectedProducts = $products->random(rand(10, 15));
            
            foreach ($selectedProducts as $product) {
                // تجنب التكرار
                $exists = PharmacyInventory::where('pharmacy_id', $pharmacy->id)
                    ->where('product_id', $product->id)
                    ->exists();
                
                if ($exists) {
                    continue;
                }

                $costPrice = rand(10, 500);
                $sellingPrice = $costPrice * (1 + (rand(10, 50) / 100)); // هامش ربح 10-50%
                $quantity = rand(0, 1000);
                $minLevel = rand(5, 20);
                $maxLevel = rand(500, 1500);
                $reorderLevel = rand($minLevel, $minLevel + 20);

                // تواريخ عشوائية
                $manufacturingDate = Carbon::now()->subDays(rand(30, 365));
                $expiryDate = $manufacturingDate->copy()->addDays(rand(365, 1095)); // 1-3 سنوات

                $inventory = PharmacyInventory::create([
                    'pharmacy_id' => $pharmacy->id,
                    'product_id' => $product->id,
                    'batch_number' => 'BATCH' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                    'expiry_date' => $expiryDate,
                    'manufacturing_date' => $manufacturingDate,
                    'quantity_in_stock' => $quantity,
                    'reserved_quantity' => rand(0, min(10, $quantity)),
                    'minimum_stock_level' => $minLevel,
                    'maximum_stock_level' => $maxLevel,
                    'reorder_level' => $reorderLevel,
                    'cost_price' => $costPrice,
                    'selling_price' => $sellingPrice,
                    'supplier_name' => $suppliers[array_rand($suppliers)],
                    'storage_location' => 'رف ' . chr(65 + rand(0, 5)) . rand(1, 10), // رف A1, B2, etc.
                    'storage_condition' => $storageConditions[array_rand($storageConditions)],
                    'is_active' => true,
                    'is_prescription_required' => rand(0, 1),
                    'notes' => rand(0, 1) ? 'ملاحظات تجريبية للمنتج' : null,
                ]);

                // إنشاء حركة إدخال أولية
                if ($quantity > 0) {
                    $inventory->addStock(
                        $quantity,
                        'إدخال أولي للمخزون - بيانات تجريبية'
                    );
                }

                // إنشاء بعض الحركات العشوائية
                $movementsCount = rand(1, 5);
                for ($i = 0; $i < $movementsCount; $i++) {
                    $moveType = rand(1, 3);
                    $moveQuantity = rand(1, min(50, $inventory->available_quantity));
                    
                    if ($moveType == 1 && $inventory->available_quantity >= $moveQuantity) {
                        // خصم
                        $inventory->removeStock(
                            $moveQuantity,
                            'بيع - بيانات تجريبية'
                        );
                    } elseif ($moveType == 2) {
                        // إضافة
                        $inventory->addStock(
                            rand(10, 100),
                            'توريد جديد - بيانات تجريبية'
                        );
                    }
                }

                // فحص وإنشاء التنبيهات
                $inventory->fresh()->checkAndCreateAlerts();
            }
        }

        $this->command->info('تم إنشاء بيانات المخزون التجريبية بنجاح');
    }
}

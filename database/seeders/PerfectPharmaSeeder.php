<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class PerfectPharmaSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء عملاء تجريبيين
        $customers = [
            [
                'name' => 'أحمد محمد علي',
                'email' => '<EMAIL>',
                'phone' => '01234567890',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'فاطمة أحمد',
                'email' => '<EMAIL>',
                'phone' => '01234567891',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'محمد حسن',
                'email' => '<EMAIL>',
                'phone' => '01234567892',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($customers as $customer) {
            $customerId = DB::table('ec_customers')->insertGetId($customer);
            
            // إنشاء مريض لكل عميل
            DB::table('patients')->insert([
                'user_id' => $customerId,
                'patient_code' => 'P' . str_pad($customerId, 6, '0', STR_PAD_LEFT),
                'birth_date' => Carbon::now()->subYears(rand(20, 60)),
                'gender' => rand(0, 1) ? 'male' : 'female',
                'blood_type' => ['A+', 'B+', 'O+', 'AB+', 'A-', 'B-', 'O-', 'AB-'][rand(0, 7)],
                'medical_history' => 'تاريخ طبي تجريبي',
                'allergies' => 'حساسية من البنسلين',
                'chronic_diseases' => rand(0, 1) ? 'السكري' : null,
                'is_chronic_patient' => rand(0, 1),
                'emergency_contact_name' => 'جهة اتصال طوارئ',
                'emergency_contact_phone' => '01234567899',
                'national_id' => '1234567890123' . $customerId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // إنشاء أطباء تجريبيين
        $doctors = [
            [
                'name' => 'د. أحمد السيد',
                'email' => '<EMAIL>',
                'phone' => '01234567893',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'د. سارة محمد',
                'email' => '<EMAIL>',
                'phone' => '01234567894',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($doctors as $doctor) {
            $doctorId = DB::table('ec_customers')->insertGetId($doctor);
            
            DB::table('doctors')->insert([
                'user_id' => $doctorId,
                'doctor_code' => 'D' . str_pad($doctorId, 6, '0', STR_PAD_LEFT),
                'specialization' => ['باطنة', 'جراحة', 'أطفال', 'نساء وتوليد'][rand(0, 3)],
                'license_number' => 'LIC' . rand(100000, 999999),
                'qualification' => 'بكالوريوس طب وجراحة',
                'bio' => 'طبيب متخصص مع خبرة واسعة',
                'clinic_address' => 'عنوان العيادة التجريبي',
                'clinic_phone' => '01234567899',
                'consultation_fee' => rand(200, 500),
                'is_verified' => rand(0, 1),
                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // إنشاء صيدليات تجريبية
        $pharmacies = [
            [
                'name' => 'صيدلية النور',
                'email' => '<EMAIL>',
                'phone' => '01234567895',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'صيدلية الشفاء',
                'email' => '<EMAIL>',
                'phone' => '01234567896',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($pharmacies as $pharmacy) {
            $pharmacyId = DB::table('ec_customers')->insertGetId($pharmacy);
            
            DB::table('pharmacies')->insert([
                'user_id' => $pharmacyId,
                'pharmacy_code' => 'PH' . str_pad($pharmacyId, 6, '0', STR_PAD_LEFT),
                'license_number' => 'PHLIC' . rand(100000, 999999),
                'name' => $pharmacy['name'],
                'address' => 'عنوان الصيدلية التجريبي',
                'phone' => $pharmacy['phone'],
                'email' => $pharmacy['email'],
                'latitude' => 30.0444 + (rand(-1000, 1000) / 10000),
                'longitude' => 31.2357 + (rand(-1000, 1000) / 10000),
                'home_delivery' => rand(0, 1),
                'delivery_fee' => rand(10, 50),
                'delivery_radius_km' => rand(5, 15),
                'is_verified' => rand(0, 1),
                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // إنشاء معامل تجريبية
        $labs = [
            [
                'name' => 'معمل الدقة',
                'email' => '<EMAIL>',
                'phone' => '01234567897',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'معمل التحاليل المتقدمة',
                'email' => '<EMAIL>',
                'phone' => '01234567898',
                'password' => Hash::make('123456'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($labs as $lab) {
            $labId = DB::table('ec_customers')->insertGetId($lab);
            
            DB::table('labs')->insert([
                'user_id' => $labId,
                'lab_code' => 'L' . str_pad($labId, 6, '0', STR_PAD_LEFT),
                'license_number' => 'LABLIC' . rand(100000, 999999),
                'name' => $lab['name'],
                'address' => 'عنوان المعمل التجريبي',
                'phone' => $lab['phone'],
                'email' => $lab['email'],
                'latitude' => 30.0444 + (rand(-1000, 1000) / 10000),
                'longitude' => 31.2357 + (rand(-1000, 1000) / 10000),
                'home_collection' => rand(0, 1),
                'collection_fee' => rand(50, 100),
                'is_verified' => rand(0, 1),
                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // إنشاء محافظ تجريبية
        $customerIds = DB::table('ec_customers')->pluck('id');
        foreach ($customerIds as $customerId) {
            DB::table('pharma_wallets')->insert([
                'user_id' => $customerId,
                'wallet_number' => 'W' . str_pad($customerId, 8, '0', STR_PAD_LEFT),
                'balance' => rand(100, 5000),
                'currency' => 'EGP',
                'is_active' => 1,
                'is_verified' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // إنشاء دورات تدريبية تجريبية
        $courses = [
            [
                'title' => 'أساسيات الصيدلة الإكلينيكية',
                'description' => 'دورة شاملة في أساسيات الصيدلة الإكلينيكية',
                'duration_hours' => 40,
                'price' => 500,
                'max_students' => 50,
                'status' => 'published',
                'is_featured' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'التفاعلات الدوائية',
                'description' => 'دورة متقدمة في التفاعلات الدوائية',
                'duration_hours' => 30,
                'price' => 400,
                'max_students' => 30,
                'status' => 'published',
                'is_featured' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($courses as $course) {
            DB::table('academy_courses')->insert($course);
        }

        echo "تم إنشاء البيانات التجريبية بنجاح!\n";
        echo "- " . count($customers) . " مرضى\n";
        echo "- " . count($doctors) . " أطباء\n";
        echo "- " . count($pharmacies) . " صيدليات\n";
        echo "- " . count($labs) . " معامل\n";
        echo "- " . count($customerIds) . " محافظ مالية\n";
        echo "- " . count($courses) . " دورات تدريبية\n";
    }
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Botble\PerfectPharma\Models\UserType;

class UserTypesSeeder extends Seeder
{
    public function run(): void
    {
        $userTypes = UserType::getDefaultTypes();

        foreach ($userTypes as $typeData) {
            UserType::updateOrCreate(
                ['slug' => $typeData['slug']],
                $typeData + ['is_active' => true]
            );
        }

        $this->command->info('تم إنشاء أنواع المستخدمين الافتراضية بنجاح');
    }
}

# 🗄️ تصميم قاعدة البيانات - Perfect Pharma

## 📋 نظرة عامة

تم تصميم قاعدة البيانات لتدعم نظام Perfect Pharma المتكامل الذي يربط بين:
- المرضى
- الأطباء  
- الصيدليات
- المعامل
- المشرفين
- المتبرعين

## 🏗️ الجداول الأساسية

### 1. إدارة المستخدمين

#### `user_types` - أنواع المستخدمين
```sql
- id (PK)
- name (patient, doctor, pharmacy, lab, admin, donor)
- display_name
- description
- permissions (JSON)
- is_active
```

#### `patients` - المرضى
```sql
- id (PK)
- patient_code (UNIQUE) - رقم تعريفي فريد
- user_id (FK -> ec_customers)
- birth_date
- gender (male/female)
- blood_type
- medical_history
- allergies
- chronic_diseases
- emergency_contact_name
- emergency_contact_phone
- national_id
- is_chronic_patient
```

#### `doctors` - الأطباء
```sql
- id (PK)
- doctor_code (UNIQUE)
- user_id (FK -> users)
- license_number (UNIQUE)
- specialization
- qualification
- bio
- clinic_address
- clinic_phone
- work_start_time
- work_end_time
- work_days (JSON)
- consultation_fee
- is_verified
- is_active
```

#### `pharmacies` - الصيدليات
```sql
- id (PK)
- pharmacy_code (UNIQUE)
- user_id (FK -> users)
- license_number (UNIQUE)
- name
- description
- address
- phone, email
- latitude, longitude
- opening_time, closing_time
- is_24_hours
- accepts_insurance
- home_delivery
- delivery_fee
- delivery_radius_km
- is_verified, is_active
```

#### `labs` - المعامل
```sql
- id (PK)
- lab_code (UNIQUE)
- user_id (FK -> users)
- license_number (UNIQUE)
- name, description
- address, phone, email
- latitude, longitude
- opening_time, closing_time
- home_collection
- collection_fee
- available_tests (JSON)
- is_verified, is_active
```

### 2. نظام المحفظة الرقمية

#### `pharma_wallets` - المحافظ
```sql
- id (PK)
- user_id (FK -> users)
- wallet_number (UNIQUE)
- balance
- pending_balance
- currency (default: EGP)
- is_active, is_verified
- last_transaction_at
```

#### `wallet_transactions` - معاملات المحفظة
```sql
- id (PK)
- wallet_id (FK -> pharma_wallets)
- transaction_id (UNIQUE)
- type (credit, debit, transfer_in, transfer_out, refund)
- amount
- balance_before, balance_after
- reference_type, reference_id
- description
- status (pending, completed, failed, cancelled)
- metadata (JSON)
```

### 3. النظام الطبي

#### `prescriptions` - الوصفات الطبية
```sql
- id (PK)
- prescription_number (UNIQUE)
- patient_id (FK -> patients)
- doctor_id (FK -> doctors)
- diagnosis
- symptoms
- notes
- prescription_date
- expiry_date
- status (active, partially_filled, completed, expired, cancelled)
- is_chronic
- refill_count, max_refills
```

#### `prescription_medications` - أدوية الوصفة
```sql
- id (PK)
- prescription_id (FK -> prescriptions)
- product_id (FK -> ec_products)
- medication_name
- dosage, frequency
- duration_days
- quantity, dispensed_quantity
- instructions
- status (pending, partially_dispensed, completed)
```

#### `lab_test_requests` - طلبات التحاليل
```sql
- id (PK)
- request_number (UNIQUE)
- patient_id (FK -> patients)
- doctor_id (FK -> doctors)
- lab_id (FK -> labs)
- clinical_data
- notes
- request_date, required_date
- priority (normal, urgent, emergency)
- status (pending, accepted, in_progress, completed, cancelled)
- total_cost, is_paid
```

#### `lab_test_items` - تفاصيل التحاليل
```sql
- id (PK)
- request_id (FK -> lab_test_requests)
- test_name, test_code
- test_description
- price
- status (pending, in_progress, completed)
```

#### `lab_test_results` - نتائج التحاليل
```sql
- id (PK)
- test_item_id (FK -> lab_test_items)
- parameter_name
- result_value
- unit
- reference_range
- status (normal, abnormal, critical)
- notes
```

### 4. نظام الصرف والتوزيع

#### `medication_dispensings` - صرف الأدوية
```sql
- id (PK)
- dispensing_number (UNIQUE)
- prescription_medication_id (FK)
- pharmacy_id (FK -> pharmacies)
- pharmacist_id (FK -> users)
- quantity_dispensed
- unit_price, total_price
- discount_amount, final_price
- dispensing_date
- pharmacist_notes
- payment_method
```

#### `chronic_medications` - الأدوية المزمنة
```sql
- id (PK)
- patient_id (FK -> patients)
- product_id (FK -> ec_products)
- medication_name
- dosage, frequency
- refill_interval_days
- last_refill_date, next_refill_date
- auto_reminder
- is_active
```

#### `inter_pharmacy_orders` - التوزيع بين الصيدليات
```sql
- id (PK)
- order_number (UNIQUE)
- requesting_pharmacy_id (FK -> pharmacies)
- supplying_pharmacy_id (FK -> pharmacies)
- total_amount
- status (pending, accepted, rejected, shipped, delivered, completed)
- notes
- requested_date, delivery_date
- payment_method
- is_paid
```

#### `inter_pharmacy_order_items` - تفاصيل طلبات التوزيع
```sql
- id (PK)
- order_id (FK -> inter_pharmacy_orders)
- product_id (FK -> ec_products)
- quantity
- unit_price, total_price
- expiry_date
- batch_number
```

### 5. الأكاديمية التعليمية

#### `academy_courses` - الدورات
```sql
- id (PK)
- title, description, content
- category (pharmacy, medical_marketing, pharmacy_assistant)
- type (video, pdf, mixed, live)
- instructor_name, instructor_bio
- price, duration_hours
- difficulty_level
- image, video_url, pdf_url
- learning_objectives (JSON)
- has_certificate
- passing_score
- is_featured
- status (draft, published, archived)
```

#### `course_contents` - محتوى الدورات
```sql
- id (PK)
- course_id (FK -> academy_courses)
- title, description
- type (video, pdf, text, quiz)
- content_url, text_content
- order, duration_minutes
- is_free
```

#### `course_progress` - تقدم الطلاب
```sql
- id (PK)
- user_id (FK -> users)
- course_id (FK -> academy_courses)
- content_id (FK -> course_contents)
- status (not_started, in_progress, completed)
- progress_percentage
- started_at, completed_at
- time_spent_minutes
```

### 6. نظام التبرعات

#### `charity_hospitals` - المستشفيات الخيرية
```sql
- id (PK)
- name, description
- address, phone, email, website
- license_number (UNIQUE)
- latitude, longitude
- contact_person_name, contact_person_phone
- needed_specialties (JSON)
- needed_medications (JSON)
- needed_equipment (JSON)
- is_verified, is_active
```

#### `donations` - التبرعات
```sql
- id (PK)
- donation_number (UNIQUE)
- user_id (FK -> users)
- hospital_id (FK -> charity_hospitals)
- type (money, medication, equipment, service)
- amount
- description
- items (JSON)
- payment_method
- status (pending, approved, delivered, completed, cancelled)
- donation_date, delivery_date
- notes
- is_anonymous
- receipt_number
```

## 🔗 العلاقات الرئيسية

1. **مريض ← وصفات ← أدوية ← صرف**
2. **مريض ← طلبات تحاليل ← تفاصيل ← نتائج**
3. **صيدلية ← طلبات توزيع ← تفاصيل**
4. **مستخدم ← محفظة ← معاملات**
5. **مستخدم ← دورات ← تقدم ← شهادات**
6. **مستخدم ← تبرعات ← مستشفيات**

## 📊 الفهارس المقترحة

```sql
-- فهارس الأداء
CREATE INDEX idx_patients_code ON patients(patient_code);
CREATE INDEX idx_prescriptions_patient ON prescriptions(patient_id, status);
CREATE INDEX idx_wallet_transactions_wallet ON wallet_transactions(wallet_id, created_at);
CREATE INDEX idx_lab_requests_patient ON lab_test_requests(patient_id, status);
CREATE INDEX idx_notifications_user ON pharma_notifications(user_id, is_read);
```

## 🔒 الأمان والخصوصية

- تشفير البيانات الطبية الحساسة
- صلاحيات متدرجة حسب نوع المستخدم
- تسجيل جميع العمليات الحساسة
- نسخ احتياطية دورية
- التوافق مع قوانين حماية البيانات

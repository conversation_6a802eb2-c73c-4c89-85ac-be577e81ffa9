<?php

use <PERSON><PERSON><PERSON>\Slug\Facades\SlugHelper;
use Bo<PERSON>ble\Theme\Events\ThemeRoutingAfterEvent;
use <PERSON><PERSON>ble\Theme\Events\ThemeRoutingBeforeEvent;
use <PERSON><PERSON>ble\Theme\Facades\SiteMapManager;
use Bo<PERSON>ble\Theme\Facades\Theme;
use Bo<PERSON>ble\Theme\Http\Controllers\PublicController;
use Illuminate\Support\Facades\Route;

Theme::registerRoutes(function (): void {
    Route::group(['controller' => PublicController::class], function (): void {
        event(new ThemeRoutingBeforeEvent(app()->make('router')));

        Route::get('/', 'getIndex')->name('public.index');

        // Main sitemap index
        Route::get('sitemap.xml', 'getSiteMap')->name('public.sitemap');

        // Handle both standard and paginated sitemaps
        Route::get('{key}.{extension}', 'getSiteMapIndex')
            ->whereIn('extension', SiteMapManager::allowedExtensions())
            ->name('public.sitemap.index');

        Route::get('{slug?}', 'getView')->name('public.single');

        Route::get('{prefix}/{slug?}', 'getViewWithPrefix')
            ->whereIn('prefix', SlugHelper::getAllPrefixes() ?: ['1437bcd2-d94e-4a5fd-9a39-b5d60225e9af']);

        event(new ThemeRoutingAfterEvent(app()->make('router')));
    });
});

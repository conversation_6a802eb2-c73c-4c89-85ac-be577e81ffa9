# نظام إدارة المخزون الذكية - Perfect Pharma

## 📋 نظرة عامة

نظام إدارة المخزون الذكية هو جزء متكامل من منصة Perfect Pharma يوفر إدارة شاملة ومتقدمة لمخزون الصيدليات مع تنبيهات ذكية وتتبع دقيق لجميع الحركات.

## 🚀 المميزات الرئيسية

### 📊 **إدارة المخزون المتقدمة**
- تتبع دقيق للكميات (المخزون، المحجوز، المتاح)
- إدارة مستويات المخزون (الحد الأدنى، الأقصى، إعادة الطلب)
- تتبع تواريخ الإنتاج وانتهاء الصلاحية
- إدارة أرقام الدفعات والموردين

### 🔔 **التنبيهات الذكية**
- تنبيهات نفاد المخزون (أولوية حرجة)
- تنبيهات المخزون المنخفض (أولوية عالية)
- تنبيهات انتهاء الصلاحية (أولوية حرجة)
- تنبيهات قرب انتهاء الصلاحية (أولوية عالية)
- تنبيهات المخزون الزائد

### 📈 **تتبع الحركات**
- تسجيل جميع حركات الإدخال والإخراج
- تتبع أسباب الحركات والمستخدمين
- حساب التكاليف والقيم
- سجل تاريخي شامل لجميع العمليات

### 🏪 **ظروف التخزين**
- درجة حرارة الغرفة (15-25°C)
- مبرد (2-8°C)
- مجمد (-18°C)
- ظروف خاصة (حسب المنتج)

## 🗄️ **هيكل قاعدة البيانات**

### الجداول الرئيسية:

#### `pharmacy_inventory`
```sql
- id: المعرف الفريد
- pharmacy_id: معرف الصيدلية
- product_id: معرف المنتج
- batch_number: رقم الدفعة
- expiry_date: تاريخ انتهاء الصلاحية
- manufacturing_date: تاريخ الإنتاج
- quantity_in_stock: الكمية في المخزون
- reserved_quantity: الكمية المحجوزة
- minimum_stock_level: الحد الأدنى للمخزون
- maximum_stock_level: الحد الأقصى للمخزون
- reorder_level: مستوى إعادة الطلب
- cost_price: سعر التكلفة
- selling_price: سعر البيع
- supplier_name: اسم المورد
- storage_location: موقع التخزين
- storage_condition: ظروف التخزين
- is_active: حالة النشاط
- is_prescription_required: يتطلب وصفة طبية
- notes: ملاحظات
```

#### `inventory_movements`
```sql
- id: المعرف الفريد
- pharmacy_inventory_id: معرف المخزون
- user_id: معرف المستخدم
- movement_number: رقم الحركة
- type: نوع الحركة (in, out, adjustment, transfer, return, expired, damaged)
- quantity: الكمية
- quantity_before: الكمية قبل الحركة
- quantity_after: الكمية بعد الحركة
- unit_cost: تكلفة الوحدة
- total_cost: التكلفة الإجمالية
- reference_type: نوع المرجع
- reference_id: معرف المرجع
- reason: السبب
- notes: ملاحظات
- movement_date: تاريخ الحركة
```

#### `inventory_alerts`
```sql
- id: المعرف الفريد
- pharmacy_id: معرف الصيدلية
- product_id: معرف المنتج
- pharmacy_inventory_id: معرف المخزون
- type: نوع التنبيه (low_stock, out_of_stock, expiry_warning, expired, overstock)
- priority: الأولوية (low, medium, high, critical)
- title: العنوان
- message: الرسالة
- current_quantity: الكمية الحالية
- threshold_quantity: كمية العتبة
- expiry_date: تاريخ انتهاء الصلاحية
- days_to_expiry: الأيام المتبقية للانتهاء
- is_read: مقروء
- is_resolved: محلول
- resolved_at: تاريخ الحل
- resolved_by: من قام بالحل
- resolution_notes: ملاحظات الحل
```

## 🎛️ **واجهات المستخدم**

### 📋 **الصفحة الرئيسية** (`/admin/inventory`)
- إحصائيات شاملة للمخزون
- فلاتر بحث متقدمة
- عرض جدولي للمنتجات مع حالاتها
- مؤشرات بصرية للحالات المختلفة

### ➕ **إضافة منتج** (`/admin/inventory/create`)
- نموذج شامل لإدخال بيانات المنتج
- التحقق من صحة البيانات
- حساب هامش الربح تلقائياً
- إعدادات ظروف التخزين

### 👁️ **عرض التفاصيل** (`/admin/inventory/{id}`)
- معلومات شاملة عن المنتج
- إحصائيات المخزون مع مؤشرات بصرية
- إجراءات سريعة (إضافة، خصم، تعديل)
- آخر الحركات والتنبيهات

### ✏️ **تعديل المنتج** (`/admin/inventory/{id}/edit`)
- تعديل جميع بيانات المنتج
- التحقق من صحة المستويات
- حساب هامش الربح المحدث
- معلومات إحصائية

### 📊 **حركات المخزون** (`/admin/inventory/{id}/movements`)
- سجل شامل لجميع الحركات
- فلاتر حسب النوع والتاريخ
- إجراءات سريعة للمخزون
- تفاصيل كل حركة

### 🔔 **التنبيهات** (`/admin/inventory/alerts`)
- إحصائيات التنبيهات
- فلاتر متقدمة للتنبيهات
- إدارة حالة التنبيهات
- حل التنبيهات مع الملاحظات

## 🔧 **الوظائف البرمجية**

### **PharmacyInventory Model**
```php
// الخصائص المحسوبة
$inventory->available_quantity    // الكمية المتاحة
$inventory->stock_percentage     // نسبة المخزون
$inventory->stock_status        // حالة المخزون
$inventory->days_to_expiry      // أيام انتهاء الصلاحية
$inventory->profit_margin       // هامش الربح

// الوظائف الرئيسية
$inventory->addStock($quantity, $reason)           // إضافة مخزون
$inventory->removeStock($quantity, $reason)        // خصم مخزون
$inventory->adjustStock($newQuantity, $reason)     // تعديل مخزون
$inventory->reserveStock($quantity)                // حجز مخزون
$inventory->unreserveStock($quantity)              // إلغاء حجز
$inventory->checkAndCreateAlerts()                 // فحص وإنشاء التنبيهات

// التحقق من الحالات
$inventory->needsReorder()       // يحتاج إعادة طلب
$inventory->isLowStock()         // مخزون منخفض
$inventory->isOutOfStock()       // نفد المخزون
$inventory->isExpired()          // منتهي الصلاحية
$inventory->isExpiringSoon()     // ينتهي قريباً
```

### **InventoryMovement Model**
```php
// الخصائص المحسوبة
$movement->type_label           // نوع الحركة بالعربية
$movement->direction           // اتجاه الحركة (in/out)
$movement->icon               // أيقونة الحركة
$movement->color             // لون الحركة
$movement->description       // وصف الحركة

// الوظائف الإحصائية
InventoryMovement::getStatistics($pharmacyId, $startDate, $endDate)
InventoryMovement::getTodayMovements($pharmacyId)
InventoryMovement::getMostActiveProducts($pharmacyId, $limit)
```

### **InventoryAlert Model**
```php
// الخصائص المحسوبة
$alert->type_label             // نوع التنبيه بالعربية
$alert->priority_label         // الأولوية بالعربية
$alert->priority_color         // لون الأولوية
$alert->icon                  // أيقونة التنبيه
$alert->time_ago             // الوقت منذ الإنشاء

// إدارة التنبيهات
$alert->markAsRead()          // تحديد كمقروء
$alert->resolve($notes)       // حل التنبيه
$alert->reopen()             // إعادة فتح التنبيه

// الوظائف الإحصائية
InventoryAlert::getStatistics($pharmacyId)
InventoryAlert::getUrgentAlerts($pharmacyId)
InventoryAlert::getTodayAlerts($pharmacyId)
InventoryAlert::generateAutomaticAlerts()
```

## 🔐 **الصلاحيات**

- `inventory.index` - عرض قائمة المخزون
- `inventory.view` - عرض تفاصيل المنتج
- `inventory.create` - إضافة منتج جديد
- `inventory.edit` - تعديل بيانات المنتج
- `inventory.destroy` - حذف منتج من المخزون
- `inventory.adjust-stock` - تعديل الكميات
- `inventory.movements` - عرض حركات المخزون
- `inventory.alerts` - إدارة التنبيهات

## 🛣️ **المسارات**

```php
// المسارات الرئيسية
GET    /admin/inventory                    // قائمة المخزون
GET    /admin/inventory/create             // إضافة منتج
POST   /admin/inventory                    // حفظ منتج جديد
GET    /admin/inventory/{id}               // عرض التفاصيل
GET    /admin/inventory/{id}/edit          // تعديل المنتج
PUT    /admin/inventory/{id}               // حفظ التعديلات
GET    /admin/inventory/{id}/movements     // حركات المخزون

// إدارة الكميات
POST   /admin/inventory/{id}/add-stock     // إضافة مخزون
POST   /admin/inventory/{id}/remove-stock  // خصم مخزون
POST   /admin/inventory/{id}/adjust-stock  // تعديل مخزون

// التنبيهات
GET    /admin/inventory/alerts             // قائمة التنبيهات
POST   /admin/inventory/alerts/{id}/resolve // حل التنبيه
POST   /admin/inventory/alerts/{id}/read   // تحديد كمقروء
```

## 📱 **الاستجابة للأجهزة المحمولة**

جميع الواجهات مصممة لتكون متجاوبة مع:
- الهواتف الذكية (320px+)
- الأجهزة اللوحية (768px+)
- أجهزة سطح المكتب (1024px+)

## 🔄 **التحديث التلقائي**

- تحديث الإحصائيات كل 5 دقائق
- تحديث التنبيهات كل دقيقة
- فحص انتهاء الصلاحية يومياً
- تنظيف التنبيهات القديمة أسبوعياً

## 🚀 **الأداء والتحسين**

- استخدام الفهارس المناسبة لقاعدة البيانات
- تحميل البيانات المترابطة بكفاءة (Eager Loading)
- تخزين مؤقت للإحصائيات المعقدة
- ضغط الاستعلامات المتكررة

## 🔒 **الأمان**

- التحقق من الصلاحيات لجميع العمليات
- تسجيل جميع الحركات مع المستخدم والوقت
- حماية من SQL Injection و XSS
- تشفير البيانات الحساسة

## 📞 **الدعم والصيانة**

للحصول على الدعم أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX
- نظام التذاكر: support.perfectpharma.com

---

**تم تطوير نظام إدارة المخزون الذكية بواسطة فريق Perfect Pharma**  
**الإصدار:** 1.0.0  
**تاريخ آخر تحديث:** 2025-06-23

<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\PerfectPharma\Http\Controllers\Doctor\DashboardController;
use <PERSON><PERSON><PERSON>\PerfectPharma\Http\Controllers\Doctor\PatientController;
use <PERSON><PERSON><PERSON>\PerfectPharma\Http\Controllers\Doctor\AppointmentController;
use <PERSON><PERSON><PERSON>\PerfectPharma\Http\Controllers\Doctor\PrescriptionController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Doctor\MedicalHistoryController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Doctor\ProfileController;

/*
|--------------------------------------------------------------------------
| Doctor Routes
|--------------------------------------------------------------------------
|
| مسارات خاصة بالأطباء - تستخدم customer guard مع middleware للتحقق من نوع المستخدم
|
*/

Route::group([
    'prefix' => 'doctor',
    'as' => 'doctor.',
    'middleware' => ['web', 'customer', 'check_user_type:doctor'],
], function () {
    
    // Dashboard Routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Patient Routes
    Route::group(['prefix' => 'patients', 'as' => 'patients.'], function () {
        Route::get('/', [PatientController::class, 'index'])->name('index');
        Route::get('/{id}', [PatientController::class, 'show'])->name('show');
        Route::get('/search/ajax', [PatientController::class, 'search'])->name('search');
    });
    
    // Appointment Routes
    Route::group(['prefix' => 'appointments', 'as' => 'appointments.'], function () {
        Route::get('/', [AppointmentController::class, 'index'])->name('index');
        Route::get('/{id}', [AppointmentController::class, 'show'])->name('show');
        Route::post('/{id}/confirm', [AppointmentController::class, 'confirm'])->name('confirm');
        Route::post('/{id}/cancel', [AppointmentController::class, 'cancel'])->name('cancel');
        Route::post('/{id}/complete', [AppointmentController::class, 'complete'])->name('complete');
        Route::get('/calendar/data', [AppointmentController::class, 'calendar'])->name('calendar');
    });
    
    // Prescription Routes
    Route::group(['prefix' => 'prescriptions', 'as' => 'prescriptions.'], function () {
        Route::get('/', [PrescriptionController::class, 'index'])->name('index');
        Route::get('/create', [PrescriptionController::class, 'create'])->name('create');
        Route::post('/', [PrescriptionController::class, 'store'])->name('store');
        Route::get('/{id}', [PrescriptionController::class, 'show'])->name('show');
        Route::get('/{id}/print', [PrescriptionController::class, 'print'])->name('print');
        Route::post('/{id}/cancel', [PrescriptionController::class, 'cancel'])->name('cancel');
    });
    
    // Medical History Routes
    Route::group(['prefix' => 'medical-history', 'as' => 'medical-history.'], function () {
        Route::get('/search', function() {
            return view('plugins/perfect-pharma::doctor.medical-history.search');
        })->name('search');
        Route::get('/search-patient', [MedicalHistoryController::class, 'searchPatient'])->name('search-patient');
        Route::get('/patient/{patientId}', [MedicalHistoryController::class, 'patientHistory'])->name('patient');
        Route::get('/appointment/{appointmentId}/details', [MedicalHistoryController::class, 'appointmentDetails'])->name('appointment-details');
        Route::get('/prescription/{prescriptionId}/details', [MedicalHistoryController::class, 'prescriptionDetails'])->name('prescription-details');
    });

    // Profile Routes
    Route::group(['prefix' => 'profile', 'as' => 'profile.'], function () {
        Route::get('/', [ProfileController::class, 'index'])->name('index');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [ProfileController::class, 'update'])->name('update');
        Route::get('/change-password', [ProfileController::class, 'showChangePasswordForm'])->name('change-password');
        Route::post('/change-password', [ProfileController::class, 'changePassword'])->name('change-password.update');
        Route::get('/statistics', [ProfileController::class, 'statistics'])->name('statistics');
    });
    
});

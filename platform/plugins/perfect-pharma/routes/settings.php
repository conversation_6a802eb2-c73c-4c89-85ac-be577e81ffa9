<?php

use Bo<PERSON>ble\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function (): void {
    Route::group([
        'namespace' => 'Botble\PerfectPharma\Http\Controllers\Settings',
        'prefix' => 'perfect-pharma/settings',
        'as' => 'perfect-pharma.settings.',
        'middleware' => ['web', 'core'],
    ], function (): void {
        
        // الإعدادات العامة
        Route::get('general', [
            'as' => 'general',
            'uses' => 'GeneralSettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('general', [
            'as' => 'general.update',
            'uses' => 'GeneralSettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // إعدادات المستخدمين والخصومات
        Route::get('users', [
            'as' => 'users',
            'uses' => 'UserSettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('users', [
            'as' => 'users.update',
            'uses' => 'UserSettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // إعدادات المواعيد
        Route::get('appointments', [
            'as' => 'appointments',
            'uses' => 'AppointmentSettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('appointments', [
            'as' => 'appointments.update',
            'uses' => 'AppointmentSettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // إعدادات المحفظة
        Route::get('wallet', [
            'as' => 'wallet',
            'uses' => 'WalletSettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('wallet', [
            'as' => 'wallet.update',
            'uses' => 'WalletSettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // إعدادات الأكاديمية
        Route::get('academy', [
            'as' => 'academy',
            'uses' => 'AcademySettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('academy', [
            'as' => 'academy.update',
            'uses' => 'AcademySettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // إعدادات التبرعات
        Route::get('donations', [
            'as' => 'donations',
            'uses' => 'DonationSettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('donations', [
            'as' => 'donations.update',
            'uses' => 'DonationSettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // إعدادات الإشعارات
        Route::get('notifications', [
            'as' => 'notifications',
            'uses' => 'NotificationSettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('notifications', [
            'as' => 'notifications.update',
            'uses' => 'NotificationSettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // إعدادات الأمان
        Route::get('security', [
            'as' => 'security',
            'uses' => 'SecuritySettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('security', [
            'as' => 'security.update',
            'uses' => 'SecuritySettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // إعدادات التكامل
        Route::get('integration', [
            'as' => 'integration',
            'uses' => 'IntegrationSettingController@edit',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::put('integration', [
            'as' => 'integration.update',
            'uses' => 'IntegrationSettingController@update',
            'permission' => 'perfect-pharma.settings',
        ]);

        // تكامل العملات
        Route::get('currency-integration', [
            'as' => 'currency-integration',
            'uses' => 'CurrencyIntegrationController@index',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::post('currency-integration/initialize-sar', [
            'as' => 'currency-integration.initialize-sar',
            'uses' => 'CurrencyIntegrationController@initializeSAR',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::post('currency-integration/set-sar-default', [
            'as' => 'currency-integration.set-sar-default',
            'uses' => 'CurrencyIntegrationController@setSARAsDefault',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::get('currency-integration/status', [
            'as' => 'currency-integration.status',
            'uses' => 'CurrencyIntegrationController@getStatus',
            'permission' => 'perfect-pharma.settings',
        ]);

        Route::post('currency-integration/test-formatting', [
            'as' => 'currency-integration.test-formatting',
            'uses' => 'CurrencyIntegrationController@testFormatting',
            'permission' => 'perfect-pharma.settings',
        ]);
    });
});

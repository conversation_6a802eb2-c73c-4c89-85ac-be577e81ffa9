<?php

use Illuminate\Support\Facades\Route;

Route::group([
    'namespace' => 'Botble\PerfectPharma\Http\Controllers\Customer',
    'prefix' => 'customer/perfect-pharma',
    'as' => 'customer.perfect-pharma.',
    'middleware' => ['web', 'customer'],
], function () {
    
    // لوحة التحكم الرئيسية
    Route::get('/', 'DashboardController@index')->name('dashboard');
    
    // الملف الطبي
    Route::get('medical-profile', 'MedicalProfileController@index')->name('medical-profile');
    Route::put('medical-profile', 'MedicalProfileController@update')->name('medical-profile.update');
    
    // المواعيد
    Route::prefix('appointments')->group(function () {
        Route::get('/', 'AppointmentController@index')->name('appointments');
        Route::get('create', 'AppointmentController@create')->name('appointments.create');
        Route::post('/', 'AppointmentController@store')->name('appointments.store');
        Route::get('{id}', 'AppointmentController@show')->name('appointments.show');
        Route::post('{id}/cancel', 'AppointmentController@cancel')->name('appointments.cancel');
    });
    
    // الوصفات الطبية
    Route::prefix('prescriptions')->group(function () {
        Route::get('/', 'PrescriptionController@index')->name('prescriptions');
        Route::get('{id}', 'PrescriptionController@show')->name('prescriptions.show');
    });
    
    // التحاليل الطبية
    Route::prefix('lab-tests')->group(function () {
        Route::get('/', 'LabTestController@index')->name('lab-tests');
        Route::get('create', 'LabTestController@create')->name('lab-tests.create');
        Route::post('/', 'LabTestController@store')->name('lab-tests.store');
        Route::get('{id}', 'LabTestController@show')->name('lab-tests.show');
    });
    
    // المحفظة الرقمية
    Route::prefix('wallet')->group(function () {
        Route::get('/', 'WalletController@index')->name('wallet');
        Route::post('charge', 'WalletController@charge')->name('wallet.charge');
        Route::get('transactions', 'WalletController@transactions')->name('wallet.transactions');
    });
    
    // الأكاديمية - مؤقتاً معطلة
    /*
    Route::prefix('academy')->group(function () {
        Route::get('/', 'AcademyController@index')->name('academy');
        Route::get('courses', 'AcademyController@courses')->name('academy.courses');
        Route::get('courses/{id}', 'AcademyController@showCourse')->name('academy.courses.show');
        Route::post('courses/{id}/enroll', 'AcademyController@enrollCourse')->name('academy.courses.enroll');
        Route::get('my-courses', 'AcademyController@myCourses')->name('academy.my-courses');
        Route::get('certificates', 'AcademyController@certificates')->name('academy.certificates');
    });
    */
    
    // التبرعات - مؤقتاً معطلة
    /*
    Route::prefix('donations')->group(function () {
        Route::get('/', 'DonationController@index')->name('donations');
        Route::post('/', 'DonationController@store')->name('donations.store');
        Route::get('history', 'DonationController@history')->name('donations.history');
    });
    */
});

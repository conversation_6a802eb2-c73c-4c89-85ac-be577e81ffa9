<?php

use Illuminate\Support\Facades\Route;
use Botble\Ecommerce\Models\Product;

// مسار اختبار عرض الخصومات للمشرفين
Route::get('/admin/perfect-pharma/test-discounts', function () {
    if (!auth()->check() || !is_in_admin(true)) {
        abort(403, 'غير مصرح لك بالوصول');
    }

    $products = Product::where('enable_user_type_discounts', true)->take(5)->get();
    
    $html = '<!DOCTYPE html>
    <html>
    <head>
        <title>اختبار عرض خصومات Perfect Pharma</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
        ' . perfect_pharma_admin_styles() . '
    </head>
    <body class="admin-panel">
        <div class="container mt-4">
            <h2>🧪 اختبار عرض خصومات Perfect Pharma للمشرفين</h2>
            
            <div class="alert alert-info">
                <strong>ملاحظة:</strong> هذه الصفحة لاختبار عرض الخصومات للمشرفين فقط.
            </div>';

    if ($products->count() > 0) {
        $html .= '<div class="row">';
        
        foreach ($products as $product) {
            $html .= '<div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>' . $product->name . '</h5>
                        <small class="text-muted">السعر: ' . format_price($product->price) . '</small>
                    </div>
                    <div class="card-body">
                        <h6>عرض شارات:</h6>
                        ' . perfect_pharma_admin_discounts($product, 'badges') . '
                        
                        <hr>
                        
                        <h6>عرض مضغوط:</h6>
                        ' . perfect_pharma_admin_discounts($product, 'inline') . '
                        
                        <hr>
                        
                        <h6>عرض ملخص:</h6>
                        ' . perfect_pharma_admin_discounts($product, 'summary') . '
                        
                        <hr>
                        
                        <h6>عرض جدول مفصل:</h6>
                        ' . perfect_pharma_admin_discounts($product, 'table') . '
                    </div>
                </div>
            </div>';
        }
        
        $html .= '</div>';
    } else {
        $html .= '<div class="alert alert-warning">
            <h5>لا توجد منتجات بخصومات مفعلة!</h5>
            <p>لاختبار النظام، يجب:</p>
            <ol>
                <li>إضافة منتج جديد أو تعديل منتج موجود</li>
                <li>تفعيل "التسعير المتدرج" للمنتج</li>
                <li>تحديد نسب الخصم لأنواع المستخدمين</li>
            </ol>
            <a href="' . route('products.index') . '" class="btn btn-primary">إدارة المنتجات</a>
        </div>';
    }

    $html .= '
            <div class="mt-4">
                <h4>🔗 روابط سريعة:</h4>
                <div class="btn-group">
                    <a href="' . route('products.index') . '" class="btn btn-secondary">إدارة المنتجات</a>
                    <a href="' . route('admin.perfect-pharma.product-discounts.index') . '" class="btn btn-primary">إدارة الخصومات</a>
                    <a href="' . route('products.create') . '" class="btn btn-success">إضافة منتج جديد</a>
                </div>
            </div>
        </div>
    </body>
    </html>';

    return $html;
})->name('admin.perfect-pharma.test-discounts');

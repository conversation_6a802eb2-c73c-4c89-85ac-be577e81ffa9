<?php

use Illuminate\Support\Facades\Route;
use Bo<PERSON><PERSON>\PerfectPharma\Http\Controllers\Pharmacy\DashboardController;
use <PERSON><PERSON><PERSON>\PerfectPharma\Http\Controllers\Pharmacy\OrderController;
use Bo<PERSON><PERSON>\PerfectPharma\Http\Controllers\Pharmacy\InventoryController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Pharmacy\PosController;
use Botble\PerfectPharma\Http\Controllers\Pharmacy\PrescriptionController;
use Botble\PerfectPharma\Http\Controllers\Pharmacy\ReportController;
use Botble\PerfectPharma\Http\Controllers\Pharmacy\ProfileController;

/*
|--------------------------------------------------------------------------
| Pharmacy Routes
|--------------------------------------------------------------------------
|
| مسارات خاصة بالصيدليات - تستخدم customer guard مع middleware للتحقق من نوع المستخدم
|
*/

Route::group([
    'prefix' => 'pharmacy',
    'as' => 'pharmacy.',
    'middleware' => ['web', 'customer', 'check_user_type:pharmacy'],
], function () {
    
    // Dashboard Routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Order Routes
    Route::group(['prefix' => 'orders', 'as' => 'orders.'], function () {
        Route::get('/', [OrderController::class, 'index'])->name('index');
        Route::get('/{id}', [OrderController::class, 'show'])->name('show');
        Route::post('/{id}/confirm', [OrderController::class, 'confirm'])->name('confirm');
        Route::post('/{id}/prepare', [OrderController::class, 'prepare'])->name('prepare');
        Route::post('/{id}/complete', [OrderController::class, 'complete'])->name('complete');
        Route::post('/{id}/cancel', [OrderController::class, 'cancel'])->name('cancel');
        Route::get('/{id}/invoice', [OrderController::class, 'printInvoice'])->name('invoice');
    });
    
    // Inventory Routes
    Route::group(['prefix' => 'inventory', 'as' => 'inventory.'], function () {
        Route::get('/', [InventoryController::class, 'index'])->name('index');
        Route::get('/{id}', [InventoryController::class, 'show'])->name('show');
        Route::post('/{id}/update-quantity', [InventoryController::class, 'updateQuantity'])->name('update-quantity');
        Route::post('/{id}/update-price', [InventoryController::class, 'updatePrice'])->name('update-price');
        Route::get('/reports/stock', [InventoryController::class, 'report'])->name('report');
    });
    
    // POS Routes
    Route::group(['prefix' => 'pos', 'as' => 'pos.'], function () {
        Route::get('/', [PosController::class, 'index'])->name('index');
        Route::get('/search-product', [PosController::class, 'searchProduct'])->name('search-product');
        Route::post('/create-invoice', [PosController::class, 'createInvoice'])->name('create-invoice');
        Route::get('/invoice/{orderId}', [PosController::class, 'printInvoice'])->name('print-invoice');
        Route::get('/daily-report', [PosController::class, 'dailySalesReport'])->name('daily-report');
    });
    
    // Prescription Routes
    Route::group(['prefix' => 'prescriptions', 'as' => 'prescriptions.'], function () {
        Route::get('/', [PrescriptionController::class, 'index'])->name('index');
        Route::get('/search-patient', function() {
            return view('plugins/perfect-pharma::pharmacy.prescriptions.search');
        })->name('search-patient');
        Route::get('/search-patient-ajax', [PrescriptionController::class, 'searchPatient'])->name('search-patient-ajax');
        Route::get('/patient/{patientId}', [PrescriptionController::class, 'patientPrescriptions'])->name('patient');
        Route::get('/{id}', [PrescriptionController::class, 'show'])->name('show');
        Route::post('/{id}/dispense', [PrescriptionController::class, 'dispense'])->name('dispense');
        Route::post('/{id}/reject', [PrescriptionController::class, 'reject'])->name('reject');
        Route::get('/{id}/print', [PrescriptionController::class, 'print'])->name('print');
    });
    
    // Report Routes
    Route::group(['prefix' => 'reports', 'as' => 'reports.'], function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/sales', [ReportController::class, 'salesReport'])->name('sales');
        Route::get('/inventory', [ReportController::class, 'inventoryReport'])->name('inventory');
        Route::get('/prescriptions', [ReportController::class, 'prescriptionReport'])->name('prescriptions');
        Route::get('/financial', [ReportController::class, 'financialReport'])->name('financial');
        Route::post('/export', [ReportController::class, 'exportReport'])->name('export');
    });
    
    // Profile Routes
    Route::group(['prefix' => 'profile', 'as' => 'profile.'], function () {
        Route::get('/', [ProfileController::class, 'index'])->name('index');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [ProfileController::class, 'update'])->name('update');
        Route::get('/change-password', [ProfileController::class, 'showChangePasswordForm'])->name('change-password');
        Route::post('/change-password', [ProfileController::class, 'changePassword'])->name('change-password.update');
        Route::get('/settings', [ProfileController::class, 'settings'])->name('settings');
        Route::post('/settings', [ProfileController::class, 'updateSettings'])->name('settings.update');
    });
    
});

<?php

use Illuminate\Support\Facades\Route;
use Bo<PERSON><PERSON>\PerfectPharma\Http\Controllers\Lab\DashboardController;
use <PERSON><PERSON><PERSON>\PerfectPharma\Http\Controllers\Lab\TestRequestController;
use <PERSON><PERSON><PERSON>\PerfectPharma\Http\Controllers\Lab\ResultController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Lab\PatientController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Lab\ReportController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Lab\QualityController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Lab\ProfileController;

/*
|--------------------------------------------------------------------------
| Lab Routes
|--------------------------------------------------------------------------
|
| مسارات خاصة بالمختبرات - تستخدم customer guard مع middleware للتحقق من نوع المستخدم
|
*/

Route::group([
    'prefix' => 'lab',
    'as' => 'lab.',
    'middleware' => ['web', 'customer', 'check_user_type:lab'],
], function () {
    
    // Dashboard Routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Test Request Routes
    Route::group(['prefix' => 'test-requests', 'as' => 'test-requests.'], function () {
        Route::get('/', [TestRequestController::class, 'index'])->name('index');
        Route::get('/{id}', [TestRequestController::class, 'show'])->name('show');
        Route::post('/{id}/accept', [TestRequestController::class, 'accept'])->name('accept');
        Route::post('/{id}/start-processing', [TestRequestController::class, 'startProcessing'])->name('start-processing');
        Route::post('/{id}/reject', [TestRequestController::class, 'reject'])->name('reject');
        Route::post('/{id}/update-priority', [TestRequestController::class, 'updatePriority'])->name('update-priority');
        Route::get('/{id}/sample-label', [TestRequestController::class, 'printSampleLabel'])->name('sample-label');
    });
    
    // Result Routes
    Route::group(['prefix' => 'results', 'as' => 'results.'], function () {
        Route::get('/', [ResultController::class, 'index'])->name('index');
        Route::get('/create/{testRequestId}', [ResultController::class, 'create'])->name('create');
        Route::post('/store/{testRequestId}', [ResultController::class, 'store'])->name('store');
        Route::get('/{id}', [ResultController::class, 'show'])->name('show');
        Route::post('/{id}/approve', [ResultController::class, 'approve'])->name('approve');
        Route::post('/{id}/deliver', [ResultController::class, 'deliver'])->name('deliver');
        Route::get('/{id}/print', [ResultController::class, 'print'])->name('print');
    });
    
    // Patient Routes
    Route::group(['prefix' => 'patients', 'as' => 'patients.'], function () {
        Route::get('/', [PatientController::class, 'index'])->name('index');
        Route::get('/{id}', [PatientController::class, 'show'])->name('show');
        Route::get('/{id}/history', [PatientController::class, 'testHistory'])->name('history');
        Route::get('/search/ajax', [PatientController::class, 'searchAjax'])->name('search');
    });
    
    // Report Routes
    Route::group(['prefix' => 'reports', 'as' => 'reports.'], function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/daily', [ReportController::class, 'dailyReport'])->name('daily');
        Route::get('/monthly', [ReportController::class, 'monthlyReport'])->name('monthly');
        Route::get('/test-volume', [ReportController::class, 'testVolumeReport'])->name('test-volume');
        Route::get('/turnaround-time', [ReportController::class, 'turnaroundTimeReport'])->name('turnaround-time');
        Route::get('/quality-metrics', [ReportController::class, 'qualityMetricsReport'])->name('quality-metrics');
        Route::post('/export', [ReportController::class, 'exportReport'])->name('export');
    });
    
    // Quality Control Routes
    Route::group(['prefix' => 'quality', 'as' => 'quality.'], function () {
        Route::get('/', [QualityController::class, 'index'])->name('index');
        Route::get('/controls', [QualityController::class, 'controls'])->name('controls');
        Route::post('/controls', [QualityController::class, 'storeControl'])->name('controls.store');
        Route::get('/calibration', [QualityController::class, 'calibration'])->name('calibration');
        Route::post('/calibration', [QualityController::class, 'storeCalibration'])->name('calibration.store');
        Route::get('/maintenance', [QualityController::class, 'maintenance'])->name('maintenance');
        Route::post('/maintenance', [QualityController::class, 'storeMaintenance'])->name('maintenance.store');
        Route::get('/audit-trail', [QualityController::class, 'auditTrail'])->name('audit-trail');
    });
    
    // Profile Routes
    Route::group(['prefix' => 'profile', 'as' => 'profile.'], function () {
        Route::get('/', [ProfileController::class, 'index'])->name('index');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [ProfileController::class, 'update'])->name('update');
        Route::get('/change-password', [ProfileController::class, 'showChangePasswordForm'])->name('change-password');
        Route::post('/change-password', [ProfileController::class, 'changePassword'])->name('change-password.update');
        Route::get('/settings', [ProfileController::class, 'settings'])->name('settings');
        Route::post('/settings', [ProfileController::class, 'updateSettings'])->name('settings.update');
        Route::get('/accreditation', [ProfileController::class, 'accreditation'])->name('accreditation');
        Route::post('/accreditation', [ProfileController::class, 'updateAccreditation'])->name('accreditation.update');
    });
    
});

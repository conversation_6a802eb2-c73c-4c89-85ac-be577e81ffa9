<?php

use Botble\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;


// مسارات التسجيل المحسن للعملاء (خارج منطقة الإدارة)
Route::group(['namespace' => 'Botble\PerfectPharma\Http\Controllers'], function () {
    Route::get('register/choice', 'CustomerRegistrationController@showRegistrationChoice')->name('customer.register.choice');
    Route::get('register/enhanced', 'CustomerRegistrationController@showEnhancedRegistration')->name('customer.register.enhanced');
    Route::post('register/enhanced', 'CustomerRegistrationController@processEnhancedRegistration')->name('customer.register.enhanced.post');

    // مسارات رفع المستندات (تحتاج تسجيل دخول)
    Route::middleware(['auth:customer'])->group(function () {
        Route::get('documents/upload', 'CustomerRegistrationController@showDocumentUpload')->name('customer.documents.upload');
        Route::post('documents/upload', 'CustomerRegistrationController@processDocumentUpload')->name('customer.documents.upload.post');
    });
});

// مسارات Perfect Pharma للعملاء (تحتاج تسجيل دخول)
Route::middleware(['auth:customer'])->prefix('customer/perfect-pharma')->name('customer.perfect-pharma.')->group(function () {
    Route::namespace('Botble\PerfectPharma\Http\Controllers\Customer')->group(function () {
        // لوحة التحكم الرئيسية
        Route::get('/', 'DashboardController@index')->name('dashboard');

        // الملف الطبي
        Route::get('/medical-profile', 'MedicalProfileController@index')->name('medical-profile');
        Route::post('/medical-profile', 'MedicalProfileController@update')->name('medical-profile.update');

        // المواعيد
        Route::get('/appointments', 'AppointmentController@index')->name('appointments');
        Route::get('/appointments/book', 'AppointmentController@create')->name('appointments.create');
        Route::post('/appointments/book', 'AppointmentController@store')->name('appointments.store');
        Route::get('/appointments/{id}', 'AppointmentController@show')->name('appointments.show');
        Route::post('/appointments/{id}/cancel', 'AppointmentController@cancel')->name('appointments.cancel');

        // الوصفات الطبية
        Route::get('/prescriptions', 'PrescriptionController@index')->name('prescriptions');
        Route::get('/prescriptions/{id}', 'PrescriptionController@show')->name('prescriptions.show');

        // التحاليل الطبية
        Route::get('/lab-tests', 'LabTestController@index')->name('lab-tests');
        Route::get('/lab-tests/book', 'LabTestController@create')->name('lab-tests.create');
        Route::post('/lab-tests/book', 'LabTestController@store')->name('lab-tests.store');
        Route::get('/lab-tests/{id}', 'LabTestController@show')->name('lab-tests.show');

        // المحفظة الرقمية
        Route::get('/wallet', 'WalletController@index')->name('wallet');
        Route::post('/wallet/charge', 'WalletController@charge')->name('wallet.charge');
        Route::get('/wallet/transactions', 'WalletController@transactions')->name('wallet.transactions');

        // الأكاديمية
        Route::get('/academy', 'AcademyController@index')->name('academy');
        Route::get('/academy/courses/{id}', 'AcademyController@show')->name('academy.courses.show');
        Route::post('/academy/courses/{id}/enroll', 'AcademyController@enroll')->name('academy.courses.enroll');
        Route::get('/academy/my-courses', 'AcademyController@myCourses')->name('academy.my-courses');

        // التبرعات
        Route::get('/donations', 'DonationController@index')->name('donations');
        Route::post('/donations', 'DonationController@store')->name('donations.store');
        Route::get('/donations/history', 'DonationController@history')->name('donations.history');
    });
});

AdminHelper::registerRoutes(function () {
    // مسارات إدارة التحقق
    Route::group([
        'prefix' => 'perfect-pharma/verification',
        'as' => 'admin.perfect-pharma.verification.',
        'namespace' => 'Botble\PerfectPharma\Http\Controllers'
    ], function () {
        Route::get('/', 'Admin\VerificationController@index')->name('index');
        Route::get('/{id}', 'Admin\VerificationController@show')->name('show');
        Route::post('/{id}/approve', 'Admin\VerificationController@approve')->name('approve');
        Route::post('/{id}/reject', 'Admin\VerificationController@reject')->name('reject');
        Route::get('/{id}/document/{document}', 'Admin\VerificationController@viewDocument')->name('view-document');
        Route::get('/{id}/download/{document}', 'Admin\VerificationController@downloadDocument')->name('download-document');
        Route::get('/export/report', 'Admin\VerificationController@exportReport')->name('export');
    });

    // مسارات التقارير والإحصائيات
    Route::group([
        'prefix' => 'perfect-pharma/reports',
        'as' => 'admin.perfect-pharma.reports.',
        'namespace' => 'Botble\PerfectPharma\Http\Controllers'
    ], function () {
        Route::get('/', 'Admin\ReportsController@index')->name('index');
        Route::get('/user-types', 'Admin\ReportsController@userTypes')->name('user-types');
        Route::get('/verification', 'Admin\ReportsController@verification')->name('verification');
        Route::get('/activity', 'Admin\ReportsController@activity')->name('activity');
        Route::get('/export', 'Admin\ReportsController@export')->name('export');
    });

    // مسارات إدارة خصومات المنتجات
    Route::group([
        'prefix' => 'perfect-pharma/product-discounts',
        'as' => 'admin.perfect-pharma.product-discounts.',
        'namespace' => 'Botble\PerfectPharma\Http\Controllers'
    ], function () {
        Route::get('/', 'Admin\ProductDiscountController@index')->name('index');
        Route::get('/{id}/edit', 'Admin\ProductDiscountController@edit')->name('edit');
        Route::put('/{id}', 'Admin\ProductDiscountController@update')->name('update');
        Route::post('/bulk-update', 'Admin\ProductDiscountController@bulkUpdate')->name('bulk-update');
        Route::post('/apply-defaults', 'Admin\ProductDiscountController@applyDefaultDiscounts')->name('apply-defaults');
        Route::get('/{id}/preview', 'Admin\ProductDiscountController@previewPrices')->name('preview');
        Route::get('/statistics', 'Admin\ProductDiscountController@statistics')->name('statistics');
    });

    // API للخصومات (للمشرفين)
    Route::group([
        'prefix' => 'perfect-pharma/api',
        'as' => 'admin.perfect-pharma.api.',
        'namespace' => 'Botble\PerfectPharma\Http\Controllers\Api'
    ], function () {
        Route::get('/product-discounts/{productId}', 'ProductDiscountApiController@getProductDiscounts')->name('product-discounts.show');
        Route::post('/product-discounts/multiple', 'ProductDiscountApiController@getMultipleProductDiscounts')->name('product-discounts.multiple');
        Route::put('/product-discounts/{productId}', 'ProductDiscountApiController@updateProductDiscounts')->name('product-discounts.update');
    });

    Route::group(['namespace' => 'Botble\PerfectPharma\Http\Controllers\Admin', 'prefix' => 'perfect-pharma'], function () {
        
        // مسارات إدارة المرضى
        Route::group(['prefix' => 'patients', 'as' => 'admin.patients.'], function () {
            Route::resource('', 'PatientController')->parameters(['' => 'patient']);
            Route::get('search', 'PatientController@search')->name('search');
            Route::get('statistics', 'PatientController@statistics')->name('statistics');
        });

        // مسارات إدارة الأطباء
        Route::group(['prefix' => 'doctors', 'as' => 'admin.doctors.'], function () {
            Route::resource('', 'DoctorController')->parameters(['' => 'doctor']);
            Route::get('verification', 'DoctorController@verification')->name('verification');
            Route::post('{doctor}/verify', 'DoctorController@verify')->name('verify');
            Route::post('{doctor}/reject', 'DoctorController@reject')->name('reject');
        });

        // مسارات إدارة الصيدليات
        Route::group(['prefix' => 'pharmacies', 'as' => 'admin.pharmacies.'], function () {
            Route::resource('', 'PharmacyController')->parameters(['' => 'pharmacy']);
            Route::get('verification', 'PharmacyController@verification')->name('verification');
            Route::post('{pharmacy}/verify', 'PharmacyController@verify')->name('verify');
            Route::post('{pharmacy}/reject', 'PharmacyController@reject')->name('reject');
        });

        // مسارات إدارة المعامل
        Route::group(['prefix' => 'labs', 'as' => 'admin.labs.'], function () {
            Route::resource('', 'LabController')->parameters(['' => 'lab']);
            Route::get('verification', 'LabController@verification')->name('verification');
            Route::post('{lab}/verify', 'LabController@verify')->name('verify');
            Route::post('{lab}/reject', 'LabController@reject')->name('reject');
        });

        // مسارات إدارة المواعيد
        Route::group(['prefix' => 'appointments', 'as' => 'admin.appointments.'], function () {
            Route::get('', 'AppointmentController@index')->name('index');
            Route::get('create', 'AppointmentController@create')->name('create');
            Route::post('', 'AppointmentController@store')->name('store');
            Route::get('{appointment}', 'AppointmentController@show')->name('show');
            Route::get('{appointment}/edit', 'AppointmentController@edit')->name('edit');
            Route::put('{appointment}', 'AppointmentController@update')->name('update');
            Route::delete('{appointment}', 'AppointmentController@destroy')->name('destroy');

            Route::get('calendar', 'AppointmentController@calendar')->name('calendar');
            Route::get('today', 'AppointmentController@today')->name('today');
            Route::get('upcoming', 'AppointmentController@upcoming')->name('upcoming');
            Route::get('statistics', 'AppointmentController@statistics')->name('statistics');
            Route::post('{appointment}/confirm', 'AppointmentController@confirm')->name('confirm');
            Route::post('{appointment}/cancel', 'AppointmentController@cancel')->name('cancel');
            Route::post('{appointment}/reschedule', 'AppointmentController@reschedule')->name('reschedule');
            Route::post('{appointment}/complete', 'AppointmentController@complete')->name('complete');
            Route::post('{appointment}/no-show', 'AppointmentController@noShow')->name('no-show');
            Route::get('{appointment}/history', 'AppointmentController@history')->name('history');
            Route::get('check-availability', 'AppointmentController@checkAvailability')->name('check-availability');
        });

        // مسارات التسعير المتدرج
        Route::group(['prefix' => 'tiered-pricing', 'as' => 'admin.tiered-pricing.'], function () {
            Route::get('', 'TieredPricingController@index')->name('index');
            Route::get('product/{product}', 'TieredPricingController@productPricing')->name('product');
            Route::post('product/{product}', 'TieredPricingController@storeProductPricing')->name('product.store');
            Route::get('bulk', 'TieredPricingController@bulkPricing')->name('bulk');
            Route::post('bulk', 'TieredPricingController@storeBulkPricing')->name('bulk.store');
            Route::get('category', 'TieredPricingController@categoryPricing')->name('category');
            Route::post('category', 'TieredPricingController@storeCategoryPricing')->name('category.store');
            Route::delete('pricing/{pricing}', 'TieredPricingController@deletePricing')->name('delete');
            Route::get('statistics', 'TieredPricingController@statistics')->name('statistics');
            Route::post('preview-price', 'TieredPricingController@previewPrice')->name('preview-price');
        });

        // مسارات الصيدليات للوصفات الطبية
        Route::group(['prefix' => 'pharmacy', 'as' => 'admin.pharmacy.'], function () {
            Route::group(['prefix' => 'prescriptions', 'as' => 'prescriptions.'], function () {
                Route::get('', 'PharmacyPrescriptionController@index')->name('index');
                Route::post('search-by-phone', 'PharmacyPrescriptionController@searchByPhone')->name('search-by-phone');
                Route::get('{prescription}', 'PharmacyPrescriptionController@showPrescription')->name('show');
                Route::post('medication/{medication}/dispense', 'PharmacyPrescriptionController@dispenseMedication')->name('dispense');
                Route::get('dispensing/history', 'PharmacyPrescriptionController@dispensingHistory')->name('history');
                Route::get('dispensing/{dispensing}/print', 'PharmacyPrescriptionController@printDispensing')->name('print');
                Route::get('statistics', 'PharmacyPrescriptionController@statistics')->name('statistics');
                Route::post('prescription/{prescription}/validate', 'PharmacyPrescriptionController@validatePrescription')->name('validate');
            });
        });

        // مسارات إدارة المخزون
        Route::group(['prefix' => 'inventory', 'as' => 'admin.inventory.'], function () {
            Route::get('', 'InventoryController@index')->name('index');
            Route::get('create', 'InventoryController@create')->name('create');
            Route::post('', 'InventoryController@store')->name('store');

            // مسارات التنبيهات (يجب أن تكون قبل المسارات التي تحتوي على {inventory})
            Route::get('alerts', 'InventoryController@alerts')->name('alerts');
            Route::post('alerts/{alert}/resolve', 'InventoryController@resolveAlert')->name('alerts.resolve');
            Route::post('alerts/{alert}/read', 'InventoryController@markAlertAsRead')->name('alerts.read');

            // مسارات المنتجات (يجب أن تكون بعد المسارات الثابتة)
            Route::get('{inventory}', 'InventoryController@show')->name('show');
            Route::get('{inventory}/edit', 'InventoryController@edit')->name('edit');
            Route::put('{inventory}', 'InventoryController@update')->name('update');
            Route::get('{inventory}/movements', 'InventoryController@movements')->name('movements');
            Route::post('{inventory}/adjust-stock', 'InventoryController@adjustStock')->name('adjust-stock');
            Route::post('{inventory}/add-stock', 'InventoryController@addStock')->name('add-stock');
            Route::post('{inventory}/remove-stock', 'InventoryController@removeStock')->name('remove-stock');
        });

        // مسارات نظام نقاط البيع (POS)
        Route::group(['prefix' => 'pos', 'as' => 'admin.pos.'], function () {
            // الصفحة الرئيسية
            Route::get('', 'PosController@index')->name('index');
            Route::get('dashboard/stats', 'PosController@getDashboardStats')->name('dashboard.stats');

            // واجهة الكاشير
            Route::get('cashier', 'PosController@cashier')->name('cashier');
            Route::get('search-products', 'PosController@searchProducts')->name('search-products');
            Route::post('create-sale', 'PosController@createSale')->name('create-sale');
            Route::get('sale/{id}', 'PosController@getSale')->name('get-sale');
            Route::post('sale/{id}/cancel', 'PosController@cancelSale')->name('cancel-sale');
            Route::get('sale/{id}/receipt', 'PosController@printReceipt')->name('print-receipt');

            // إدارة جلسات الكاشير
            Route::group(['prefix' => 'session', 'as' => 'session.'], function () {
                Route::get('open', 'PosController@openSessionForm')->name('open');
                Route::post('open', 'PosController@openSession')->name('store');
                Route::get('close', 'PosController@closeSessionForm')->name('close');
                Route::post('close', 'PosController@closeSession')->name('update');
            });

            // إدارة المبيعات
            Route::group(['prefix' => 'sales', 'as' => 'sales.'], function () {
                Route::get('', 'PosSalesController@index')->name('index');
                Route::get('{id}', 'PosSalesController@show')->name('show');
                Route::post('{id}/cancel', 'PosSalesController@cancel')->name('cancel');
                Route::get('{id}/refund', 'PosSalesController@refund')->name('refund');
                Route::post('{id}/refund', 'PosSalesController@processRefund')->name('process-refund');
                Route::get('{id}/receipt', 'PosSalesController@receipt')->name('receipt');
                Route::get('reports', 'PosSalesController@reports')->name('reports');
            });
        });

        // مسارات إدارة الوصفات
        Route::group(['prefix' => 'prescriptions', 'as' => 'admin.prescriptions.'], function () {
            Route::resource('', 'PrescriptionController')->parameters(['' => 'prescription']);
            Route::get('active', 'PrescriptionController@active')->name('active');
            Route::get('expired', 'PrescriptionController@expired')->name('expired');
            Route::post('{prescription}/cancel', 'PrescriptionController@cancel')->name('cancel');
        });

        // مسارات إدارة التحاليل
        Route::group(['prefix' => 'lab-tests', 'as' => 'admin.lab-tests.'], function () {
            Route::resource('', 'LabTestController')->parameters(['' => 'labTest']);
            Route::get('pending', 'LabTestController@pending')->name('pending');
            Route::post('{labTest}/cancel', 'LabTestController@cancel')->name('cancel');
        });

        // مسارات إدارة المحافظ
        Route::group(['prefix' => 'wallets', 'as' => 'admin.wallets.'], function () {
            Route::resource('', 'WalletController')->parameters(['' => 'wallet']);
            Route::post('{wallet}/toggle', 'WalletController@toggle')->name('toggle');
        });

        // مسارات المعاملات المالية
        Route::group(['prefix' => 'wallet-transactions', 'as' => 'admin.wallet-transactions.'], function () {
            Route::get('', 'WalletTransactionController@index')->name('index');
            Route::get('{transaction}', 'WalletTransactionController@show')->name('show');
        });

        // مسارات الأكاديمية التعليمية
        Route::group(['prefix' => 'academy', 'as' => 'admin.academy.'], function () {
            // لوحة تحكم الأكاديمية
            Route::get('dashboard', function() {
                return view('plugins/perfect-pharma::admin.academy.dashboard');
            })->name('dashboard');

            // مسارات الاختبارات العامة
            Route::group(['prefix' => 'quizzes', 'as' => 'quizzes.'], function () {
                Route::get('', 'QuizController@index')->name('index');
            });

            // مسارات محاولات الاختبارات
            Route::group(['prefix' => 'quiz-attempts', 'as' => 'quiz-attempts.'], function () {
                Route::get('', 'QuizAttemptController@index')->name('index');
                Route::get('analytics', 'QuizAttemptController@analytics')->name('analytics');
                Route::get('{attempt}', 'QuizAttemptController@show')->name('show');
            });

            // مسارات التسجيلات
            Route::group(['prefix' => 'enrollments', 'as' => 'enrollments.'], function () {
                Route::get('', 'EnrollmentController@index')->name('index');
                Route::get('create', 'EnrollmentController@create')->name('create');
                Route::post('', 'EnrollmentController@store')->name('store');
                Route::get('bulk', 'EnrollmentController@bulkCreate')->name('bulk');
                Route::post('bulk', 'EnrollmentController@bulkStore')->name('bulk.store');
            });
            // مسارات فئات الدورات
            Route::group(['prefix' => 'categories', 'as' => 'categories.'], function () {
                Route::get('', 'CourseCategoryController@index')->name('index');
                Route::get('create', 'CourseCategoryController@create')->name('create');
                Route::post('', 'CourseCategoryController@store')->name('store');
                Route::get('{category}', 'CourseCategoryController@show')->name('show');
                Route::get('{category}/edit', 'CourseCategoryController@edit')->name('edit');
                Route::put('{category}', 'CourseCategoryController@update')->name('update');
                Route::delete('{category}', 'CourseCategoryController@destroy')->name('destroy');
                Route::post('{category}/toggle', 'CourseCategoryController@toggle')->name('toggle');
                Route::post('reorder', 'CourseCategoryController@reorder')->name('reorder');
                Route::get('select-options', 'CourseCategoryController@getSelectOptions')->name('select-options');
            });

            Route::group(['prefix' => 'courses', 'as' => 'courses.'], function () {
                Route::get('', 'AcademyCourseController@index')->name('index');
                Route::get('create', 'AcademyCourseController@create')->name('create');
                Route::post('', 'AcademyCourseController@store')->name('store');
                Route::get('{course}', 'AcademyCourseController@show')->name('show');
                Route::get('{course}/edit', 'AcademyCourseController@edit')->name('edit');
                Route::put('{course}', 'AcademyCourseController@update')->name('update');
                Route::delete('{course}', 'AcademyCourseController@destroy')->name('destroy');
                Route::post('{course}/toggle-featured', 'AcademyCourseController@toggleFeatured')->name('toggle-featured');

                // مسارات محتوى الدورات (الدروس)
                Route::group(['prefix' => '{course}/contents', 'as' => 'contents.'], function () {
                    Route::get('', 'CourseContentController@index')->name('index');
                    Route::get('create', 'CourseContentController@create')->name('create');
                    Route::post('', 'CourseContentController@store')->name('store');
                    Route::get('{content}', 'CourseContentController@show')->name('show');
                    Route::get('{content}/edit', 'CourseContentController@edit')->name('edit');
                    Route::put('{content}', 'CourseContentController@update')->name('update');
                    Route::delete('{content}', 'CourseContentController@destroy')->name('destroy');
                    Route::post('reorder', 'CourseContentController@reorder')->name('reorder');
                    Route::post('{content}/toggle', 'CourseContentController@toggle')->name('toggle');
                });

                // مسارات اختبارات الدورات
                Route::group(['prefix' => '{course}/quizzes', 'as' => 'quizzes.'], function () {
                    Route::get('', 'QuizController@index')->name('index');
                    Route::get('create', 'QuizController@create')->name('create');
                    Route::post('', 'QuizController@store')->name('store');
                    Route::get('{quiz}', 'QuizController@show')->name('show');
                    Route::get('{quiz}/edit', 'QuizController@edit')->name('edit');
                    Route::put('{quiz}', 'QuizController@update')->name('update');
                    Route::delete('{quiz}', 'QuizController@destroy')->name('destroy');
                    Route::post('{quiz}/duplicate', 'QuizController@duplicate')->name('duplicate');
                    Route::get('{quiz}/statistics', 'QuizController@statistics')->name('statistics');

                    // مسارات أسئلة الاختبارات
                    Route::group(['prefix' => '{quiz}/questions', 'as' => 'questions.'], function () {
                        Route::get('', 'QuizQuestionController@index')->name('index');
                        Route::get('create', 'QuizQuestionController@create')->name('create');
                        Route::post('', 'QuizQuestionController@store')->name('store');
                        Route::get('{question}', 'QuizQuestionController@show')->name('show');
                        Route::get('{question}/edit', 'QuizQuestionController@edit')->name('edit');
                        Route::put('{question}', 'QuizQuestionController@update')->name('update');
                        Route::delete('{question}', 'QuizQuestionController@destroy')->name('destroy');
                        Route::post('reorder', 'QuizQuestionController@reorder')->name('reorder');
                        Route::post('bulk-import', 'QuizQuestionController@bulkImport')->name('bulk-import');
                    });
                });

                // مسارات تتبع التقدم
                Route::group(['prefix' => '{course}/progress', 'as' => 'progress.'], function () {
                    Route::get('', 'ProgressController@courseProgress')->name('course');
                    Route::get('student/{user}', 'ProgressController@studentProgress')->name('student');
                    Route::put('student/{user}/update', 'ProgressController@updateProgress')->name('update');
                    Route::delete('student/{user}/reset', 'ProgressController@resetProgress')->name('reset');
                    Route::get('analytics', 'ProgressController@progressAnalytics')->name('analytics');
                    Route::get('export', 'ProgressController@exportProgress')->name('export');
                });

                // مسار إصدار الشهادات للدورة
                Route::post('{course}/certificates/issue', 'CertificateController@issue')->name('certificates.issue');
            });



            // مسارات تتبع التقدم العامة
            Route::group(['prefix' => 'progress', 'as' => 'progress.'], function () {
                Route::get('overview', 'ProgressController@overview')->name('overview');
                Route::get('analytics', 'ProgressController@analytics')->name('analytics');
                Route::get('all', 'ProgressController@allStudentsProgress')->name('all');
                Route::post('bulk-update-status', 'ProgressController@bulkUpdateStatus')->name('bulk-update-status');
            });

            // مسارات الشهادات
            Route::group(['prefix' => 'certificates', 'as' => 'certificates.'], function () {
                Route::get('', 'CertificateController@index')->name('index');
                Route::get('create', 'CertificateController@create')->name('create');
                Route::post('', 'CertificateController@store')->name('store');
                Route::get('statistics', 'CertificateController@statistics')->name('statistics');
                Route::get('{certificate}', 'CertificateController@show')->name('show');
                Route::post('issue', 'CertificateController@issue')->name('issue');
                Route::delete('{certificate}/revoke', 'CertificateController@revoke')->name('revoke');
                Route::get('{certificate}/download', 'CertificateController@download')->name('download');
                Route::post('bulk-issue', 'CertificateController@bulkIssue')->name('bulk-issue');
            });

            // مسارات رفع الملفات
            Route::group(['prefix' => 'files', 'as' => 'files.'], function () {
                Route::get('', 'FileUploadController@index')->name('index');
                Route::post('upload-video', 'FileUploadController@uploadVideo')->name('upload-video');
                Route::post('upload-document', 'FileUploadController@uploadDocument')->name('upload-document');
                Route::post('upload-image', 'FileUploadController@uploadImage')->name('upload-image');
                Route::delete('delete', 'FileUploadController@deleteFile')->name('delete');
                Route::get('info', 'FileUploadController@getFileInfo')->name('info');
                Route::get('list', 'FileUploadController@listFiles')->name('list');
            });

            // مسارات التقييمات
            Route::group(['prefix' => 'ratings', 'as' => 'ratings.'], function () {
                Route::get('courses', 'RatingController@courseRatings')->name('courses');
                Route::get('content', 'RatingController@contentRatings')->name('content');
                Route::get('analytics', 'RatingController@ratingsAnalytics')->name('analytics');
                Route::post('courses/{rating}/approve', 'RatingController@approveCourseRating')->name('courses.approve');
                Route::post('courses/{rating}/reject', 'RatingController@rejectCourseRating')->name('courses.reject');
                Route::delete('courses/{rating}', 'RatingController@deleteCourseRating')->name('courses.delete');
                Route::delete('content/{rating}', 'RatingController@deleteContentRating')->name('content.delete');
                Route::post('courses/bulk-approve', 'RatingController@bulkApproveCourseRatings')->name('courses.bulk-approve');
                Route::post('courses/bulk-delete', 'RatingController@bulkDeleteCourseRatings')->name('courses.bulk-delete');
                Route::get('export', 'RatingController@exportRatings')->name('export');
            });
        });

        // مسارات إدارة مراكز الأشعة
        Route::group([
            'prefix' => 'radiology',
            'as' => 'admin.radiology.'
        ], function () {
            // إدارة مراكز الأشعة
            Route::get('/', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'index'])->name('index');
            Route::get('/create', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'create'])->name('create');
            Route::get('/verification', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'verification'])->name('verification');
            Route::post('/', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'store'])->name('store');
            Route::get('/{id}', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'edit'])->name('edit');
            Route::put('/{id}', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'update'])->name('update');
            Route::delete('/{id}', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'destroy'])->name('destroy');
            Route::post('/{id}/verify', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'verify'])->name('verify');
            Route::post('/{id}/reject', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'reject'])->name('reject');
            Route::post('/{id}/toggle-status', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyController::class, 'toggleStatus'])->name('toggle-status');

            // أنواع الأشعة
            Route::group(['prefix' => 'scan-types', 'as' => 'scan-types.'], function () {
                Route::get('', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyScanTypeController::class, 'index'])->name('index');
                Route::get('create', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyScanTypeController::class, 'create'])->name('create');
                Route::post('', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyScanTypeController::class, 'store'])->name('store');
                Route::get('{id}', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyScanTypeController::class, 'show'])->name('show');
                Route::get('{id}/edit', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyScanTypeController::class, 'edit'])->name('edit');
                Route::put('{id}', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyScanTypeController::class, 'update'])->name('update');
                Route::delete('{id}', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyScanTypeController::class, 'destroy'])->name('destroy');
                Route::post('{id}/toggle', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyScanTypeController::class, 'toggle'])->name('toggle');
            });

            // طلبات الأشعة
            Route::group(['prefix' => 'requests', 'as' => 'requests.'], function () {
                Route::get('/', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyRequestController::class, 'index'])->name('index');
                Route::get('/{id}', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyRequestController::class, 'show'])->name('show');
                Route::post('/{id}/approve', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyRequestController::class, 'approve'])->name('approve');
                Route::post('/{id}/cancel', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyRequestController::class, 'cancel'])->name('cancel');
                Route::get('/export', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyRequestController::class, 'export'])->name('export');
                Route::get('/statistics', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyRequestController::class, 'statistics'])->name('statistics');
            });

            // النتائج والتقارير
            Route::group(['prefix' => 'results', 'as' => 'results.'], function () {
                Route::get('/', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyResultController::class, 'index'])->name('index');
                Route::get('/{id}', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyResultController::class, 'show'])->name('show');
                Route::post('/{id}/approve', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyResultController::class, 'approve'])->name('approve');
                Route::post('/{id}/reject', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyResultController::class, 'reject'])->name('reject');
                Route::get('/{id}/download', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyResultController::class, 'download'])->name('download');
                Route::get('/quality-control', [\Botble\PerfectPharma\Http\Controllers\Admin\RadiologyResultController::class, 'qualityControl'])->name('quality-control');
            });

            // مسارات إضافية للأدمن للوصول إلى واجهات مراكز الأشعة
            Route::group(['prefix' => 'radiology-interface', 'as' => 'radiology-interface.'], function () {
                // الطلبات
                Route::group(['prefix' => 'requests', 'as' => 'requests.'], function () {
                    Route::get('/', function(Request $request) {
                        $request->merge(['is_admin_access' => true]);
                        return app(\Botble\PerfectPharma\Http\Controllers\Radiology\RequestController::class)->index($request);
                    })->name('index');
                    Route::get('/{id}', function(Request $request, $id) {
                        $request->merge(['is_admin_access' => true]);
                        return app(\Botble\PerfectPharma\Http\Controllers\Radiology\RequestController::class)->show($request, $id);
                    })->name('show');
                });

                // النتائج
                Route::group(['prefix' => 'results', 'as' => 'results.'], function () {
                    Route::get('/', function(Request $request) {
                        $request->merge(['is_admin_access' => true]);
                        return app(\Botble\PerfectPharma\Http\Controllers\Radiology\ResultController::class)->index($request);
                    })->name('index');
                    Route::get('/{id}', function(Request $request, $id) {
                        $request->merge(['is_admin_access' => true]);
                        return app(\Botble\PerfectPharma\Http\Controllers\Radiology\ResultController::class)->show($request, $id);
                    })->name('show');
                });

                // التقارير
                Route::group(['prefix' => 'reports', 'as' => 'reports.'], function () {
                    Route::get('/', function(Request $request) {
                        $request->merge(['is_admin_access' => true]);
                        return app(\Botble\PerfectPharma\Http\Controllers\Radiology\ReportController::class)->index($request);
                    })->name('index');
                    Route::get('/daily', function(Request $request) {
                        $request->merge(['is_admin_access' => true]);
                        return app(\Botble\PerfectPharma\Http\Controllers\Radiology\ReportController::class)->dailyReport($request);
                    })->name('daily');
                    Route::get('/monthly', function(Request $request) {
                        $request->merge(['is_admin_access' => true]);
                        return app(\Botble\PerfectPharma\Http\Controllers\Radiology\ReportController::class)->monthlyReport($request);
                    })->name('monthly');
                });

                // التقارير
                Route::group(['prefix' => 'reports', 'as' => 'reports.'], function () {
                    Route::get('/', function(Request $request) {
                        $request->merge(['is_admin_access' => true]);
                        return app(\Botble\PerfectPharma\Http\Controllers\Radiology\ReportController::class)->index($request);
                    })->name('index');
                });
            });
        });

        // مسارات نظام التبرعات
        Route::group(['prefix' => 'donations', 'as' => 'admin.donations.'], function () {
            Route::resource('', 'DonationController')->parameters(['' => 'donation']);
            Route::post('{donation}/approve', 'DonationController@approve')->name('approve');
            Route::post('{donation}/reject', 'DonationController@reject')->name('reject');
        });

        Route::group(['prefix' => 'charity-hospitals', 'as' => 'admin.charity-hospitals.'], function () {
            Route::resource('', 'CharityHospitalController')->parameters(['' => 'hospital']);
            Route::post('{hospital}/verify', 'CharityHospitalController@verify')->name('verify');
        });

        // مسارات التقارير
        Route::group(['prefix' => 'reports', 'as' => 'admin.reports.'], function () {
            Route::get('financial', 'ReportController@financial')->name('financial');
            Route::get('medical', 'ReportController@medical')->name('medical');
            Route::get('users', 'ReportController@users')->name('users');
            Route::get('export/{type}', 'ReportController@export')->name('export');
        });

        // مسارات الإعدادات
        Route::get('settings', 'SettingController@index')->name('admin.pharma.settings');
        Route::post('settings', 'SettingController@store')->name('admin.pharma.settings.store');

        // مسارات Dashboard الرئيسي
        Route::get('dashboard', 'DashboardController@index')->name('admin.pharma.dashboard');
        Route::get('dashboard/stats', 'DashboardController@stats')->name('admin.pharma.dashboard.stats');

        // مسارات نظام الإشعارات
        Route::group(['prefix' => 'notifications', 'as' => 'admin.notifications.'], function () {
            // لوحة تحكم الإشعارات
            Route::get('dashboard', 'NotificationController@dashboard')->name('dashboard');

            // إدارة الإشعارات
            Route::get('/', 'NotificationController@index')->name('index');
            Route::get('create', 'NotificationController@create')->name('create');
            Route::post('/', 'NotificationController@store')->name('store');
            Route::get('{id}', 'NotificationController@show')->name('show');
            Route::delete('{id}', 'NotificationController@destroy')->name('destroy');

            // إجراءات الإشعارات
            Route::post('{id}/send', 'NotificationController@sendNow')->name('send');
            Route::post('{id}/cancel', 'NotificationController@cancel')->name('cancel');
            Route::post('{id}/retry', 'NotificationController@retry')->name('retry');

            // اختبار القنوات
            Route::post('test-channel', 'NotificationController@testChannel')->name('test-channel');

            // معالجة قائمة الانتظار
            Route::post('process-queue', 'NotificationController@processQueue')->name('process-queue');

            // البحث في المستقبلين
            Route::get('search/recipients', 'NotificationController@searchRecipients')->name('search.recipients');
        });

        // مسارات إدارة قوالب الإشعارات
        Route::group(['prefix' => 'notification-templates', 'as' => 'admin.notification-templates.'], function () {
            Route::get('/', 'NotificationTemplateController@index')->name('index');
            Route::get('create', 'NotificationTemplateController@create')->name('create');
            Route::post('/', 'NotificationTemplateController@store')->name('store');
            Route::get('{id}', 'NotificationTemplateController@show')->name('show');
            Route::get('{id}/edit', 'NotificationTemplateController@edit')->name('edit');
            Route::put('{id}', 'NotificationTemplateController@update')->name('update');
            Route::delete('{id}', 'NotificationTemplateController@destroy')->name('destroy');

            // إجراءات القوالب
            Route::post('{id}/duplicate', 'NotificationTemplateController@duplicate')->name('duplicate');
            Route::post('{id}/toggle', 'NotificationTemplateController@toggle')->name('toggle');
            Route::post('{id}/set-default', 'NotificationTemplateController@setDefault')->name('set-default');
            Route::post('{id}/preview', 'NotificationTemplateController@preview')->name('preview');

            // تصدير واستيراد القوالب
            Route::post('export', 'NotificationTemplateController@export')->name('export');
            Route::post('import', 'NotificationTemplateController@import')->name('import');

            // إنشاء القوالب الافتراضية
            Route::post('create-defaults', 'NotificationTemplateController@createDefaults')->name('create-defaults');
        });

        // مسارات إدارة تفضيلات الإشعارات
        Route::group(['prefix' => 'notification-preferences', 'as' => 'admin.notification-preferences.'], function () {
            Route::get('/', 'NotificationPreferenceController@index')->name('index');
            Route::put('/', 'NotificationPreferenceController@update')->name('update');

            // إجراءات التفضيلات
            Route::post('enable-all', 'NotificationPreferenceController@enableAll')->name('enable-all');
            Route::post('disable-all', 'NotificationPreferenceController@disableAll')->name('disable-all');
            Route::post('reset', 'NotificationPreferenceController@reset')->name('reset');

            // البحث في المستخدمين
            Route::get('search/users', 'NotificationPreferenceController@searchUsers')->name('search.users');

            // تصدير واستيراد التفضيلات
            Route::post('export', 'NotificationPreferenceController@export')->name('export');
            Route::post('import', 'NotificationPreferenceController@import')->name('import');
        });

        // مسارات إحصائيات الإشعارات
        Route::group(['prefix' => 'notification-statistics', 'as' => 'admin.notification-statistics.'], function () {
            Route::get('/', 'NotificationStatisticsController@index')->name('index');
            Route::get('dashboard', 'NotificationStatisticsController@dashboard')->name('dashboard');
            Route::get('detailed-report', 'NotificationStatisticsController@detailedReport')->name('detailed-report');
            Route::get('comparison', 'NotificationStatisticsController@comparison')->name('comparison');

            // API للإحصائيات
            Route::get('api/summary', 'NotificationStatisticsController@apiSummary')->name('api.summary');
            Route::get('api/trends', 'NotificationStatisticsController@apiTrends')->name('api.trends');
            Route::get('api/real-time', 'NotificationStatisticsController@apiRealTime')->name('api.real-time');

            // تصدير الإحصائيات
            Route::post('export', 'NotificationStatisticsController@export')->name('export');

            // إدارة الإحصائيات
            Route::post('update-today', 'NotificationStatisticsController@updateToday')->name('update-today');
            Route::post('rebuild', 'NotificationStatisticsController@rebuild')->name('rebuild');
            Route::post('clean-old', 'NotificationStatisticsController@cleanOld')->name('clean-old');
        });

        // مسارات جدولة الإشعارات
        Route::group(['prefix' => 'notification-schedules', 'as' => 'admin.notification-schedules.'], function () {
            Route::get('/', 'NotificationScheduleController@index')->name('index');
            Route::get('create', 'NotificationScheduleController@create')->name('create');
            Route::post('/', 'NotificationScheduleController@store')->name('store');
            Route::get('{id}', 'NotificationScheduleController@show')->name('show');
            Route::get('{id}/edit', 'NotificationScheduleController@edit')->name('edit');
            Route::put('{id}', 'NotificationScheduleController@update')->name('update');
            Route::delete('{id}', 'NotificationScheduleController@destroy')->name('destroy');

            // إجراءات الجدولة
            Route::post('{id}/run', 'NotificationScheduleController@run')->name('run');
            Route::post('{id}/pause', 'NotificationScheduleController@pause')->name('pause');
            Route::post('{id}/resume', 'NotificationScheduleController@resume')->name('resume');
            Route::post('{id}/reset', 'NotificationScheduleController@reset')->name('reset');

            // تشغيل جميع الجدولات المستحقة
            Route::post('run-due', 'NotificationScheduleController@runDue')->name('run-due');

            // إنشاء جدولات افتراضية
            Route::post('create-defaults', 'NotificationScheduleController@createDefaults')->name('create-defaults');
        });

        // مسارات التقارير الطبية
        Route::group(['prefix' => 'medical-reports', 'as' => 'admin.medical-reports.'], function () {
            Route::get('/', 'MedicalReportController@index')->name('index');
            Route::get('patient-report', 'MedicalReportController@patientReport')->name('patient-report');
            Route::get('doctor-report', 'MedicalReportController@doctorReport')->name('doctor-report');
            Route::get('appointments-report', 'MedicalReportController@appointmentsReport')->name('appointments-report');
            Route::get('prescriptions-report', 'MedicalReportController@prescriptionsReport')->name('prescriptions-report');
            Route::get('lab-tests-report', 'MedicalReportController@labTestsReport')->name('lab-tests-report');
            Route::get('diagnosis-report', 'MedicalReportController@diagnosisReport')->name('diagnosis-report');
            Route::get('medications-report', 'MedicalReportController@medicationsReport')->name('medications-report');
            Route::get('statistics-report', 'MedicalReportController@statisticsReport')->name('statistics-report');
            Route::get('financial-report', 'MedicalReportController@financialReport')->name('financial-report');

            // إجراءات التقارير
            Route::post('export', 'MedicalReportController@exportReport')->name('export');
            Route::post('schedule', 'MedicalReportController@scheduleReport')->name('schedule');
            Route::post('preview', 'MedicalReportController@previewReport')->name('preview');

            // البحث
            Route::get('search/patients', 'MedicalReportController@searchPatients')->name('search.patients');
            Route::get('search/doctors', 'MedicalReportController@searchDoctors')->name('search.doctors');

            // إحصائيات سريعة
            Route::get('quick-stats', 'MedicalReportController@quickStats')->name('quick-stats');
        });

        // مسارات الأمان
        Route::group(['prefix' => 'security', 'as' => 'admin.security.'], function () {
            Route::get('/', 'SecurityController@index')->name('index');
            Route::get('logs', 'SecurityController@logs')->name('logs');
            Route::get('report', 'SecurityController@report')->name('report');

            // إجراءات الأمان
            Route::post('check-password-strength', 'SecurityController@checkPasswordStrength')->name('check-password-strength');
            Route::post('block-ip', 'SecurityController@blockIp')->name('block-ip');
            Route::post('unblock-ip', 'SecurityController@unblockIp')->name('unblock-ip');
            Route::post('clean-old-logs', 'SecurityController@cleanOldLogs')->name('clean-old-logs');
            Route::post('export-report', 'SecurityController@exportReport')->name('export-report');

            // حالة الأمان
            Route::get('status', 'SecurityController@securityStatus')->name('status');
        });

        // مسارات النسخ الاحتياطي
        Route::group(['prefix' => 'backup', 'as' => 'admin.backup.'], function () {
            Route::get('/', 'BackupController@index')->name('index');

            // إجراءات النسخ الاحتياطي
            Route::post('create', 'BackupController@create')->name('create');
            Route::post('restore', 'BackupController@restore')->name('restore');
            Route::get('download', 'BackupController@download')->name('download');
            Route::delete('delete', 'BackupController@delete')->name('delete');
            Route::post('cleanup', 'BackupController@cleanup')->name('cleanup');

            // الجدولة
            Route::post('schedule', 'BackupController@schedule')->name('schedule');
            Route::get('get-schedule', 'BackupController@getSchedule')->name('get-schedule');
            Route::post('disable-schedule', 'BackupController@disableSchedule')->name('disable-schedule');

            // الحالة
            Route::get('status', 'BackupController@status')->name('status');
        });

        // مسارات الأذونات المتقدمة
        Route::group(['prefix' => 'advanced-permissions', 'as' => 'admin.advanced-permissions.'], function () {
            Route::get('/', 'AdvancedPermissionController@index')->name('index');
            Route::get('user-permissions', 'AdvancedPermissionController@userPermissions')->name('user-permissions');
            Route::get('roles', 'AdvancedPermissionController@roles')->name('roles');

            // إدارة المستخدمين والأذونات
            Route::post('update-user-permissions', 'AdvancedPermissionController@updateUserPermissions')->name('update-user-permissions');
            Route::get('get-user-permissions/{userId}', 'AdvancedPermissionController@getUserPermissions')->name('get-user-permissions');
            Route::post('check-user-permission', 'AdvancedPermissionController@checkUserPermission')->name('check-user-permission');

            // إدارة الأدوار
            Route::post('create-role', 'AdvancedPermissionController@createRole')->name('create-role');
            Route::put('update-role/{id}', 'AdvancedPermissionController@updateRole')->name('update-role');
            Route::delete('delete-role/{id}', 'AdvancedPermissionController@deleteRole')->name('delete-role');
            Route::post('copy-permissions', 'AdvancedPermissionController@copyPermissions')->name('copy-permissions');

            // التحليل والتنظيف
            Route::get('analyze-usage', 'AdvancedPermissionController@analyzeUsage')->name('analyze-usage');
            Route::get('find-conflicts', 'AdvancedPermissionController@findConflicts')->name('find-conflicts');
            Route::post('cleanup', 'AdvancedPermissionController@cleanup')->name('cleanup');

            // التصدير والاستيراد
            Route::get('export', 'AdvancedPermissionController@export')->name('export');
            Route::post('import', 'AdvancedPermissionController@import')->name('import');

            // الكاش والبحث
            Route::post('clear-cache', 'AdvancedPermissionController@clearCache')->name('clear-cache');
            Route::get('search/users', 'AdvancedPermissionController@searchUsers')->name('search.users');
        });

        // مسارات التكامل الخارجي
        Route::group(['prefix' => 'external-integration', 'as' => 'admin.external-integration.'], function () {
            Route::get('/', 'ExternalIntegrationController@index')->name('index');
            Route::get('settings', 'ExternalIntegrationController@settings')->name('settings');
            Route::get('logs', 'ExternalIntegrationController@logs')->name('logs');

            // اختبار الاتصال
            Route::post('test-connection', 'ExternalIntegrationController@testConnection')->name('test-connection');
            Route::get('systems-status', 'ExternalIntegrationController@getSystemsStatus')->name('systems-status');

            // التزامن مع الأنظمة
            Route::post('sync-his', 'ExternalIntegrationController@syncHIS')->name('sync-his');
            Route::post('sync-lims', 'ExternalIntegrationController@syncLIMS')->name('sync-lims');
            Route::post('sync-epharmacy', 'ExternalIntegrationController@syncEPharmacy')->name('sync-epharmacy');

            // الخدمات
            Route::post('verify-insurance', 'ExternalIntegrationController@verifyInsurance')->name('verify-insurance');
            Route::post('process-payment', 'ExternalIntegrationController@processPayment')->name('process-payment');
            Route::post('send-sms', 'ExternalIntegrationController@sendSMS')->name('send-sms');
            Route::post('send-email', 'ExternalIntegrationController@sendEmail')->name('send-email');
            Route::post('upload-to-cloud', 'ExternalIntegrationController@uploadToCloud')->name('upload-to-cloud');

            // الإدارة
            Route::post('update-settings', 'ExternalIntegrationController@updateSettings')->name('update-settings');
            Route::post('clean-logs', 'ExternalIntegrationController@cleanLogs')->name('clean-logs');
            Route::post('retry-operation', 'ExternalIntegrationController@retryOperation')->name('retry-operation');
        });
    });
});

// مسارات اختبار الإعدادات (للإدارة)
AdminHelper::registerRoutes(function (): void {
    Route::group([
        'namespace' => 'Botble\PerfectPharma\Http\Controllers',
        'prefix' => 'perfect-pharma',
        'as' => 'perfect-pharma.',
        'middleware' => ['web', 'core'],
    ], function (): void {
        Route::get('test-settings', 'TestSettingsController@index')->name('test-settings');
        Route::get('test-settings/set', 'TestSettingsController@testSet')->name('test-settings.set');
        Route::get('test-settings/price', 'TestSettingsController@testPrice')->name('test-settings.price');

        // اختبار التكامل مع Ecommerce
        Route::get('test-ecommerce', 'TestEcommerceController@index')->name('test-ecommerce');
        Route::get('test-ecommerce/product/{id}', 'TestEcommerceController@testProduct')->name('test-ecommerce.product');
        Route::get('test-ecommerce/cart', 'TestEcommerceController@testCart')->name('test-ecommerce.cart');
        Route::get('test-ecommerce/user-type/{userType}', 'TestEcommerceController@testUserType')->name('test-ecommerce.user-type');
        Route::get('test-ecommerce/currency', 'TestEcommerceController@testCurrency')->name('test-ecommerce.currency');

        // أمثلة الاستخدام
        Route::get('examples/product-pricing', 'ExampleController@productPricing')->name('examples.product-pricing');

        // API Routes for AJAX calls
        Route::prefix('api')->group(function () {
            // Currency API
            Route::get('system-currency', [\Botble\PerfectPharma\Http\Controllers\Api\SystemCurrencyController::class, 'getSystemCurrency'])->name('api.system-currency');
            Route::get('all-currencies', [\Botble\PerfectPharma\Http\Controllers\Api\SystemCurrencyController::class, 'getAllCurrencies'])->name('api.all-currencies');
            Route::post('format-price', [\Botble\PerfectPharma\Http\Controllers\Api\SystemCurrencyController::class, 'formatPrice'])->name('api.format-price');
        });
    });
});

// حل نهائي لمشكلة scan-types 404
Route::any('scan-types{any?}', function () {
    return response()->json([
        'status' => 'resolved',
        'message' => 'scan-types endpoint resolved',
        'redirect' => '/admin/perfect-pharma/radiology/scan-types'
    ]);
})->where('any', '.*');





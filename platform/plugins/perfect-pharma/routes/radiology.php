<?php

use Illuminate\Support\Facades\Route;
use Bo<PERSON><PERSON>\PerfectPharma\Http\Controllers\Radiology\DashboardController;
use <PERSON><PERSON><PERSON>\PerfectPharma\Http\Controllers\Radiology\RequestController;
use Bo<PERSON><PERSON>\PerfectPharma\Http\Controllers\Radiology\AppointmentController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Radiology\ResultController;
use Botble\PerfectPharma\Http\Controllers\Radiology\PatientController;
use Botble\PerfectPharma\Http\Controllers\Radiology\EquipmentController;
use Botble\PerfectPharma\Http\Controllers\Radiology\ReportController;
use Bo<PERSON>ble\PerfectPharma\Http\Controllers\Radiology\ProfileController;

/*
|--------------------------------------------------------------------------
| Radiology Routes
|--------------------------------------------------------------------------
|
| مسارات خاصة بمراكز الأشعة - تستخدم customer guard مع middleware للتحقق من نوع المستخدم
|
*/

Route::group([
    'prefix' => 'radiology',
    'as' => 'radiology.',
    'middleware' => ['web', 'customer', 'check_user_type:radiology'],
], function () {
    
    // Dashboard Routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Request Routes
    Route::group(['prefix' => 'requests', 'as' => 'requests.'], function () {
        Route::get('/', [RequestController::class, 'index'])->name('index');
        Route::get('/{id}', [RequestController::class, 'show'])->name('show');
        Route::post('/{id}/accept', [RequestController::class, 'accept'])->name('accept');
        Route::post('/{id}/reject', [RequestController::class, 'reject'])->name('reject');
        Route::post('/{id}/schedule', [RequestController::class, 'schedule'])->name('schedule');
        Route::post('/{id}/start', [RequestController::class, 'start'])->name('start');
        Route::post('/{id}/complete', [RequestController::class, 'complete'])->name('complete');
        Route::post('/{id}/update-priority', [RequestController::class, 'updatePriority'])->name('update-priority');
    });
    
    // Appointment Routes
    Route::group(['prefix' => 'appointments', 'as' => 'appointments.'], function () {
        Route::get('/', [AppointmentController::class, 'index'])->name('index');
        Route::get('/{id}', [AppointmentController::class, 'show'])->name('show');
        Route::post('/{id}/confirm', [AppointmentController::class, 'confirm'])->name('confirm');
        Route::post('/{id}/reschedule', [AppointmentController::class, 'reschedule'])->name('reschedule');
        Route::post('/{id}/cancel', [AppointmentController::class, 'cancel'])->name('cancel');
        Route::post('/{id}/no-show', [AppointmentController::class, 'markNoShow'])->name('no-show');
        Route::get('/calendar/view', [AppointmentController::class, 'calendar'])->name('calendar');
    });
    
    // Result Routes
    Route::group(['prefix' => 'results', 'as' => 'results.'], function () {
        Route::get('/', [ResultController::class, 'index'])->name('index');
        Route::get('/create/{requestId}', [ResultController::class, 'create'])->name('create');
        Route::post('/store/{requestId}', [ResultController::class, 'store'])->name('store');
        Route::get('/{id}', [ResultController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [ResultController::class, 'edit'])->name('edit');
        Route::put('/{id}', [ResultController::class, 'update'])->name('update');
        Route::post('/{id}/approve', [ResultController::class, 'approve'])->name('approve');
        Route::post('/{id}/deliver', [ResultController::class, 'deliver'])->name('deliver');
        Route::get('/{id}/print', [ResultController::class, 'print'])->name('print');
        Route::post('/{id}/upload-images', [ResultController::class, 'uploadImages'])->name('upload-images');
    });
    
    // Patient Routes
    Route::group(['prefix' => 'patients', 'as' => 'patients.'], function () {
        Route::get('/', [PatientController::class, 'index'])->name('index');
        Route::get('/{id}', [PatientController::class, 'show'])->name('show');
        Route::get('/{id}/history', [PatientController::class, 'radiologyHistory'])->name('history');
        Route::get('/search/ajax', [PatientController::class, 'searchAjax'])->name('search');
    });
    
    // Equipment Routes
    Route::group(['prefix' => 'equipment', 'as' => 'equipment.'], function () {
        Route::get('/', [EquipmentController::class, 'index'])->name('index');
        Route::get('/create', [EquipmentController::class, 'create'])->name('create');
        Route::post('/', [EquipmentController::class, 'store'])->name('store');
        Route::get('/{id}', [EquipmentController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [EquipmentController::class, 'edit'])->name('edit');
        Route::put('/{id}', [EquipmentController::class, 'update'])->name('update');
        Route::delete('/{id}', [EquipmentController::class, 'destroy'])->name('destroy');
        Route::post('/{id}/maintenance', [EquipmentController::class, 'recordMaintenance'])->name('maintenance');
        Route::get('/maintenance/schedule', [EquipmentController::class, 'maintenanceSchedule'])->name('maintenance.schedule');
    });
    
    // Report Routes
    Route::group(['prefix' => 'reports', 'as' => 'reports.'], function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/daily', [ReportController::class, 'dailyReport'])->name('daily');
        Route::get('/monthly', [ReportController::class, 'monthlyReport'])->name('monthly');
        Route::get('/scan-volume', [ReportController::class, 'scanVolumeReport'])->name('scan-volume');
        Route::get('/equipment-usage', [ReportController::class, 'equipmentUsageReport'])->name('equipment-usage');
        Route::get('/revenue', [ReportController::class, 'revenueReport'])->name('revenue');
        Route::get('/patient-demographics', [ReportController::class, 'patientDemographicsReport'])->name('patient-demographics');
        Route::post('/export', [ReportController::class, 'exportReport'])->name('export');
    });
    
    // Profile Routes
    Route::group(['prefix' => 'profile', 'as' => 'profile.'], function () {
        Route::get('/', [ProfileController::class, 'index'])->name('index');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [ProfileController::class, 'update'])->name('update');
        Route::get('/change-password', [ProfileController::class, 'showChangePasswordForm'])->name('change-password');
        Route::post('/change-password', [ProfileController::class, 'changePassword'])->name('change-password.update');
        Route::get('/settings', [ProfileController::class, 'settings'])->name('settings');
        Route::post('/settings', [ProfileController::class, 'updateSettings'])->name('settings.update');
        Route::get('/license', [ProfileController::class, 'license'])->name('license');
        Route::post('/license', [ProfileController::class, 'updateLicense'])->name('license.update');
        Route::get('/equipment-settings', [ProfileController::class, 'equipmentSettings'])->name('equipment-settings');
        Route::post('/equipment-settings', [ProfileController::class, 'updateEquipmentSettings'])->name('equipment-settings.update');
    });
    
});

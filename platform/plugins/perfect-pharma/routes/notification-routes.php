<?php

use Illuminate\Support\Facades\Route;
use Botble\PerfectPharma\Http\Controllers\Admin\NotificationController;
use <PERSON>tble\PerfectPharma\Http\Controllers\Admin\NotificationTemplateController;
use Botble\PerfectPharma\Http\Controllers\Admin\NotificationPreferenceController;
use Botble\PerfectPharma\Http\Controllers\Admin\NotificationScheduleController;
use Botble\PerfectPharma\Http\Controllers\Admin\NotificationStatisticsController;

// مسارات إدارة الإشعارات
Route::group([
    'prefix' => 'admin/notifications',
    'as' => 'admin.notifications.',
    'middleware' => ['web', 'auth', 'core'],
], function () {
    
    // لوحة تحكم الإشعارات
    Route::get('dashboard', [NotificationController::class, 'dashboard'])->name('dashboard');
    
    // إدارة الإشعارات
    Route::get('/', [NotificationController::class, 'index'])->name('index');
    Route::get('create', [NotificationController::class, 'create'])->name('create');
    Route::post('/', [NotificationController::class, 'store'])->name('store');
    Route::get('{id}', [NotificationController::class, 'show'])->name('show');
    Route::delete('{id}', [NotificationController::class, 'destroy'])->name('destroy');
    
    // إجراءات الإشعارات
    Route::post('{id}/send', [NotificationController::class, 'sendNow'])->name('send');
    Route::post('{id}/cancel', [NotificationController::class, 'cancel'])->name('cancel');
    Route::post('{id}/retry', [NotificationController::class, 'retry'])->name('retry');
    
    // اختبار القنوات
    Route::post('test-channel', [NotificationController::class, 'testChannel'])->name('test-channel');
    
    // معالجة قائمة الانتظار
    Route::post('process-queue', [NotificationController::class, 'processQueue'])->name('process-queue');
    
    // البحث في المستقبلين
    Route::get('search/recipients', [NotificationController::class, 'searchRecipients'])->name('search.recipients');
});

// مسارات إدارة قوالب الإشعارات
Route::group([
    'prefix' => 'admin/notification-templates',
    'as' => 'admin.notification-templates.',
    'middleware' => ['web', 'auth', 'core'],
], function () {
    
    // إدارة القوالب
    Route::get('/', [NotificationTemplateController::class, 'index'])->name('index');
    Route::get('create', [NotificationTemplateController::class, 'create'])->name('create');
    Route::post('/', [NotificationTemplateController::class, 'store'])->name('store');
    Route::get('{id}', [NotificationTemplateController::class, 'show'])->name('show');
    Route::get('{id}/edit', [NotificationTemplateController::class, 'edit'])->name('edit');
    Route::put('{id}', [NotificationTemplateController::class, 'update'])->name('update');
    Route::delete('{id}', [NotificationTemplateController::class, 'destroy'])->name('destroy');
    
    // إجراءات القوالب
    Route::post('{id}/duplicate', [NotificationTemplateController::class, 'duplicate'])->name('duplicate');
    Route::post('{id}/toggle', [NotificationTemplateController::class, 'toggle'])->name('toggle');
    Route::post('{id}/set-default', [NotificationTemplateController::class, 'setDefault'])->name('set-default');
    Route::post('{id}/preview', [NotificationTemplateController::class, 'preview'])->name('preview');
    
    // تصدير واستيراد القوالب
    Route::post('export', [NotificationTemplateController::class, 'export'])->name('export');
    Route::post('import', [NotificationTemplateController::class, 'import'])->name('import');
    
    // إنشاء القوالب الافتراضية
    Route::post('create-defaults', [NotificationTemplateController::class, 'createDefaults'])->name('create-defaults');
});

// مسارات إدارة تفضيلات الإشعارات
Route::group([
    'prefix' => 'admin/notification-preferences',
    'as' => 'admin.notification-preferences.',
    'middleware' => ['web', 'auth', 'core'],
], function () {
    
    // إدارة التفضيلات
    Route::get('/', [NotificationPreferenceController::class, 'index'])->name('index');
    Route::put('/', [NotificationPreferenceController::class, 'update'])->name('update');
    
    // إجراءات التفضيلات
    Route::post('enable-all', [NotificationPreferenceController::class, 'enableAll'])->name('enable-all');
    Route::post('disable-all', [NotificationPreferenceController::class, 'disableAll'])->name('disable-all');
    Route::post('reset', [NotificationPreferenceController::class, 'reset'])->name('reset');
    
    // البحث في المستخدمين
    Route::get('search/users', [NotificationPreferenceController::class, 'searchUsers'])->name('search.users');
    
    // تصدير واستيراد التفضيلات
    Route::post('export', [NotificationPreferenceController::class, 'export'])->name('export');
    Route::post('import', [NotificationPreferenceController::class, 'import'])->name('import');
});

// مسارات إدارة جدولة الإشعارات
Route::group([
    'prefix' => 'admin/notification-schedules',
    'as' => 'admin.notification-schedules.',
    'middleware' => ['web', 'auth', 'core'],
], function () {
    
    // إدارة الجدولة
    Route::get('/', [NotificationScheduleController::class, 'index'])->name('index');
    Route::get('create', [NotificationScheduleController::class, 'create'])->name('create');
    Route::post('/', [NotificationScheduleController::class, 'store'])->name('store');
    Route::get('{id}', [NotificationScheduleController::class, 'show'])->name('show');
    Route::get('{id}/edit', [NotificationScheduleController::class, 'edit'])->name('edit');
    Route::put('{id}', [NotificationScheduleController::class, 'update'])->name('update');
    Route::delete('{id}', [NotificationScheduleController::class, 'destroy'])->name('destroy');
    
    // إجراءات الجدولة
    Route::post('{id}/run', [NotificationScheduleController::class, 'run'])->name('run');
    Route::post('{id}/pause', [NotificationScheduleController::class, 'pause'])->name('pause');
    Route::post('{id}/resume', [NotificationScheduleController::class, 'resume'])->name('resume');
    Route::post('{id}/reset', [NotificationScheduleController::class, 'reset'])->name('reset');
    
    // تشغيل جميع الجدولات المستحقة
    Route::post('run-due', [NotificationScheduleController::class, 'runDue'])->name('run-due');
    
    // إنشاء جدولات افتراضية
    Route::post('create-defaults', [NotificationScheduleController::class, 'createDefaults'])->name('create-defaults');
});

// مسارات إحصائيات الإشعارات
Route::group([
    'prefix' => 'admin/notification-statistics',
    'as' => 'admin.notification-statistics.',
    'middleware' => ['web', 'auth', 'core'],
], function () {
    
    // عرض الإحصائيات
    Route::get('/', [NotificationStatisticsController::class, 'index'])->name('index');
    Route::get('dashboard', [NotificationStatisticsController::class, 'dashboard'])->name('dashboard');
    Route::get('detailed-report', [NotificationStatisticsController::class, 'detailedReport'])->name('detailed-report');
    Route::get('comparison', [NotificationStatisticsController::class, 'comparison'])->name('comparison');
    
    // API للإحصائيات
    Route::get('api/summary', [NotificationStatisticsController::class, 'apiSummary'])->name('api.summary');
    Route::get('api/trends', [NotificationStatisticsController::class, 'apiTrends'])->name('api.trends');
    Route::get('api/real-time', [NotificationStatisticsController::class, 'apiRealTime'])->name('api.real-time');
    
    // تصدير الإحصائيات
    Route::post('export', [NotificationStatisticsController::class, 'export'])->name('export');
    
    // إدارة الإحصائيات
    Route::post('update-today', [NotificationStatisticsController::class, 'updateToday'])->name('update-today');
    Route::post('rebuild', [NotificationStatisticsController::class, 'rebuild'])->name('rebuild');
    Route::post('clean-old', [NotificationStatisticsController::class, 'cleanOld'])->name('clean-old');
});

// مسارات API للإشعارات (للتطبيقات الخارجية)
Route::group([
    'prefix' => 'api/v1/notifications',
    'as' => 'api.notifications.',
    'middleware' => ['api', 'auth:sanctum'],
], function () {
    
    // إدارة الإشعارات الشخصية
    Route::get('my', [NotificationController::class, 'myNotifications'])->name('my');
    Route::get('my/unread-count', [NotificationController::class, 'unreadCount'])->name('unread-count');
    Route::post('my/{id}/mark-read', [NotificationController::class, 'markAsRead'])->name('mark-read');
    Route::post('my/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
    
    // تفضيلات المستخدم
    Route::get('preferences', [NotificationPreferenceController::class, 'myPreferences'])->name('my-preferences');
    Route::put('preferences', [NotificationPreferenceController::class, 'updateMyPreferences'])->name('update-my-preferences');
    
    // تسجيل رموز الأجهزة للإشعارات الفورية
    Route::post('device-tokens', [NotificationController::class, 'registerDeviceToken'])->name('register-device-token');
    Route::delete('device-tokens/{token}', [NotificationController::class, 'unregisterDeviceToken'])->name('unregister-device-token');
});

// مسارات Webhook للقنوات الخارجية
Route::group([
    'prefix' => 'webhooks/notifications',
    'as' => 'webhooks.notifications.',
    'middleware' => ['web'],
], function () {
    
    // Webhook لتحديثات حالة الرسائل النصية
    Route::post('sms/status', [NotificationController::class, 'smsStatusWebhook'])->name('sms.status');
    
    // Webhook لتحديثات حالة البريد الإلكتروني
    Route::post('email/status', [NotificationController::class, 'emailStatusWebhook'])->name('email.status');
    
    // Webhook لتحديثات حالة WhatsApp
    Route::post('whatsapp/status', [NotificationController::class, 'whatsappStatusWebhook'])->name('whatsapp.status');
    
    // Webhook لتحديثات حالة الإشعارات الفورية
    Route::post('push/status', [NotificationController::class, 'pushStatusWebhook'])->name('push.status');
});

// مسارات AJAX للواجهات التفاعلية
Route::group([
    'prefix' => 'ajax/notifications',
    'as' => 'ajax.notifications.',
    'middleware' => ['web', 'auth'],
], function () {
    
    // البحث والفلترة
    Route::get('search', [NotificationController::class, 'ajaxSearch'])->name('search');
    Route::get('filter', [NotificationController::class, 'ajaxFilter'])->name('filter');
    
    // معاينة القوالب
    Route::post('templates/preview', [NotificationTemplateController::class, 'ajaxPreview'])->name('templates.preview');
    
    // إحصائيات سريعة
    Route::get('quick-stats', [NotificationStatisticsController::class, 'quickStats'])->name('quick-stats');
    
    // حالة النظام
    Route::get('system-status', [NotificationController::class, 'systemStatus'])->name('system-status');
});

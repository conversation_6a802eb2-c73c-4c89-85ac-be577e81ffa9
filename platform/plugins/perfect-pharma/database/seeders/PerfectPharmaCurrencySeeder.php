<?php

namespace Botble\PerfectPharma\Database\Seeders;

use Illuminate\Database\Seeder;
use Botble\Ecommerce\Models\Currency;

class PerfectPharmaCurrencySeeder extends Seeder
{
    public function run(): void
    {
        $currencies = [
            [
                'title' => 'SAR',
                'symbol' => 'ر.س',
                'is_prefix_symbol' => false,
                'decimals' => 2,
                'order' => 1,
                'is_default' => true,
                'exchange_rate' => 1,
            ],
            [
                'title' => 'USD',
                'symbol' => '$',
                'is_prefix_symbol' => true,
                'decimals' => 2,
                'order' => 2,
                'is_default' => false,
                'exchange_rate' => 0.27, // تقريبي
            ],
            [
                'title' => 'EUR',
                'symbol' => '€',
                'is_prefix_symbol' => true,
                'decimals' => 2,
                'order' => 3,
                'is_default' => false,
                'exchange_rate' => 0.25, // تقريبي
            ],
            [
                'title' => 'EGP',
                'symbol' => 'ج.م',
                'is_prefix_symbol' => false,
                'decimals' => 2,
                'order' => 4,
                'is_default' => false,
                'exchange_rate' => 8.5, // تقريبي
            ],
            [
                'title' => 'AED',
                'symbol' => 'د.إ',
                'is_prefix_symbol' => false,
                'decimals' => 2,
                'order' => 5,
                'is_default' => false,
                'exchange_rate' => 0.98, // تقريبي
            ],
        ];

        foreach ($currencies as $currencyData) {
            $existingCurrency = Currency::where('title', $currencyData['title'])->first();
            
            if (!$existingCurrency) {
                Currency::create($currencyData);
            } else {
                // تحديث العملة الموجودة إذا كانت SAR
                if ($currencyData['title'] === 'SAR') {
                    // إزالة الافتراضية من العملات الأخرى
                    Currency::where('id', '!=', $existingCurrency->id)
                        ->update(['is_default' => false]);
                    
                    // تحديث عملة الريال السعودي
                    $existingCurrency->update([
                        'symbol' => $currencyData['symbol'],
                        'is_prefix_symbol' => $currencyData['is_prefix_symbol'],
                        'decimals' => $currencyData['decimals'],
                        'is_default' => true,
                        'exchange_rate' => 1,
                    ]);
                }
            }
        }
    }
}

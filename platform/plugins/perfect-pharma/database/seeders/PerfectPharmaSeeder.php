<?php

namespace Botble\PerfectPharma\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PerfectPharmaSeeder extends Seeder
{
    public function run(): void
    {
        $this->seedPatients();
        $this->seedDoctors();
        $this->seedPharmacies();
        $this->seedLabs();
        $this->seedMedications();
        $this->seedAppointments();
        $this->seedPrescriptions();
        $this->seedLabTests();
        $this->seedInventory();
    }

    private function seedPatients(): void
    {
        $patients = [
            [
                'name' => 'أحمد محمد علي',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'date_of_birth' => '1985-05-15',
                'gender' => 'male',
                'address' => 'الرياض، حي النخيل',
                'national_id' => '**********',
                'blood_type' => 'O+',
                'allergies' => 'البنسلين',
                'chronic_diseases' => 'السكري',
                'emergency_contact' => '**********',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'فاطمة عبدالله',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'date_of_birth' => '1990-08-22',
                'gender' => 'female',
                'address' => 'جدة، حي الصفا',
                'national_id' => '**********',
                'blood_type' => 'A+',
                'allergies' => null,
                'chronic_diseases' => null,
                'emergency_contact' => '0501122334',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'محمد سعد الغامدي',
                'email' => '<EMAIL>',
                'phone' => '0555555555',
                'date_of_birth' => '1978-12-10',
                'gender' => 'male',
                'address' => 'الدمام، حي الفيصلية',
                'national_id' => '**********',
                'blood_type' => 'B+',
                'allergies' => 'الأسبرين',
                'chronic_diseases' => 'ضغط الدم',
                'emergency_contact' => '**********',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('patients')->insert($patients);
    }

    private function seedDoctors(): void
    {
        $doctors = [
            [
                'name' => 'د. عبدالرحمن الأحمد',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'specialization' => 'طب باطني',
                'license_number' => 'DOC001',
                'clinic_address' => 'مستشفى الملك فهد، الرياض',
                'experience_years' => 15,
                'consultation_fee' => 200.00,
                'working_hours' => json_encode([
                    'sunday' => ['09:00', '17:00'],
                    'monday' => ['09:00', '17:00'],
                    'tuesday' => ['09:00', '17:00'],
                    'wednesday' => ['09:00', '17:00'],
                    'thursday' => ['09:00', '17:00'],
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'د. نورا السالم',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'specialization' => 'أمراض نساء وولادة',
                'license_number' => 'DOC002',
                'clinic_address' => 'مستشفى الملك عبدالعزيز، جدة',
                'experience_years' => 12,
                'consultation_fee' => 250.00,
                'working_hours' => json_encode([
                    'sunday' => ['08:00', '16:00'],
                    'monday' => ['08:00', '16:00'],
                    'tuesday' => ['08:00', '16:00'],
                    'wednesday' => ['08:00', '16:00'],
                    'thursday' => ['08:00', '14:00'],
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'د. خالد المطيري',
                'email' => '<EMAIL>',
                'phone' => '**********',
                'specialization' => 'طب أطفال',
                'license_number' => 'DOC003',
                'clinic_address' => 'مستشفى الأطفال، الدمام',
                'experience_years' => 8,
                'consultation_fee' => 180.00,
                'working_hours' => json_encode([
                    'sunday' => ['10:00', '18:00'],
                    'monday' => ['10:00', '18:00'],
                    'tuesday' => ['10:00', '18:00'],
                    'wednesday' => ['10:00', '18:00'],
                    'thursday' => ['10:00', '15:00'],
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('doctors')->insert($doctors);
    }

    private function seedPharmacies(): void
    {
        $pharmacies = [
            [
                'name' => 'صيدلية النهدي',
                'address' => 'شارع الملك فهد، الرياض',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'license_number' => 'PHARM001',
                'manager_name' => 'أحمد النهدي',
                'working_hours' => json_encode([
                    'sunday' => ['08:00', '23:00'],
                    'monday' => ['08:00', '23:00'],
                    'tuesday' => ['08:00', '23:00'],
                    'wednesday' => ['08:00', '23:00'],
                    'thursday' => ['08:00', '23:00'],
                    'friday' => ['14:00', '23:00'],
                    'saturday' => ['08:00', '23:00'],
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'صيدلية الدواء',
                'address' => 'طريق الملك عبدالعزيز، جدة',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'license_number' => 'PHARM002',
                'manager_name' => 'سارة الدواء',
                'working_hours' => json_encode([
                    'sunday' => ['09:00', '22:00'],
                    'monday' => ['09:00', '22:00'],
                    'tuesday' => ['09:00', '22:00'],
                    'wednesday' => ['09:00', '22:00'],
                    'thursday' => ['09:00', '22:00'],
                    'friday' => ['15:00', '22:00'],
                    'saturday' => ['09:00', '22:00'],
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('pharmacies')->insert($pharmacies);
    }

    private function seedLabs(): void
    {
        $labs = [
            [
                'name' => 'مختبر البرج',
                'address' => 'حي العليا، الرياض',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'license_number' => 'LAB001',
                'manager_name' => 'د. محمد البرج',
                'services' => json_encode(['تحاليل دم', 'تحاليل بول', 'تحاليل هرمونات']),
                'working_hours' => json_encode([
                    'sunday' => ['07:00', '20:00'],
                    'monday' => ['07:00', '20:00'],
                    'tuesday' => ['07:00', '20:00'],
                    'wednesday' => ['07:00', '20:00'],
                    'thursday' => ['07:00', '20:00'],
                    'friday' => ['16:00', '20:00'],
                    'saturday' => ['07:00', '20:00'],
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'مختبر المدينة',
                'address' => 'شارع التحلية، جدة',
                'phone' => '**********',
                'email' => '<EMAIL>',
                'license_number' => 'LAB002',
                'manager_name' => 'د. فاطمة المدينة',
                'services' => json_encode(['تحاليل دم', 'أشعة', 'تحاليل جينية']),
                'working_hours' => json_encode([
                    'sunday' => ['08:00', '19:00'],
                    'monday' => ['08:00', '19:00'],
                    'tuesday' => ['08:00', '19:00'],
                    'wednesday' => ['08:00', '19:00'],
                    'thursday' => ['08:00', '19:00'],
                    'friday' => ['17:00', '19:00'],
                    'saturday' => ['08:00', '19:00'],
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('labs')->insert($labs);
    }

    private function seedMedications(): void
    {
        $medications = [
            [
                'name' => 'باراسيتامول 500 مجم',
                'generic_name' => 'Paracetamol',
                'manufacturer' => 'شركة الدواء السعودية',
                'dosage_form' => 'أقراص',
                'strength' => '500mg',
                'price' => 15.50,
                'barcode' => '**********123',
                'description' => 'مسكن للألم وخافض للحرارة',
                'side_effects' => 'نادراً: طفح جلدي، غثيان',
                'contraindications' => 'حساسية للباراسيتامول',
                'storage_conditions' => 'يحفظ في مكان بارد وجاف',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'أموكسيسيلين 250 مجم',
                'generic_name' => 'Amoxicillin',
                'manufacturer' => 'شركة المضادات الحيوية',
                'dosage_form' => 'كبسولات',
                'strength' => '250mg',
                'price' => 25.00,
                'barcode' => '2345678901234',
                'description' => 'مضاد حيوي واسع المجال',
                'side_effects' => 'إسهال، غثيان، طفح جلدي',
                'contraindications' => 'حساسية للبنسلين',
                'storage_conditions' => 'يحفظ في الثلاجة',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'أسبرين 100 مجم',
                'generic_name' => 'Aspirin',
                'manufacturer' => 'شركة القلب والأوعية',
                'dosage_form' => 'أقراص',
                'strength' => '100mg',
                'price' => 12.00,
                'barcode' => '3456789012345',
                'description' => 'مضاد للتجلط ومسكن للألم',
                'side_effects' => 'نزيف، حرقة معدة',
                'contraindications' => 'قرحة المعدة، نزيف',
                'storage_conditions' => 'يحفظ في مكان بارد وجاف',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('medications')->insert($medications);
    }

    private function seedAppointments(): void
    {
        $appointments = [
            [
                'patient_id' => 1,
                'doctor_id' => 1,
                'appointment_date' => now()->addDays(1)->setTime(10, 0),
                'status' => 'confirmed',
                'notes' => 'فحص دوري للسكري',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'patient_id' => 2,
                'doctor_id' => 2,
                'appointment_date' => now()->addDays(2)->setTime(14, 30),
                'status' => 'pending',
                'notes' => 'متابعة حمل',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'patient_id' => 3,
                'doctor_id' => 3,
                'appointment_date' => now()->addDays(3)->setTime(16, 0),
                'status' => 'confirmed',
                'notes' => 'فحص طفل',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('appointments')->insert($appointments);
    }

    private function seedPrescriptions(): void
    {
        $prescriptions = [
            [
                'patient_id' => 1,
                'doctor_id' => 1,
                'appointment_id' => 1,
                'diagnosis' => 'السكري النوع الثاني',
                'notes' => 'متابعة مستوى السكر',
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'patient_id' => 2,
                'doctor_id' => 2,
                'appointment_id' => 2,
                'diagnosis' => 'التهاب المسالك البولية',
                'notes' => 'شرب الماء بكثرة',
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('prescriptions')->insert($prescriptions);

        // إضافة الأدوية للوصفات
        $prescriptionMedications = [
            [
                'prescription_id' => 1,
                'medication_id' => 1,
                'dosage' => '500mg',
                'frequency' => 'مرتين يومياً',
                'duration' => '7 أيام',
                'instructions' => 'بعد الأكل',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'prescription_id' => 2,
                'medication_id' => 2,
                'dosage' => '250mg',
                'frequency' => 'ثلاث مرات يومياً',
                'duration' => '5 أيام',
                'instructions' => 'قبل الأكل',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('prescription_medications')->insert($prescriptionMedications);
    }

    private function seedLabTests(): void
    {
        $labTests = [
            [
                'patient_id' => 1,
                'lab_id' => 1,
                'test_name' => 'تحليل السكر التراكمي',
                'test_type' => 'blood',
                'status' => 'completed',
                'result' => json_encode(['HbA1c' => '7.2%']),
                'notes' => 'مستوى السكر مرتفع قليلاً',
                'requested_at' => now()->subDays(3),
                'completed_at' => now()->subDays(1),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'patient_id' => 2,
                'lab_id' => 2,
                'test_name' => 'تحليل البول',
                'test_type' => 'urine',
                'status' => 'pending',
                'result' => null,
                'notes' => 'للتأكد من عدم وجود التهاب',
                'requested_at' => now(),
                'completed_at' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('lab_test_requests')->insert($labTests);
    }

    private function seedInventory(): void
    {
        $inventory = [
            [
                'pharmacy_id' => 1,
                'medication_id' => 1,
                'quantity' => 100,
                'expiry_date' => now()->addMonths(12),
                'batch_number' => 'BATCH001',
                'cost_price' => 12.00,
                'selling_price' => 15.50,
                'reorder_level' => 20,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'pharmacy_id' => 1,
                'medication_id' => 2,
                'quantity' => 50,
                'expiry_date' => now()->addMonths(8),
                'batch_number' => 'BATCH002',
                'cost_price' => 20.00,
                'selling_price' => 25.00,
                'reorder_level' => 15,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'pharmacy_id' => 2,
                'medication_id' => 3,
                'quantity' => 75,
                'expiry_date' => now()->addMonths(18),
                'batch_number' => 'BATCH003',
                'cost_price' => 9.00,
                'selling_price' => 12.00,
                'reorder_level' => 25,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('inventory')->insert($inventory);
    }
}

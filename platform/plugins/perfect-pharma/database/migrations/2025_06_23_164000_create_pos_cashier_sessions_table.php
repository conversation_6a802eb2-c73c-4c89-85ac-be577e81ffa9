<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_cashier_sessions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('cashier_id'); // معرف الكاشير
            $table->unsignedBigInteger('pharmacy_id'); // معرف الصيدلية
            $table->enum('status', ['open', 'closed'])->default('open'); // حالة الجلسة
            $table->decimal('opening_balance', 10, 2)->default(0); // الرصيد الافتتاحي
            $table->decimal('closing_balance', 10, 2)->nullable(); // الرصيد الختامي
            $table->decimal('total_sales', 10, 2)->default(0); // إجمالي المبيعات
            $table->decimal('total_cash', 10, 2)->default(0); // إجمالي النقد
            $table->decimal('total_card', 10, 2)->default(0); // إجمالي البطاقات
            $table->decimal('total_other', 10, 2)->default(0); // طرق دفع أخرى
            $table->integer('transactions_count')->default(0); // عدد المعاملات
            $table->timestamp('opened_at'); // وقت فتح الجلسة
            $table->timestamp('closed_at')->nullable(); // وقت إغلاق الجلسة
            $table->text('opening_notes')->nullable(); // ملاحظات الافتتاح
            $table->text('closing_notes')->nullable(); // ملاحظات الإغلاق
            $table->json('payment_breakdown')->nullable(); // تفصيل طرق الدفع
            $table->timestamps();

            // الفهارس
            $table->index(['cashier_id', 'status']);
            $table->index(['pharmacy_id', 'status']);
            $table->index(['status', 'opened_at']);
            $table->index('opened_at');
            $table->index('closed_at');

            // المفاتيح الخارجية
            $table->foreign('cashier_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('pharmacy_id')->references('id')->on('pharmacies')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_cashier_sessions');
    }
};

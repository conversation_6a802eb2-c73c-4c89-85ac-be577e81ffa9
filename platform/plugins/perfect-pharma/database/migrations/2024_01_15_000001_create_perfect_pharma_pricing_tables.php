<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول أسعار المنتجات الخاصة بـ Perfect Pharma
        Schema::create('perfect_pharma_product_prices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('currency_id')->constrained('ec_currencies')->onDelete('cascade');
            $table->decimal('price', 15, 2)->nullable(); // السعر الأساسي
            $table->decimal('sale_price', 15, 2)->nullable(); // سعر التخفيض
            $table->decimal('cost_per_item', 15, 2)->nullable(); // تكلفة القطعة
            $table->timestamp('sale_start_date')->nullable(); // بداية التخفيض
            $table->timestamp('sale_end_date')->nullable(); // نهاية التخفيض
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['product_id', 'currency_id']);
            $table->index(['is_active', 'sale_start_date', 'sale_end_date']);
        });

        // جدول خصومات أنواع المستخدمين
        Schema::create('perfect_pharma_user_type_discounts', function (Blueprint $table) {
            $table->id();
            $table->string('user_type_slug', 50); // patient, doctor, pharmacy, etc
            $table->string('user_type_name', 100); // اسم نوع المستخدم
            $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة الخصم
            $table->decimal('min_order_amount', 15, 2)->nullable(); // الحد الأدنى للطلب
            $table->decimal('max_discount_amount', 15, 2)->nullable(); // الحد الأقصى للخصم
            $table->timestamp('start_date')->nullable(); // تاريخ البداية
            $table->timestamp('end_date')->nullable(); // تاريخ النهاية
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(0); // أولوية التطبيق
            $table->timestamps();
            
            $table->unique('user_type_slug');
            $table->index(['is_active', 'start_date', 'end_date']);
        });

        // جدول خصومات المنتجات لأنواع المستخدمين
        Schema::create('perfect_pharma_product_user_discounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->string('user_type_slug', 50);
            $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة خصم خاصة بالمنتج
            $table->decimal('fixed_discount_amount', 15, 2)->nullable(); // خصم ثابت
            $table->decimal('special_price', 15, 2)->nullable(); // سعر خاص
            $table->integer('min_quantity')->default(1); // الحد الأدنى للكمية
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['product_id', 'user_type_slug']);
            $table->index(['is_active', 'start_date', 'end_date']);
            $table->foreign('user_type_slug')->references('user_type_slug')->on('perfect_pharma_user_type_discounts');
        });

        // جدول خصومات الفئات لأنواع المستخدمين
        Schema::create('perfect_pharma_category_user_discounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('ec_product_categories')->onDelete('cascade');
            $table->string('user_type_slug', 50);
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->decimal('max_discount_amount', 15, 2)->nullable();
            $table->integer('min_quantity')->default(1);
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(0);
            $table->timestamps();
            
            $table->unique(['category_id', 'user_type_slug']);
            $table->index(['is_active', 'start_date', 'end_date']);
            $table->foreign('user_type_slug')->references('user_type_slug')->on('perfect_pharma_user_type_discounts');
        });

        // جدول إعدادات التسعير
        Schema::create('perfect_pharma_pricing_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key', 100)->unique();
            $table->text('value')->nullable();
            $table->string('type', 20)->default('string'); // string, boolean, integer, decimal, json
            $table->string('group', 50)->default('general'); // general, currency, discount, etc
            $table->text('description')->nullable();
            $table->timestamps();
            
            $table->index(['group', 'key']);
        });

        // جدول سجل تغييرات الأسعار
        Schema::create('perfect_pharma_price_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('currency_id')->constrained('ec_currencies')->onDelete('cascade');
            $table->decimal('old_price', 15, 2)->nullable();
            $table->decimal('new_price', 15, 2)->nullable();
            $table->decimal('old_sale_price', 15, 2)->nullable();
            $table->decimal('new_sale_price', 15, 2)->nullable();
            $table->string('changed_by_type', 50); // admin, system, api
            $table->unsignedBigInteger('changed_by_id')->nullable(); // معرف المستخدم
            $table->text('reason')->nullable(); // سبب التغيير
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->timestamps();
            
            $table->index(['product_id', 'created_at']);
            $table->index(['changed_by_type', 'changed_by_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('perfect_pharma_price_history');
        Schema::dropIfExists('perfect_pharma_pricing_settings');
        Schema::dropIfExists('perfect_pharma_category_user_discounts');
        Schema::dropIfExists('perfect_pharma_product_user_discounts');
        Schema::dropIfExists('perfect_pharma_user_type_discounts');
        Schema::dropIfExists('perfect_pharma_product_prices');
    }
};

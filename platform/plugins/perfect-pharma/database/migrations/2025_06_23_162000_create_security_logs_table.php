<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('security_logs', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // login_attempt, suspicious_activity, etc.
            $table->ipAddress('ip_address');
            $table->text('user_agent')->nullable();
            $table->json('data')->nullable(); // تفاصيل إضافية
            $table->boolean('successful')->default(false);
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('session_id')->nullable();
            $table->string('severity')->default('info'); // info, warning, error, critical
            $table->text('description')->nullable();
            $table->timestamps();

            // الفهارس
            $table->index(['type', 'created_at']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['successful', 'created_at']);
            $table->index('severity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('security_logs');
    }
};

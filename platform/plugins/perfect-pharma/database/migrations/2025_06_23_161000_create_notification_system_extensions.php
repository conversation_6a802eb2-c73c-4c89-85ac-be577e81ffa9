<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول جدولة الإشعارات المتكررة
        if (!Schema::hasTable('notification_schedules')) {
            Schema::create('notification_schedules', function (Blueprint $table) {
                $table->id();
                $table->string('name'); // اسم الجدولة
                $table->string('type'); // نوع الإشعار
                $table->string('frequency'); // daily, weekly, monthly, yearly, custom
                $table->json('frequency_data')->nullable(); // بيانات التكرار المخصص
                $table->time('time')->nullable(); // وقت الإرسال
                $table->json('days')->nullable(); // أيام الأسبوع للإرسال
                $table->date('start_date')->nullable(); // تاريخ البداية
                $table->date('end_date')->nullable(); // تاريخ النهاية
                $table->timestamp('last_run_at')->nullable(); // آخر تشغيل
                $table->timestamp('next_run_at')->nullable(); // التشغيل التالي
                $table->boolean('is_active')->default(true);
                $table->json('conditions')->nullable(); // شروط التشغيل
                $table->json('template_data')->nullable(); // بيانات القالب
                $table->timestamps();
                
                $table->index(['is_active', 'next_run_at']);
                $table->index('type');
            });
        }

        // جدول قواعد الإشعارات التلقائية
        if (!Schema::hasTable('notification_rules')) {
            Schema::create('notification_rules', function (Blueprint $table) {
                $table->id();
                $table->string('name'); // اسم القاعدة
                $table->string('event'); // الحدث المحفز
                $table->string('notification_type'); // نوع الإشعار
                $table->json('conditions'); // شروط التفعيل
                $table->json('template_data'); // بيانات القالب
                $table->json('channels'); // قنوات الإرسال
                $table->integer('delay_minutes')->default(0); // تأخير الإرسال بالدقائق
                $table->boolean('is_active')->default(true);
                $table->integer('priority')->default(1); // أولوية القاعدة
                $table->timestamps();
                
                $table->index(['event', 'is_active']);
                $table->index('notification_type');
            });
        }

        // جدول إحصائيات الإشعارات
        if (!Schema::hasTable('notification_statistics')) {
            Schema::create('notification_statistics', function (Blueprint $table) {
                $table->id();
                $table->date('date');
                $table->string('notification_type');
                $table->string('channel');
                $table->integer('sent_count')->default(0);
                $table->integer('delivered_count')->default(0);
                $table->integer('read_count')->default(0);
                $table->integer('failed_count')->default(0);
                $table->decimal('delivery_rate', 5, 2)->default(0); // معدل التسليم
                $table->decimal('read_rate', 5, 2)->default(0); // معدل القراءة
                $table->timestamps();
                
                $table->unique(['date', 'notification_type', 'channel'], 'notif_stats_unique');
                $table->index(['date', 'notification_type']);
            });
        }

        // جدول قوائم الانتظار للإشعارات
        if (!Schema::hasTable('notification_queues')) {
            Schema::create('notification_queues', function (Blueprint $table) {
                $table->id();
                $table->string('queue_name')->default('default');
                $table->foreignId('notification_id')->constrained('notifications')->onDelete('cascade');
                $table->string('channel');
                $table->integer('priority')->default(1);
                $table->timestamp('available_at'); // متى يصبح متاحاً للمعالجة
                $table->timestamp('reserved_at')->nullable(); // متى تم حجزه للمعالجة
                $table->integer('attempts')->default(0);
                $table->timestamps();
                
                $table->index(['queue_name', 'available_at', 'reserved_at']);
                $table->index(['notification_id', 'channel']);
            });
        }

        // تحديث جدول الإشعارات الموجود إذا لزم الأمر
        if (Schema::hasTable('notifications')) {
            Schema::table('notifications', function (Blueprint $table) {
                // إضافة الأعمدة المفقودة إذا لم تكن موجودة
                if (!Schema::hasColumn('notifications', 'notification_id')) {
                    $table->string('notification_id')->unique()->after('id');
                }
                if (!Schema::hasColumn('notifications', 'category')) {
                    $table->string('category')->after('type');
                }
                if (!Schema::hasColumn('notifications', 'priority')) {
                    $table->string('priority')->default('normal')->after('category');
                }
                if (!Schema::hasColumn('notifications', 'recipient_type')) {
                    $table->string('recipient_type')->after('priority');
                }
                if (!Schema::hasColumn('notifications', 'recipient_id')) {
                    $table->unsignedBigInteger('recipient_id')->after('recipient_type');
                }
                if (!Schema::hasColumn('notifications', 'recipient_email')) {
                    $table->string('recipient_email')->nullable()->after('recipient_id');
                }
                if (!Schema::hasColumn('notifications', 'recipient_phone')) {
                    $table->string('recipient_phone')->nullable()->after('recipient_email');
                }
                if (!Schema::hasColumn('notifications', 'reference_type')) {
                    $table->string('reference_type')->nullable()->after('data');
                }
                if (!Schema::hasColumn('notifications', 'reference_id')) {
                    $table->unsignedBigInteger('reference_id')->nullable()->after('reference_type');
                }
                if (!Schema::hasColumn('notifications', 'scheduled_at')) {
                    $table->timestamp('scheduled_at')->nullable()->after('reference_id');
                }
                if (!Schema::hasColumn('notifications', 'sent_at')) {
                    $table->timestamp('sent_at')->nullable()->after('scheduled_at');
                }
                if (!Schema::hasColumn('notifications', 'expires_at')) {
                    $table->timestamp('expires_at')->nullable()->after('read_at');
                }
                if (!Schema::hasColumn('notifications', 'status')) {
                    $table->string('status')->default('pending')->after('expires_at');
                }
                if (!Schema::hasColumn('notifications', 'failure_reason')) {
                    $table->text('failure_reason')->nullable()->after('status');
                }
                if (!Schema::hasColumn('notifications', 'retry_count')) {
                    $table->integer('retry_count')->default(0)->after('failure_reason');
                }
                if (!Schema::hasColumn('notifications', 'max_retries')) {
                    $table->integer('max_retries')->default(3)->after('retry_count');
                }
                if (!Schema::hasColumn('notifications', 'channels')) {
                    $table->json('channels')->nullable()->after('max_retries');
                }
                if (!Schema::hasColumn('notifications', 'metadata')) {
                    $table->json('metadata')->nullable()->after('channels');
                }
            });

            // إضافة الفهارس المفقودة
            try {
                Schema::table('notifications', function (Blueprint $table) {
                    $table->index(['recipient_type', 'recipient_id'], 'notif_recipient_idx');
                    $table->index(['type', 'category'], 'notif_type_category_idx');
                    $table->index(['status', 'scheduled_at'], 'notif_status_scheduled_idx');
                    $table->index(['reference_type', 'reference_id'], 'notif_reference_idx');
                    $table->index('notification_id', 'notif_id_idx');
                });
            } catch (\Exception $e) {
                // الفهارس موجودة بالفعل
            }
        }

        // تحديث جدول notification_templates إذا لزم الأمر
        if (Schema::hasTable('notification_templates')) {
            Schema::table('notification_templates', function (Blueprint $table) {
                if (!Schema::hasColumn('notification_templates', 'variables')) {
                    $table->json('variables')->nullable()->after('content');
                }
                if (!Schema::hasColumn('notification_templates', 'settings')) {
                    $table->json('settings')->nullable()->after('variables');
                }
                if (!Schema::hasColumn('notification_templates', 'is_active')) {
                    $table->boolean('is_active')->default(true)->after('settings');
                }
                if (!Schema::hasColumn('notification_templates', 'is_default')) {
                    $table->boolean('is_default')->default(false)->after('is_active');
                }
            });

            // إضافة الفهارس المفقودة
            try {
                Schema::table('notification_templates', function (Blueprint $table) {
                    $table->index(['type', 'channel'], 'notif_templates_type_channel_idx');
                    $table->index('is_active', 'notif_templates_active_idx');
                });
            } catch (\Exception $e) {
                // الفهارس موجودة بالفعل
            }
        }

        // تحديث جدول notification_preferences إذا لزم الأمر
        if (Schema::hasTable('notification_preferences')) {
            Schema::table('notification_preferences', function (Blueprint $table) {
                if (!Schema::hasColumn('notification_preferences', 'settings')) {
                    $table->json('settings')->nullable()->after('is_enabled');
                }
            });

            // إضافة الفهارس المفقودة
            try {
                Schema::table('notification_preferences', function (Blueprint $table) {
                    $table->unique(['user_type', 'user_id', 'notification_type'], 'notif_prefs_unique');
                    $table->index(['user_type', 'user_id'], 'notif_prefs_user_idx');
                });
            } catch (\Exception $e) {
                // الفهارس موجودة بالفعل
            }
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('notification_queues');
        Schema::dropIfExists('notification_statistics');
        Schema::dropIfExists('notification_rules');
        Schema::dropIfExists('notification_schedules');
    }
};

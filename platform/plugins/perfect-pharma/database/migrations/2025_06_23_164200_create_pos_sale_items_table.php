<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_sale_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sale_id'); // معرف البيع
            $table->unsignedBigInteger('product_id'); // معرف المنتج
            $table->unsignedBigInteger('pharmacy_inventory_id')->nullable(); // معرف المخزون
            $table->string('product_name'); // اسم المنتج
            $table->string('product_sku')->nullable(); // رمز المنتج
            $table->integer('quantity'); // الكمية
            $table->decimal('unit_price', 10, 2); // سعر الوحدة
            $table->decimal('total_price', 10, 2); // السعر الإجمالي
            $table->decimal('discount_amount', 10, 2)->default(0); // مبلغ الخصم
            $table->decimal('tax_amount', 10, 2)->default(0); // مبلغ الضريبة
            $table->string('batch_number')->nullable(); // رقم الدفعة
            $table->date('expiry_date')->nullable(); // تاريخ انتهاء الصلاحية
            $table->boolean('is_prescription_required')->default(false); // يتطلب وصفة طبية
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();

            // الفهارس
            $table->index(['sale_id', 'product_id']);
            $table->index('product_id');
            $table->index('pharmacy_inventory_id');

            // المفاتيح الخارجية
            $table->foreign('sale_id')->references('id')->on('pos_sales')->onDelete('cascade');
            // تم تعطيل المفتاح الخارجي للمنتجات مؤقتاً
            // $table->foreign('product_id')->references('id')->on('ec_products')->onDelete('cascade');
            // $table->foreign('pharmacy_inventory_id')->references('id')->on('inventory')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_sale_items');
    }
};

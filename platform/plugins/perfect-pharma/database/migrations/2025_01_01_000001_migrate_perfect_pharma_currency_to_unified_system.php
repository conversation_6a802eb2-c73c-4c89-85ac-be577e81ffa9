<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // التحقق من وجود جدول perfect_pharma_currencies
        if (!Schema::hasTable('perfect_pharma_currencies')) {
            return;
        }

        // نقل البيانات من perfect_pharma_currencies إلى ec_currencies
        $this->migrateCurrencyData();

        // تحديث المراجع في الجداول الأخرى
        $this->updateCurrencyReferences();

        // حذف جدول perfect_pharma_currencies
        Schema::dropIfExists('perfect_pharma_currencies');
    }

    public function down(): void
    {
        // إعادة إنشاء جدول perfect_pharma_currencies
        Schema::create('perfect_pharma_currencies', function (Blueprint $table) {
            $table->id();
            $table->string('title', 3);
            $table->string('symbol', 10);
            $table->boolean('is_prefix_symbol')->default(false);
            $table->tinyInteger('decimals')->default(2);
            $table->integer('order')->default(0);
            $table->boolean('is_default')->default(false);
            $table->double('exchange_rate')->default(1);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['is_default', 'is_active']);
        });

        // استعادة البيانات (إذا كانت متوفرة)
        $this->restoreCurrencyData();
    }

    /**
     * نقل بيانات العملات من perfect_pharma_currencies إلى ec_currencies
     */
    private function migrateCurrencyData(): void
    {
        $perfectPharmaCurrencies = DB::table('perfect_pharma_currencies')->get();

        foreach ($perfectPharmaCurrencies as $currency) {
            // التحقق من وجود العملة في ec_currencies
            $existingCurrency = DB::table('ec_currencies')
                ->where('title', $currency->title)
                ->first();

            if (!$existingCurrency) {
                // إنشاء عملة جديدة في ec_currencies
                DB::table('ec_currencies')->insert([
                    'title' => $currency->title,
                    'symbol' => $currency->symbol,
                    'is_prefix_symbol' => $currency->is_prefix_symbol ? 1 : 0,
                    'decimals' => $currency->decimals,
                    'order' => $currency->order,
                    'is_default' => $currency->is_default ? 1 : 0,
                    'exchange_rate' => $currency->exchange_rate,
                    'created_at' => $currency->created_at,
                    'updated_at' => $currency->updated_at,
                ]);
            } else {
                // تحديث العملة الموجودة إذا كانت Perfect Pharma currency هي الافتراضية
                if ($currency->is_default) {
                    DB::table('ec_currencies')
                        ->where('id', '!=', $existingCurrency->id)
                        ->update(['is_default' => 0]);
                    
                    DB::table('ec_currencies')
                        ->where('id', $existingCurrency->id)
                        ->update([
                            'symbol' => $currency->symbol,
                            'is_prefix_symbol' => $currency->is_prefix_symbol ? 1 : 0,
                            'decimals' => $currency->decimals,
                            'order' => $currency->order,
                            'is_default' => 1,
                            'exchange_rate' => $currency->exchange_rate,
                        ]);
                }
            }
        }
    }

    /**
     * تحديث المراجع في الجداول الأخرى
     */
    private function updateCurrencyReferences(): void
    {
        // تحديث perfect_pharma_product_prices
        if (Schema::hasTable('perfect_pharma_product_prices')) {
            $productPrices = DB::table('perfect_pharma_product_prices')->get();
            
            foreach ($productPrices as $price) {
                $oldCurrency = DB::table('perfect_pharma_currencies')
                    ->where('id', $price->currency_id)
                    ->first();
                
                if ($oldCurrency) {
                    $newCurrency = DB::table('ec_currencies')
                        ->where('title', $oldCurrency->title)
                        ->first();
                    
                    if ($newCurrency) {
                        DB::table('perfect_pharma_product_prices')
                            ->where('id', $price->id)
                            ->update(['currency_id' => $newCurrency->id]);
                    }
                }
            }
        }

        // تحديث perfect_pharma_price_history
        if (Schema::hasTable('perfect_pharma_price_history')) {
            $priceHistory = DB::table('perfect_pharma_price_history')->get();
            
            foreach ($priceHistory as $history) {
                $oldCurrency = DB::table('perfect_pharma_currencies')
                    ->where('id', $history->currency_id)
                    ->first();
                
                if ($oldCurrency) {
                    $newCurrency = DB::table('ec_currencies')
                        ->where('title', $oldCurrency->title)
                        ->first();
                    
                    if ($newCurrency) {
                        DB::table('perfect_pharma_price_history')
                            ->where('id', $history->id)
                            ->update(['currency_id' => $newCurrency->id]);
                    }
                }
            }
        }
    }

    /**
     * استعادة بيانات العملات (للتراجع)
     */
    private function restoreCurrencyData(): void
    {
        // هذه الدالة للتراجع - يمكن تنفيذها حسب الحاجة
        // في الوقت الحالي، سنتركها فارغة لأن البيانات ستكون في ec_currencies
    }
};

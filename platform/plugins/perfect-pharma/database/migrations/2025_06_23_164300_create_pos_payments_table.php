<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_number')->unique(); // رقم الدفعة
            $table->unsignedBigInteger('sale_id'); // معرف البيع
            $table->unsignedBigInteger('cashier_id'); // معرف الكاشير
            $table->enum('payment_method', ['cash', 'card', 'wallet', 'bank_transfer', 'insurance', 'credit']); // طريقة الدفع
            $table->decimal('amount', 10, 2); // المبلغ
            $table->enum('status', ['pending', 'approved', 'declined', 'cancelled', 'refunded'])->default('pending');
            $table->timestamp('payment_date'); // تاريخ الدفع
            $table->timestamp('processed_at')->nullable(); // تاريخ المعالجة
            $table->string('reference_number')->nullable(); // رقم المرجع
            $table->string('card_last_four')->nullable(); // آخر 4 أرقام من البطاقة
            $table->string('card_type')->nullable(); // نوع البطاقة
            $table->string('authorization_code')->nullable(); // رمز التفويض
            $table->text('gateway_response')->nullable(); // استجابة البوابة
            $table->text('notes')->nullable(); // ملاحظات
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->timestamps();

            // الفهارس
            $table->index(['sale_id', 'payment_date']);
            $table->index(['cashier_id', 'payment_date']);
            $table->index(['payment_method', 'payment_date']);
            $table->index(['status', 'payment_date']);
            $table->index('payment_date');
            $table->index('reference_number');

            // المفاتيح الخارجية
            $table->foreign('sale_id')->references('id')->on('pos_sales')->onDelete('cascade');
            $table->foreign('cashier_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_payments');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // التحقق من وجود الجدول أولاً
        if (!Schema::hasTable('ec_products')) {
            return;
        }

        Schema::table('ec_products', function (Blueprint $table) {
            // التحقق من عدم وجود الحقول مسبقاً
            if (!Schema::hasColumn('ec_products', 'hospital_discount_percentage')) {
                // إضافة حقول نسب الخصم لكل نوع مستخدم
                $table->decimal('hospital_discount_percentage', 5, 2)->default(0)->comment('نسبة خصم المستشفيات');
                $table->decimal('pharmacy_discount_percentage', 5, 2)->default(0)->comment('نسبة خصم الصيدليات');
                $table->decimal('clinic_discount_percentage', 5, 2)->default(0)->comment('نسبة خصم العيادات');
                $table->decimal('lab_discount_percentage', 5, 2)->default(0)->comment('نسبة خصم المعامل');
                $table->decimal('doctor_discount_percentage', 5, 2)->default(0)->comment('نسبة خصم الأطباء');
                $table->decimal('drug_supplier_discount_percentage', 5, 2)->default(0)->comment('نسبة خصم موردين الأدوية');

                // حقل لتفعيل/إلغاء نظام الخصومات المتدرجة لهذا المنتج
                $table->boolean('enable_user_type_discounts')->default(false)->comment('تفعيل الخصومات المتدرجة');

                // حقل للملاحظات حول التسعير
                $table->text('pricing_notes')->nullable()->comment('ملاحظات حول التسعير');

                // فهرسة للبحث السريع
                $table->index(['enable_user_type_discounts']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ec_products', function (Blueprint $table) {
            $table->dropColumn([
                'hospital_discount_percentage',
                'pharmacy_discount_percentage', 
                'clinic_discount_percentage',
                'lab_discount_percentage',
                'doctor_discount_percentage',
                'drug_supplier_discount_percentage',
                'enable_user_type_discounts',
                'pricing_notes'
            ]);
        });
    }
};

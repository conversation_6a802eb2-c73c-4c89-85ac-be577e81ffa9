<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_user_types', function (Blueprint $table) {
            $table->id();
            
            // ربط مع جدول Vendors (nullable في حالة عدم وجود الجدول)
            $table->unsignedBigInteger('vendor_id')->nullable();
            // سنضيف Foreign Key لاحقاً إذا كان الجدول موجوداً
            
            // ربط مع جدول Customers (لأن Vendor له Customer)
            $table->unsignedBigInteger('customer_id');
            $table->foreign('customer_id')->references('id')->on('ec_customers')->onDelete('cascade');
            
            // ربط مع جدول أنواع المستخدمين
            $table->unsignedBigInteger('user_type_id');
            $table->foreign('user_type_id')->references('id')->on('user_types')->onDelete('cascade');
            
            // حالة التحقق
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            $table->unsignedBigInteger('verified_by')->nullable();
            // Foreign key للمستخدم الذي وافق
            if (Schema::hasTable('users')) {
                $table->foreign('verified_by')->references('id')->on('users')->onDelete('set null');
            }
            
            // المستندات المرفوعة (JSON)
            $table->json('verification_documents')->nullable();
            
            // ملاحظات التحقق
            $table->text('verification_notes')->nullable();
            
            // خصومات مخصصة (تتجاوز الافتراضية)
            $table->decimal('custom_discount_percentage', 5, 2)->nullable();
            
            // مزايا مخصصة (JSON)
            $table->json('custom_features')->nullable();
            
            $table->timestamps();
            
            // فهارس للبحث السريع
            $table->index(['vendor_id', 'is_verified']);
            $table->index(['customer_id', 'user_type_id']);
            $table->index(['is_verified', 'verified_at']);
            
            // منع التكرار (فقط إذا كان vendor_id ليس null)
            // $table->unique(['vendor_id', 'user_type_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_user_types');
    }
};

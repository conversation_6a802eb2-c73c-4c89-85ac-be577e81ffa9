<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول مراكز الأشعة
        Schema::create('radiology_centers', function (Blueprint $table) {
            $table->id();
            $table->string('center_code')->unique();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('license_number')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('address');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->time('opening_time')->nullable();
            $table->time('closing_time')->nullable();
            $table->boolean('home_service')->default(false); // خدمة منزلية
            $table->decimal('home_service_fee', 8, 2)->nullable();
            $table->decimal('home_service_radius_km', 5, 2)->nullable();
            $table->json('available_scans')->nullable(); // أنواع الأشعة المتاحة
            $table->json('equipment')->nullable(); // الأجهزة المتاحة
            $table->boolean('emergency_service')->default(false); // خدمة طوارئ
            $table->boolean('accepts_insurance')->default(false);
            $table->json('insurance_companies')->nullable(); // شركات التأمين المقبولة
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['is_verified', 'is_active']);
            $table->index(['latitude', 'longitude']);
        });
        
        // جدول أنواع الأشعة
        Schema::create('radiology_scan_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم نوع الأشعة
            $table->string('code')->unique(); // كود نوع الأشعة
            $table->text('description')->nullable();
            $table->string('category'); // فئة الأشعة (X-Ray, CT, MRI, Ultrasound, etc.)
            $table->text('preparation_instructions')->nullable(); // تعليمات التحضير
            $table->integer('duration_minutes')->nullable(); // مدة الفحص بالدقائق
            $table->decimal('base_price', 8, 2)->nullable(); // السعر الأساسي
            $table->boolean('requires_contrast')->default(false); // يتطلب صبغة
            $table->boolean('requires_fasting')->default(false); // يتطلب صيام
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['category', 'is_active']);
        });
        
        // جدول طلبات الأشعة
        Schema::create('radiology_requests', function (Blueprint $table) {
            $table->id();
            $table->string('request_code')->unique();
            $table->foreignId('patient_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('doctors')->onDelete('cascade');
            $table->foreignId('radiology_center_id')->constrained('radiology_centers')->onDelete('cascade');
            $table->foreignId('scan_type_id')->constrained('radiology_scan_types')->onDelete('cascade');
            $table->text('clinical_notes')->nullable(); // ملاحظات إكلينيكية
            $table->text('symptoms')->nullable(); // الأعراض
            $table->text('medical_history')->nullable(); // التاريخ المرضي ذو الصلة
            $table->enum('urgency', ['normal', 'urgent', 'emergency'])->default('normal');
            $table->enum('status', ['pending', 'accepted', 'scheduled', 'in_progress', 'completed', 'cancelled', 'rejected'])->default('pending');
            $table->datetime('scheduled_at')->nullable(); // موعد الفحص
            $table->decimal('total_cost', 8, 2)->nullable();
            $table->enum('payment_status', ['pending', 'paid', 'refunded'])->default('pending');
            $table->text('rejection_reason')->nullable();
            $table->datetime('accepted_at')->nullable();
            $table->foreignId('accepted_by')->nullable()->constrained('users');
            $table->datetime('completed_at')->nullable();
            $table->foreignId('completed_by')->nullable()->constrained('users');
            $table->timestamps();
            
            $table->index(['status', 'urgency']);
            $table->index(['scheduled_at']);
            $table->index(['patient_id', 'created_at']);
        });
        
        // جدول نتائج الأشعة
        Schema::create('radiology_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('request_id')->constrained('radiology_requests')->onDelete('cascade');
            $table->text('findings'); // النتائج والملاحظات
            $table->text('impression')->nullable(); // الانطباع التشخيصي
            $table->text('recommendations')->nullable(); // التوصيات
            $table->json('images')->nullable(); // مسارات الصور
            $table->json('attachments')->nullable(); // مرفقات إضافية
            $table->enum('status', ['draft', 'pending_review', 'approved', 'delivered'])->default('draft');
            $table->foreignId('radiologist_id')->constrained('users'); // أخصائي الأشعة
            $table->datetime('reported_at')->nullable(); // تاريخ كتابة التقرير
            $table->foreignId('reviewed_by')->nullable()->constrained('users'); // مراجع التقرير
            $table->datetime('reviewed_at')->nullable();
            $table->datetime('delivered_at')->nullable();
            $table->timestamps();
            
            $table->index(['status']);
            $table->index(['reported_at']);
        });
        
        // جدول مواعيد الأشعة
        Schema::create('radiology_appointments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('request_id')->constrained('radiology_requests')->onDelete('cascade');
            $table->foreignId('radiology_center_id')->constrained('radiology_centers')->onDelete('cascade');
            $table->foreignId('patient_id')->constrained('users')->onDelete('cascade');
            $table->datetime('appointment_date');
            $table->integer('duration_minutes')->default(30);
            $table->enum('status', ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])->default('scheduled');
            $table->text('preparation_notes')->nullable(); // ملاحظات التحضير
            $table->text('cancellation_reason')->nullable();
            $table->datetime('confirmed_at')->nullable();
            $table->datetime('cancelled_at')->nullable();
            $table->timestamps();
            
            $table->index(['appointment_date', 'status'], 'rad_appt_date_status_idx');
            $table->index(['radiology_center_id', 'appointment_date'], 'rad_appt_center_date_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('radiology_appointments');
        Schema::dropIfExists('radiology_results');
        Schema::dropIfExists('radiology_requests');
        Schema::dropIfExists('radiology_scan_types');
        Schema::dropIfExists('radiology_centers');
    }
};

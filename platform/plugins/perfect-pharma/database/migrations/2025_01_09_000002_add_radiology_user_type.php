<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة نوع مستخدم مراكز الأشعة
        DB::table('user_types')->insert([
            'name' => 'radiology',
            'slug' => 'radiology',
            'display_name' => 'مركز أشعة',
            'description' => 'مراكز الأشعة والتصوير الطبي',
            'default_discount_percentage' => 15.00, // خصم 15% مثل المعامل
            'requires_verification' => true,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // إضافة العلاقة بين مراكز الأشعة والمستخدمين
        Schema::table('radiology_centers', function (Blueprint $table) {
            // إضافة فهرس للبحث السريع
            $table->index(['user_id']);
            $table->index(['is_active', 'is_verified']);
        });

        // إنشاء جدول خصومات مراكز الأشعة (مثل المعامل والصيدليات)
        Schema::create('radiology_center_discounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('radiology_center_id')->constrained('radiology_centers')->onDelete('cascade');
            $table->foreignId('product_id')->nullable()->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('product_category_id')->nullable()->constrained('ec_product_categories')->onDelete('cascade');
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->decimal('fixed_discount_amount', 10, 2)->default(0);
            $table->date('valid_from')->nullable();
            $table->date('valid_to')->nullable();
            $table->integer('max_uses')->nullable();
            $table->integer('used_count')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['radiology_center_id', 'is_active']);
            $table->index(['valid_from', 'valid_to']);
        });

        // إنشاء جدول عمولات مراكز الأشعة
        Schema::create('radiology_center_commissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('radiology_center_id')->constrained('radiology_centers')->onDelete('cascade');
            $table->foreignId('order_id')->constrained('ec_orders')->onDelete('cascade');
            $table->decimal('order_amount', 10, 2);
            $table->decimal('commission_percentage', 5, 2);
            $table->decimal('commission_amount', 10, 2);
            $table->enum('status', ['pending', 'paid', 'cancelled'])->default('pending');
            $table->date('due_date')->nullable();
            $table->date('paid_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->index(['radiology_center_id', 'status']);
            $table->index(['due_date']);
        });

        // إنشاء جدول إعدادات مراكز الأشعة
        Schema::create('radiology_center_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('radiology_center_id')->constrained('radiology_centers')->onDelete('cascade');
            $table->string('setting_key');
            $table->text('setting_value')->nullable();
            $table->string('setting_type')->default('string'); // string, number, boolean, json
            $table->timestamps();
            
            $table->unique(['radiology_center_id', 'setting_key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('radiology_center_settings');
        Schema::dropIfExists('radiology_center_commissions');
        Schema::dropIfExists('radiology_center_discounts');
        
        Schema::table('radiology_centers', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
            $table->dropIndex(['is_active', 'is_verified']);
        });

        // حذف نوع المستخدم
        DB::table('user_types')->where('name', 'radiology')->delete();
    }
};

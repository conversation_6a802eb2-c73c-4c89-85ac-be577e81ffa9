<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول مراكز الأشعة الأساسي
        Schema::create('radiology_centers', function (Blueprint $table) {
            $table->id();
            $table->string('center_code')->unique();
            $table->foreignId('user_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('license_number')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('address');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->time('opening_time')->nullable();
            $table->time('closing_time')->nullable();
            $table->boolean('home_service')->default(false);
            $table->decimal('home_service_fee', 8, 2)->nullable();
            $table->decimal('home_service_radius_km', 5, 2)->nullable();
            $table->json('available_scans')->nullable();
            $table->json('equipment')->nullable();
            $table->boolean('emergency_service')->default(false);
            $table->boolean('accepts_insurance')->default(false);
            $table->json('insurance_companies')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['is_verified', 'is_active']);
            $table->index(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('radiology_centers');
    }
};

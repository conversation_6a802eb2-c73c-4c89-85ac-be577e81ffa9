<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // جدول قوالب الإشعارات
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم القالب
            $table->string('type'); // نوع الإشعار
            $table->string('channel'); // قناة الإرسال (email, sms, push, in_app)
            $table->string('subject')->nullable(); // موضوع الإشعار
            $table->text('content'); // محتوى القالب مع المتغيرات
            $table->json('variables')->nullable(); // المتغيرات المتاحة
            $table->json('settings')->nullable(); // إعدادات إضافية
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // القالب الافتراضي للنوع
            $table->timestamps();
            
            $table->index(['type', 'channel']);
            $table->index('is_active');
        });

        // جدول الإشعارات الموحد
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('notification_id')->unique(); // معرف فريد للإشعار
            $table->string('type'); // نوع الإشعار
            $table->string('category'); // فئة الإشعار
            $table->string('priority')->default('normal'); // أولوية الإشعار
            
            // المستقبل
            $table->string('recipient_type'); // نوع المستقبل (user, patient, doctor, pharmacy)
            $table->unsignedBigInteger('recipient_id'); // معرف المستقبل
            $table->string('recipient_email')->nullable();
            $table->string('recipient_phone')->nullable();
            
            // المحتوى
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // بيانات إضافية
            
            // المرجع
            $table->string('reference_type')->nullable(); // نوع المرجع
            $table->unsignedBigInteger('reference_id')->nullable(); // معرف المرجع
            
            // التوقيت
            $table->timestamp('scheduled_at')->nullable(); // موعد الإرسال المجدول
            $table->timestamp('sent_at')->nullable(); // وقت الإرسال الفعلي
            $table->timestamp('read_at')->nullable(); // وقت القراءة
            $table->timestamp('expires_at')->nullable(); // تاريخ انتهاء الصلاحية
            
            // الحالة
            $table->string('status')->default('pending'); // pending, sent, failed, cancelled, expired
            $table->text('failure_reason')->nullable(); // سبب الفشل
            $table->integer('retry_count')->default(0);
            $table->integer('max_retries')->default(3);
            
            // الإعدادات
            $table->json('channels')->nullable(); // قنوات الإرسال المطلوبة
            $table->json('metadata')->nullable(); // بيانات وصفية إضافية
            
            $table->timestamps();
            
            // الفهارس
            $table->index(['recipient_type', 'recipient_id']);
            $table->index(['type', 'category']);
            $table->index(['status', 'scheduled_at']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('notification_id');
        });

        // جدول سجل إرسال الإشعارات
        Schema::create('notification_deliveries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('notification_id')->constrained('notifications')->onDelete('cascade');
            $table->string('channel'); // email, sms, push, in_app
            $table->string('status'); // pending, sent, failed, delivered, read
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->text('response')->nullable(); // استجابة مقدم الخدمة
            $table->text('error_message')->nullable();
            $table->json('metadata')->nullable(); // بيانات إضافية خاصة بالقناة
            $table->timestamps();
            
            $table->index(['notification_id', 'channel']);
            $table->index(['status', 'sent_at']);
        });

        // جدول تفضيلات الإشعارات للمستخدمين
        Schema::create('notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->string('user_type'); // user, patient, doctor, pharmacy
            $table->unsignedBigInteger('user_id');
            $table->string('notification_type'); // نوع الإشعار
            $table->json('channels'); // القنوات المفضلة
            $table->boolean('is_enabled')->default(true);
            $table->json('settings')->nullable(); // إعدادات إضافية
            $table->timestamps();
            
            $table->unique(['user_type', 'user_id', 'notification_type'], 'notif_prefs_unique');
            $table->index(['user_type', 'user_id']);
        });

        // جدول جدولة الإشعارات المتكررة
        Schema::create('notification_schedules', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الجدولة
            $table->string('type'); // نوع الإشعار
            $table->string('frequency'); // daily, weekly, monthly, yearly, custom
            $table->json('frequency_data')->nullable(); // بيانات التكرار المخصص
            $table->time('time')->nullable(); // وقت الإرسال
            $table->json('days')->nullable(); // أيام الأسبوع للإرسال
            $table->date('start_date')->nullable(); // تاريخ البداية
            $table->date('end_date')->nullable(); // تاريخ النهاية
            $table->timestamp('last_run_at')->nullable(); // آخر تشغيل
            $table->timestamp('next_run_at')->nullable(); // التشغيل التالي
            $table->boolean('is_active')->default(true);
            $table->json('conditions')->nullable(); // شروط التشغيل
            $table->json('template_data')->nullable(); // بيانات القالب
            $table->timestamps();
            
            $table->index(['is_active', 'next_run_at']);
            $table->index('type');
        });

        // جدول قواعد الإشعارات التلقائية
        Schema::create('notification_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم القاعدة
            $table->string('event'); // الحدث المحفز
            $table->string('notification_type'); // نوع الإشعار
            $table->json('conditions'); // شروط التفعيل
            $table->json('template_data'); // بيانات القالب
            $table->json('channels'); // قنوات الإرسال
            $table->integer('delay_minutes')->default(0); // تأخير الإرسال بالدقائق
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(1); // أولوية القاعدة
            $table->timestamps();
            
            $table->index(['event', 'is_active']);
            $table->index('notification_type');
        });

        // جدول إحصائيات الإشعارات
        Schema::create('notification_statistics', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->string('notification_type');
            $table->string('channel');
            $table->integer('sent_count')->default(0);
            $table->integer('delivered_count')->default(0);
            $table->integer('read_count')->default(0);
            $table->integer('failed_count')->default(0);
            $table->decimal('delivery_rate', 5, 2)->default(0); // معدل التسليم
            $table->decimal('read_rate', 5, 2)->default(0); // معدل القراءة
            $table->timestamps();
            
            $table->unique(['date', 'notification_type', 'channel']);
            $table->index(['date', 'notification_type']);
        });

        // جدول قوائم الانتظار للإشعارات
        Schema::create('notification_queues', function (Blueprint $table) {
            $table->id();
            $table->string('queue_name')->default('default');
            $table->foreignId('notification_id')->constrained('notifications')->onDelete('cascade');
            $table->string('channel');
            $table->integer('priority')->default(1);
            $table->timestamp('available_at'); // متى يصبح متاحاً للمعالجة
            $table->timestamp('reserved_at')->nullable(); // متى تم حجزه للمعالجة
            $table->integer('attempts')->default(0);
            $table->timestamps();
            
            $table->index(['queue_name', 'available_at', 'reserved_at']);
            $table->index(['notification_id', 'channel']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notification_queues');
        Schema::dropIfExists('notification_statistics');
        Schema::dropIfExists('notification_rules');
        Schema::dropIfExists('notification_schedules');
        Schema::dropIfExists('notification_preferences');
        Schema::dropIfExists('notification_deliveries');
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('notification_templates');
    }
};

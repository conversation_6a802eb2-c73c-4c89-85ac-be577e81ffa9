<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('integration_logs', function (Blueprint $table) {
            $table->id();
            $table->string('system'); // his, lims, epharmacy, insurance, payment, etc.
            $table->string('operation'); // sync, verify, process, send, etc.
            $table->enum('status', ['pending', 'success', 'failed', 'timeout'])->default('pending');
            $table->json('request_data')->nullable(); // البيانات المرسلة
            $table->json('response_data')->nullable(); // الاستجابة المستلمة
            $table->text('error_message')->nullable(); // رسالة الخطأ في حالة الفشل
            $table->integer('retry_count')->default(0); // عدد المحاولات
            $table->integer('response_time')->nullable(); // وقت الاستجابة بالميلي ثانية
            $table->string('correlation_id')->nullable(); // معرف الربط
            $table->unsignedBigInteger('user_id')->nullable(); // المستخدم الذي بدأ العملية
            $table->timestamps();

            // الفهارس
            $table->index(['system', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['operation', 'created_at']);
            $table->index('correlation_id');
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('integration_logs');
    }
};

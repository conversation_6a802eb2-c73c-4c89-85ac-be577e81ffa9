<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_sales', function (Blueprint $table) {
            $table->id();
            $table->string('sale_number')->unique(); // رقم البيع
            $table->unsignedBigInteger('pharmacy_id'); // معرف الصيدلية
            $table->unsignedBigInteger('cashier_id'); // معرف الكاشير
            $table->unsignedBigInteger('customer_id')->nullable(); // معرف العميل
            $table->decimal('subtotal', 10, 2); // المجموع الفرعي
            $table->decimal('tax_amount', 10, 2)->default(0); // مبلغ الضريبة
            $table->decimal('discount_amount', 10, 2)->default(0); // مبلغ الخصم
            $table->decimal('total_amount', 10, 2); // المبلغ الإجمالي
            $table->decimal('paid_amount', 10, 2)->default(0); // المبلغ المدفوع
            $table->decimal('change_amount', 10, 2)->default(0); // مبلغ الباقي
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded'])->default('pending');
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'refunded'])->default('pending');
            $table->timestamp('sale_date'); // تاريخ البيع
            $table->timestamp('completed_at')->nullable(); // تاريخ الإكمال
            $table->timestamp('cancelled_at')->nullable(); // تاريخ الإلغاء
            $table->text('notes')->nullable(); // ملاحظات
            $table->text('cancellation_reason')->nullable(); // سبب الإلغاء
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->timestamps();

            // الفهارس
            $table->index(['pharmacy_id', 'sale_date']);
            $table->index(['cashier_id', 'sale_date']);
            $table->index(['customer_id', 'sale_date']);
            $table->index(['status', 'sale_date']);
            $table->index(['payment_status', 'sale_date']);
            $table->index('sale_date');

            // المفاتيح الخارجية
            $table->foreign('pharmacy_id')->references('id')->on('pharmacies')->onDelete('cascade');
            $table->foreign('cashier_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('customer_id')->references('id')->on('ec_customers')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_sales');
    }
};

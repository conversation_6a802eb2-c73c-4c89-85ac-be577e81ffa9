<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_id')->unique(); // معرف المعاملة من بوابة الدفع
            $table->decimal('amount', 10, 2); // المبلغ
            $table->string('currency', 3)->default('SAR'); // العملة
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->string('gateway'); // بوابة الدفع المستخدمة
            $table->json('gateway_response')->nullable(); // استجابة بوابة الدفع
            $table->json('customer_data')->nullable(); // بيانات العميل
            $table->string('reference_type')->nullable(); // نوع المرجع (sale, appointment, etc.)
            $table->unsignedBigInteger('reference_id')->nullable(); // معرف المرجع
            $table->text('description')->nullable(); // وصف المعاملة
            $table->timestamp('processed_at')->nullable(); // وقت المعالجة
            $table->decimal('fee_amount', 8, 2)->nullable(); // رسوم المعاملة
            $table->string('payment_method')->nullable(); // طريقة الدفع (card, bank_transfer, etc.)
            $table->timestamps();

            // الفهارس
            $table->index(['status', 'created_at']);
            $table->index(['gateway', 'created_at']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('processed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};

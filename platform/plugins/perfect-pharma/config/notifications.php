<?php

return [
    /*
    |--------------------------------------------------------------------------
    | إعدادات نظام الإشعارات
    |--------------------------------------------------------------------------
    */

    // القنوات المتاحة
    'channels' => [
        'email' => [
            'enabled' => env('NOTIFICATION_EMAIL_ENABLED', true),
            'driver' => env('MAIL_MAILER', 'smtp'),
        ],
        'sms' => [
            'enabled' => env('NOTIFICATION_SMS_ENABLED', true),
            'provider' => env('SMS_PROVIDER', 'taqnyat'),
        ],
        'push' => [
            'enabled' => env('NOTIFICATION_PUSH_ENABLED', true),
            'provider' => env('PUSH_PROVIDER', 'firebase'),
        ],
        'in_app' => [
            'enabled' => true,
            'real_time_method' => env('NOTIFICATION_REAL_TIME_METHOD', 'polling'),
            'polling_interval' => env('NOTIFICATION_POLLING_INTERVAL', 30),
        ],
        'whatsapp' => [
            'enabled' => env('NOTIFICATION_WHATSAPP_ENABLED', false),
            'provider' => env('WHATSAPP_PROVIDER', 'twilio'),
        ],
    ],

    // أنواع الإشعارات
    'types' => [
        'appointment_reminder' => [
            'name' => 'تذكير موعد',
            'category' => 'medical',
            'default_channels' => ['email', 'sms', 'in_app'],
            'priority' => 'normal',
        ],
        'appointment_confirmation' => [
            'name' => 'تأكيد موعد',
            'category' => 'medical',
            'default_channels' => ['email', 'sms', 'in_app'],
            'priority' => 'high',
        ],
        'appointment_cancellation' => [
            'name' => 'إلغاء موعد',
            'category' => 'medical',
            'default_channels' => ['email', 'sms', 'in_app'],
            'priority' => 'high',
        ],
        'medication_reminder' => [
            'name' => 'تذكير دواء',
            'category' => 'medical',
            'default_channels' => ['sms', 'push', 'in_app'],
            'priority' => 'high',
        ],
        'prescription_ready' => [
            'name' => 'وصفة جاهزة',
            'category' => 'medical',
            'default_channels' => ['email', 'sms', 'in_app'],
            'priority' => 'normal',
        ],
        'test_result_ready' => [
            'name' => 'نتيجة تحليل جاهزة',
            'category' => 'medical',
            'default_channels' => ['email', 'sms', 'in_app'],
            'priority' => 'high',
        ],
        'inventory_alert' => [
            'name' => 'تنبيه مخزون',
            'category' => 'administrative',
            'default_channels' => ['email', 'in_app'],
            'priority' => 'normal',
        ],
        'payment_reminder' => [
            'name' => 'تذكير دفع',
            'category' => 'financial',
            'default_channels' => ['email', 'sms', 'in_app'],
            'priority' => 'normal',
        ],
        'system_maintenance' => [
            'name' => 'صيانة النظام',
            'category' => 'system',
            'default_channels' => ['email', 'in_app'],
            'priority' => 'low',
        ],
        'promotional' => [
            'name' => 'عرض ترويجي',
            'category' => 'marketing',
            'default_channels' => ['email', 'in_app'],
            'priority' => 'low',
        ],
        'security_alert' => [
            'name' => 'تنبيه أمني',
            'category' => 'system',
            'default_channels' => ['email', 'sms', 'in_app'],
            'priority' => 'urgent',
        ],
    ],

    // الأولويات
    'priorities' => [
        'low' => [
            'name' => 'منخفضة',
            'delay_minutes' => 60,
            'retry_attempts' => 1,
        ],
        'normal' => [
            'name' => 'عادية',
            'delay_minutes' => 15,
            'retry_attempts' => 2,
        ],
        'high' => [
            'name' => 'عالية',
            'delay_minutes' => 5,
            'retry_attempts' => 3,
        ],
        'urgent' => [
            'name' => 'عاجلة',
            'delay_minutes' => 0,
            'retry_attempts' => 5,
        ],
    ],

    // إعدادات قوائم الانتظار
    'queues' => [
        'default' => [
            'connection' => env('QUEUE_CONNECTION', 'database'),
            'queue' => 'notifications',
            'retry_after' => 90,
            'max_tries' => 3,
        ],
        'high_priority' => [
            'connection' => env('QUEUE_CONNECTION', 'database'),
            'queue' => 'notifications-high',
            'retry_after' => 60,
            'max_tries' => 5,
        ],
        'low_priority' => [
            'connection' => env('QUEUE_CONNECTION', 'database'),
            'queue' => 'notifications-low',
            'retry_after' => 300,
            'max_tries' => 1,
        ],
    ],

    // إعدادات الجدولة
    'scheduling' => [
        'enabled' => env('NOTIFICATION_SCHEDULING_ENABLED', true),
        'check_interval' => env('NOTIFICATION_SCHEDULE_CHECK_INTERVAL', 60), // ثواني
        'max_schedules_per_run' => env('NOTIFICATION_MAX_SCHEDULES_PER_RUN', 50),
    ],

    // إعدادات الإحصائيات
    'statistics' => [
        'enabled' => env('NOTIFICATION_STATISTICS_ENABLED', true),
        'retention_days' => env('NOTIFICATION_STATISTICS_RETENTION_DAYS', 365),
        'update_interval' => env('NOTIFICATION_STATISTICS_UPDATE_INTERVAL', 3600), // ثواني
    ],

    // إعدادات التفضيلات الافتراضية
    'default_preferences' => [
        'patient' => [
            'appointment_reminder' => ['email', 'sms'],
            'appointment_confirmation' => ['email', 'sms'],
            'appointment_cancellation' => ['email', 'sms'],
            'medication_reminder' => ['sms', 'push'],
            'prescription_ready' => ['email', 'sms'],
            'test_result_ready' => ['email', 'sms'],
            'payment_reminder' => ['email'],
            'promotional' => ['email'],
        ],
        'doctor' => [
            'appointment_confirmation' => ['email', 'in_app'],
            'appointment_cancellation' => ['email', 'in_app'],
            'test_result_ready' => ['email', 'in_app'],
            'system_maintenance' => ['email'],
        ],
        'pharmacy' => [
            'prescription_ready' => ['email', 'in_app'],
            'inventory_alert' => ['email', 'in_app'],
            'system_maintenance' => ['email'],
        ],
        'user' => [
            'system_maintenance' => ['email', 'in_app'],
            'security_alert' => ['email', 'sms'],
        ],
    ],

    // إعدادات الأمان
    'security' => [
        'rate_limiting' => [
            'enabled' => env('NOTIFICATION_RATE_LIMITING_ENABLED', true),
            'max_per_minute' => env('NOTIFICATION_MAX_PER_MINUTE', 60),
            'max_per_hour' => env('NOTIFICATION_MAX_PER_HOUR', 1000),
            'max_per_day' => env('NOTIFICATION_MAX_PER_DAY', 10000),
        ],
        'content_filtering' => [
            'enabled' => env('NOTIFICATION_CONTENT_FILTERING_ENABLED', true),
            'blocked_words' => [],
            'max_length' => env('NOTIFICATION_MAX_LENGTH', 1600),
        ],
        'encryption' => [
            'enabled' => env('NOTIFICATION_ENCRYPTION_ENABLED', false),
            'algorithm' => 'AES-256-CBC',
        ],
    ],

    // إعدادات التسجيل
    'logging' => [
        'enabled' => env('NOTIFICATION_LOGGING_ENABLED', true),
        'level' => env('NOTIFICATION_LOG_LEVEL', 'info'),
        'channels' => ['daily'],
        'retention_days' => env('NOTIFICATION_LOG_RETENTION_DAYS', 30),
    ],

    // إعدادات التخزين المؤقت
    'cache' => [
        'enabled' => env('NOTIFICATION_CACHE_ENABLED', true),
        'ttl' => env('NOTIFICATION_CACHE_TTL', 3600), // ثواني
        'prefix' => 'notifications:',
    ],

    // إعدادات التصدير والاستيراد
    'export_import' => [
        'max_file_size' => env('NOTIFICATION_MAX_FILE_SIZE', 10485760), // 10MB
        'allowed_formats' => ['json', 'csv', 'xlsx'],
        'encryption_enabled' => env('NOTIFICATION_EXPORT_ENCRYPTION', false),
    ],

    // إعدادات الصيانة
    'maintenance' => [
        'cleanup_old_notifications' => [
            'enabled' => env('NOTIFICATION_CLEANUP_ENABLED', true),
            'retention_days' => env('NOTIFICATION_RETENTION_DAYS', 90),
            'schedule' => '0 2 * * *', // يومياً في الساعة 2 صباحاً
        ],
        'cleanup_old_statistics' => [
            'enabled' => env('NOTIFICATION_CLEANUP_STATS_ENABLED', true),
            'retention_days' => env('NOTIFICATION_STATS_RETENTION_DAYS', 365),
            'schedule' => '0 3 * * 0', // أسبوعياً يوم الأحد في الساعة 3 صباحاً
        ],
    ],

    // إعدادات التطوير والاختبار
    'development' => [
        'fake_sending' => env('NOTIFICATION_FAKE_SENDING', false),
        'log_all_notifications' => env('NOTIFICATION_LOG_ALL', false),
        'test_mode' => env('NOTIFICATION_TEST_MODE', false),
        'debug_mode' => env('NOTIFICATION_DEBUG_MODE', false),
    ],
];

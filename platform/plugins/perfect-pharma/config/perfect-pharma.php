<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Perfect Pharma Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the Perfect Pharma
    | plugin including integrations, backup settings, and other options.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Backup Configuration
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'database' => true,
        'files' => true,
        'compression' => true,
        'encryption' => false,
        'retention_days' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | External Integrations
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        /*
        |--------------------------------------------------------------------------
        | Health Information System (HIS)
        |--------------------------------------------------------------------------
        */
        'his' => [
            'enabled' => false,
            'endpoint' => env('HIS_ENDPOINT', ''),
            'api_key' => env('HIS_API_KEY', ''),
            'health_check' => '/health',
            'timeout' => 30,
        ],

        /*
        |--------------------------------------------------------------------------
        | Laboratory Information Management System (LIMS)
        |--------------------------------------------------------------------------
        */
        'lims' => [
            'enabled' => false,
            'endpoint' => env('LIMS_ENDPOINT', ''),
            'credentials' => [
                'username' => env('LIMS_USERNAME', ''),
                'password' => env('LIMS_PASSWORD', ''),
            ],
            'health_check' => '/status',
            'timeout' => 30,
        ],

        /*
        |--------------------------------------------------------------------------
        | Electronic Pharmacy System
        |--------------------------------------------------------------------------
        */
        'epharmacy' => [
            'enabled' => false,
            'endpoint' => env('EPHARMACY_ENDPOINT', ''),
            'api_key' => env('EPHARMACY_API_KEY', ''),
            'health_check' => '/ping',
            'timeout' => 30,
        ],

        /*
        |--------------------------------------------------------------------------
        | Insurance System
        |--------------------------------------------------------------------------
        */
        'insurance' => [
            'enabled' => false,
            'endpoint' => env('INSURANCE_ENDPOINT', ''),
            'credentials' => [
                'token' => env('INSURANCE_TOKEN', ''),
            ],
            'health_check' => '/health',
            'timeout' => 30,
        ],

        /*
        |--------------------------------------------------------------------------
        | Payment Gateways
        |--------------------------------------------------------------------------
        */
        'payment' => [
            'default' => [
                'enabled' => false,
                'endpoint' => env('PAYMENT_ENDPOINT', ''),
                'api_key' => env('PAYMENT_API_KEY', ''),
                'health_check' => '/status',
            ],
            'paypal' => [
                'enabled' => false,
                'endpoint' => 'https://api.paypal.com',
                'api_key' => env('PAYPAL_API_KEY', ''),
                'secret' => env('PAYPAL_SECRET', ''),
            ],
            'stripe' => [
                'enabled' => false,
                'endpoint' => 'https://api.stripe.com',
                'api_key' => env('STRIPE_API_KEY', ''),
                'secret' => env('STRIPE_SECRET', ''),
            ],
            'mada' => [
                'enabled' => false,
                'endpoint' => env('MADA_ENDPOINT', ''),
                'api_key' => env('MADA_API_KEY', ''),
                'merchant_id' => env('MADA_MERCHANT_ID', ''),
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | SMS Providers
        |--------------------------------------------------------------------------
        */
        'sms' => [
            'default_provider' => 'taqnyat',
            'providers' => [
                'taqnyat' => [
                    'enabled' => false,
                    'endpoint' => 'https://api.taqnyat.sa/v1/messages',
                    'api_key' => env('TAQNYAT_API_KEY', ''),
                    'sender_name' => env('TAQNYAT_SENDER', 'PerfectPharma'),
                ],
                'twilio' => [
                    'enabled' => false,
                    'endpoint' => 'https://api.twilio.com/2010-04-01',
                    'api_key' => env('TWILIO_SID', ''),
                    'secret' => env('TWILIO_TOKEN', ''),
                    'sender_name' => env('TWILIO_FROM', ''),
                ],
                'nexmo' => [
                    'enabled' => false,
                    'endpoint' => 'https://rest.nexmo.com/sms/json',
                    'api_key' => env('NEXMO_KEY', ''),
                    'secret' => env('NEXMO_SECRET', ''),
                    'sender_name' => env('NEXMO_FROM', 'PerfectPharma'),
                ],
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | Email Providers
        |--------------------------------------------------------------------------
        */
        'email' => [
            'default_provider' => 'sendgrid',
            'providers' => [
                'sendgrid' => [
                    'enabled' => false,
                    'endpoint' => 'https://api.sendgrid.com/v3/mail/send',
                    'api_key' => env('SENDGRID_API_KEY', ''),
                    'from_email' => env('SENDGRID_FROM_EMAIL', '<EMAIL>'),
                ],
                'mailgun' => [
                    'enabled' => false,
                    'endpoint' => 'https://api.mailgun.net/v3',
                    'api_key' => env('MAILGUN_SECRET', ''),
                    'domain' => env('MAILGUN_DOMAIN', ''),
                    'from_email' => env('MAILGUN_FROM_EMAIL', '<EMAIL>'),
                ],
                'ses' => [
                    'enabled' => false,
                    'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
                    'key' => env('AWS_ACCESS_KEY_ID', ''),
                    'secret' => env('AWS_SECRET_ACCESS_KEY', ''),
                    'from_email' => env('SES_FROM_EMAIL', '<EMAIL>'),
                ],
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | Cloud Storage Providers
        |--------------------------------------------------------------------------
        */
        'cloud' => [
            'default_provider' => 'aws',
            'providers' => [
                'aws' => [
                    'enabled' => false,
                    'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
                    'bucket' => env('AWS_BUCKET', ''),
                    'key' => env('AWS_ACCESS_KEY_ID', ''),
                    'secret' => env('AWS_SECRET_ACCESS_KEY', ''),
                ],
                'google' => [
                    'enabled' => false,
                    'project_id' => env('GOOGLE_CLOUD_PROJECT_ID', ''),
                    'bucket' => env('GOOGLE_CLOUD_BUCKET', ''),
                    'key_file' => env('GOOGLE_CLOUD_KEY_FILE', ''),
                ],
                'azure' => [
                    'enabled' => false,
                    'account_name' => env('AZURE_STORAGE_ACCOUNT', ''),
                    'account_key' => env('AZURE_STORAGE_KEY', ''),
                    'container' => env('AZURE_STORAGE_CONTAINER', ''),
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'queue_driver' => env('NOTIFICATION_QUEUE_DRIVER', 'database'),
        'retry_attempts' => 3,
        'retry_delay' => 300, // seconds
        'batch_size' => 100,
        'rate_limit' => [
            'sms' => 10, // per minute
            'email' => 50, // per minute
            'push' => 100, // per minute
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'password_policy' => [
            'min_length' => 8,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_symbols' => true,
        ],
        'session_timeout' => 120, // minutes
        'max_login_attempts' => 5,
        'lockout_duration' => 30, // minutes
        'two_factor_auth' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    */
    'api' => [
        'rate_limit' => 60, // requests per minute
        'version' => 'v1',
        'prefix' => 'api/perfect-pharma',
        'middleware' => ['api', 'auth:sanctum'],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Settings
    |--------------------------------------------------------------------------
    */
    'uploads' => [
        'max_file_size' => 10240, // KB
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'scan_for_viruses' => false,
        'auto_optimize_images' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'default_ttl' => 3600, // seconds
        'tags' => [
            'patients' => 'perfect_pharma_patients',
            'doctors' => 'perfect_pharma_doctors',
            'appointments' => 'perfect_pharma_appointments',
            'prescriptions' => 'perfect_pharma_prescriptions',
            'inventory' => 'perfect_pharma_inventory',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'channels' => [
            'security' => [
                'driver' => 'daily',
                'path' => storage_path('logs/perfect-pharma-security.log'),
                'level' => 'info',
                'days' => 30,
            ],
            'integration' => [
                'driver' => 'daily',
                'path' => storage_path('logs/perfect-pharma-integration.log'),
                'level' => 'info',
                'days' => 30,
            ],
            'audit' => [
                'driver' => 'daily',
                'path' => storage_path('logs/perfect-pharma-audit.log'),
                'level' => 'info',
                'days' => 90,
            ],
        ],
    ],
];

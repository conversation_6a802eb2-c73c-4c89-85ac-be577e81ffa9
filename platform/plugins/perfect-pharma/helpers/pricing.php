<?php

use Botble\PerfectPharma\Helpers\PricingHelper;
use Botble\Ecommerce\Models\Product;

if (!function_exists('perfect_pharma_calculate_price')) {
    /**
     * حساب السعر للمنتج حسب المستخدم الحالي
     */
    function perfect_pharma_calculate_price(Product $product, $customer = null): array
    {
        return PricingHelper::calculatePrice($product, $customer);
    }
}

if (!function_exists('perfect_pharma_format_price')) {
    /**
     * عرض السعر مع التنسيق
     */
    function perfect_pharma_format_price(Product $product, array $options = []): string
    {
        return PricingHelper::formatPrice($product, $options);
    }
}

if (!function_exists('perfect_pharma_user_discount_info')) {
    /**
     * الحصول على معلومات خصم المستخدم
     */
    function perfect_pharma_user_discount_info($customer = null): array
    {
        return PricingHelper::getUserDiscountInfo($customer);
    }
}

if (!function_exists('perfect_pharma_is_eligible_for_discount')) {
    /**
     * التحقق من أهلية المستخدم للخصم
     */
    function perfect_pharma_is_eligible_for_discount($customer = null): bool
    {
        return PricingHelper::isEligibleForDiscount($customer);
    }
}

if (!function_exists('perfect_pharma_discount_status_message')) {
    /**
     * عرض رسالة حالة الخصم
     */
    function perfect_pharma_discount_status_message($customer = null): string
    {
        return PricingHelper::getDiscountStatusMessage($customer);
    }
}

if (!function_exists('perfect_pharma_price_comparison_table')) {
    /**
     * عرض جدول مقارنة الأسعار
     */
    function perfect_pharma_price_comparison_table(Product $product): string
    {
        return PricingHelper::getPriceComparisonTable($product);
    }
}

if (!function_exists('perfect_pharma_calculate_cart_total')) {
    /**
     * حساب إجمالي السلة
     */
    function perfect_pharma_calculate_cart_total(array $cartItems, $customer = null): array
    {
        return PricingHelper::calculateCartTotal($cartItems, $customer);
    }
}

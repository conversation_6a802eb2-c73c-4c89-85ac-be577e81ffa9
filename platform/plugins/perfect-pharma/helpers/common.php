<?php

use Bo<PERSON><PERSON>\PerfectPharma\Supports\PerfectPharmaHelper;

if (! function_exists('get_perfect_pharma_setting')) {
    /**
     * Get Perfect Pharma setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function get_perfect_pharma_setting(string $key, mixed $default = null): mixed
    {
        return setting(PerfectPharmaHelper::getSettingPrefix() . $key, $default);
    }
}

if (! function_exists('set_perfect_pharma_setting')) {
    /**
     * Set Perfect Pharma setting value
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    function set_perfect_pharma_setting(string $key, mixed $value): void
    {
        setting()->set(PerfectPharmaHelper::getSettingPrefix() . $key, $value);
        setting()->save();
    }
}

if (! function_exists('perfect_pharma_user_types')) {
    /**
     * Get available user types
     *
     * @return array
     */
    function perfect_pharma_user_types(): array
    {
        return config('plugins.perfect-pharma.general.user_types', []);
    }
}

if (! function_exists('perfect_pharma_user_type_discount')) {
    /**
     * Get discount rate for user type
     *
     * @param string $userType
     * @return float
     */
    function perfect_pharma_user_type_discount(string $userType): float
    {
        $userTypes = perfect_pharma_user_types();
        
        if (isset($userTypes[$userType])) {
            return (float) get_perfect_pharma_setting("discount_{$userType}", $userTypes[$userType]['discount_rate']);
        }
        
        return 0.0;
    }
}

if (! function_exists('perfect_pharma_appointment_types')) {
    /**
     * Get available appointment types
     *
     * @return array
     */
    function perfect_pharma_appointment_types(): array
    {
        return config('plugins.perfect-pharma.general.appointment_types', []);
    }
}

if (! function_exists('perfect_pharma_medical_specializations')) {
    /**
     * Get available medical specializations
     *
     * @return array
     */
    function perfect_pharma_medical_specializations(): array
    {
        return config('plugins.perfect-pharma.general.medical_specializations', []);
    }
}

if (! function_exists('perfect_pharma_lab_test_categories')) {
    /**
     * Get available lab test categories
     *
     * @return array
     */
    function perfect_pharma_lab_test_categories(): array
    {
        return config('plugins.perfect-pharma.general.lab_test_categories', []);
    }
}

if (! function_exists('perfect_pharma_is_feature_enabled')) {
    /**
     * Check if a feature is enabled
     *
     * @param string $feature
     * @return bool
     */
    function perfect_pharma_is_feature_enabled(string $feature): bool
    {
        return (bool) get_perfect_pharma_setting("{$feature}_enabled", true);
    }
}

if (! function_exists('perfect_pharma_format_currency')) {
    /**
     * Format currency for Perfect Pharma
     *
     * @param float $amount
     * @param bool $showSymbol
     * @return string
     */
    function perfect_pharma_format_currency(float $amount, bool $showSymbol = true): string
    {
        return \Botble\PerfectPharma\Supports\CurrencyHelper::formatPrice($amount, $showSymbol);
    }
}

if (! function_exists('perfect_pharma_calculate_commission')) {
    /**
     * Calculate commission for user type
     *
     * @param float $amount
     * @param string $userType
     * @return float
     */
    function perfect_pharma_calculate_commission(float $amount, string $userType): float
    {
        $commissionRate = (float) get_perfect_pharma_setting("{$userType}_commission_rate", 0);
        return ($amount * $commissionRate) / 100;
    }
}

if (! function_exists('perfect_pharma_calculate_discounted_price')) {
    /**
     * Calculate discounted price for user type
     *
     * @param float $originalPrice
     * @param string $userType
     * @return float
     */
    function perfect_pharma_calculate_discounted_price(float $originalPrice, string $userType): float
    {
        return \Botble\PerfectPharma\Supports\PerfectPharmaHelper::calculateDiscountedPrice($originalPrice, $userType);
    }
}

if (! function_exists('perfect_pharma_get_system_info')) {
    /**
     * Get system information
     *
     * @return array
     */
    function perfect_pharma_get_system_info(): array
    {
        return [
            'name' => get_perfect_pharma_setting('system_name', 'Perfect Pharma'),
            'description' => get_perfect_pharma_setting('system_description', 'نظام طبي متكامل'),
            'email' => get_perfect_pharma_setting('system_email', '<EMAIL>'),
            'phone' => get_perfect_pharma_setting('system_phone', '+966501234567'),
            'address' => get_perfect_pharma_setting('system_address', 'الرياض، المملكة العربية السعودية'),
        ];
    }
}

if (! function_exists('perfect_pharma_can_book_appointment')) {
    /**
     * Check if user can book appointment
     *
     * @param int $doctorId
     * @param string $date
     * @param string $time
     * @return bool
     */
    function perfect_pharma_can_book_appointment(int $doctorId, string $date, string $time): bool
    {
        $advanceDays = (int) get_perfect_pharma_setting('appointment_advance_booking_days', 30);
        $appointmentDate = \Carbon\Carbon::parse($date);
        $maxDate = now()->addDays($advanceDays);
        
        if ($appointmentDate->gt($maxDate)) {
            return false;
        }
        
        // التحقق من عدم وجود موعد آخر في نفس الوقت
        return !\Botble\PerfectPharma\Models\Appointment::where('doctor_id', $doctorId)
            ->where('appointment_date', $date)
            ->where('appointment_time', $time)
            ->where('status', '!=', 'cancelled')
            ->exists();
    }
}

if (! function_exists('perfect_pharma_can_cancel_appointment')) {
    /**
     * Check if appointment can be cancelled
     *
     * @param string $appointmentDate
     * @param string $appointmentTime
     * @return bool
     */
    function perfect_pharma_can_cancel_appointment(string $appointmentDate, string $appointmentTime): bool
    {
        $cancellationHours = (int) get_perfect_pharma_setting('appointment_cancellation_hours', 24);
        $appointmentDateTime = \Carbon\Carbon::parse($appointmentDate . ' ' . $appointmentTime);
        
        return $appointmentDateTime->diffInHours(now()) >= $cancellationHours;
    }
}

if (! function_exists('perfect_pharma_wallet_limits')) {
    /**
     * Get wallet charge limits
     *
     * @return array
     */
    function perfect_pharma_wallet_limits(): array
    {
        return [
            'min' => (float) get_perfect_pharma_setting('wallet_min_charge', 10),
            'max' => (float) get_perfect_pharma_setting('wallet_max_charge', 10000),
        ];
    }
}

if (! function_exists('perfect_pharma_donation_limits')) {
    /**
     * Get donation limits
     *
     * @return array
     */
    function perfect_pharma_donation_limits(): array
    {
        return [
            'min' => (float) get_perfect_pharma_setting('donations_min_amount', 10),
            'max' => (float) get_perfect_pharma_setting('donations_max_amount', 50000),
        ];
    }
}

if (! function_exists('perfect_pharma_get_currency')) {
    /**
     * Get Perfect Pharma currency
     *
     * @return \Botble\Ecommerce\Models\Currency|null
     */
    function perfect_pharma_get_currency(): ?\Botble\Ecommerce\Models\Currency
    {
        return \Botble\PerfectPharma\Supports\CurrencyHelper::getPerfectPharmaCurrency();
    }
}

if (! function_exists('perfect_pharma_currency_symbol')) {
    /**
     * Get Perfect Pharma currency symbol
     *
     * @return string
     */
    function perfect_pharma_currency_symbol(): string
    {
        return \Botble\PerfectPharma\Supports\CurrencyHelper::getCurrencySymbol();
    }
}

if (! function_exists('perfect_pharma_currency_code')) {
    /**
     * Get Perfect Pharma currency code
     *
     * @return string
     */
    function perfect_pharma_currency_code(): string
    {
        return \Botble\PerfectPharma\Supports\CurrencyHelper::getCurrencyCode();
    }
}

if (! function_exists('perfect_pharma_convert_to_sar')) {
    /**
     * Convert price to SAR
     *
     * @param float $price
     * @return float
     */
    function perfect_pharma_convert_to_sar(float $price): float
    {
        return \Botble\PerfectPharma\Supports\CurrencyHelper::convertToSAR($price);
    }
}

if (! function_exists('perfect_pharma_convert_from_sar')) {
    /**
     * Convert price from SAR
     *
     * @param float $price
     * @return float
     */
    function perfect_pharma_convert_from_sar(float $price): float
    {
        return \Botble\PerfectPharma\Supports\CurrencyHelper::convertFromSAR($price);
    }
}

if (! function_exists('perfect_pharma_price_with_discount')) {
    /**
     * Calculate price with Perfect Pharma discount and format with currency
     *
     * @param float $originalPrice
     * @param string $userType
     * @param bool $showSymbol
     * @return string
     */
    function perfect_pharma_price_with_discount(float $originalPrice, string $userType, bool $showSymbol = true): string
    {
        $discountedPrice = perfect_pharma_calculate_discounted_price($originalPrice, $userType);
        return perfect_pharma_format_currency($discountedPrice, $showSymbol);
    }
}

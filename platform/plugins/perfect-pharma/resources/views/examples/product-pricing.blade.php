@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-tag me-2"></i>
                        مثال على استخدام Perfect Pharma Price Component
                    </h4>
                    <p class="card-subtitle mb-0">
                        كيفية عرض الأسعار مع خصومات Perfect Pharma
                    </p>
                </div>
                <div class="card-body">
                    
                    @if($products->count() > 0)
                        <div class="row">
                            @foreach($products as $product)
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card product-card">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ $product->name }}</h5>
                                        <p class="card-text text-muted">{{ Str::limit($product->description, 100) }}</p>
                                        
                                        {{-- استخدام Component للمستخدم الحالي --}}
                                        <div class="mb-3">
                                            <h6>سعر المستخدم الحالي:</h6>
                                            <x-perfect-pharma-price-display 
                                                :product="$product" 
                                                :show-discount="true" 
                                                size="normal" 
                                            />
                                        </div>
                                        
                                        {{-- استخدام Component لعرض جميع الأسعار (للإدارة) --}}
                                        <div class="mb-3">
                                            <h6>جميع أسعار أنواع المستخدمين:</h6>
                                            <x-perfect-pharma-price-display 
                                                :product="$product" 
                                                :show-all-user-types="true" 
                                                :show-discount="true" 
                                                size="small" 
                                            />
                                        </div>
                                        
                                        {{-- معلومات إضافية --}}
                                        <div class="mt-3">
                                            <small class="text-muted">
                                                <i class="ti ti-info-circle me-1"></i>
                                                ID: {{ $product->id }} | 
                                                السعر الأصلي: {{ number_format($product->price, 2) }} {{ perfect_pharma_currency_symbol() }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        
                        {{-- مثال على الاستخدام في الكود --}}
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">كيفية الاستخدام في الكود</h5>
                                    </div>
                                    <div class="card-body">
                                        <h6>1. استخدام Component في Blade:</h6>
                                        <pre class="bg-light p-3 rounded"><code>&lt;!-- عرض سعر المستخدم الحالي --&gt;
&lt;x-perfect-pharma-price-display 
    :product="$product" 
    :show-discount="true" 
    size="normal" 
/&gt;

&lt;!-- عرض جميع أسعار أنواع المستخدمين (للإدارة) --&gt;
&lt;x-perfect-pharma-price-display 
    :product="$product" 
    :show-all-user-types="true" 
    :show-discount="true" 
    size="small" 
/&gt;</code></pre>
                                        
                                        <h6 class="mt-4">2. استخدام Helper Functions:</h6>
                                        <pre class="bg-light p-3 rounded"><code>// الحصول على سعر مع خصم
$discountedPrice = perfect_pharma_calculate_discounted_price(100, 'doctor');

// تنسيق العملة
$formattedPrice = perfect_pharma_format_currency(100);

// الحصول على نسبة خصم نوع مستخدم
$discountRate = perfect_pharma_user_type_discount('pharmacy');

// حساب السعر مع الخصم وتنسيقه
$priceWithDiscount = perfect_pharma_price_with_discount(100, 'hospital');</code></pre>
                                        
                                        <h6 class="mt-4">3. استخدام EcommercePriceIntegration:</h6>
                                        <pre class="bg-light p-3 rounded"><code>use Botble\PerfectPharma\Supports\EcommercePriceIntegration;

// الحصول على معلومات التسعير الكاملة
$priceInfo = EcommercePriceIntegration::getProductPriceWithDiscount($product);

// الحصول على أسعار جميع أنواع المستخدمين
$allPrices = EcommercePriceIntegration::getAllUserTypePrices($product);

// حساب إجمالي السلة مع الخصومات
$cartTotal = EcommercePriceIntegration::calculateCartTotal($cartItems);</code></pre>
                                        
                                        <h6 class="mt-4">4. استخدام CurrencyHelper:</h6>
                                        <pre class="bg-light p-3 rounded"><code>use Botble\PerfectPharma\Supports\CurrencyHelper;

// تنسيق السعر
$formatted = CurrencyHelper::formatPrice(100);

// تحويل إلى الريال
$sarPrice = CurrencyHelper::convertToSAR(100);

// الحصول على رمز العملة
$symbol = CurrencyHelper::getCurrencySymbol();</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    @else
                        <div class="alert alert-warning">
                            <i class="ti ti-alert-triangle me-2"></i>
                            لا توجد منتجات للعرض. يرجى إضافة بعض المنتجات في قسم Ecommerce أولاً.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('header')
    <style>
        .product-card {
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .product-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }
        
        .card-title {
            color: #495057;
            font-weight: 600;
        }
        
        pre {
            font-size: 0.85rem;
            line-height: 1.4;
        }
        
        code {
            color: #495057;
        }
        
        h6 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 10px;
        }
    </style>
@endpush

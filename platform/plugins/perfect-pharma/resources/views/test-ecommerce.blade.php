@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-shopping-cart me-2"></i>
                        اختبار تكامل Perfect Pharma مع Ecommerce
                    </h4>
                    <p class="card-subtitle mb-0">
                        اختبار نظام التسعير والعملات والخصومات
                    </p>
                </div>
                <div class="card-body">
                    
                    {{-- أزرار الاختبار --}}
                    <div class="mb-4">
                        <button onclick="testCart()" class="btn btn-primary me-2">
                            <i class="ti ti-shopping-cart me-1"></i>
                            اختبار سلة التسوق
                        </button>
                        <button onclick="testCurrency()" class="btn btn-info me-2">
                            <i class="ti ti-currency-dollar me-1"></i>
                            اختبار العملة
                        </button>
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="ti ti-users me-1"></i>
                                اختبار أنواع المستخدمين
                            </button>
                            <ul class="dropdown-menu">
                                @foreach(perfect_pharma_user_types() as $slug => $info)
                                <li><a class="dropdown-item" href="#" onclick="testUserType('{{ $slug }}')">{{ $info['name'] }}</a></li>
                                @endforeach
                            </ul>
                        </div>
                        <a href="{{ route('perfect-pharma.test-settings') }}" class="btn btn-secondary">
                            <i class="ti ti-settings me-1"></i>
                            اختبار الإعدادات
                        </a>
                    </div>
                    
                    {{-- الإحصائيات --}}
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-1">{{ $statistics['total_products_tested'] }}</h3>
                                    <p class="mb-0">منتجات تم اختبارها</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-1">{{ $statistics['products_with_discounts'] }}</h3>
                                    <p class="mb-0">منتجات بها خصومات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-1">{{ $statistics['total_user_types'] }}</h3>
                                    <p class="mb-0">أنواع المستخدمين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-1">{{ $currencyTests['currency_code'] }}</h3>
                                    <p class="mb-0">العملة الحالية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {{-- معلومات المستخدم الحالي --}}
                    <div class="alert alert-info">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>المستخدم الحالي:</strong> {{ perfect_pharma_user_types()[$statistics['current_user_type']]['name'] ?? $statistics['current_user_type'] }}
                        | <strong>يمكن رؤية الأسعار:</strong> {{ $statistics['can_see_prices'] ? 'نعم' : 'لا' }}
                        | <strong>العملة:</strong> {{ $currencyTests['currency_code'] }} ({{ $currencyTests['currency_symbol'] }})
                    </div>
                    
                    {{-- اختبار العملة --}}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">اختبار العملة</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        @foreach($currencyTests as $key => $value)
                                        <tr>
                                            <td><strong>{{ ucfirst(str_replace('_', ' ', $key)) }}</strong></td>
                                            <td>
                                                @if(is_bool($value))
                                                    <span class="badge bg-{{ $value ? 'success' : 'danger' }}">
                                                        {{ $value ? 'نعم' : 'لا' }}
                                                    </span>
                                                @elseif(is_object($value))
                                                    {{ $value->title ?? 'N/A' }} ({{ $value->symbol ?? 'N/A' }})
                                                @else
                                                    {{ $value }}
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        {{-- اختبار سلة التسوق --}}
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">اختبار سلة التسوق</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>المجموع الفرعي:</strong>
                                        <span class="badge bg-primary">{{ $cartTotal['formatted_subtotal'] }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>إجمالي الخصم:</strong>
                                        <span class="badge bg-success">{{ $cartTotal['formatted_total_discount'] }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>عدد العناصر:</strong>
                                        <span class="badge bg-info">{{ count($cartTotal['items']) }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>نوع المستخدم:</strong>
                                        <span class="badge bg-warning">{{ perfect_pharma_user_types()[$cartTotal['user_type']]['name'] ?? $cartTotal['user_type'] }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {{-- المنتجات المختبرة --}}
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">المنتجات المختبرة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>السعر الأصلي</th>
                                                    <th>سعر المستخدم الحالي</th>
                                                    <th>نسبة الخصم</th>
                                                    <th>يوجد خصومات</th>
                                                    <th>عدد التنويعات</th>
                                                    <th>إجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($testResults as $result)
                                                <tr>
                                                    <td>
                                                        <strong>{{ $result['product']->name }}</strong><br>
                                                        <small class="text-muted">ID: {{ $result['product']->id }}</small>
                                                    </td>
                                                    <td>{{ $result['current_user_price']['formatted_original'] }}</td>
                                                    <td>
                                                        <span class="badge bg-success">{{ $result['current_user_price']['formatted_discounted'] }}</span>
                                                    </td>
                                                    <td>
                                                        @if($result['current_user_price']['discount_rate'] > 0)
                                                            <span class="badge bg-primary">{{ $result['current_user_price']['discount_rate'] }}%</span>
                                                        @else
                                                            <span class="badge bg-secondary">0%</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{{ $result['has_discounts'] ? 'success' : 'danger' }}">
                                                            {{ $result['has_discounts'] ? 'نعم' : 'لا' }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $result['variations_count'] }}</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-primary" onclick="testProduct({{ $result['product']->id }})">
                                                            <i class="ti ti-eye"></i>
                                                            تفاصيل
                                                        </button>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {{-- نتائج الاختبارات --}}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">نتائج الاختبارات</h5>
                                </div>
                                <div class="card-body">
                                    <div id="testResults" class="d-none">
                                        <pre id="testResultsContent"></pre>
                                    </div>
                                    <div id="testLoading" class="d-none text-center">
                                        <i class="ti ti-loader-2 me-2"></i>
                                        جاري تشغيل الاختبار...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function showLoading() {
            document.getElementById('testLoading').classList.remove('d-none');
            document.getElementById('testResults').classList.add('d-none');
        }
        
        function showResults(data) {
            document.getElementById('testLoading').classList.add('d-none');
            document.getElementById('testResults').classList.remove('d-none');
            document.getElementById('testResultsContent').textContent = JSON.stringify(data, null, 2);
        }
        
        function testProduct(productId) {
            showLoading();
            fetch(`{{ route('perfect-pharma.test-ecommerce.product', '') }}/${productId}`)
                .then(response => response.json())
                .then(data => showResults(data))
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الاختبار');
                    document.getElementById('testLoading').classList.add('d-none');
                });
        }
        
        function testCart() {
            showLoading();
            fetch('{{ route("perfect-pharma.test-ecommerce.cart") }}')
                .then(response => response.json())
                .then(data => showResults(data))
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في اختبار السلة');
                    document.getElementById('testLoading').classList.add('d-none');
                });
        }
        
        function testUserType(userType) {
            showLoading();
            fetch(`{{ route('perfect-pharma.test-ecommerce.user-type', '') }}/${userType}`)
                .then(response => response.json())
                .then(data => showResults(data))
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في اختبار نوع المستخدم');
                    document.getElementById('testLoading').classList.add('d-none');
                });
        }
        
        function testCurrency() {
            showLoading();
            fetch('{{ route("perfect-pharma.test-ecommerce.currency") }}')
                .then(response => response.json())
                .then(data => showResults(data))
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في اختبار العملة');
                    document.getElementById('testLoading').classList.add('d-none');
                });
        }
    </script>
@endpush

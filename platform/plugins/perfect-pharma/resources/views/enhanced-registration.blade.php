@extends(Theme::getThemeNamespace('layouts.base'))

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-user-plus"></i>
                        إنشاء حساب جديد
                    </h2>
                    <p class="mb-0 mt-2">انضم إلى Perfect Pharma واستفد من خدماتنا المتميزة</p>
                </div>
                
                <div class="card-body p-5">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('customer.register.enhanced.post') }}" id="registrationForm">
                        @csrf
                        
                        <!-- اختيار نوع المستخدم -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-users"></i>
                                نوع الحساب <span class="text-danger">*</span>
                            </label>
                            <div class="row g-3">
                                @foreach($userTypes as $userType)
                                <div class="col-md-6">
                                    <div class="form-check user-type-option">
                                        <input class="form-check-input" type="radio" name="user_type" 
                                               id="user_type_{{ $userType->name }}" value="{{ $userType->name }}"
                                               {{ old('user_type', $selectedUserType) == $userType->name ? 'checked' : '' }}>
                                        <label class="form-check-label w-100" for="user_type_{{ $userType->name }}">
                                            <div class="d-flex align-items-center">
                                                @switch($userType->name)
                                                    @case('patient')
                                                        <i class="fas fa-user-injured text-primary me-2"></i>
                                                        @break
                                                    @case('doctor')
                                                        <i class="fas fa-user-md text-success me-2"></i>
                                                        @break
                                                    @case('pharmacy')
                                                        <i class="fas fa-pills text-info me-2"></i>
                                                        @break
                                                    @case('lab')
                                                        <i class="fas fa-flask text-warning me-2"></i>
                                                        @break
                                                    @case('hospital')
                                                        <i class="fas fa-hospital text-danger me-2"></i>
                                                        @break
                                                    @case('clinic')
                                                        <i class="fas fa-clinic-medical text-secondary me-2"></i>
                                                        @break
                                                @endswitch
                                                <span>{{ $userType->display_name }}</span>
                                                @if($userType->name !== 'patient')
                                                    <small class="badge bg-warning ms-auto">يحتاج تحقق</small>
                                                @endif
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- معلومات نوع المستخدم -->
                        <div id="userTypeInfo" class="mb-4" style="display: none;">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>

                        <!-- الاسم الكامل -->
                        <div class="mb-3">
                            <label for="name" class="form-label fw-bold">
                                <i class="fas fa-user"></i>
                                الاسم الكامل <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control form-control-lg" id="name" name="name" 
                                   value="{{ old('name') }}" placeholder="أدخل اسمك الكامل" required>
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div class="mb-3">
                            <label for="email" class="form-label fw-bold">
                                <i class="fas fa-envelope"></i>
                                البريد الإلكتروني <span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control form-control-lg" id="email" name="email" 
                                   value="{{ old('email') }}" placeholder="أدخل بريدك الإلكتروني" required>
                        </div>

                        <!-- رقم الهاتف -->
                        <div class="mb-3">
                            <label for="phone" class="form-label fw-bold">
                                <i class="fas fa-phone"></i>
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control form-control-lg" id="phone" name="phone" 
                                   value="{{ old('phone') }}" placeholder="أدخل رقم هاتفك">
                            <div class="form-text">سيتم استخدامه للتواصل معك</div>
                        </div>

                        <!-- كلمة المرور -->
                        <div class="mb-3">
                            <label for="password" class="form-label fw-bold">
                                <i class="fas fa-lock"></i>
                                كلمة المرور <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control form-control-lg" id="password" name="password" 
                                       placeholder="أدخل كلمة مرور قوية" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">يجب أن تكون على الأقل 6 أحرف</div>
                        </div>

                        <!-- تأكيد كلمة المرور -->
                        <div class="mb-4">
                            <label for="password_confirmation" class="form-label fw-bold">
                                <i class="fas fa-lock"></i>
                                تأكيد كلمة المرور <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control form-control-lg" id="password_confirmation" 
                                   name="password_confirmation" placeholder="أعد إدخال كلمة المرور" required>
                        </div>

                        <!-- الموافقة على الشروط -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agree_terms_and_policy" 
                                       name="agree_terms_and_policy" value="1" required>
                                <label class="form-check-label" for="agree_terms_and_policy">
                                    أوافق على 
                                    <a href="#" target="_blank" class="text-primary">الشروط والأحكام</a> 
                                    و 
                                    <a href="#" target="_blank" class="text-primary">سياسة الخصوصية</a>
                                    <span class="text-danger">*</span>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus"></i>
                                إنشاء الحساب
                            </button>
                            <a href="{{ route('customer.register.choice') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i>
                                العودة لاختيار نوع الحساب
                            </a>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <p class="text-muted">
                            لديك حساب بالفعل؟ 
                            <a href="{{ route('customer.login') }}" class="text-primary text-decoration-none">
                                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.user-type-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.user-type-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.user-type-option input[type="radio"]:checked + label {
    color: #007bff;
    font-weight: bold;
}

.user-type-option input[type="radio"]:checked {
    border-color: #007bff;
}

.form-control-lg {
    padding: 0.75rem 1rem;
}

.card {
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.input-group .btn {
    border-color: #ced4da;
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .card-body {
        padding: 2rem !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معلومات أنواع المستخدمين
    const userTypeInfo = {
        'patient': {
            title: 'مريض',
            icon: 'fas fa-user-injured text-primary',
            description: 'حساب للمرضى للحصول على الخدمات الطبية والأدوية. لا يحتاج إلى وثائق تحقق.',
            features: [
                'شراء الأدوية والمنتجات الطبية',
                'حجز المواعيد مع الأطباء',
                'طلب التحاليل الطبية',
                'الوصول للأكاديمية التعليمية'
            ],
            verification: false
        },
        'doctor': {
            title: 'طبيب',
            icon: 'fas fa-user-md text-success',
            description: 'حساب للأطباء لتقديم الخدمات الطبية. يحتاج إلى تحقق من الوثائق.',
            features: [
                'إدارة المواعيد والمرضى',
                'كتابة الوصفات الطبية',
                'خصومات خاصة على المنتجات الطبية',
                'الوصول لأدوات الأطباء المتقدمة'
            ],
            verification: true,
            documents: 'رخصة مزاولة المهنة + الهوية الشخصية'
        },
        'pharmacy': {
            title: 'صيدلية',
            icon: 'fas fa-pills text-info',
            description: 'حساب للصيدليات لبيع الأدوية والمنتجات الطبية. يحتاج إلى تحقق من الوثائق.',
            features: [
                'بيع الأدوية والمنتجات الطبية',
                'صرف الوصفات الطبية',
                'خصومات تجارية خاصة',
                'إدارة المخزون والمبيعات'
            ],
            verification: true,
            documents: 'ترخيص الصيدلية + هوية المالك'
        },
        'lab': {
            title: 'معمل تحاليل',
            icon: 'fas fa-flask text-warning',
            description: 'حساب للمعامل لتقديم خدمات التحاليل الطبية. يحتاج إلى تحقق من الوثائق.',
            features: [
                'تقديم خدمات التحاليل الطبية',
                'إدارة طلبات التحاليل',
                'خصومات تجارية خاصة',
                'تقارير التحاليل الإلكترونية'
            ],
            verification: true,
            documents: 'ترخيص المعمل + هوية المالك'
        },
        'hospital': {
            title: 'مستشفى',
            icon: 'fas fa-hospital text-danger',
            description: 'حساب للمستشفيات لتقديم الخدمات الطبية الشاملة. يحتاج إلى تحقق من الوثائق.',
            features: [
                'إدارة الأقسام والخدمات الطبية',
                'حجز الأسرة والعمليات',
                'خصومات مؤسسية خاصة',
                'نظام إدارة المرضى المتقدم'
            ],
            verification: true,
            documents: 'ترخيص المستشفى + هوية المدير'
        },
        'clinic': {
            title: 'عيادة',
            icon: 'fas fa-clinic-medical text-secondary',
            description: 'حساب للعيادات الطبية المتخصصة. يحتاج إلى تحقق من الوثائق.',
            features: [
                'إدارة المواعيد والمرضى',
                'تقديم الخدمات الطبية المتخصصة',
                'خصومات تجارية خاصة',
                'نظام إدارة العيادة'
            ],
            verification: true,
            documents: 'ترخيص العيادة + هوية الطبيب'
        }
    };

    // عرض معلومات نوع المستخدم
    function showUserTypeInfo(userType) {
        const info = userTypeInfo[userType];
        if (!info) return;

        const infoContainer = document.getElementById('userTypeInfo');
        const alertClass = info.verification ? 'alert-warning' : 'alert-info';
        
        let html = `
            <div class="alert ${alertClass}">
                <h6><i class="${info.icon}"></i> ${info.title}</h6>
                <p>${info.description}</p>
                <ul class="mb-2">
                    ${info.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
        `;
        
        if (info.verification) {
            html += `<p class="mb-0"><small><strong>المستندات المطلوبة:</strong> ${info.documents}</small></p>`;
        }
        
        html += '</div>';
        
        infoContainer.innerHTML = html;
        infoContainer.style.display = 'block';
    }

    // معالجة تغيير نوع المستخدم
    const userTypeRadios = document.querySelectorAll('input[name="user_type"]');
    userTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                showUserTypeInfo(this.value);
            }
        });
        
        // إظهار المعلومات للخيار المحدد مسبقاً
        if (radio.checked) {
            showUserTypeInfo(radio.value);
        }
    });

    // إظهار/إخفاء كلمة المرور
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });

    // التحقق من تطابق كلمات المرور
    const passwordConfirmation = document.getElementById('password_confirmation');
    
    function checkPasswordMatch() {
        if (passwordField.value !== passwordConfirmation.value) {
            passwordConfirmation.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            passwordConfirmation.setCustomValidity('');
        }
    }
    
    passwordField.addEventListener('input', checkPasswordMatch);
    passwordConfirmation.addEventListener('input', checkPasswordMatch);
});
</script>
@endsection

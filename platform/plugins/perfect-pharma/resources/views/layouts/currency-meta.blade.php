{{-- 
    Currency Meta Tags
    يحمل إعدادات العملة من النظام ويجعلها متاحة للـ JavaScript
--}}

@php
    $systemCurrency = get_application_currency();
    $currencyData = null;
    
    if ($systemCurrency) {
        $currencyData = [
            'id' => $systemCurrency->id,
            'title' => $systemCurrency->title,
            'symbol' => $systemCurrency->symbol,
            'is_prefix_symbol' => $systemCurrency->is_prefix_symbol,
            'decimals' => $systemCurrency->decimals,
            'is_default' => $systemCurrency->is_default,
            'exchange_rate' => $systemCurrency->exchange_rate,
            'locale' => match($systemCurrency->title) {
                'SAR' => 'ar-SA',
                'USD' => 'en-US',
                'EUR' => 'en-EU',
                'GBP' => 'en-GB',
                'EGP' => 'ar-EG',
                'AED' => 'ar-AE',
                'KWD' => 'ar-KW',
                'QAR' => 'ar-QA',
                'BHD' => 'ar-BH',
                'OMR' => 'ar-OM',
                'JOD' => 'ar-JO',
                'LBP' => 'ar-LB',
                'SYP' => 'ar-SY',
                'IQD' => 'ar-IQ',
                'MAD' => 'ar-MA',
                'TND' => 'ar-TN',
                'DZD' => 'ar-DZ',
                'LYD' => 'ar-LY',
                'SDG' => 'ar-SD',
                'YER' => 'ar-YE',
                default => 'ar-SA'
            }
        ];
    }
@endphp

@if($currencyData)
    {{-- Meta tag لإعدادات العملة --}}
    <meta name="system-currency" content="{{ json_encode($currencyData) }}">
    
    {{-- تحميل إعدادات العملة في JavaScript --}}
    <script>
        window.systemCurrency = @json($currencyData);
    </script>
@else
    {{-- Fallback للريال السعودي --}}
    <script>
        window.systemCurrency = {
            title: 'SAR',
            symbol: 'ر.س',
            is_prefix_symbol: false,
            decimals: 2,
            locale: 'ar-SA',
            is_default: true,
            exchange_rate: 1
        };
    </script>
@endif

{{-- تحميل مساعد العملة --}}
<script src="{{ asset('vendor/core/plugins/perfect-pharma/js/currency-helper.js') }}?v={{ time() }}"></script>

@extends('plugins/perfect-pharma::pharmacy.layout')

@section('title', 'لوحة تحكم الصيدلية')
@section('page-title', 'لوحة التحكم')

@section('content')
<div class="row">
    <!-- Welcome Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">مرحباً، صيدلية {{ $pharmacy->name }}</h3>
                        <p class="text-muted mb-0">{{ $pharmacy->address }}</p>
                        <small class="text-muted">آخر تسجيل دخول: {{ auth('customer')->user()->last_login_at?->diffForHumans() ?? 'الآن' }}</small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('pharmacy.prescriptions.search-patient') }}" class="btn btn-info">
                                <i class="fas fa-search me-1"></i>
                                البحث عن مريض
                            </a>
                            <a href="{{ route('pharmacy.pos.index') }}" class="btn pos-button">
                                <i class="fas fa-cash-register me-1"></i>
                                نقطة البيع
                            </a>
                            <a href="{{ route('pharmacy.orders.index') }}" class="btn btn-outline-primary">
                                <i class="fas fa-shopping-cart me-1"></i>
                                الطلبات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-shopping-cart fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_orders'] }}</h3>
                <p class="mb-0">إجمالي الطلبات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['today_orders'] }}</h3>
                <p class="mb-0">طلبات اليوم</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['pending_orders'] }}</h3>
                <p class="mb-0">طلبات معلقة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['low_stock_count'] }}</h3>
                <p class="mb-0">منتجات منخفضة</p>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Cards -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h4 class="text-success">{{ number_format($stats['monthly_revenue']) }} ر.س</h4>
                <p class="text-muted mb-0">إيرادات الشهر</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h4 class="text-info">{{ $stats['total_products'] }}</h4>
                <p class="text-muted mb-0">إجمالي المنتجات</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Orders -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    آخر الطلبات
                </h5>
                <a href="{{ route('pharmacy.orders.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($recentOrders->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentOrders as $order)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">طلب #{{ $order->code }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ $order->user->name ?? 'عميل مباشر' }}
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $order->created_at->diffForHumans() }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ $order->status == 'completed' ? 'success' : ($order->status == 'pending' ? 'warning' : 'info') }}">
                                        {{ $order->status == 'completed' ? 'مكتمل' : ($order->status == 'pending' ? 'معلق' : 'قيد التجهيز') }}
                                    </span>
                                    <br>
                                    <strong class="text-success">{{ number_format($order->amount) }} ر.س</strong>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد طلبات حديثة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Pending Prescriptions -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-prescription-bottle-alt me-2"></i>
                    الوصفات المعلقة
                </h5>
                <a href="{{ route('pharmacy.prescriptions.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($pendingPrescriptions->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($pendingPrescriptions as $prescription)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $prescription->prescription_code }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ $prescription->patient->user->name }}
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-user-md me-1"></i>
                                        د. {{ $prescription->doctor->user->name }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-warning">معلقة</span>
                                    <br>
                                    <small class="text-muted">{{ $prescription->created_at->diffForHumans() }}</small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-prescription-bottle fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد وصفات معلقة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Low Stock Products -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    منتجات منخفضة المخزون
                </h5>
                <a href="{{ route('pharmacy.inventory.index', ['stock_status' => 'low']) }}" class="btn btn-sm btn-outline-danger">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($lowStockProducts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية المتبقية</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($lowStockProducts as $product)
                                    <tr>
                                        <td>
                                            <strong>{{ $product->name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $product->sku }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $product->quantity <= 0 ? 'danger' : 'warning' }}">
                                                {{ $product->quantity }}
                                            </span>
                                        </td>
                                        <td>{{ number_format($product->price) }} ر.س</td>
                                        <td>
                                            @if($product->quantity <= 0)
                                                <span class="badge bg-danger">نفد المخزون</span>
                                            @else
                                                <span class="badge bg-warning">منخفض</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">جميع المنتجات متوفرة بكميات كافية</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('pharmacy.pos.index') }}" class="btn btn-warning">
                        <i class="fas fa-cash-register me-2"></i>
                        فتح نقطة البيع
                    </a>
                    
                    <a href="{{ route('pharmacy.inventory.index') }}" class="btn btn-info">
                        <i class="fas fa-boxes me-2"></i>
                        إدارة المخزون
                    </a>
                    
                    <a href="{{ route('pharmacy.orders.index', ['status' => 'pending']) }}" class="btn btn-primary">
                        <i class="fas fa-clock me-2"></i>
                        الطلبات المعلقة
                    </a>
                    
                    <a href="{{ route('pharmacy.reports.index') }}" class="btn btn-success">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h6 class="text-muted">إحصائيات اليوم</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <strong class="text-success">{{ $stats['completed_orders_today'] }}</strong>
                            <br>
                            <small class="text-muted">طلبات مكتملة</small>
                        </div>
                        <div class="col-6">
                            <strong class="text-info">{{ $stats['dispensed_prescriptions_today'] }}</strong>
                            <br>
                            <small class="text-muted">وصفات مصروفة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // تحديث الإحصائيات كل دقيقة
    setInterval(function() {
        // يمكن إضافة AJAX call لتحديث الإحصائيات
    }, 60000);
});
</script>
@endpush

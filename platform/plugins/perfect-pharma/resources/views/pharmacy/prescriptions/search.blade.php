@extends('plugins/perfect-pharma::pharmacy.layout')

@section('title', 'البحث عن وصفات المرضى')
@section('page-title', 'البحث عن الوصفات')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    البحث عن وصفات المرضى
                </h5>
            </div>
            <div class="card-body">
                <!-- نموذج البحث -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   id="searchInput" 
                                   placeholder="أدخل رقم الهاتف أو رقم البطاقة الشخصية">
                            <button class="btn btn-primary" type="button" id="searchBtn">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                        </div>
                        <small class="text-muted">
                            يمكنك البحث برقم الهاتف أو رقم البطاقة الشخصية للمريض
                        </small>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" id="clearBtn">
                                <i class="fas fa-times me-1"></i>
                                مسح النتائج
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- منطقة النتائج -->
                <div id="searchResults" style="display: none;">
                    <!-- معلومات المريض -->
                    <div id="patientInfo" class="alert alert-info">
                        <h6 class="mb-2">
                            <i class="fas fa-user me-2"></i>
                            معلومات المريض
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>الاسم:</strong> <span id="patientName"></span>
                            </div>
                            <div class="col-md-4">
                                <strong>الهاتف:</strong> <span id="patientPhone"></span>
                            </div>
                            <div class="col-md-4">
                                <strong>رقم البطاقة:</strong> <span id="patientNationalId"></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الوصفات غير المصروفة -->
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-prescription-bottle-alt me-2"></i>
                                الوصفات غير المصروفة
                                <span class="badge bg-dark ms-2" id="prescriptionsCount">0</span>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="prescriptionsList">
                                <!-- سيتم ملء الوصفات هنا -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- رسالة عدم وجود نتائج -->
                <div id="noResults" class="text-center py-5" style="display: none;">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لم يتم العثور على نتائج</h5>
                    <p class="text-muted">تأكد من صحة رقم الهاتف أو رقم البطاقة</p>
                </div>
                
                <!-- رسالة التحميل -->
                <div id="loadingMessage" class="text-center py-5" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري البحث...</span>
                    </div>
                    <p class="mt-3 text-muted">جاري البحث...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتأكيد صرف الوصفة -->
<div class="modal fade" id="dispenseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد صرف الوصفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد تأكيد صرف هذه الوصفة؟</p>
                <div id="prescriptionDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmDispenseBtn">
                    <i class="fas fa-check me-1"></i>
                    تأكيد الصرف
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let currentPrescriptionId = null;
    
    // البحث عند الضغط على زر البحث
    $('#searchBtn').click(function() {
        performSearch();
    });
    
    // البحث عند الضغط على Enter
    $('#searchInput').keypress(function(e) {
        if (e.which == 13) {
            performSearch();
        }
    });
    
    // مسح النتائج
    $('#clearBtn').click(function() {
        clearResults();
    });
    
    // تأكيد صرف الوصفة
    $('#confirmDispenseBtn').click(function() {
        if (currentPrescriptionId) {
            dispensePrescription(currentPrescriptionId);
        }
    });
    
    function performSearch() {
        const searchTerm = $('#searchInput').val().trim();
        
        if (!searchTerm) {
            alert('يرجى إدخال رقم الهاتف أو رقم البطاقة');
            return;
        }
        
        // إظهار رسالة التحميل
        $('#loadingMessage').show();
        $('#searchResults').hide();
        $('#noResults').hide();
        
        // إجراء البحث
        $.get('{{ route("pharmacy.prescriptions.search-patient-ajax") }}', {
            search: searchTerm
        })
        .done(function(response) {
            if (response.success) {
                displayResults(response.patient, response.prescriptions);
            } else {
                showNoResults();
            }
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON?.error || 'حدث خطأ أثناء البحث';
            alert(error);
            showNoResults();
        })
        .always(function() {
            $('#loadingMessage').hide();
        });
    }
    
    function displayResults(patient, prescriptions) {
        // عرض معلومات المريض
        $('#patientName').text(patient.name);
        $('#patientPhone').text(patient.phone);
        $('#patientNationalId').text(patient.national_id || 'غير محدد');
        $('#prescriptionsCount').text(prescriptions.length);
        
        // عرض الوصفات
        const prescriptionsList = $('#prescriptionsList');
        prescriptionsList.empty();
        
        if (prescriptions.length === 0) {
            prescriptionsList.html(`
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h6 class="text-success">لا توجد وصفات معلقة</h6>
                    <p class="text-muted">جميع الوصفات تم صرفها</p>
                </div>
            `);
        } else {
            prescriptions.forEach(function(prescription) {
                const prescriptionCard = `
                    <div class="card mb-3 prescription-card" data-id="${prescription.id}">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1">
                                        <i class="fas fa-prescription-bottle-alt me-2"></i>
                                        ${prescription.prescription_code}
                                    </h6>
                                    <p class="mb-1">
                                        <strong>الطبيب:</strong> ${prescription.doctor_name}
                                    </p>
                                    <p class="mb-1">
                                        <strong>التاريخ:</strong> ${prescription.created_at}
                                    </p>
                                    <p class="mb-1">
                                        <strong>عدد الأدوية:</strong> ${prescription.medications_count}
                                    </p>
                                    ${prescription.notes ? `<p class="mb-0 text-muted"><small>${prescription.notes}</small></p>` : ''}
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-outline-primary btn-sm view-prescription" 
                                                data-id="${prescription.id}">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التفاصيل
                                        </button>
                                        <button type="button" class="btn btn-success btn-sm dispense-prescription" 
                                                data-id="${prescription.id}"
                                                data-code="${prescription.prescription_code}">
                                            <i class="fas fa-check me-1"></i>
                                            صرف الوصفة
                                        </button>
                                    </div>
                                    ${prescription.total_cost ? `<div class="mt-2"><strong class="text-success">${prescription.total_cost} ر.س</strong></div>` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                prescriptionsList.append(prescriptionCard);
            });
        }
        
        $('#searchResults').show();
    }
    
    function showNoResults() {
        $('#noResults').show();
        $('#searchResults').hide();
    }
    
    function clearResults() {
        $('#searchInput').val('');
        $('#searchResults').hide();
        $('#noResults').hide();
        $('#loadingMessage').hide();
    }
    
    // عرض تفاصيل الوصفة
    $(document).on('click', '.view-prescription', function() {
        const prescriptionId = $(this).data('id');
        window.open(`{{ route('pharmacy.prescriptions.show', '') }}/${prescriptionId}`, '_blank');
    });
    
    // صرف الوصفة
    $(document).on('click', '.dispense-prescription', function() {
        currentPrescriptionId = $(this).data('id');
        const prescriptionCode = $(this).data('code');
        
        $('#prescriptionDetails').html(`
            <div class="alert alert-warning">
                <strong>رقم الوصفة:</strong> ${prescriptionCode}
            </div>
        `);
        
        $('#dispenseModal').modal('show');
    });
    
    function dispensePrescription(prescriptionId) {
        $.post(`{{ route('pharmacy.prescriptions.dispense', '') }}/${prescriptionId}`, {
            _token: $('meta[name="csrf-token"]').attr('content')
        })
        .done(function(response) {
            if (response.success) {
                $('#dispenseModal').modal('hide');
                
                // إزالة الوصفة من القائمة
                $(`.prescription-card[data-id="${prescriptionId}"]`).fadeOut(function() {
                    $(this).remove();
                    
                    // تحديث العداد
                    const remainingCount = $('.prescription-card').length;
                    $('#prescriptionsCount').text(remainingCount);
                    
                    if (remainingCount === 0) {
                        $('#prescriptionsList').html(`
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h6 class="text-success">تم صرف جميع الوصفات</h6>
                            </div>
                        `);
                    }
                });
                
                alert('تم صرف الوصفة بنجاح');
            }
        })
        .fail(function() {
            alert('حدث خطأ أثناء صرف الوصفة');
        });
    }
});
</script>
@endpush

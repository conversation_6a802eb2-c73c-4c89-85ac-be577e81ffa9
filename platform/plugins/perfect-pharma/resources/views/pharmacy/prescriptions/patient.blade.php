@extends('plugins/perfect-pharma::pharmacy.layout')

@section('title', 'وصفات المريض')
@section('page-title', 'وصفات المريض')

@section('content')
<div class="row">
    <!-- معلومات المريض -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">
                            <i class="fas fa-user me-2"></i>
                            {{ $patient->name }}
                        </h4>
                        <div class="row">
                            <div class="col-md-4">
                                <p class="mb-1">
                                    <strong>الهاتف:</strong> {{ $patient->phone }}
                                </p>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-1">
                                    <strong>رقم البطاقة:</strong> {{ $patient->national_id ?? $patient->id_number ?? 'غير محدد' }}
                                </p>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-1">
                                    <strong>تاريخ التسجيل:</strong> {{ $patient->created_at->format('Y-m-d') }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ route('pharmacy.prescriptions.search-patient') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للبحث
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الوصفات غير المصروفة -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-prescription-bottle-alt me-2"></i>
                    الوصفات غير المصروفة
                </h5>
                <span class="badge bg-dark">{{ $pendingPrescriptions->count() }}</span>
            </div>
            <div class="card-body">
                @if($pendingPrescriptions->count() > 0)
                    @foreach($pendingPrescriptions as $prescription)
                        <div class="card mb-3 prescription-card" data-id="{{ $prescription->id }}">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="mb-2">
                                            <i class="fas fa-prescription-bottle-alt me-2"></i>
                                            {{ $prescription->prescription_code }}
                                        </h6>
                                        <p class="mb-1">
                                            <strong>الطبيب:</strong> 
                                            د. {{ $prescription->doctor->user->name ?? 'غير محدد' }}
                                        </p>
                                        <p class="mb-1">
                                            <strong>تاريخ الكتابة:</strong> 
                                            {{ $prescription->created_at->format('Y-m-d H:i') }}
                                        </p>
                                        <p class="mb-1">
                                            <strong>عدد الأدوية:</strong> 
                                            {{ $prescription->medications->count() }}
                                        </p>
                                        @if($prescription->notes)
                                            <p class="mb-0 text-muted">
                                                <small><strong>ملاحظات:</strong> {{ $prescription->notes }}</small>
                                            </p>
                                        @endif
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="d-grid gap-2">
                                            <a href="{{ route('pharmacy.prescriptions.show', $prescription->id) }}" 
                                               class="btn btn-outline-primary btn-sm" target="_blank">
                                                <i class="fas fa-eye me-1"></i>
                                                عرض التفاصيل
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-success btn-sm dispense-prescription" 
                                                    data-id="{{ $prescription->id }}"
                                                    data-code="{{ $prescription->prescription_code }}">
                                                <i class="fas fa-check me-1"></i>
                                                صرف الوصفة
                                            </button>
                                            <button type="button" 
                                                    class="btn btn-outline-danger btn-sm reject-prescription" 
                                                    data-id="{{ $prescription->id }}"
                                                    data-code="{{ $prescription->prescription_code }}">
                                                <i class="fas fa-times me-1"></i>
                                                رفض
                                            </button>
                                        </div>
                                        @if($prescription->total_cost)
                                            <div class="mt-2">
                                                <strong class="text-success">{{ number_format($prescription->total_cost) }} ر.س</strong>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">لا توجد وصفات معلقة</h5>
                        <p class="text-muted">جميع الوصفات تم صرفها أو لا توجد وصفات للمريض</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- تاريخ الوصفات المصروفة -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر الوصفات المصروفة
                </h6>
            </div>
            <div class="card-body">
                @if($dispensedPrescriptions->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($dispensedPrescriptions as $prescription)
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ $prescription->prescription_code }}</h6>
                                        <p class="mb-1 text-muted small">
                                            د. {{ $prescription->doctor->user->name ?? 'غير محدد' }}
                                        </p>
                                        <small class="text-success">
                                            <i class="fas fa-check me-1"></i>
                                            تم الصرف: {{ $prescription->dispensed_at->format('Y-m-d') }}
                                        </small>
                                    </div>
                                    <a href="{{ route('pharmacy.prescriptions.show', $prescription->id) }}" 
                                       class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-prescription-bottle fa-2x text-muted mb-2"></i>
                        <p class="text-muted small">لا توجد وصفات مصروفة سابقاً</p>
                    </div>
                @endif
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات المريض
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-warning">{{ $pendingPrescriptions->count() }}</h4>
                        <small class="text-muted">معلقة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ $dispensedPrescriptions->count() }}</h4>
                        <small class="text-muted">مصروفة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتأكيد صرف الوصفة -->
<div class="modal fade" id="dispenseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد صرف الوصفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد تأكيد صرف هذه الوصفة؟</p>
                <div id="prescriptionDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmDispenseBtn">
                    <i class="fas fa-check me-1"></i>
                    تأكيد الصرف
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal لرفض الوصفة -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض الوصفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>يرجى تحديد سبب رفض الوصفة:</p>
                <div id="rejectPrescriptionDetails"></div>
                <textarea class="form-control mt-3" id="rejectReason" rows="3" 
                          placeholder="اكتب سبب الرفض..."></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmRejectBtn">
                    <i class="fas fa-times me-1"></i>
                    تأكيد الرفض
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let currentPrescriptionId = null;
    
    // صرف الوصفة
    $(document).on('click', '.dispense-prescription', function() {
        currentPrescriptionId = $(this).data('id');
        const prescriptionCode = $(this).data('code');
        
        $('#prescriptionDetails').html(`
            <div class="alert alert-warning">
                <strong>رقم الوصفة:</strong> ${prescriptionCode}
            </div>
        `);
        
        $('#dispenseModal').modal('show');
    });
    
    // رفض الوصفة
    $(document).on('click', '.reject-prescription', function() {
        currentPrescriptionId = $(this).data('id');
        const prescriptionCode = $(this).data('code');
        
        $('#rejectPrescriptionDetails').html(`
            <div class="alert alert-danger">
                <strong>رقم الوصفة:</strong> ${prescriptionCode}
            </div>
        `);
        
        $('#rejectReason').val('');
        $('#rejectModal').modal('show');
    });
    
    // تأكيد صرف الوصفة
    $('#confirmDispenseBtn').click(function() {
        if (currentPrescriptionId) {
            dispensePrescription(currentPrescriptionId);
        }
    });
    
    // تأكيد رفض الوصفة
    $('#confirmRejectBtn').click(function() {
        const reason = $('#rejectReason').val().trim();
        if (!reason) {
            alert('يرجى كتابة سبب الرفض');
            return;
        }
        
        if (currentPrescriptionId) {
            rejectPrescription(currentPrescriptionId, reason);
        }
    });
    
    function dispensePrescription(prescriptionId) {
        $.post(`{{ route('pharmacy.prescriptions.dispense', '') }}/${prescriptionId}`, {
            _token: $('meta[name="csrf-token"]').attr('content')
        })
        .done(function(response) {
            if (response.success) {
                $('#dispenseModal').modal('hide');
                
                // إزالة الوصفة من القائمة
                $(`.prescription-card[data-id="${prescriptionId}"]`).fadeOut(function() {
                    $(this).remove();
                    location.reload(); // إعادة تحميل الصفحة لتحديث الإحصائيات
                });
                
                alert('تم صرف الوصفة بنجاح');
            }
        })
        .fail(function() {
            alert('حدث خطأ أثناء صرف الوصفة');
        });
    }
    
    function rejectPrescription(prescriptionId, reason) {
        $.post(`{{ route('pharmacy.prescriptions.reject', '') }}/${prescriptionId}`, {
            _token: $('meta[name="csrf-token"]').attr('content'),
            reason: reason
        })
        .done(function(response) {
            if (response.success) {
                $('#rejectModal').modal('hide');
                
                // إزالة الوصفة من القائمة
                $(`.prescription-card[data-id="${prescriptionId}"]`).fadeOut(function() {
                    $(this).remove();
                    location.reload(); // إعادة تحميل الصفحة لتحديث الإحصائيات
                });
                
                alert('تم رفض الوصفة');
            }
        })
        .fail(function() {
            alert('حدث خطأ أثناء رفض الوصفة');
        });
    }
});
</script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-test-pipe me-2"></i>
                        اختبار إعدادات Perfect Pharma
                    </h4>
                    <p class="card-subtitle mb-0">
                        اختبار جميع Helper Functions والإعدادات
                    </p>
                </div>
                <div class="card-body">
                    
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif
                    
                    {{-- أزرار الاختبار --}}
                    <div class="mb-4">
                        <a href="{{ route('perfect-pharma.test-settings.set') }}" class="btn btn-primary me-2">
                            <i class="ti ti-edit me-1"></i>
                            اختبار تعديل إعداد
                        </a>
                        <button onclick="testPriceCalculation()" class="btn btn-info me-2">
                            <i class="ti ti-calculator me-1"></i>
                            اختبار حساب الأسعار
                        </button>
                        <a href="{{ route('perfect-pharma.settings.general') }}" class="btn btn-success">
                            <i class="ti ti-settings me-1"></i>
                            الذهاب للإعدادات
                        </a>
                    </div>
                    
                    <div class="row">
                        {{-- الإعدادات العامة --}}
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">الإعدادات العامة</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        @foreach($settings as $key => $value)
                                        <tr>
                                            <td><strong>{{ $key }}</strong></td>
                                            <td>
                                                @if(is_bool($value))
                                                    <span class="badge bg-{{ $value ? 'success' : 'danger' }}">
                                                        {{ $value ? 'مفعل' : 'معطل' }}
                                                    </span>
                                                @else
                                                    {{ $value }}
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                        <tr>
                                            <td><strong>test_setting</strong></td>
                                            <td>{{ get_perfect_pharma_setting('test_setting', 'لم يتم الاختبار بعد') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        {{-- نسب الخصومات --}}
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">نسب الخصومات</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        @foreach($discountRates as $type => $rate)
                                        <tr>
                                            <td><strong>{{ $userTypes[$type]['name'] ?? $type }}</strong></td>
                                            <td>
                                                <span class="badge bg-primary">{{ $rate }}%</span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        {{-- الميزات المفعلة --}}
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">الميزات المفعلة</h5>
                                </div>
                                <div class="card-body">
                                    @foreach($features as $feature => $enabled)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>{{ ucfirst($feature) }}</span>
                                        <span class="badge bg-{{ $enabled ? 'success' : 'danger' }}">
                                            {{ $enabled ? 'مفعل' : 'معطل' }}
                                        </span>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        
                        {{-- معلومات النظام --}}
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">معلومات النظام</h5>
                                </div>
                                <div class="card-body">
                                    @foreach($systemInfo as $key => $value)
                                    <div class="mb-2">
                                        <strong>{{ ucfirst($key) }}:</strong><br>
                                        <small class="text-muted">{{ $value }}</small>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        
                        {{-- حدود المحفظة --}}
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">حدود المحفظة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>الحد الأدنى:</strong>
                                        <span class="badge bg-info">{{ perfect_pharma_format_currency($walletLimits['min']) }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>الحد الأقصى:</strong>
                                        <span class="badge bg-warning">{{ perfect_pharma_format_currency($walletLimits['max']) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        {{-- معلومات العملة --}}
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">معلومات العملة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>رمز العملة:</strong>
                                        <span class="badge bg-primary">{{ $currencyInfo['code'] }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>رمز العملة:</strong>
                                        <span class="badge bg-secondary">{{ $currencyInfo['symbol'] }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>مثال 100:</strong>
                                        <span class="badge bg-success">{{ $currencyInfo['formatted_100'] }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>مثال 1500.75:</strong>
                                        <span class="badge bg-info">{{ $currencyInfo['formatted_1500'] }}</span>
                                    </div>
                                    @if($currencyInfo['currency'])
                                    <div class="mb-2">
                                        <strong>معرف العملة:</strong>
                                        <span class="badge bg-warning">{{ $currencyInfo['currency']->id }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        {{-- اختبار الأسعار --}}
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">اختبار الأسعار</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>سعر أصلي:</strong>
                                        <span class="badge bg-secondary">{{ perfect_pharma_format_currency(100) }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>خصم طبيب:</strong>
                                        <span class="badge bg-success">{{ perfect_pharma_price_with_discount(100, 'doctor') }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>خصم صيدلية:</strong>
                                        <span class="badge bg-info">{{ perfect_pharma_price_with_discount(100, 'pharmacy') }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>خصم مستشفى:</strong>
                                        <span class="badge bg-warning">{{ perfect_pharma_price_with_discount(100, 'hospital') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        {{-- أنواع المواعيد --}}
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">أنواع المواعيد</h5>
                                </div>
                                <div class="card-body">
                                    @foreach($appointmentTypes as $key => $name)
                                    <span class="badge bg-secondary me-1 mb-1">{{ $name }}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        
                        {{-- التخصصات الطبية --}}
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">التخصصات الطبية</h5>
                                </div>
                                <div class="card-body">
                                    @foreach($medicalSpecializations as $key => $name)
                                    <span class="badge bg-info me-1 mb-1">{{ $name }}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {{-- نتائج اختبار الأسعار --}}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">نتائج اختبار حساب الأسعار</h5>
                                </div>
                                <div class="card-body">
                                    <div id="priceResults" class="d-none">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>نوع المستخدم</th>
                                                        <th>السعر الأصلي</th>
                                                        <th>نسبة الخصم</th>
                                                        <th>السعر بعد الخصم</th>
                                                        <th>العمولة</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="priceResultsBody">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div id="priceLoading" class="d-none text-center">
                                        <i class="ti ti-loader-2 me-2"></i>
                                        جاري حساب الأسعار...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('header')
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0 !important;
            border: none;
        }
        
        .card-title, .card-header h5 {
            color: white;
            margin-bottom: 0.5rem;
        }
        
        .card-subtitle {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.75rem;
        }
    </style>
@endpush

@push('footer')
    <script>
        function testPriceCalculation() {
            document.getElementById('priceLoading').classList.remove('d-none');
            document.getElementById('priceResults').classList.add('d-none');
            
            fetch('{{ route("perfect-pharma.test-settings.price") }}')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('priceResultsBody');
                    tbody.innerHTML = '';
                    
                    Object.keys(data).forEach(userType => {
                        const result = data[userType];
                        const row = `
                            <tr>
                                <td><strong>${userType}</strong></td>
                                <td>${result.original_price} ريال</td>
                                <td><span class="badge bg-primary">${result.discount_rate}%</span></td>
                                <td><span class="badge bg-success">${result.discounted_price} ريال</span></td>
                                <td><span class="badge bg-warning">${result.commission} ريال</span></td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                    
                    document.getElementById('priceLoading').classList.add('d-none');
                    document.getElementById('priceResults').classList.remove('d-none');
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('priceLoading').classList.add('d-none');
                    alert('حدث خطأ في اختبار الأسعار');
                });
        }
    </script>
@endpush

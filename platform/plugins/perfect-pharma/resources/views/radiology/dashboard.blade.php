@extends('plugins/perfect-pharma::radiology.layout')

@section('title', 'لوحة تحكم مركز الأشعة')
@section('page-title', 'لوحة التحكم')

@section('content')
<div class="row">
    <!-- Welcome Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">مرحباً، مركز {{ $radiologyCenter->name }}</h3>
                        <p class="text-muted mb-0">{{ $radiologyCenter->address }}</p>
                        <small class="text-muted">آخر تسجيل دخول: {{ auth('customer')->user()->last_login_at?->diffForHumans() ?? 'الآن' }}</small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('radiology.requests.index', ['status' => 'pending']) }}" class="btn btn-warning">
                                <i class="fas fa-clock me-1"></i>
                                الطلبات المعلقة
                            </a>
                            <a href="{{ route('radiology.appointments.index') }}" class="btn btn-success">
                                <i class="fas fa-calendar-check me-1"></i>
                                مواعيد اليوم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-file-medical fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_requests'] }}</h3>
                <p class="mb-0">إجمالي الطلبات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['pending_requests'] }}</h3>
                <p class="mb-0">طلبات معلقة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['today_appointments'] }}</h3>
                <p class="mb-0">مواعيد اليوم</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                <i class="fas fa-file-image fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['ready_results'] }}</h3>
                <p class="mb-0">نتائج جاهزة</p>
            </div>
        </div>
    </div>
</div>

<!-- Revenue and Patients Cards -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h4 class="text-success">{{ number_format($stats['monthly_revenue']) }} ر.س</h4>
                <p class="text-muted mb-0">إيرادات الشهر</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h4 class="text-info">{{ $stats['total_patients'] }}</h4>
                <p class="text-muted mb-0">إجمالي المرضى</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Requests -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-medical me-2"></i>
                    آخر طلبات الأشعة
                </h5>
                <a href="{{ route('radiology.requests.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($recentRequests->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentRequests as $request)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="scan-type-icon scan-type-{{ strtolower($request->scanType->category ?? 'default') }}">
                                        <i class="fas fa-x-ray text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">{{ $request->scanType->name }}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            {{ $request->patient->name }}
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-user-md me-1"></i>
                                            د. {{ $request->doctor->user->name }}
                                        </small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    @php
                                        $statusColors = [
                                            'pending' => 'warning',
                                            'accepted' => 'info',
                                            'scheduled' => 'primary',
                                            'in_progress' => 'warning',
                                            'completed' => 'success',
                                            'cancelled' => 'secondary',
                                            'rejected' => 'danger'
                                        ];
                                    @endphp
                                    <span class="badge bg-{{ $statusColors[$request->status] ?? 'secondary' }}">
                                        {{ $request->status_text }}
                                    </span>
                                    @if($request->urgency !== 'normal')
                                        <br>
                                        <span class="badge urgency-{{ $request->urgency }} mt-1">
                                            {{ $request->urgency_text }}
                                        </span>
                                    @endif
                                    <br>
                                    <small class="text-muted">{{ $request->created_at->diffForHumans() }}</small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-file-medical fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد طلبات حديثة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Pending Requests -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الطلبات المعلقة
                </h5>
                <a href="{{ route('radiology.requests.index', ['status' => 'pending']) }}" class="btn btn-sm btn-outline-warning">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($pendingRequests->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($pendingRequests as $request)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="scan-type-icon scan-type-{{ strtolower($request->scanType->category ?? 'default') }}">
                                        <i class="fas fa-x-ray text-white"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">{{ $request->scanType->name }}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            {{ $request->patient->name }}
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $request->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    @if($request->urgency !== 'normal')
                                        <span class="badge urgency-{{ $request->urgency }}">
                                            {{ $request->urgency_text }}
                                        </span>
                                        <br>
                                    @endif
                                    <a href="{{ route('radiology.requests.show', $request->id) }}" 
                                       class="btn btn-sm btn-outline-primary mt-1">
                                        عرض
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">لا توجد طلبات معلقة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Today's Appointments -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    مواعيد اليوم
                </h5>
                <a href="{{ route('radiology.appointments.index') }}" class="btn btn-sm btn-outline-success">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($todayAppointments->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>المريض</th>
                                    <th>نوع الأشعة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($todayAppointments as $appointment)
                                    <tr>
                                        <td>{{ $appointment->appointment_date->format('H:i') }}</td>
                                        <td>
                                            {{ $appointment->patient->name }}
                                            <br>
                                            <small class="text-muted">{{ $appointment->patient->phone }}</small>
                                        </td>
                                        <td>{{ $appointment->request->scanType->name }}</td>
                                        <td>
                                            <span class="badge bg-{{ $appointment->status === 'confirmed' ? 'success' : 'warning' }}">
                                                {{ $appointment->status === 'confirmed' ? 'مؤكد' : 'مجدول' }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('radiology.appointments.show', $appointment->id) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($appointment->status === 'scheduled')
                                                    <button type="button" class="btn btn-sm btn-outline-success confirm-appointment" 
                                                            data-id="{{ $appointment->id }}" title="تأكيد">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مواعيد اليوم</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Quick Actions & Ready Results -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('radiology.requests.index', ['status' => 'pending']) }}" class="btn btn-warning">
                        <i class="fas fa-clock me-2"></i>
                        الطلبات المعلقة ({{ $stats['pending_requests'] }})
                    </a>
                    
                    <a href="{{ route('radiology.appointments.index') }}" class="btn btn-success">
                        <i class="fas fa-calendar-check me-2"></i>
                        مواعيد اليوم ({{ $stats['today_appointments'] }})
                    </a>
                    
                    <a href="{{ route('radiology.results.index', ['status' => 'approved']) }}" class="btn btn-info">
                        <i class="fas fa-file-image me-2"></i>
                        النتائج الجاهزة ({{ $stats['ready_results'] }})
                    </a>
                    
                    <a href="{{ route('radiology.reports.index') }}" class="btn btn-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        التقارير
                    </a>
                </div>
                
                <hr>
                
                <h6 class="text-muted">النتائج الجاهزة للتسليم</h6>
                @if($readyResults->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($readyResults->take(3) as $result)
                            <div class="list-group-item px-0 py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <small class="fw-bold">{{ $result->request->patient->name }}</small>
                                        <br>
                                        <small class="text-muted">{{ $result->request->scanType->name }}</small>
                                    </div>
                                    <a href="{{ route('radiology.results.show', $result->id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted small">لا توجد نتائج جاهزة</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تأكيد الموعد
    $('.confirm-appointment').click(function() {
        const appointmentId = $(this).data('id');
        
        if (confirm('هل تريد تأكيد هذا الموعد؟')) {
            $.post(`/radiology/appointments/${appointmentId}/confirm`, {
                _token: $('meta[name="csrf-token"]').attr('content')
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                }
            })
            .fail(function() {
                alert('حدث خطأ أثناء تأكيد الموعد');
            });
        }
    });
    
    // Auto refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
@endpush

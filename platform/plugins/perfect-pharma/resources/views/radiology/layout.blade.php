<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'لوحة تحكم مركز الأشعة') - {{ get_application_name() }}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #e83e8c 0%, #fd7e14 100%);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #e83e8c 0%, #fd7e14 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        .navbar-brand {
            font-weight: bold;
            color: #e83e8c !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #e83e8c 0%, #fd7e14 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(232, 62, 140, 0.4);
        }
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .badge {
            border-radius: 20px;
            padding: 8px 12px;
        }
        .urgency-emergency {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            animation: pulse 2s infinite;
        }
        .urgency-urgent {
            background-color: #fd7e14;
            color: white;
        }
        .urgency-normal {
            background-color: #28a745;
            color: white;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .scan-type-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        .scan-type-xray { background: linear-gradient(135deg, #17a2b8, #20c997); }
        .scan-type-ct { background: linear-gradient(135deg, #6f42c1, #e83e8c); }
        .scan-type-mri { background: linear-gradient(135deg, #fd7e14, #ffc107); }
        .scan-type-ultrasound { background: linear-gradient(135deg, #28a745, #20c997); }
    </style>
    @stack('styles')
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-3 text-center border-bottom border-light border-opacity-25">
                        <h4 class="text-white mb-0">
                            <i class="fas fa-x-ray me-2"></i>
                            مركز الأشعة
                        </h4>
                    </div>
                    
                    <nav class="nav flex-column py-3">
                        <a href="{{ route('radiology.dashboard') }}" 
                           class="nav-link {{ request()->routeIs('radiology.dashboard') ? 'active' : '' }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            الرئيسية
                        </a>
                        
                        <a href="{{ route('radiology.requests.index') }}" 
                           class="nav-link {{ request()->routeIs('radiology.requests.*') ? 'active' : '' }}">
                            <i class="fas fa-file-medical me-2"></i>
                            طلبات الأشعة
                        </a>
                        
                        <a href="{{ route('radiology.appointments.index') }}" 
                           class="nav-link {{ request()->routeIs('radiology.appointments.*') ? 'active' : '' }}">
                            <i class="fas fa-calendar-check me-2"></i>
                            المواعيد
                        </a>
                        
                        <a href="{{ route('radiology.results.index') }}" 
                           class="nav-link {{ request()->routeIs('radiology.results.*') ? 'active' : '' }}">
                            <i class="fas fa-file-image me-2"></i>
                            النتائج والتقارير
                        </a>
                        
                        <a href="{{ route('radiology.patients.index') }}" 
                           class="nav-link {{ request()->routeIs('radiology.patients.*') ? 'active' : '' }}">
                            <i class="fas fa-users me-2"></i>
                            المرضى
                        </a>
                        
                        <a href="{{ route('radiology.equipment.index') }}" 
                           class="nav-link {{ request()->routeIs('radiology.equipment.*') ? 'active' : '' }}">
                            <i class="fas fa-cogs me-2"></i>
                            الأجهزة والمعدات
                        </a>
                        
                        <a href="{{ route('radiology.reports.index') }}" 
                           class="nav-link {{ request()->routeIs('radiology.reports.*') ? 'active' : '' }}">
                            <i class="fas fa-chart-line me-2"></i>
                            التقارير
                        </a>
                        
                        <a href="{{ route('radiology.profile.index') }}" 
                           class="nav-link {{ request()->routeIs('radiology.profile.*') ? 'active' : '' }}">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                        
                        <hr class="border-light border-opacity-25 mx-3">
                        
                        <a href="{{ route('customer.logout') }}" 
                           class="nav-link"
                           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm mb-4">
                    <div class="container-fluid">
                        <span class="navbar-brand">@yield('page-title', 'لوحة التحكم')</span>
                        
                        <div class="navbar-nav ms-auto">
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" 
                                   id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-x-ray me-2"></i>
                                    {{ auth('customer')->user()->name }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="{{ route('radiology.profile.index') }}">
                                            <i class="fas fa-cog me-2"></i>
                                            إعدادات المركز
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ route('radiology.equipment.index') }}">
                                            <i class="fas fa-cogs me-2"></i>
                                            الأجهزة والمعدات
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ route('radiology.reports.index') }}">
                                            <i class="fas fa-chart-line me-2"></i>
                                            التقارير
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="{{ route('customer.logout') }}"
                                           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                            <i class="fas fa-sign-out-alt me-2"></i>
                                            تسجيل الخروج
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- Page Content -->
                <div class="main-content">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif
                    
                    @yield('content')
                </div>
            </div>
        </div>
    </div>
    
    <!-- Logout Form -->
    <form id="logout-form" action="{{ route('customer.logout') }}" method="POST" style="display: none;">
        @csrf
    </form>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    @stack('scripts')
</body>
</html>

{{-- شورت كود الأكاديمية التعليمية --}}
<div class="perfect-pharma-academy py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">
                    <i class="ti ti-school text-warning me-2"></i>
                    الأكاديمية التعليمية
                </h2>
                <p class="text-muted lead">
                    دورات طبية متخصصة وشهادات معتمدة لتطوير مهاراتك المهنية
                </p>
            </div>
        </div>
        
        @if($courses->count() > 0)
        <div class="row g-4">
            @foreach($courses as $course)
            <div class="col-lg-4 col-md-6">
                <div class="course-card bg-white rounded-3 shadow-sm overflow-hidden h-100">
                    {{-- صورة الدورة --}}
                    <div class="course-image position-relative">
                        @if($course->image)
                            <img src="{{ RvMedia::getImageUrl($course->image) }}" 
                                 alt="{{ $course->title }}" 
                                 class="w-100" style="height: 200px; object-fit: cover;">
                        @else
                            <div class="placeholder-image d-flex align-items-center justify-content-center bg-warning text-white" 
                                 style="height: 200px;">
                                <i class="ti ti-book" style="font-size: 3rem;"></i>
                            </div>
                        @endif
                        
                        {{-- شارة الفئة --}}
                        @if($course->category)
                        <div class="category-badge position-absolute top-0 start-0 m-3">
                            <span class="badge bg-warning text-dark rounded-pill">
                                {{ $course->category }}
                            </span>
                        </div>
                        @endif
                        
                        {{-- مستوى الصعوبة --}}
                        @if($course->difficulty_level)
                        <div class="difficulty-badge position-absolute top-0 end-0 m-3">
                            <span class="badge bg-info rounded-pill">
                                {{ $course->difficulty_level }}
                            </span>
                        </div>
                        @endif
                    </div>
                    
                    {{-- معلومات الدورة --}}
                    <div class="course-info p-4">
                        <h5 class="course-title mb-3 text-dark">
                            {{ $course->title }}
                        </h5>
                        
                        <p class="course-description text-muted mb-3">
                            {{ Str::limit($course->description, 100) }}
                        </p>
                        
                        {{-- تفاصيل الدورة --}}
                        <div class="course-details mb-3">
                            @if($course->duration_hours)
                            <div class="detail-item d-flex align-items-center mb-2">
                                <i class="ti ti-clock text-primary me-2"></i>
                                <span class="text-muted">{{ $course->duration_hours }} ساعة</span>
                            </div>
                            @endif
                            
                            @if($course->lessons_count)
                            <div class="detail-item d-flex align-items-center mb-2">
                                <i class="ti ti-list text-success me-2"></i>
                                <span class="text-muted">{{ $course->lessons_count }} درس</span>
                            </div>
                            @endif
                            
                            @if($course->instructor_name)
                            <div class="detail-item d-flex align-items-center mb-2">
                                <i class="ti ti-user text-info me-2"></i>
                                <span class="text-muted">{{ $course->instructor_name }}</span>
                            </div>
                            @endif
                        </div>
                        
                        {{-- السعر --}}
                        @if($course->price)
                        <div class="course-price mb-3">
                            <span class="price text-success fw-bold fs-5">
                                {{ number_format($course->price) }} ريال
                            </span>
                            @if($course->original_price && $course->original_price > $course->price)
                            <span class="original-price text-muted text-decoration-line-through ms-2">
                                {{ number_format($course->original_price) }} ريال
                            </span>
                            @endif
                        </div>
                        @else
                        <div class="course-price mb-3">
                            <span class="price text-success fw-bold fs-5">مجاني</span>
                        </div>
                        @endif
                        
                        {{-- أزرار العمل --}}
                        <div class="course-actions d-flex gap-2">
                            @auth('customer')
                                <a href="#" class="btn btn-warning flex-fill btn-sm text-white">
                                    <i class="ti ti-play me-1"></i>
                                    ابدأ الدورة
                                </a>
                            @else
                                <a href="{{ route('customer.login') }}" class="btn btn-warning flex-fill btn-sm text-white">
                                    <i class="ti ti-login me-1"></i>
                                    سجل دخول للدراسة
                                </a>
                            @endauth
                            <a href="#" class="btn btn-outline-warning btn-sm">
                                <i class="ti ti-eye me-1"></i>
                                التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        {{-- رابط عرض المزيد --}}
        <div class="row mt-5">
            <div class="col-12 text-center">
                <a href="{{ route('customer.register.choice') }}" class="btn btn-warning btn-lg rounded-pill px-5 text-white">
                    <i class="ti ti-books me-2"></i>
                    انضم للأكاديمية
                    <i class="ti ti-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
        
        @else
        {{-- رسالة عدم وجود دورات --}}
        <div class="row">
            <div class="col-12 text-center">
                <div class="empty-state py-5">
                    <i class="ti ti-school text-muted mb-3" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mb-3">لا توجد دورات متاحة حالياً</h4>
                    <p class="text-muted">سيتم إضافة الدورات التعليمية قريباً</p>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<style>
.course-card {
    transition: all 0.3s ease;
    border: none;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.course-image {
    overflow: hidden;
}

.course-image img {
    transition: transform 0.3s ease;
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

.category-badge .badge,
.difficulty-badge .badge {
    font-size: 0.75rem;
}

.course-title {
    font-weight: 600;
    line-height: 1.4;
}

.course-description {
    font-size: 0.9rem;
    line-height: 1.5;
}

.detail-item {
    font-size: 0.875rem;
}

.course-price .price {
    font-size: 1.25rem;
}

.course-price .original-price {
    font-size: 1rem;
}

.course-actions .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

.empty-state {
    background: rgba(255,193,7,0.1);
    border-radius: 1rem;
    margin: 2rem 0;
}

@media (max-width: 768px) {
    .perfect-pharma-academy {
        padding: 3rem 0 !important;
    }
    
    .course-card {
        margin-bottom: 1.5rem;
    }
    
    .course-actions {
        flex-direction: column;
    }
    
    .course-actions .btn {
        margin-bottom: 0.5rem;
    }
}
</style>

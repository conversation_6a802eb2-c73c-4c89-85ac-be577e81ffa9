{{-- شورت كود إحصائيات Perfect Pharma --}}
<div class="perfect-pharma-stats py-5 bg-primary text-white position-relative overflow-hidden">
    {{-- خلفية متحركة --}}
    <div class="animated-bg position-absolute top-0 start-0 w-100 h-100"></div>
    
    <div class="container position-relative">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3 text-white">
                    <i class="ti ti-chart-bar me-2"></i>
                    إحصائيات Perfect Pharma
                </h2>
                <p class="text-white-75 lead">
                    أرقام تعكس ثقة عملائنا وجودة خدماتنا
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            @foreach($stats as $index => $stat)
            <div class="col-lg-3 col-md-6">
                <div class="stat-card text-center p-4 rounded-3 bg-white bg-opacity-10 backdrop-blur h-100 position-relative overflow-hidden">
                    {{-- تأثير الإضاءة --}}
                    <div class="stat-glow position-absolute top-0 start-0 w-100 h-100 rounded-3"></div>
                    
                    <div class="stat-icon mb-3 position-relative">
                        <div class="icon-wrapper d-inline-flex align-items-center justify-content-center rounded-circle bg-white text-{{ $stat['color'] }}" 
                             style="width: 80px; height: 80px; font-size: 2.5rem;">
                            <i class="{{ $stat['icon'] }}"></i>
                        </div>
                    </div>
                    
                    <div class="stat-content position-relative">
                        <h3 class="stat-number mb-2 text-white fw-bold" data-count="{{ $stat['count'] }}">
                            0
                        </h3>
                        <h5 class="stat-title mb-0 text-white-75">{{ $stat['title'] }}</h5>
                    </div>
                    
                    {{-- خط الزخرفة --}}
                    <div class="stat-decoration position-absolute bottom-0 start-50 translate-middle-x bg-white bg-opacity-25" 
                         style="width: 50px; height: 3px; border-radius: 2px;"></div>
                </div>
            </div>
            @endforeach
        </div>
        
        {{-- معلومات إضافية --}}
        <div class="row mt-5 pt-4">
            <div class="col-12 text-center">
                <div class="additional-info">
                    <h4 class="text-white mb-3">
                        <i class="ti ti-shield-check me-2"></i>
                        خدمات موثوقة ومعتمدة
                    </h4>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="info-item">
                                <i class="ti ti-clock text-warning fs-4 mb-2"></i>
                                <p class="text-white-75 mb-0">خدمة 24/7</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item">
                                <i class="ti ti-certificate text-success fs-4 mb-2"></i>
                                <p class="text-white-75 mb-0">شهادات معتمدة</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item">
                                <i class="ti ti-heart text-danger fs-4 mb-2"></i>
                                <p class="text-white-75 mb-0">رعاية شاملة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.perfect-pharma-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.animated-bg {
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.stat-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255,255,255,0.4);
}

.stat-card:hover .stat-glow {
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
}

.icon-wrapper {
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.stat-card:hover .icon-wrapper {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.stat-number {
    font-size: 3rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    font-family: 'Arial', sans-serif;
}

.stat-title {
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.text-white-75 {
    color: rgba(255,255,255,0.75) !important;
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

.bg-opacity-10 {
    background-color: rgba(255,255,255,0.1) !important;
}

.bg-opacity-25 {
    background-color: rgba(255,255,255,0.25) !important;
}

.info-item {
    transition: transform 0.3s ease;
}

.info-item:hover {
    transform: translateY(-5px);
}

/* تأثير العد التصاعدي */
.stat-number {
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .perfect-pharma-stats {
        padding: 3rem 0 !important;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .icon-wrapper {
        width: 60px !important;
        height: 60px !important;
        font-size: 2rem !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثير العد التصاعدي للأرقام
    const statNumbers = document.querySelectorAll('.stat-number[data-count]');
    
    const animateCount = (element) => {
        const target = parseInt(element.getAttribute('data-count'));
        const duration = 2000; // 2 ثانية
        const increment = target / (duration / 16); // 60 FPS
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current).toLocaleString('ar-SA') + '+';
        }, 16);
    };
    
    // تشغيل الأنيميشن عند ظهور العنصر في الشاشة
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCount(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    statNumbers.forEach(number => {
        observer.observe(number);
    });
});
</script>

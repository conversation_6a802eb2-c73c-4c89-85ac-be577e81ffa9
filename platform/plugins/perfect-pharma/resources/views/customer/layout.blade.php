<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Perfect Pharma - لوحة التحكم')</title>
    
    {{-- Bootstrap CSS --}}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    {{-- Tabler Icons --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/tabler-icons.min.css">
    
    {{-- Google Fonts --}}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    {{-- Custom CSS --}}
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 12px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #667eea !important;
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }
            
            .sidebar.show {
                right: 0;
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
    
    @yield('styles')
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            {{-- Sidebar --}}
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-3">
                        <h5 class="text-white mb-0">
                            <i class="ti ti-medical-cross me-2"></i>
                            Perfect Pharma
                        </h5>
                        <small class="text-white-50">لوحة التحكم</small>
                    </div>
                    
                    <nav class="nav flex-column px-3">
                        <a class="nav-link {{ request()->routeIs('customer.perfect-pharma.dashboard') ? 'active' : '' }}" 
                           href="{{ route('customer.perfect-pharma.dashboard') }}">
                            <i class="ti ti-dashboard"></i>
                            الرئيسية
                        </a>
                        
                        <a class="nav-link {{ request()->routeIs('customer.perfect-pharma.medical-profile*') ? 'active' : '' }}" 
                           href="{{ route('customer.perfect-pharma.medical-profile') }}">
                            <i class="ti ti-user-heart"></i>
                            الملف الطبي
                        </a>
                        
                        <a class="nav-link {{ request()->routeIs('customer.perfect-pharma.appointments*') ? 'active' : '' }}" 
                           href="{{ route('customer.perfect-pharma.appointments') }}">
                            <i class="ti ti-calendar"></i>
                            المواعيد
                        </a>
                        
                        <a class="nav-link {{ request()->routeIs('customer.perfect-pharma.prescriptions*') ? 'active' : '' }}" 
                           href="{{ route('customer.perfect-pharma.prescriptions') }}">
                            <i class="ti ti-prescription"></i>
                            الوصفات الطبية
                        </a>
                        
                        <a class="nav-link {{ request()->routeIs('customer.perfect-pharma.lab-tests*') ? 'active' : '' }}" 
                           href="{{ route('customer.perfect-pharma.lab-tests') }}">
                            <i class="ti ti-flask"></i>
                            التحاليل الطبية
                        </a>
                        
                        <a class="nav-link {{ request()->routeIs('customer.perfect-pharma.wallet*') ? 'active' : '' }}" 
                           href="{{ route('customer.perfect-pharma.wallet') }}">
                            <i class="ti ti-wallet"></i>
                            المحفظة الرقمية
                        </a>
                        
                        <a class="nav-link {{ request()->routeIs('customer.perfect-pharma.academy*') ? 'active' : '' }}" 
                           href="{{ route('customer.perfect-pharma.academy') }}">
                            <i class="ti ti-school"></i>
                            الأكاديمية
                        </a>
                        
                        <a class="nav-link {{ request()->routeIs('customer.perfect-pharma.donations*') ? 'active' : '' }}" 
                           href="{{ route('customer.perfect-pharma.donations') }}">
                            <i class="ti ti-heart"></i>
                            التبرعات
                        </a>
                        
                        <hr class="text-white-50">
                        
                        <a class="nav-link" href="{{ route('public.index') }}">
                            <i class="ti ti-home"></i>
                            العودة للموقع
                        </a>
                        
                        <a class="nav-link" href="{{ route('customer.logout') }}">
                            <i class="ti ti-logout"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>
            
            {{-- Main Content --}}
            <div class="col-md-9 col-lg-10">
                {{-- Top Navigation --}}
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm mb-4">
                    <div class="container-fluid">
                        <button class="navbar-toggler d-md-none" type="button" onclick="toggleSidebar()">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        
                        <div class="navbar-nav me-auto">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    @yield('breadcrumb')
                                </ol>
                            </nav>
                        </div>
                        
                        <div class="navbar-nav">
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="ti ti-user me-1"></i>
                                    {{ auth('customer')->user()->first_name }}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{{ route('customer.perfect-pharma.medical-profile') }}">الملف الشخصي</a></li>
                                    <li><a class="dropdown-item" href="{{ route('customer.overview') }}">حسابي</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ route('customer.logout') }}">تسجيل الخروج</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
                
                {{-- Page Content --}}
                <div class="main-content">
                    @yield('content')
                </div>
            </div>
        </div>
    </div>

    {{-- Bootstrap JS --}}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }
        
        // إغلاق الـ sidebar عند النقر خارجه في الجوال
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggleButton = document.querySelector('.navbar-toggler');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggleButton.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });
    </script>
    
    @yield('scripts')
</body>
</html>

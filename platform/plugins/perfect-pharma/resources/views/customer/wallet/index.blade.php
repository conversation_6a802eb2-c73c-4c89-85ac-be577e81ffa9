@extends('plugins/perfect-pharma::customer.layout')

@section('title', 'المحفظة الرقمية - Perfect Pharma')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('customer.perfect-pharma.dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">المحفظة الرقمية</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">المحفظة الرقمية</h2>
                <p class="text-muted mb-0">إدارة رصيدك ومعاملاتك المالية</p>
            </div>
        </div>
    </div>
</div>

{{-- رصيد المحفظة --}}
<div class="row g-4 mb-4">
    <div class="col-lg-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center p-4">
                <i class="ti ti-wallet mb-3" style="font-size: 3rem;"></i>
                <h3 class="mb-2">{{ number_format($balance) }} ريال</h3>
                <p class="mb-0">الرصيد المتاح</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center p-4">
                <i class="ti ti-arrow-up mb-3" style="font-size: 3rem;"></i>
                <h3 class="mb-2">{{ number_format($monthlyStats['income']) }} ريال</h3>
                <p class="mb-0">إيداعات هذا الشهر</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card bg-danger text-white">
            <div class="card-body text-center p-4">
                <i class="ti ti-arrow-down mb-3" style="font-size: 3rem;"></i>
                <h3 class="mb-2">{{ number_format($monthlyStats['expenses']) }} ريال</h3>
                <p class="mb-0">مصروفات هذا الشهر</p>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    {{-- شحن المحفظة --}}
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="ti ti-plus me-2"></i>
                    شحن المحفظة
                </h5>
            </div>
            <div class="card-body">
                <form id="chargeForm">
                    @csrf
                    <div class="mb-3">
                        <label class="form-label">المبلغ (ريال)</label>
                        <input type="number" class="form-control" name="amount" min="10" max="10000" required>
                        <small class="text-muted">الحد الأدنى: 10 ريال، الحد الأقصى: 10,000 ريال</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" name="payment_method" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="credit_card">بطاقة ائتمانية</option>
                            <option value="mada">مدى</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="tabby">تابي</option>
                            <option value="tamara">تمارا</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="ti ti-credit-card me-2"></i>
                        شحن المحفظة
                    </button>
                </form>
            </div>
        </div>
        
        {{-- مبالغ سريعة --}}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">مبالغ سريعة</h6>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-6">
                        <button class="btn btn-outline-primary w-100 quick-amount" data-amount="50">50 ريال</button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-primary w-100 quick-amount" data-amount="100">100 ريال</button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-primary w-100 quick-amount" data-amount="200">200 ريال</button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-primary w-100 quick-amount" data-amount="500">500 ريال</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {{-- آخر المعاملات --}}
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="ti ti-history me-2"></i>
                    آخر المعاملات
                </h5>
                <a href="{{ route('customer.perfect-pharma.wallet.transactions') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($recentTransactions->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                    <th>طريقة الدفع</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentTransactions as $transaction)
                                <tr>
                                    <td>
                                        @if($transaction->type == 'credit')
                                            <span class="badge bg-success">
                                                <i class="ti ti-arrow-up me-1"></i>
                                                إيداع
                                            </span>
                                        @else
                                            <span class="badge bg-danger">
                                                <i class="ti ti-arrow-down me-1"></i>
                                                سحب
                                            </span>
                                        @endif
                                    </td>
                                    <td class="fw-bold">{{ number_format($transaction->amount) }} ريال</td>
                                    <td>{{ $transaction->description }}</td>
                                    <td>
                                        @if($transaction->payment_method)
                                            <small class="text-muted">{{ $transaction->payment_method }}</small>
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>{{ $transaction->created_at->format('Y-m-d H:i') }}</td>
                                    <td>
                                        @if($transaction->status == 'completed')
                                            <span class="badge bg-success">مكتمل</span>
                                        @elseif($transaction->status == 'pending')
                                            <span class="badge bg-warning">معلق</span>
                                        @else
                                            <span class="badge bg-danger">فاشل</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="ti ti-credit-card text-muted mb-3" style="font-size: 4rem;"></i>
                        <h5 class="text-muted">لا توجد معاملات</h5>
                        <p class="text-muted">ابدأ بشحن محفظتك لرؤية المعاملات هنا</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

{{-- معلومات إضافية --}}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="ti ti-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>كيفية استخدام المحفظة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-check text-success me-2"></i>ادفع رسوم المواعيد والاستشارات</li>
                            <li><i class="ti ti-check text-success me-2"></i>اشتري الأدوية من الصيدليات المعتمدة</li>
                            <li><i class="ti ti-check text-success me-2"></i>ادفع رسوم التحاليل الطبية</li>
                            <li><i class="ti ti-check text-success me-2"></i>اشترك في الدورات التعليمية</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>الأمان والحماية:</h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-shield-check text-primary me-2"></i>تشفير عالي المستوى</li>
                            <li><i class="ti ti-shield-check text-primary me-2"></i>حماية متعددة الطبقات</li>
                            <li><i class="ti ti-shield-check text-primary me-2"></i>مراقبة مستمرة للمعاملات</li>
                            <li><i class="ti ti-shield-check text-primary me-2"></i>دعم فني 24/7</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // المبالغ السريعة
    document.querySelectorAll('.quick-amount').forEach(button => {
        button.addEventListener('click', function() {
            const amount = this.dataset.amount;
            document.querySelector('input[name="amount"]').value = amount;
        });
    });
    
    // نموذج الشحن
    document.getElementById('chargeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        
        // تعطيل الزر وإظهار التحميل
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="ti ti-loader-2 me-2"></i>جاري المعالجة...';
        
        fetch('{{ route("customer.perfect-pharma.wallet.charge") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('خطأ: ' + data.message);
            } else {
                alert('تم شحن المحفظة بنجاح!');
                location.reload();
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        });
    });
});
</script>
@endsection

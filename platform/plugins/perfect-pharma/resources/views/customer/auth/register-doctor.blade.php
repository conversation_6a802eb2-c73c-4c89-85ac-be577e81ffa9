@extends('plugins/ecommerce::themes.customers.master')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user-md"></i>
                        تسجيل طبيب جديد
                    </h4>
                    <p class="text-muted mb-0">انضم إلى منصة Perfect Pharma كطبيب معتمد</p>
                </div>
                
                <div class="card-body">
                    <!-- خطوات التسجيل -->
                    <div class="registration-steps mb-4">
                        <div class="step-indicator">
                            <div class="step active" data-step="1">
                                <span class="step-number">1</span>
                                <span class="step-title">المعلومات الأساسية</span>
                            </div>
                            <div class="step" data-step="2">
                                <span class="step-number">2</span>
                                <span class="step-title">رفع المستندات</span>
                            </div>
                            <div class="step" data-step="3">
                                <span class="step-number">3</span>
                                <span class="step-title">مراجعة وتأكيد</span>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('customer.register') }}" id="doctorRegistrationForm">
                        @csrf
                        
                        <!-- نوع المستخدم مخفي -->
                        <input type="hidden" name="user_type_id" value="{{ $doctorUserType->id }}">
                        
                        <!-- الخطوة الأولى: المعلومات الأساسية -->
                        <div class="step-content" id="step-1">
                            <h5 class="mb-3">
                                <i class="fas fa-user"></i>
                                المعلومات الشخصية
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                                               id="first_name" name="first_name" value="{{ old('first_name') }}" required>
                                        @error('first_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="last_name" class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                               id="last_name" name="last_name" value="{{ old('last_name') }}" required>
                                        @error('last_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group mb-3">
                                <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone') }}" required>
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                               id="password" name="password" required>
                                        @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="password_confirmation" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control" 
                                               id="password_confirmation" name="password_confirmation" required>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات طبية إضافية -->
                            <h5 class="mb-3 mt-4">
                                <i class="fas fa-stethoscope"></i>
                                المعلومات المهنية
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="medical_license_number" class="form-label">رقم الرخصة الطبية <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('medical_license_number') is-invalid @enderror" 
                                               id="medical_license_number" name="medical_license_number" value="{{ old('medical_license_number') }}" required>
                                        @error('medical_license_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="specialization" class="form-label">التخصص <span class="text-danger">*</span></label>
                                        <select class="form-control @error('specialization') is-invalid @enderror" 
                                                id="specialization" name="specialization" required>
                                            <option value="">اختر التخصص</option>
                                            <option value="general_medicine">طب عام</option>
                                            <option value="internal_medicine">باطنة</option>
                                            <option value="pediatrics">أطفال</option>
                                            <option value="surgery">جراحة</option>
                                            <option value="orthopedics">عظام</option>
                                            <option value="cardiology">قلب</option>
                                            <option value="neurology">أعصاب</option>
                                            <option value="dermatology">جلدية</option>
                                            <option value="ophthalmology">عيون</option>
                                            <option value="ent">أنف وأذن وحنجرة</option>
                                            <option value="psychiatry">نفسية</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                        @error('specialization')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label for="years_of_experience" class="form-label">سنوات الخبرة</label>
                                <select class="form-control" id="years_of_experience" name="years_of_experience">
                                    <option value="">اختر سنوات الخبرة</option>
                                    <option value="0-2">أقل من سنتين</option>
                                    <option value="2-5">2-5 سنوات</option>
                                    <option value="5-10">5-10 سنوات</option>
                                    <option value="10-15">10-15 سنة</option>
                                    <option value="15+">أكثر من 15 سنة</option>
                                </select>
                            </div>

                            <div class="form-group mb-3">
                                <label for="clinic_address" class="form-label">عنوان العيادة/المستشفى</label>
                                <textarea class="form-control" id="clinic_address" name="clinic_address" rows="3" 
                                          placeholder="أدخل العنوان الكامل للعيادة أو المستشفى">{{ old('clinic_address') }}</textarea>
                            </div>
                        </div>

                        <!-- الخطوة الثانية: رفع المستندات -->
                        <div class="step-content d-none" id="step-2">
                            <h5 class="mb-3">
                                <i class="fas fa-file-upload"></i>
                                المستندات المطلوبة
                            </h5>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>ملاحظة:</strong> يمكنك رفع المستندات الآن أو لاحقاً من لوحة التحكم الخاصة بك.
                                المستندات المطلوبة: رخصة مزاولة المهنة الطبية، الهوية الشخصية، شهادة التخصص (إن وجدت).
                            </div>

                            <div class="document-upload-section">
                                <div class="form-group mb-3">
                                    <label for="medical_license" class="form-label">رخصة مزاولة المهنة الطبية</label>
                                    <input type="file" class="form-control" id="medical_license" name="medical_license" 
                                           accept=".pdf,.jpg,.jpeg,.png">
                                    <small class="form-text text-muted">الصيغ المدعومة: PDF, JPG, PNG (الحد الأقصى: 5MB)</small>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="government_id" class="form-label">الهوية الشخصية</label>
                                    <input type="file" class="form-control" id="government_id" name="government_id" 
                                           accept=".pdf,.jpg,.jpeg,.png">
                                    <small class="form-text text-muted">الصيغ المدعومة: PDF, JPG, PNG (الحد الأقصى: 5MB)</small>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="specialization_certificate" class="form-label">شهادة التخصص (اختياري)</label>
                                    <input type="file" class="form-control" id="specialization_certificate" name="specialization_certificate" 
                                           accept=".pdf,.jpg,.jpeg,.png">
                                    <small class="form-text text-muted">الصيغ المدعومة: PDF, JPG, PNG (الحد الأقصى: 5MB)</small>
                                </div>
                            </div>
                        </div>

                        <!-- الخطوة الثالثة: مراجعة وتأكيد -->
                        <div class="step-content d-none" id="step-3">
                            <h5 class="mb-3">
                                <i class="fas fa-check-circle"></i>
                                مراجعة البيانات والموافقة
                            </h5>
                            
                            <div class="review-section">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>مهم:</strong> يرجى مراجعة جميع البيانات المدخلة قبل التأكيد. 
                                    سيتم إرسال طلبك للمراجعة الإدارية وستتلقى إشعاراً بالنتيجة خلال 24-48 ساعة.
                                </div>

                                <div class="terms-section">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                        <label class="form-check-label" for="agree_terms">
                                            أوافق على <a href="#" target="_blank">الشروط والأحكام</a> و <a href="#" target="_blank">سياسة الخصوصية</a>
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="agree_verification" name="agree_verification" required>
                                        <label class="form-check-label" for="agree_verification">
                                            أوافق على عملية التحقق من المستندات والمعلومات المقدمة
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="agree_communication" name="agree_communication">
                                        <label class="form-check-label" for="agree_communication">
                                            أوافق على تلقي الإشعارات والتحديثات عبر البريد الإلكتروني والرسائل النصية
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التنقل -->
                        <div class="form-navigation mt-4">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                                    <i class="fas fa-arrow-right"></i>
                                    السابق
                                </button>
                                
                                <div class="ms-auto">
                                    <button type="button" class="btn btn-primary" id="nextBtn">
                                        التالي
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    
                                    <button type="submit" class="btn btn-success d-none" id="submitBtn">
                                        <i class="fas fa-check"></i>
                                        تأكيد التسجيل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.registration-steps {
    margin-bottom: 2rem;
}

.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.step {
    flex: 1;
    text-align: center;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #e9ecef;
    z-index: 1;
}

.step.active:not(:last-child)::after {
    background-color: #007bff;
}

.step-number {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    font-weight: bold;
    position: relative;
    z-index: 2;
}

.step.active .step-number {
    background-color: #007bff;
    color: white;
}

.step-title {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.step.active .step-title {
    color: #007bff;
    font-weight: bold;
}

.document-upload-section {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    background-color: #f8f9fa;
}

.review-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 3;
    
    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    function showStep(step) {
        // إخفاء جميع الخطوات
        document.querySelectorAll('.step-content').forEach(content => {
            content.classList.add('d-none');
        });
        
        // إظهار الخطوة الحالية
        document.getElementById(`step-${step}`).classList.remove('d-none');
        
        // تحديث مؤشر الخطوات
        document.querySelectorAll('.step').forEach((stepEl, index) => {
            if (index + 1 <= step) {
                stepEl.classList.add('active');
            } else {
                stepEl.classList.remove('active');
            }
        });
        
        // تحديث الأزرار
        prevBtn.style.display = step === 1 ? 'none' : 'inline-block';
        
        if (step === totalSteps) {
            nextBtn.classList.add('d-none');
            submitBtn.classList.remove('d-none');
        } else {
            nextBtn.classList.remove('d-none');
            submitBtn.classList.add('d-none');
        }
    }
    
    nextBtn.addEventListener('click', function() {
        if (currentStep < totalSteps) {
            currentStep++;
            showStep(currentStep);
        }
    });
    
    prevBtn.addEventListener('click', function() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    });
    
    // تهيئة الخطوة الأولى
    showStep(currentStep);
});
</script>
@endsection

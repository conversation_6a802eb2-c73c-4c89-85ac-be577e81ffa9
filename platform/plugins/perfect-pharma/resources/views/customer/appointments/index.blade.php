@extends('plugins/perfect-pharma::customer.layout')

@section('title', 'مواعيدي - Perfect Pharma')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('customer.perfect-pharma.dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">المواعيد</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">مواعيدي</h2>
                <p class="text-muted mb-0">إدارة جميع مواعيدك الطبية</p>
            </div>
            <a href="{{ route('customer.perfect-pharma.appointments.create') }}" class="btn btn-primary">
                <i class="ti ti-calendar-plus me-2"></i>
                حجز موعد جديد
            </a>
        </div>
    </div>
</div>

{{-- فلاتر البحث --}}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status">
                            <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>جميع الحالات</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                            <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">التاريخ</label>
                        <select class="form-select" name="date_filter">
                            <option value="" {{ !request('date_filter') ? 'selected' : '' }}>جميع التواريخ</option>
                            <option value="upcoming" {{ request('date_filter') == 'upcoming' ? 'selected' : '' }}>المواعيد القادمة</option>
                            <option value="past" {{ request('date_filter') == 'past' ? 'selected' : '' }}>المواعيد السابقة</option>
                            <option value="today" {{ request('date_filter') == 'today' ? 'selected' : '' }}>اليوم</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-search me-1"></i>
                                بحث
                            </button>
                            <a href="{{ route('customer.perfect-pharma.appointments') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-refresh me-1"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{{-- قائمة المواعيد --}}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="ti ti-calendar me-2"></i>
                    قائمة المواعيد
                </h5>
            </div>
            <div class="card-body">
                @if($appointments->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الطبيب</th>
                                    <th>التاريخ والوقت</th>
                                    <th>نوع الموعد</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($appointments as $appointment)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center">
                                                <i class="ti ti-user-heart"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">
                                                    د. {{ $appointment->doctor->user->first_name }} {{ $appointment->doctor->user->last_name }}
                                                </h6>
                                                <small class="text-muted">{{ $appointment->doctor->specialization }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ \Carbon\Carbon::parse($appointment->appointment_date)->format('Y-m-d') }}</strong><br>
                                            <small class="text-muted">{{ $appointment->appointment_time }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        @switch($appointment->appointment_type)
                                            @case('consultation')
                                                <span class="badge bg-info">استشارة طبية</span>
                                                @break
                                            @case('follow_up')
                                                <span class="badge bg-warning">متابعة</span>
                                                @break
                                            @case('emergency')
                                                <span class="badge bg-danger">طارئ</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">غير محدد</span>
                                        @endswitch
                                    </td>
                                    <td>
                                        @switch($appointment->status)
                                            @case('pending')
                                                <span class="badge bg-warning">في الانتظار</span>
                                                @break
                                            @case('confirmed')
                                                <span class="badge bg-success">مؤكد</span>
                                                @break
                                            @case('completed')
                                                <span class="badge bg-primary">مكتمل</span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge bg-danger">ملغي</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">غير محدد</span>
                                        @endswitch
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('customer.perfect-pharma.appointments.show', $appointment->id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                            
                                            @if($appointment->status == 'pending' || $appointment->status == 'confirmed')
                                                @php
                                                    $appointmentDateTime = \Carbon\Carbon::parse($appointment->appointment_date . ' ' . $appointment->appointment_time);
                                                    $canCancel = $appointmentDateTime->diffInHours(now()) >= 24;
                                                @endphp
                                                
                                                @if($canCancel)
                                                    <button class="btn btn-sm btn-outline-danger cancel-appointment" 
                                                            data-id="{{ $appointment->id }}" title="إلغاء الموعد">
                                                        <i class="ti ti-x"></i>
                                                    </button>
                                                @endif
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    {{-- Pagination --}}
                    <div class="d-flex justify-content-center mt-4">
                        {{ $appointments->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="ti ti-calendar-off text-muted mb-3" style="font-size: 4rem;"></i>
                        <h5 class="text-muted">لا توجد مواعيد</h5>
                        <p class="text-muted">لم تقم بحجز أي مواعيد بعد</p>
                        <a href="{{ route('customer.perfect-pharma.appointments.create') }}" class="btn btn-primary">
                            <i class="ti ti-calendar-plus me-2"></i>
                            احجز موعدك الأول
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

{{-- إحصائيات سريعة --}}
@if($appointments->count() > 0)
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="ti ti-calendar mb-2" style="font-size: 2rem;"></i>
                <h4 class="mb-0">{{ $appointments->total() }}</h4>
                <small>إجمالي المواعيد</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="ti ti-calendar-check mb-2" style="font-size: 2rem;"></i>
                <h4 class="mb-0">{{ $appointments->where('status', 'confirmed')->count() }}</h4>
                <small>مواعيد مؤكدة</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="ti ti-clock mb-2" style="font-size: 2rem;"></i>
                <h4 class="mb-0">{{ $appointments->where('status', 'pending')->count() }}</h4>
                <small>في الانتظار</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="ti ti-check mb-2" style="font-size: 2rem;"></i>
                <h4 class="mb-0">{{ $appointments->where('status', 'completed')->count() }}</h4>
                <small>مكتملة</small>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@section('styles')
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group .btn {
    margin-left: 2px;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إلغاء الموعد
    document.querySelectorAll('.cancel-appointment').forEach(button => {
        button.addEventListener('click', function() {
            const appointmentId = this.dataset.id;
            
            if (confirm('هل أنت متأكد من إلغاء هذا الموعد؟')) {
                fetch(`{{ route('customer.perfect-pharma.appointments.cancel', '') }}/${appointmentId}`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('خطأ: ' + data.message);
                    } else {
                        alert('تم إلغاء الموعد بنجاح');
                        location.reload();
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }
        });
    });
});
</script>
@endsection

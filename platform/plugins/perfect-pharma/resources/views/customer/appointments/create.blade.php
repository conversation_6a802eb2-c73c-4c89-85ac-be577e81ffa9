@extends('plugins/perfect-pharma::customer.layout')

@section('title', 'حجز موعد جديد - Perfect Pharma')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('customer.perfect-pharma.dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="{{ route('customer.perfect-pharma.appointments') }}">المواعيد</a></li>
    <li class="breadcrumb-item active">حجز موعد جديد</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">حجز موعد جديد</h2>
                <p class="text-muted mb-0">احجز موعدك مع أفضل الأطباء المتخصصين</p>
            </div>
        </div>
    </div>
</div>

<form id="appointmentForm" method="POST" action="{{ route('customer.perfect-pharma.appointments.store') }}">
    @csrf
    <div class="row g-4">
        {{-- اختيار الطبيب --}}
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ti ti-user-heart me-2"></i>
                        اختيار الطبيب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">الطبيب المطلوب</label>
                        <select class="form-select" name="doctor_id" id="doctorSelect" required>
                            <option value="">اختر الطبيب</option>
                            @foreach($doctors as $doctor)
                            <option value="{{ $doctor->id }}" 
                                    data-specialization="{{ $doctor->specialization }}"
                                    data-fee="{{ $doctor->consultation_fee }}"
                                    {{ $selectedDoctor && $selectedDoctor->id == $doctor->id ? 'selected' : '' }}>
                                د. {{ $doctor->user->first_name }} {{ $doctor->user->last_name }}
                                - {{ $doctor->specialization }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    
                    {{-- معلومات الطبيب المختار --}}
                    <div id="doctorInfo" class="d-none">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-info-circle me-2"></i>
                                <div>
                                    <strong id="doctorName"></strong><br>
                                    <small>التخصص: <span id="doctorSpecialization"></span></small><br>
                                    <small>رسوم الكشف: <span id="doctorFee"></span> ريال</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {{-- تفاصيل الموعد --}}
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ti ti-calendar me-2"></i>
                        تفاصيل الموعد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">نوع الموعد</label>
                        <select class="form-select" name="appointment_type" required>
                            <option value="">اختر نوع الموعد</option>
                            <option value="consultation">استشارة طبية</option>
                            <option value="follow_up">متابعة</option>
                            <option value="emergency">طارئ</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="اكتب أي ملاحظات أو أعراض تريد إبلاغ الطبيب بها"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {{-- اختيار التاريخ والوقت --}}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ti ti-clock me-2"></i>
                        اختيار التاريخ والوقت
                    </h5>
                </div>
                <div class="card-body">
                    <div id="availableSlots">
                        <div class="text-center py-4">
                            <i class="ti ti-calendar text-muted mb-2" style="font-size: 3rem;"></i>
                            <p class="text-muted">اختر طبيباً أولاً لعرض الأوقات المتاحة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {{-- أزرار الإجراء --}}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('customer.perfect-pharma.appointments') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-right me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitButton" disabled>
                            <i class="ti ti-calendar-plus me-2"></i>
                            تأكيد الحجز
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@section('styles')
<style>
.time-slot {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.time-slot:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.time-slot.selected {
    border-color: #667eea;
    background-color: #667eea;
    color: white;
}

.time-slot.unavailable {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.day-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}
</style>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const doctorSelect = document.getElementById('doctorSelect');
    const doctorInfo = document.getElementById('doctorInfo');
    const availableSlots = document.getElementById('availableSlots');
    const submitButton = document.getElementById('submitButton');
    
    let selectedDate = null;
    let selectedTime = null;
    
    // عند تغيير الطبيب
    doctorSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (this.value) {
            // عرض معلومات الطبيب
            document.getElementById('doctorName').textContent = selectedOption.text.split(' - ')[0];
            document.getElementById('doctorSpecialization').textContent = selectedOption.dataset.specialization;
            document.getElementById('doctorFee').textContent = selectedOption.dataset.fee || 'غير محدد';
            doctorInfo.classList.remove('d-none');
            
            // تحميل الأوقات المتاحة
            loadAvailableSlots(this.value);
        } else {
            doctorInfo.classList.add('d-none');
            availableSlots.innerHTML = `
                <div class="text-center py-4">
                    <i class="ti ti-calendar text-muted mb-2" style="font-size: 3rem;"></i>
                    <p class="text-muted">اختر طبيباً أولاً لعرض الأوقات المتاحة</p>
                </div>
            `;
            submitButton.disabled = true;
        }
    });
    
    // تحميل الأوقات المتاحة
    function loadAvailableSlots(doctorId) {
        availableSlots.innerHTML = `
            <div class="text-center py-4">
                <i class="ti ti-loader-2 text-primary mb-2" style="font-size: 3rem; animation: spin 1s linear infinite;"></i>
                <p class="text-muted">جاري تحميل الأوقات المتاحة...</p>
            </div>
        `;
        
        // محاكاة تحميل الأوقات (في التطبيق الحقيقي سيتم استدعاء API)
        setTimeout(() => {
            generateAvailableSlots();
        }, 1000);
    }
    
    // إنشاء الأوقات المتاحة
    function generateAvailableSlots() {
        let slotsHtml = '';
        
        // إنشاء أوقات للأسبوع القادم
        for (let i = 1; i <= 7; i++) {
            const date = new Date();
            date.setDate(date.getDate() + i);
            
            // تجاهل الجمعة والسبت
            if (date.getDay() === 5 || date.getDay() === 6) continue;
            
            const dateStr = date.toISOString().split('T')[0];
            const dayName = date.toLocaleDateString('ar-SA', { weekday: 'long' });
            const dateFormatted = date.toLocaleDateString('ar-SA');
            
            slotsHtml += `
                <div class="day-header">
                    <h6 class="mb-0">${dayName} - ${dateFormatted}</h6>
                </div>
                <div class="row g-2 mb-4">
            `;
            
            // الفترة الصباحية
            slotsHtml += '<div class="col-12"><small class="text-muted fw-bold">الفترة الصباحية</small></div>';
            for (let hour = 9; hour < 12; hour++) {
                const time = `${hour.toString().padStart(2, '0')}:00`;
                const isAvailable = Math.random() > 0.3; // محاكاة التوفر
                
                slotsHtml += `
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                        <div class="time-slot ${isAvailable ? '' : 'unavailable'}" 
                             data-date="${dateStr}" 
                             data-time="${time}"
                             ${isAvailable ? '' : 'title="غير متاح"'}>
                            ${time}
                        </div>
                    </div>
                `;
            }
            
            // الفترة المسائية
            slotsHtml += '<div class="col-12 mt-3"><small class="text-muted fw-bold">الفترة المسائية</small></div>';
            for (let hour = 16; hour < 20; hour++) {
                const time = `${hour.toString().padStart(2, '0')}:00`;
                const isAvailable = Math.random() > 0.3; // محاكاة التوفر
                
                slotsHtml += `
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                        <div class="time-slot ${isAvailable ? '' : 'unavailable'}" 
                             data-date="${dateStr}" 
                             data-time="${time}"
                             ${isAvailable ? '' : 'title="غير متاح"'}>
                            ${time}
                        </div>
                    </div>
                `;
            }
            
            slotsHtml += '</div>';
        }
        
        availableSlots.innerHTML = slotsHtml;
        
        // إضافة مستمعي الأحداث للأوقات
        document.querySelectorAll('.time-slot:not(.unavailable)').forEach(slot => {
            slot.addEventListener('click', function() {
                // إزالة التحديد السابق
                document.querySelectorAll('.time-slot.selected').forEach(s => s.classList.remove('selected'));
                
                // تحديد الوقت الجديد
                this.classList.add('selected');
                selectedDate = this.dataset.date;
                selectedTime = this.dataset.time;
                
                // إضافة الحقول المخفية
                updateHiddenFields();
                
                // تفعيل زر الإرسال
                submitButton.disabled = false;
            });
        });
    }
    
    // تحديث الحقول المخفية
    function updateHiddenFields() {
        // إزالة الحقول القديمة
        document.querySelectorAll('input[name="appointment_date"], input[name="appointment_time"]').forEach(input => {
            input.remove();
        });
        
        // إضافة الحقول الجديدة
        const form = document.getElementById('appointmentForm');
        
        const dateInput = document.createElement('input');
        dateInput.type = 'hidden';
        dateInput.name = 'appointment_date';
        dateInput.value = selectedDate;
        form.appendChild(dateInput);
        
        const timeInput = document.createElement('input');
        timeInput.type = 'hidden';
        timeInput.name = 'appointment_time';
        timeInput.value = selectedTime;
        form.appendChild(timeInput);
    }
    
    // إرسال النموذج
    document.getElementById('appointmentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!selectedDate || !selectedTime) {
            alert('يرجى اختيار التاريخ والوقت');
            return;
        }
        
        const formData = new FormData(this);
        const submitButton = document.getElementById('submitButton');
        const originalText = submitButton.innerHTML;
        
        // تعطيل الزر وإظهار التحميل
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="ti ti-loader-2 me-2"></i>جاري الحجز...';
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('خطأ: ' + data.message);
            } else {
                alert('تم حجز الموعد بنجاح!');
                window.location.href = '{{ route("customer.perfect-pharma.appointments") }}';
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        });
    });
    
    // تحميل الأوقات إذا كان هناك طبيب محدد مسبقاً
    if (doctorSelect.value) {
        doctorSelect.dispatchEvent(new Event('change'));
    }
});
</script>
@endsection

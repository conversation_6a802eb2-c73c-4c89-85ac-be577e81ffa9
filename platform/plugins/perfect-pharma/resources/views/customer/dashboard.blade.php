@extends('plugins/perfect-pharma::customer.layout')

@section('title', 'لوحة التحكم - Perfect Pharma')

@section('breadcrumb')
    <li class="breadcrumb-item active">لوحة التحكم</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">مرحباً، {{ $customer->first_name }}</h2>
                <p class="text-muted mb-0">إليك ملخص حالتك الطبية والخدمات</p>
            </div>
            <div class="text-end">
                <small class="text-muted">آخر زيارة: {{ now()->format('Y-m-d H:i') }}</small>
            </div>
        </div>
    </div>
</div>

{{-- الإحصائيات السريعة --}}
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-primary me-3">
                    <i class="ti ti-calendar"></i>
                </div>
                <div>
                    <h3 class="mb-0">{{ $stats['appointments_count'] }}</h3>
                    <small class="text-muted">إجمالي المواعيد</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-success me-3">
                    <i class="ti ti-calendar-check"></i>
                </div>
                <div>
                    <h3 class="mb-0">{{ $stats['upcoming_appointments'] }}</h3>
                    <small class="text-muted">مواعيد قادمة</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-info me-3">
                    <i class="ti ti-prescription"></i>
                </div>
                <div>
                    <h3 class="mb-0">{{ $stats['prescriptions_count'] }}</h3>
                    <small class="text-muted">الوصفات الطبية</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-warning me-3">
                    <i class="ti ti-wallet"></i>
                </div>
                <div>
                    <h3 class="mb-0">{{ number_format($stats['wallet_balance']) }}</h3>
                    <small class="text-muted">رصيد المحفظة (ريال)</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    {{-- المواعيد القادمة --}}
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="ti ti-calendar me-2"></i>
                    المواعيد القادمة
                </h5>
                <a href="{{ route('customer.perfect-pharma.appointments') }}" class="btn btn-sm btn-light">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($upcomingAppointments->count() > 0)
                    @foreach($upcomingAppointments as $appointment)
                    <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                        <div class="me-3">
                            <div class="bg-primary text-white rounded p-2">
                                <i class="ti ti-user-heart"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                د. {{ $appointment->doctor->user->first_name }} {{ $appointment->doctor->user->last_name }}
                            </h6>
                            <small class="text-muted">
                                {{ \Carbon\Carbon::parse($appointment->appointment_date)->format('Y-m-d') }}
                                في {{ $appointment->appointment_time }}
                            </small>
                        </div>
                        <div>
                            <span class="badge bg-success">مؤكد</span>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="ti ti-calendar-off text-muted mb-2" style="font-size: 3rem;"></i>
                        <p class="text-muted">لا توجد مواعيد قادمة</p>
                        <a href="{{ route('customer.perfect-pharma.appointments.create') }}" class="btn btn-primary btn-sm">
                            احجز موعد جديد
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    {{-- آخر الوصفات --}}
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="ti ti-prescription me-2"></i>
                    آخر الوصفات الطبية
                </h5>
                <a href="{{ route('customer.perfect-pharma.prescriptions') }}" class="btn btn-sm btn-light">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($recentPrescriptions->count() > 0)
                    @foreach($recentPrescriptions as $prescription)
                    <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                        <div class="me-3">
                            <div class="bg-info text-white rounded p-2">
                                <i class="ti ti-prescription"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">وصفة طبية #{{ $prescription->id }}</h6>
                            <small class="text-muted">
                                د. {{ $prescription->doctor->user->first_name }} {{ $prescription->doctor->user->last_name }}
                                - {{ $prescription->created_at->format('Y-m-d') }}
                            </small>
                        </div>
                        <div>
                            <a href="{{ route('customer.perfect-pharma.prescriptions.show', $prescription->id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                عرض
                            </a>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="ti ti-prescription text-muted mb-2" style="font-size: 3rem;"></i>
                        <p class="text-muted">لا توجد وصفات طبية</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row g-4 mt-4">
    {{-- المعاملات المالية الأخيرة --}}
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="ti ti-credit-card me-2"></i>
                    آخر المعاملات المالية
                </h5>
                <a href="{{ route('customer.perfect-pharma.wallet.transactions') }}" class="btn btn-sm btn-light">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($recentTransactions->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentTransactions as $transaction)
                                <tr>
                                    <td>
                                        @if($transaction->type == 'credit')
                                            <span class="badge bg-success">إيداع</span>
                                        @else
                                            <span class="badge bg-danger">سحب</span>
                                        @endif
                                    </td>
                                    <td>{{ number_format($transaction->amount) }} ريال</td>
                                    <td>{{ $transaction->description }}</td>
                                    <td>{{ $transaction->created_at->format('Y-m-d') }}</td>
                                    <td>
                                        @if($transaction->status == 'completed')
                                            <span class="badge bg-success">مكتمل</span>
                                        @elseif($transaction->status == 'pending')
                                            <span class="badge bg-warning">معلق</span>
                                        @else
                                            <span class="badge bg-danger">فاشل</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="ti ti-credit-card text-muted mb-2" style="font-size: 3rem;"></i>
                        <p class="text-muted">لا توجد معاملات مالية</p>
                        <a href="{{ route('customer.perfect-pharma.wallet') }}" class="btn btn-primary btn-sm">
                            شحن المحفظة
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    {{-- الدورات المقترحة --}}
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="ti ti-school me-2"></i>
                    دورات مقترحة
                </h5>
            </div>
            <div class="card-body">
                @if($suggestedCourses->count() > 0)
                    @foreach($suggestedCourses->take(3) as $course)
                    <div class="mb-3 p-3 bg-light rounded">
                        <h6 class="mb-1">{{ Str::limit($course->title, 40) }}</h6>
                        <small class="text-muted mb-2 d-block">{{ Str::limit($course->description, 60) }}</small>
                        @if($course->price)
                            <span class="badge bg-warning text-dark">{{ number_format($course->price) }} ريال</span>
                        @else
                            <span class="badge bg-success">مجاني</span>
                        @endif
                    </div>
                    @endforeach
                    <div class="text-center mt-3">
                        <a href="{{ route('customer.perfect-pharma.academy') }}" class="btn btn-primary btn-sm">
                            عرض جميع الدورات
                        </a>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="ti ti-school text-muted mb-2" style="font-size: 3rem;"></i>
                        <p class="text-muted">لا توجد دورات متاحة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

{{-- أزرار الإجراءات السريعة --}}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="ti ti-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ route('customer.perfect-pharma.appointments.create') }}" 
                           class="btn btn-outline-primary w-100 p-3">
                            <i class="ti ti-calendar-plus d-block mb-2" style="font-size: 2rem;"></i>
                            احجز موعد
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ route('customer.perfect-pharma.lab-tests.create') }}" 
                           class="btn btn-outline-info w-100 p-3">
                            <i class="ti ti-flask d-block mb-2" style="font-size: 2rem;"></i>
                            احجز تحليل
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ route('customer.perfect-pharma.wallet') }}" 
                           class="btn btn-outline-warning w-100 p-3">
                            <i class="ti ti-wallet d-block mb-2" style="font-size: 2rem;"></i>
                            شحن المحفظة
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ route('customer.perfect-pharma.donations') }}" 
                           class="btn btn-outline-success w-100 p-3">
                            <i class="ti ti-heart d-block mb-2" style="font-size: 2rem;"></i>
                            تبرع خيري
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@extends('plugins/perfect-pharma::lab.layout')

@section('title', 'لوحة تحكم المختبر')
@section('page-title', 'لوحة التحكم')

@section('content')
<div class="row">
    <!-- Welcome Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">مرحباً، مختبر {{ $lab->name }}</h3>
                        <p class="text-muted mb-0">{{ $lab->address }}</p>
                        <small class="text-muted">آخر تسجيل دخول: {{ auth('customer')->user()->last_login_at?->diffForHumans() ?? 'الآن' }}</small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('lab.test-requests.index', ['status' => 'pending']) }}" class="btn btn-warning">
                                <i class="fas fa-clock me-1"></i>
                                الطلبات المعلقة
                            </a>
                            <a href="{{ route('lab.results.index', ['status' => 'ready']) }}" class="btn btn-success">
                                <i class="fas fa-file-medical me-1"></i>
                                النتائج الجاهزة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-flask fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_requests'] }}</h3>
                <p class="mb-0">إجمالي الطلبات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['pending_tests'] }}</h3>
                <p class="mb-0">تحاليل معلقة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['completed_tests_today'] }}</h3>
                <p class="mb-0">مكتملة اليوم</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card danger">
            <div class="card-body text-center">
                <i class="fas fa-file-medical fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['ready_results'] }}</h3>
                <p class="mb-0">نتائج جاهزة</p>
            </div>
        </div>
    </div>
</div>

<!-- Revenue and Patients Cards -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h4 class="text-success">{{ number_format($stats['monthly_revenue']) }} ر.س</h4>
                <p class="text-muted mb-0">إيرادات الشهر</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h4 class="text-info">{{ $stats['total_patients'] }}</h4>
                <p class="text-muted mb-0">إجمالي المرضى</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Test Requests -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-flask me-2"></i>
                    آخر طلبات التحاليل
                </h5>
                <a href="{{ route('lab.test-requests.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($recentTestRequests->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentTestRequests as $request)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $request->labTest->name }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ $request->patient->user->name }}
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-user-md me-1"></i>
                                        د. {{ $request->doctor->user->name }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    @php
                                        $statusColors = [
                                            'pending' => 'warning',
                                            'accepted' => 'info',
                                            'in_progress' => 'primary',
                                            'completed' => 'success',
                                            'rejected' => 'danger'
                                        ];
                                        $statusTexts = [
                                            'pending' => 'معلق',
                                            'accepted' => 'مقبول',
                                            'in_progress' => 'قيد التنفيذ',
                                            'completed' => 'مكتمل',
                                            'rejected' => 'مرفوض'
                                        ];
                                    @endphp
                                    <span class="badge bg-{{ $statusColors[$request->status] ?? 'secondary' }}">
                                        {{ $statusTexts[$request->status] ?? $request->status }}
                                    </span>
                                    <br>
                                    <small class="text-muted">{{ $request->created_at->diffForHumans() }}</small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-flask fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد طلبات حديثة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Pending Tests -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    التحاليل المعلقة
                </h5>
                <a href="{{ route('lab.test-requests.index', ['status' => 'pending']) }}" class="btn btn-sm btn-outline-warning">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($pendingTests->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($pendingTests as $test)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $test->labTest->name }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ $test->patient->user->name }}
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $test->created_at->diffForHumans() }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    @if($test->priority)
                                        <span class="badge priority-{{ $test->priority }}">
                                            {{ ucfirst($test->priority) }}
                                        </span>
                                        <br>
                                    @endif
                                    <a href="{{ route('lab.test-requests.show', $test->id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        عرض
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">لا توجد تحاليل معلقة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Ready Results -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-medical me-2"></i>
                    النتائج الجاهزة للتسليم
                </h5>
                <a href="{{ route('lab.results.index', ['status' => 'ready']) }}" class="btn btn-sm btn-outline-success">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($readyResults->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التحليل</th>
                                    <th>المريض</th>
                                    <th>تاريخ الإكمال</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($readyResults as $result)
                                    <tr>
                                        <td>
                                            <strong>{{ $result->testRequest->labTest->name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $result->testRequest->request_code }}</small>
                                        </td>
                                        <td>
                                            {{ $result->testRequest->patient->user->name }}
                                            <br>
                                            <small class="text-muted">{{ $result->testRequest->patient->user->phone }}</small>
                                        </td>
                                        <td>{{ $result->completed_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('lab.results.show', $result->id) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('lab.results.print', $result->id) }}" 
                                                   class="btn btn-sm btn-outline-info" title="طباعة" target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-success deliver-result" 
                                                        data-id="{{ $result->id }}" title="تسليم">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد نتائج جاهزة للتسليم</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Quick Actions & Popular Tests -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('lab.test-requests.index', ['status' => 'pending']) }}" class="btn btn-warning">
                        <i class="fas fa-clock me-2"></i>
                        الطلبات المعلقة ({{ $stats['pending_tests'] }})
                    </a>
                    
                    <a href="{{ route('lab.results.index', ['status' => 'ready']) }}" class="btn btn-success">
                        <i class="fas fa-file-medical me-2"></i>
                        النتائج الجاهزة ({{ $stats['ready_results'] }})
                    </a>
                    
                    <a href="{{ route('lab.reports.index') }}" class="btn btn-info">
                        <i class="fas fa-chart-line me-2"></i>
                        التقارير
                    </a>
                    
                    <a href="{{ route('lab.quality.index') }}" class="btn btn-primary">
                        <i class="fas fa-award me-2"></i>
                        ضمان الجودة
                    </a>
                </div>
                
                <hr>
                
                <h6 class="text-muted">أكثر التحاليل طلباً هذا الشهر</h6>
                @if($popularTests->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($popularTests as $test)
                            <div class="list-group-item d-flex justify-content-between align-items-center p-2">
                                <small>{{ $test->name }}</small>
                                <span class="badge bg-primary">{{ $test->requests_count }}</span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted small">لا توجد بيانات كافية</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تسليم النتيجة
    $('.deliver-result').click(function() {
        const resultId = $(this).data('id');
        
        if (confirm('هل تريد تأكيد تسليم هذه النتيجة؟')) {
            $.post(`/lab/results/${resultId}/deliver`, {
                _token: $('meta[name="csrf-token"]').attr('content')
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                }
            })
            .fail(function() {
                alert('حدث خطأ أثناء تسليم النتيجة');
            });
        }
    });
    
    // Auto refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
@endpush

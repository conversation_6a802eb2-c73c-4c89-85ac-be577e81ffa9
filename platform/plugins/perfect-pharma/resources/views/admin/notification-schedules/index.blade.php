@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-calendar-time me-2"></i>
                        جدولة الإشعارات
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.notification-schedules.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة جدولة جديدة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ count($schedules) }}</h3>
                                            <p class="mb-0">إجمالي الجدولات</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-calendar-time fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ collect($schedules)->where('status', 'active')->count() }}</h3>
                                            <p class="mb-0">جدولات نشطة</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-check fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ collect($schedules)->where('status', 'paused')->count() }}</h3>
                                            <p class="mb-0">جدولات متوقفة</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-pause fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ collect($schedules)->where('next_run', '<=', now())->count() }}</h3>
                                            <p class="mb-0">جدولات مستحقة</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-clock fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات السريعة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success" onclick="runDueSchedules()">
                                    <i class="ti ti-play me-1"></i>
                                    تشغيل الجدولات المستحقة
                                </button>
                                <button type="button" class="btn btn-info" onclick="createDefaults()">
                                    <i class="ti ti-settings me-1"></i>
                                    إنشاء جدولات افتراضية
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="refreshPage()">
                                    <i class="ti ti-refresh me-1"></i>
                                    تحديث
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الجدولات -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>التكرار</th>
                                    <th>الوقت</th>
                                    <th>الحالة</th>
                                    <th>آخر تشغيل</th>
                                    <th>التشغيل التالي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($schedules as $schedule)
                                    <tr>
                                        <td>
                                            <strong>{{ $schedule['name'] }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $schedule['type'] }}</span>
                                        </td>
                                        <td>{{ $schedule['frequency'] }}</td>
                                        <td>{{ $schedule['time'] }}</td>
                                        <td>
                                            @if($schedule['status'] === 'active')
                                                <span class="badge bg-success">نشط</span>
                                            @elseif($schedule['status'] === 'paused')
                                                <span class="badge bg-warning">متوقف</span>
                                            @else
                                                <span class="badge bg-danger">معطل</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($schedule['last_run'])
                                                {{ \Carbon\Carbon::parse($schedule['last_run'])->diffForHumans() }}
                                            @else
                                                <span class="text-muted">لم يتم التشغيل</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($schedule['next_run'])
                                                {{ \Carbon\Carbon::parse($schedule['next_run'])->diffForHumans() }}
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.notification-schedules.show', $schedule['id']) }}" 
                                                   class="btn btn-sm btn-info" title="عرض">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.notification-schedules.edit', $schedule['id']) }}" 
                                                   class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                                @if($schedule['status'] === 'active')
                                                    <button type="button" class="btn btn-sm btn-secondary" 
                                                            onclick="pauseSchedule({{ $schedule['id'] }})" title="إيقاف مؤقت">
                                                        <i class="ti ti-pause"></i>
                                                    </button>
                                                @else
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            onclick="resumeSchedule({{ $schedule['id'] }})" title="استئناف">
                                                        <i class="ti ti-play"></i>
                                                    </button>
                                                @endif
                                                <button type="button" class="btn btn-sm btn-primary" 
                                                        onclick="runSchedule({{ $schedule['id'] }})" title="تشغيل الآن">
                                                    <i class="ti ti-player-play"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteSchedule({{ $schedule['id'] }})" title="حذف">
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-calendar-off fs-1 mb-2"></i>
                                                <p>لا توجد جدولات إشعارات</p>
                                                <a href="{{ route('admin.notification-schedules.create') }}" class="btn btn-primary">
                                                    إضافة جدولة جديدة
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
function runDueSchedules() {
    if (confirm('هل تريد تشغيل جميع الجدولات المستحقة؟')) {
        fetch('{{ route("admin.notification-schedules.run-due") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        });
    }
}

function createDefaults() {
    if (confirm('هل تريد إنشاء الجدولات الافتراضية؟')) {
        fetch('{{ route("admin.notification-schedules.create-defaults") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        });
    }
}

function runSchedule(id) {
    if (confirm('هل تريد تشغيل هذه الجدولة الآن؟')) {
        fetch(`{{ route("admin.notification-schedules.run", ":id") }}`.replace(':id', id), {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        });
    }
}

function pauseSchedule(id) {
    fetch(`{{ route("admin.notification-schedules.pause", ":id") }}`.replace(':id', id), {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    });
}

function resumeSchedule(id) {
    fetch(`{{ route("admin.notification-schedules.resume", ":id") }}`.replace(':id', id), {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    });
}

function deleteSchedule(id) {
    if (confirm('هل تريد حذف هذه الجدولة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`{{ route("admin.notification-schedules.destroy", ":id") }}`.replace(':id', id), {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        });
    }
}

function refreshPage() {
    location.reload();
}
</script>
@endpush

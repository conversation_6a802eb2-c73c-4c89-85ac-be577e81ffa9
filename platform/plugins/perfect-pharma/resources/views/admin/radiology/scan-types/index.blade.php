@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-scan me-2"></i>
                        أنواع الأشعة الطبية
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.radiology.scan-types.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة نوع جديد
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>نوع الأشعة</th>
                                    <th>الكود</th>
                                    <th>الفئة</th>
                                    <th>السعر الأساسي</th>
                                    <th>المدة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($scanTypes as $scanType)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $scanType->name }}</strong>
                                                @if($scanType->description)
                                                    <br><small class="text-muted">{{ Str::limit($scanType->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <code>{{ $scanType->code }}</code>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $scanType->category_name }}</span>
                                        </td>
                                        <td>
                                            @if($scanType->base_price)
                                                <span class="text-success">{{ number_format($scanType->base_price, 2) }} ر.س</span>
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $scanType->duration_formatted }}</span>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox"
                                                       {{ $scanType->is_active ? 'checked' : '' }}
                                                       onchange="toggleScanType({{ $scanType->id }})">
                                                <label class="form-check-label">
                                                    {{ $scanType->is_active ? 'نشط' : 'غير نشط' }}
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.radiology.scan-types.show', $scanType->id) }}"
                                                   class="btn btn-sm btn-outline-info" title="عرض">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.radiology.scan-types.edit', $scanType->id) }}"
                                                   class="btn btn-sm btn-outline-primary" title="تعديل">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteScanType({{ $scanType->id }})" title="حذف">
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-scan fs-1 mb-3"></i>
                                                <h5>لا توجد أنواع أشعة</h5>
                                                <p>لم يتم إضافة أي أنواع أشعة بعد</p>
                                                <a href="{{ route('admin.radiology.scan-types.create') }}" class="btn btn-primary">
                                                    <i class="ti ti-plus me-1"></i>
                                                    إضافة نوع أشعة جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if($scanTypes->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $scanTypes->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function toggleScanType(scanTypeId) {
            $.ajax({
                url: `/admin/perfect-pharma/radiology/scan-types/${scanTypeId}/toggle`,
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء تغيير الحالة');
                }
            });
        }

        function deleteScanType(scanTypeId) {
            if (confirm('هل أنت متأكد من حذف نوع الأشعة هذا؟')) {
                $.ajax({
                    url: `/admin/perfect-pharma/radiology/scan-types/${scanTypeId}`,
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء حذف نوع الأشعة');
                    }
                });
            }
        }
    </script>
@endpush
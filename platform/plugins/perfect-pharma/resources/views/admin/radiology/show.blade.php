@extends('core/base::layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تفاصيل مركز الأشعة: {{ $radiologyCenter->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.radiology.index') }}">مراكز الأشعة</a></li>
                        <li class="breadcrumb-item active">{{ $radiologyCenter->name }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات المركز الأساسية -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات المركز</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المركز</label>
                                <p class="form-control-static">{{ $radiologyCenter->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">كود المركز</label>
                                <p class="form-control-static">{{ $radiologyCenter->center_code }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الترخيص</label>
                                <p class="form-control-static">{{ $radiologyCenter->license_number }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الهاتف</label>
                                <p class="form-control-static">{{ $radiologyCenter->phone }}</p>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <p class="form-control-static">{{ $radiologyCenter->address }}</p>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <p class="form-control-static">{{ $radiologyCenter->description ?? 'لا يوجد وصف' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات المركز -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إحصائيات المركز</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ $radiologyCenter->radiologyRequests->count() }}</h4>
                                <p class="text-muted mb-0">إجمالي الطلبات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ $radiologyCenter->radiologyRequests->where('status', 'completed')->count() }}</h4>
                                <p class="text-muted mb-0">طلبات مكتملة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">{{ $radiologyCenter->radiologyRequests->where('status', 'pending')->count() }}</h4>
                                <p class="text-muted mb-0">طلبات معلقة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ $radiologyCenter->radiologyRequests->where('status', 'in_progress')->count() }}</h4>
                                <p class="text-muted mb-0">قيد التنفيذ</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- آخر الطلبات -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">آخر الطلبات</h5>
                    <a href="{{ route('admin.radiology.radiology-interface.requests.index') }}?center_id={{ $radiologyCenter->id }}" class="btn btn-sm btn-primary">
                        عرض جميع الطلبات
                    </a>
                </div>
                <div class="card-body">
                    @if($radiologyCenter->radiologyRequests->take(5)->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-nowrap">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>المريض</th>
                                        <th>نوع الأشعة</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($radiologyCenter->radiologyRequests->take(5) as $request)
                                    <tr>
                                        <td>{{ $request->request_number }}</td>
                                        <td>{{ $request->patient->name ?? 'غير محدد' }}</td>
                                        <td>{{ $request->scanType->name ?? 'غير محدد' }}</td>
                                        <td>
                                            <span class="badge bg-{{ $request->status_color }}">
                                                {{ $request->status_text }}
                                            </span>
                                        </td>
                                        <td>{{ $request->created_at->format('Y-m-d') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="ri-file-list-3-line fs-1 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد طلبات حتى الآن</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- حالة المركز -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">حالة المركز</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">حالة التحقق</label>
                        <div>
                            @if($radiologyCenter->is_verified)
                                <span class="badge bg-success fs-6">
                                    <i class="ri-check-line me-1"></i>
                                    محقق
                                </span>
                            @else
                                <span class="badge bg-warning fs-6">
                                    <i class="ri-time-line me-1"></i>
                                    في انتظار التحقق
                                </span>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">حالة النشاط</label>
                        <div>
                            @if($radiologyCenter->is_active)
                                <span class="badge bg-success fs-6">
                                    <i class="ri-play-line me-1"></i>
                                    نشط
                                </span>
                            @else
                                <span class="badge bg-danger fs-6">
                                    <i class="ri-pause-line me-1"></i>
                                    غير نشط
                                </span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">تاريخ التسجيل</label>
                        <p class="form-control-static">{{ $radiologyCenter->created_at->format('Y-m-d H:i') }}</p>
                    </div>

                    @if($radiologyCenter->verified_at)
                    <div class="mb-3">
                        <label class="form-label">تاريخ التحقق</label>
                        <p class="form-control-static">{{ $radiologyCenter->verified_at->format('Y-m-d H:i') }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- معلومات المالك -->
            @if($radiologyCenter->user)
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات المالك</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">الاسم</label>
                        <p class="form-control-static">{{ $radiologyCenter->user->name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <p class="form-control-static">{{ $radiologyCenter->user->email }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الهاتف</label>
                        <p class="form-control-static">{{ $radiologyCenter->user->phone ?? 'غير محدد' }}</p>
                    </div>
                </div>
            </div>
            @endif

            <!-- إجراءات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.radiology.edit', $radiologyCenter->id) }}" class="btn btn-primary">
                            <i class="ri-edit-line me-1"></i>
                            تعديل المركز
                        </a>
                        
                        @if(!$radiologyCenter->is_verified)
                        <button type="button" class="btn btn-success" onclick="verifyCenter({{ $radiologyCenter->id }})">
                            <i class="ri-check-line me-1"></i>
                            تحقق من المركز
                        </button>
                        @endif

                        <button type="button" class="btn btn-{{ $radiologyCenter->is_active ? 'warning' : 'success' }}" 
                                onclick="toggleStatus({{ $radiologyCenter->id }})">
                            <i class="ri-{{ $radiologyCenter->is_active ? 'pause' : 'play' }}-line me-1"></i>
                            {{ $radiologyCenter->is_active ? 'إيقاف' : 'تفعيل' }} المركز
                        </button>

                        <a href="{{ route('admin.radiology.radiology-interface.requests.index') }}?center_id={{ $radiologyCenter->id }}" 
                           class="btn btn-info">
                            <i class="ri-file-list-3-line me-1"></i>
                            عرض الطلبات
                        </a>

                        <button type="button" class="btn btn-danger" onclick="deleteCenter({{ $radiologyCenter->id }})">
                            <i class="ri-delete-bin-line me-1"></i>
                            حذف المركز
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function verifyCenter(id) {
    if (confirm('هل أنت متأكد من تحقق هذا المركز؟')) {
        fetch(`/admin/perfect-pharma/radiology/${id}/verify`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء التحقق');
            }
        });
    }
}

function toggleStatus(id) {
    fetch(`/admin/perfect-pharma/radiology/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء تغيير الحالة');
        }
    });
}

function deleteCenter(id) {
    if (confirm('هل أنت متأكد من حذف هذا المركز؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/admin/perfect-pharma/radiology/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{{ route("admin.radiology.index") }}';
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        });
    }
}
</script>
@endpush
@endsection

@extends('core/base::layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">إدارة مراكز الأشعة</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">مراكز الأشعة</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 overflow-hidden">
                            <p class="text-uppercase fw-medium text-muted text-truncate mb-0">إجمالي المراكز</p>
                        </div>
                        <div class="flex-shrink-0">
                            <h5 class="text-success fs-14 mb-0">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i>
                            </h5>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                <span class="counter-value" data-target="{{ $stats['total'] }}">{{ $stats['total'] }}</span>
                            </h4>
                            <a href="#" class="text-decoration-underline">عرض الكل</a>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-success-subtle rounded fs-3">
                                <i class="bx bx-scan text-success"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 overflow-hidden">
                            <p class="text-uppercase fw-medium text-muted text-truncate mb-0">مراكز محققة</p>
                        </div>
                        <div class="flex-shrink-0">
                            <h5 class="text-success fs-14 mb-0">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i>
                            </h5>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                <span class="counter-value" data-target="{{ $stats['verified'] }}">{{ $stats['verified'] }}</span>
                            </h4>
                            <a href="?status=verified" class="text-decoration-underline">عرض المحققة</a>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-info-subtle rounded fs-3">
                                <i class="bx bx-shield-check text-info"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 overflow-hidden">
                            <p class="text-uppercase fw-medium text-muted text-truncate mb-0">غير محققة</p>
                        </div>
                        <div class="flex-shrink-0">
                            <h5 class="text-danger fs-14 mb-0">
                                <i class="ri-arrow-right-down-line fs-13 align-middle"></i>
                            </h5>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                <span class="counter-value" data-target="{{ $stats['unverified'] }}">{{ $stats['unverified'] }}</span>
                            </h4>
                            <a href="?status=unverified" class="text-decoration-underline">عرض غير المحققة</a>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-warning-subtle rounded fs-3">
                                <i class="bx bx-error text-warning"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 overflow-hidden">
                            <p class="text-uppercase fw-medium text-muted text-truncate mb-0">مراكز نشطة</p>
                        </div>
                        <div class="flex-shrink-0">
                            <h5 class="text-success fs-14 mb-0">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i>
                            </h5>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                <span class="counter-value" data-target="{{ $stats['active'] }}">{{ $stats['active'] }}</span>
                            </h4>
                            <a href="?status=active" class="text-decoration-underline">عرض النشطة</a>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-primary-subtle rounded fs-3">
                                <i class="bx bx-check text-primary"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول مراكز الأشعة -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center flex-wrap gap-2">
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-0">قائمة مراكز الأشعة</h5>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="d-flex flex-wrap align-items-start gap-2">
                                <button type="button" class="btn btn-soft-danger" id="remove-actions" style="display: none;">
                                    <i class="ri-delete-bin-2-line"></i>
                                </button>
                                <a href="{{ route('admin.radiology.create') }}" class="btn btn-primary">
                                    <i class="ri-add-line align-bottom me-1"></i> إضافة مركز جديد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <form method="GET" class="row g-3 mb-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="البحث بالاسم، الكود، أو الترخيص..." 
                                   value="{{ request('search') }}">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="verified" {{ request('status') === 'verified' ? 'selected' : '' }}>محقق</option>
                                <option value="unverified" {{ request('status') === 'unverified' ? 'selected' : '' }}>غير محقق</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>نشط</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="ri-search-line me-1"></i> بحث
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ route('admin.radiology.index') }}" class="btn btn-soft-secondary w-100">
                                <i class="ri-refresh-line me-1"></i> مسح
                            </a>
                        </div>
                    </form>

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-nowrap align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkAll">
                                        </div>
                                    </th>
                                    <th scope="col">المركز</th>
                                    <th scope="col">معلومات الاتصال</th>
                                    <th scope="col">الحالة</th>
                                    <th scope="col">تاريخ التسجيل</th>
                                    <th scope="col">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($radiologyCenters as $center)
                                <tr>
                                    <th scope="row">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="{{ $center->id }}">
                                        </div>
                                    </th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-light rounded p-1 me-2">
                                                <i class="bx bx-scan fs-4 text-primary"></i>
                                            </div>
                                            <div>
                                                <h5 class="fs-13 mb-0">
                                                    <a href="{{ route('admin.radiology.show', $center->id) }}" class="text-body">
                                                        {{ $center->name }}
                                                    </a>
                                                </h5>
                                                <p class="fs-12 mb-0 text-muted">{{ $center->center_code }}</p>
                                                <p class="fs-12 mb-0 text-muted">ترخيص: {{ $center->license_number }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="mb-0">{{ $center->phone }}</p>
                                        <p class="mb-0 text-muted">{{ $center->email }}</p>
                                        <p class="mb-0 text-muted">{{ Str::limit($center->address, 30) }}</p>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column gap-1">
                                            @if($center->is_verified)
                                                <span class="badge bg-success-subtle text-success">محقق</span>
                                            @else
                                                <span class="badge bg-warning-subtle text-warning">غير محقق</span>
                                            @endif
                                            
                                            @if($center->is_active)
                                                <span class="badge bg-primary-subtle text-primary">نشط</span>
                                            @else
                                                <span class="badge bg-secondary-subtle text-secondary">غير نشط</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $center->created_at->format('Y-m-d') }}</span>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-soft-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="ri-more-fill"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('admin.radiology.show', $center->id) }}">
                                                        <i class="ri-eye-fill align-bottom me-2 text-muted"></i> عرض
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('admin.radiology.edit', $center->id) }}">
                                                        <i class="ri-pencil-fill align-bottom me-2 text-muted"></i> تعديل
                                                    </a>
                                                </li>
                                                <li class="dropdown-divider"></li>
                                                <li>
                                                    <button class="dropdown-item toggle-status" data-id="{{ $center->id }}">
                                                        <i class="ri-toggle-line align-bottom me-2 text-muted"></i>
                                                        {{ $center->is_active ? 'إلغاء التفعيل' : 'تفعيل' }}
                                                    </button>
                                                </li>
                                                <li>
                                                    <button class="dropdown-item text-danger delete-center" data-id="{{ $center->id }}">
                                                        <i class="ri-delete-bin-fill align-bottom me-2"></i> حذف
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="ri-search-line fs-1 text-muted"></i>
                                            <h5 class="mt-2">لا توجد مراكز أشعة</h5>
                                            <p class="text-muted">لم يتم العثور على أي مراكز أشعة مطابقة للبحث</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($radiologyCenters->hasPages())
                        <div class="d-flex justify-content-end mt-3">
                            {{ $radiologyCenters->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تفعيل/إلغاء تفعيل مركز
    $('.toggle-status').click(function() {
        const centerId = $(this).data('id');
        
        if (confirm('هل تريد تغيير حالة هذا المركز؟')) {
            $.post(`/admin/perfect-pharma/radiology/${centerId}/toggle-status`, {
                _token: $('meta[name="csrf-token"]').attr('content')
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                }
            })
            .fail(function() {
                alert('حدث خطأ أثناء تغيير الحالة');
            });
        }
    });
    
    // حذف مركز
    $('.delete-center').click(function() {
        const centerId = $(this).data('id');
        
        if (confirm('هل تريد حذف هذا المركز؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            $.ajax({
                url: `/admin/perfect-pharma/radiology/${centerId}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                }
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                }
            })
            .fail(function() {
                alert('حدث خطأ أثناء الحذف');
            });
        }
    });
});
</script>
@endpush

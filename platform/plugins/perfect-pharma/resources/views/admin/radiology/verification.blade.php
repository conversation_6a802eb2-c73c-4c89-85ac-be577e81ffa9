@extends('core/base::layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">طلبات التحقق من مراكز الأشعة</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.radiology.index') }}">مراكز الأشعة</a></li>
                        <li class="breadcrumb-item active">طلبات التحقق</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-xl-4 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 overflow-hidden">
                            <p class="text-uppercase fw-medium text-muted text-truncate mb-0">طلبات معلقة</p>
                        </div>
                        <div class="flex-shrink-0">
                            <h5 class="text-warning fs-14 mb-0">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i>
                            </h5>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                <span class="counter-value" data-target="{{ $verificationRequests->where('is_verified', false)->count() }}">
                                    {{ $verificationRequests->where('is_verified', false)->count() }}
                                </span>
                            </h4>
                            <a href="?verification_status=pending" class="text-decoration-underline">عرض المعلقة</a>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-warning-subtle rounded fs-3">
                                <i class="bx bx-time text-warning"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 overflow-hidden">
                            <p class="text-uppercase fw-medium text-muted text-truncate mb-0">طلبات محققة</p>
                        </div>
                        <div class="flex-shrink-0">
                            <h5 class="text-success fs-14 mb-0">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i>
                            </h5>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                <span class="counter-value" data-target="{{ $verificationRequests->where('is_verified', true)->count() }}">
                                    {{ $verificationRequests->where('is_verified', true)->count() }}
                                </span>
                            </h4>
                            <a href="?verification_status=verified" class="text-decoration-underline">عرض المحققة</a>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-success-subtle rounded fs-3">
                                <i class="bx bx-check text-success"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 overflow-hidden">
                            <p class="text-uppercase fw-medium text-muted text-truncate mb-0">إجمالي الطلبات</p>
                        </div>
                        <div class="flex-shrink-0">
                            <h5 class="text-info fs-14 mb-0">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i>
                            </h5>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                <span class="counter-value" data-target="{{ $verificationRequests->count() }}">
                                    {{ $verificationRequests->count() }}
                                </span>
                            </h4>
                            <a href="{{ route('admin.radiology.verification') }}" class="text-decoration-underline">عرض الكل</a>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-info-subtle rounded fs-3">
                                <i class="bx bx-scan text-info"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول طلبات التحقق -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center flex-wrap gap-2">
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-0">طلبات التحقق من مراكز الأشعة</h5>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="d-flex flex-wrap align-items-start gap-2">
                                <button type="button" class="btn btn-soft-success" id="approve-selected" style="display: none;">
                                    <i class="ri-check-line"></i> موافقة مجمعة
                                </button>
                                <button type="button" class="btn btn-soft-danger" id="reject-selected" style="display: none;">
                                    <i class="ri-close-line"></i> رفض مجمع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <form method="GET" class="row g-3 mb-3">
                        <div class="col-md-6">
                            <select class="form-select" name="verification_status">
                                <option value="">جميع الطلبات</option>
                                <option value="pending" {{ request('verification_status') === 'pending' ? 'selected' : '' }}>معلقة</option>
                                <option value="verified" {{ request('verification_status') === 'verified' ? 'selected' : '' }}>محققة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="ri-search-line me-1"></i> فلترة
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.radiology.verification') }}" class="btn btn-soft-secondary w-100">
                                <i class="ri-refresh-line me-1"></i> مسح
                            </a>
                        </div>
                    </form>

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-nowrap align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkAll">
                                        </div>
                                    </th>
                                    <th scope="col">معلومات المركز</th>
                                    <th scope="col">معلومات الاتصال</th>
                                    <th scope="col">حالة التحقق</th>
                                    <th scope="col">تاريخ الطلب</th>
                                    <th scope="col">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($verificationRequests as $request)
                                <tr>
                                    <th scope="row">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="{{ $request->id }}">
                                        </div>
                                    </th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-light rounded p-1 me-2">
                                                <i class="bx bx-scan fs-4 text-primary"></i>
                                            </div>
                                            <div>
                                                <h5 class="fs-13 mb-0">{{ $request->customer->name }}</h5>
                                                <p class="fs-12 mb-0 text-muted">{{ $request->userType->display_name }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="mb-0">{{ $request->customer->phone }}</p>
                                        <p class="mb-0 text-muted">{{ $request->customer->email }}</p>
                                    </td>
                                    <td>
                                        @if($request->is_verified)
                                            <span class="badge bg-success-subtle text-success">
                                                <i class="ri-check-line me-1"></i>محقق
                                            </span>
                                            @if($request->verified_at)
                                                <br><small class="text-muted">{{ $request->verified_at->diffForHumans() }}</small>
                                            @endif
                                        @else
                                            <span class="badge bg-warning-subtle text-warning">
                                                <i class="ri-time-line me-1"></i>معلق
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $request->created_at->format('Y-m-d') }}</span>
                                        <br>
                                        <small class="text-muted">{{ $request->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-soft-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="ri-more-fill"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('customer.show', $request->customer->id) }}">
                                                        <i class="ri-eye-fill align-bottom me-2 text-muted"></i> عرض التفاصيل
                                                    </a>
                                                </li>
                                                @if(!$request->is_verified)
                                                    <li class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item text-success verify-request" data-id="{{ $request->id }}">
                                                            <i class="ri-check-fill align-bottom me-2"></i> موافقة
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item text-danger reject-request" data-id="{{ $request->id }}">
                                                            <i class="ri-close-fill align-bottom me-2"></i> رفض
                                                        </button>
                                                    </li>
                                                @endif
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="ri-search-line fs-1 text-muted"></i>
                                            <h5 class="mt-2">لا توجد طلبات تحقق</h5>
                                            <p class="text-muted">لم يتم العثور على أي طلبات تحقق مطابقة للبحث</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($verificationRequests->hasPages())
                        <div class="d-flex justify-content-end mt-3">
                            {{ $verificationRequests->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal رفض الطلب -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب التحقق</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="reason" rows="4" required 
                                  placeholder="يرجى توضيح سبب رفض طلب التحقق..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطلب</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let currentRequestId = null;
    
    // موافقة على طلب
    $('.verify-request').click(function() {
        const requestId = $(this).data('id');
        
        if (confirm('هل تريد الموافقة على هذا الطلب؟')) {
            $.post(`/admin/perfect-pharma/radiology/${requestId}/verify`, {
                _token: $('meta[name="csrf-token"]').attr('content')
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                }
            })
            .fail(function() {
                alert('حدث خطأ أثناء الموافقة على الطلب');
            });
        }
    });
    
    // رفض طلب
    $('.reject-request').click(function() {
        currentRequestId = $(this).data('id');
        $('#rejectModal').modal('show');
    });
    
    // إرسال نموذج الرفض
    $('#rejectForm').submit(function(e) {
        e.preventDefault();
        
        const reason = $('#rejection_reason').val();
        
        if (!reason.trim()) {
            alert('يرجى إدخال سبب الرفض');
            return;
        }
        
        $.post(`/admin/perfect-pharma/radiology/${currentRequestId}/reject`, {
            _token: $('meta[name="csrf-token"]').attr('content'),
            reason: reason
        })
        .done(function(response) {
            if (response.success) {
                $('#rejectModal').modal('hide');
                location.reload();
            }
        })
        .fail(function() {
            alert('حدث خطأ أثناء رفض الطلب');
        });
    });
    
    // إخفاء النموذج عند الإغلاق
    $('#rejectModal').on('hidden.bs.modal', function() {
        $('#rejection_reason').val('');
        currentRequestId = null;
    });
});
</script>
@endpush

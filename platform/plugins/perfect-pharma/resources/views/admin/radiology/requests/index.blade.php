@extends('core/base::layouts.master')



@section('content')
    <div class="page-content">
        <div class="container-fluid">
            
            <!-- Page Title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">طلبات الأشعة</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('dashboard.index') }}">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('admin.radiology.index') }}">مراكز الأشعة</a></li>
                                <li class="breadcrumb-item active">طلبات الأشعة</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" action="{{ route('admin.radiology.radiology-interface.requests.index') }}">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">البحث</label>
                                        <input type="text" class="form-control" name="search" 
                                               value="{{ request('search') }}" 
                                               placeholder="اسم المريض، رقم الهاتف...">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="">جميع الحالات</option>
                                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                            <option value="scheduled" {{ request('status') == 'scheduled' ? 'selected' : '' }}>مجدولة</option>
                                            <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>قيد التنفيذ</option>
                                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتملة</option>
                                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" name="date_from" 
                                               value="{{ request('date_from') }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" name="date_to" 
                                               value="{{ request('date_to') }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="ti ti-search"></i> بحث
                                            </button>
                                            <a href="{{ route('admin.radiology.radiology-interface.requests.index') }}" class="btn btn-secondary">
                                                <i class="ti ti-refresh"></i> إعادة تعيين
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Requests Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>المريض</th>
                                            <th>الطبيب</th>
                                            <th>نوع الأشعة</th>
                                            <th>مركز الأشعة</th>
                                            <th>الحالة</th>
                                            <th>الأولوية</th>
                                            <th>تاريخ الطلب</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($radiologyRequests as $request)
                                            <tr>
                                                <td>
                                                    <span class="fw-medium">{{ $request->request_code }}</span>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div class="fw-medium">{{ $request->patient->name }}</div>
                                                        <small class="text-muted">{{ $request->patient->phone }}</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div class="fw-medium">{{ $request->doctor->user->name }}</div>
                                                        <small class="text-muted">{{ $request->doctor->specialization }}</small>
                                                    </div>
                                                </td>
                                                <td>{{ $request->scanType->name }}</td>
                                                <td>{{ $request->radiologyCenter->name }}</td>
                                                <td>
                                                    @switch($request->status)
                                                        @case('pending')
                                                            <span class="badge bg-warning">في الانتظار</span>
                                                            @break
                                                        @case('scheduled')
                                                            <span class="badge bg-info">مجدولة</span>
                                                            @break
                                                        @case('in_progress')
                                                            <span class="badge bg-primary">قيد التنفيذ</span>
                                                            @break
                                                        @case('completed')
                                                            <span class="badge bg-success">مكتملة</span>
                                                            @break
                                                        @case('cancelled')
                                                            <span class="badge bg-danger">ملغية</span>
                                                            @break
                                                    @endswitch
                                                </td>
                                                <td>
                                                    @switch($request->urgency)
                                                        @case('urgent')
                                                            <span class="badge bg-danger">عاجل</span>
                                                            @break
                                                        @case('normal')
                                                            <span class="badge bg-secondary">عادي</span>
                                                            @break
                                                        @case('routine')
                                                            <span class="badge bg-light text-dark">روتيني</span>
                                                            @break
                                                    @endswitch
                                                </td>
                                                <td>{{ $request->created_at->format('Y-m-d H:i') }}</td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                                                type="button" data-bs-toggle="dropdown">
                                                            الإجراءات
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li>
                                                                <a class="dropdown-item"
                                                                   href="{{ route('admin.radiology.radiology-interface.requests.show', $request->id) }}">
                                                                    <i class="ti ti-eye"></i> عرض التفاصيل
                                                                </a>
                                                            </li>
                                                            @if($request->status == 'completed' && $request->results->count() > 0)
                                                                <li>
                                                                    <a class="dropdown-item"
                                                                       href="{{ route('admin.radiology.radiology-interface.results.show', $request->results->first()->id) }}">
                                                                        <i class="ti ti-file-text"></i> عرض النتيجة
                                                                    </a>
                                                                </li>
                                                            @endif
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="9" class="text-center py-4">
                                                    <div class="text-muted">
                                                        <i class="ti ti-inbox fs-3 d-block mb-2"></i>
                                                        لا توجد طلبات أشعة
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            @if($radiologyRequests->hasPages())
                                <div class="d-flex justify-content-center mt-3">
                                    {{ $radiologyRequests->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
@endsection

@push('footer')
    <script>
        // Auto-refresh every 30 seconds for real-time updates
        setInterval(function() {
            if (!document.hidden) {
                location.reload();
            }
        }, 30000);
    </script>
@endpush

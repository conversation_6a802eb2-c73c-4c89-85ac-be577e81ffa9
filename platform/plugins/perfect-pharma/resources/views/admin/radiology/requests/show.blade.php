@extends('core/base::layouts.master')

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            
            <!-- Page Title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">تفاصيل طلب الأشعة #{{ $radiologyRequest->request_code }}</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('dashboard.index') }}">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('radiology.index') }}">مراكز الأشعة</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('radiology.radiology-interface.requests.index') }}">طلبات الأشعة</a></li>
                                <li class="breadcrumb-item active">تفاصيل الطلب</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Request Details -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">معلومات الطلب</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-medium">رقم الطلب:</label>
                                        <div>{{ $radiologyRequest->request_code }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-medium">الحالة:</label>
                                        <div>
                                            @switch($radiologyRequest->status)
                                                @case('pending')
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                    @break
                                                @case('scheduled')
                                                    <span class="badge bg-info">مجدولة</span>
                                                    @break
                                                @case('in_progress')
                                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                                    @break
                                                @case('completed')
                                                    <span class="badge bg-success">مكتملة</span>
                                                    @break
                                                @case('cancelled')
                                                    <span class="badge bg-danger">ملغية</span>
                                                    @break
                                            @endswitch
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-medium">نوع الأشعة:</label>
                                        <div>{{ $radiologyRequest->scanType->name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-medium">الأولوية:</label>
                                        <div>
                                            @switch($radiologyRequest->urgency)
                                                @case('urgent')
                                                    <span class="badge bg-danger">عاجل</span>
                                                    @break
                                                @case('normal')
                                                    <span class="badge bg-secondary">عادي</span>
                                                    @break
                                                @case('routine')
                                                    <span class="badge bg-light text-dark">روتيني</span>
                                                    @break
                                            @endswitch
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-medium">تاريخ الطلب:</label>
                                        <div>{{ $radiologyRequest->created_at->format('Y-m-d H:i') }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-medium">التكلفة:</label>
                                        <div>{{ number_format($radiologyRequest->total_cost, 2) }} ريال</div>
                                    </div>
                                </div>
                                @if($radiologyRequest->scheduled_at)
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-medium">موعد الأشعة:</label>
                                            <div>{{ $radiologyRequest->scheduled_at->format('Y-m-d H:i') }}</div>
                                        </div>
                                    </div>
                                @endif
                                @if($radiologyRequest->completed_at)
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-medium">تاريخ الإنجاز:</label>
                                            <div>{{ $radiologyRequest->completed_at->format('Y-m-d H:i') }}</div>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            @if($radiologyRequest->notes)
                                <div class="mb-3">
                                    <label class="form-label fw-medium">ملاحظات:</label>
                                    <div class="border rounded p-3 bg-light">
                                        {{ $radiologyRequest->notes }}
                                    </div>
                                </div>
                            @endif

                            @if($radiologyRequest->clinical_history)
                                <div class="mb-3">
                                    <label class="form-label fw-medium">التاريخ المرضي:</label>
                                    <div class="border rounded p-3 bg-light">
                                        {{ $radiologyRequest->clinical_history }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Results Section -->
                    @if($radiologyRequest->results && $radiologyRequest->results->count() > 0)
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">النتائج</h5>
                            </div>
                            <div class="card-body">
                                @foreach($radiologyRequest->results as $result)
                                    <div class="border rounded p-3 mb-3">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>تاريخ النتيجة:</strong> {{ $result->created_at->format('Y-m-d H:i') }}
                                            </div>
                                            <div class="col-md-6">
                                                <strong>الحالة:</strong> 
                                                <span class="badge bg-{{ $result->status == 'approved' ? 'success' : 'warning' }}">
                                                    {{ $result->status == 'approved' ? 'معتمدة' : 'في انتظار الاعتماد' }}
                                                </span>
                                            </div>
                                        </div>
                                        @if($result->findings)
                                            <div class="mt-2">
                                                <strong>النتائج:</strong>
                                                <div class="mt-1">{{ $result->findings }}</div>
                                            </div>
                                        @endif
                                        @if($result->impression)
                                            <div class="mt-2">
                                                <strong>الانطباع:</strong>
                                                <div class="mt-1">{{ $result->impression }}</div>
                                            </div>
                                        @endif
                                        @if($result->recommendations)
                                            <div class="mt-2">
                                                <strong>التوصيات:</strong>
                                                <div class="mt-1">{{ $result->recommendations }}</div>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Patient Info -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">معلومات المريض</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-medium">الاسم:</label>
                                <div>{{ $radiologyRequest->patient->name }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">رقم الهاتف:</label>
                                <div>{{ $radiologyRequest->patient->phone }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">تاريخ الميلاد:</label>
                                <div>{{ $radiologyRequest->patient->date_of_birth ? $radiologyRequest->patient->date_of_birth->format('Y-m-d') : 'غير محدد' }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">الجنس:</label>
                                <div>{{ $radiologyRequest->patient->gender == 'male' ? 'ذكر' : 'أنثى' }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Doctor Info -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">معلومات الطبيب</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-medium">الاسم:</label>
                                <div>{{ $radiologyRequest->doctor->user->name }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">التخصص:</label>
                                <div>{{ $radiologyRequest->doctor->specialization }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">رقم الترخيص:</label>
                                <div>{{ $radiologyRequest->doctor->license_number }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Radiology Center Info -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">مركز الأشعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-medium">اسم المركز:</label>
                                <div>{{ $radiologyRequest->radiologyCenter->name }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">رقم الترخيص:</label>
                                <div>{{ $radiologyRequest->radiologyCenter->license_number }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">الهاتف:</label>
                                <div>{{ $radiologyRequest->radiologyCenter->phone }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
@endsection

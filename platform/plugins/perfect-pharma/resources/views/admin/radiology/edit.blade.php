@extends('core/base::layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تعديل مركز الأشعة: {{ $radiologyCenter->name }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.radiology.index') }}">مراكز الأشعة</a></li>
                        <li class="breadcrumb-item active">تعديل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <form action="{{ route('admin.radiology.update', $radiologyCenter->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">معلومات المركز الأساسية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم المركز <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ old('name', $radiologyCenter->name) }}" required>
                                    @error('name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="license_number" class="form-label">رقم الترخيص <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="license_number" name="license_number" 
                                           value="{{ old('license_number', $radiologyCenter->license_number) }}" required>
                                    @error('license_number')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="phone" name="phone" 
                                           value="{{ old('phone', $radiologyCenter->phone) }}" required>
                                    @error('phone')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ old('email', $radiologyCenter->email) }}">
                                    @error('email')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="3" required>{{ old('address', $radiologyCenter->address) }}</textarea>
                            @error('address')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المركز</label>
                            <textarea class="form-control" id="description" name="description" rows="4">{{ old('description', $radiologyCenter->description) }}</textarea>
                            @error('description')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">أوقات العمل والموقع</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="opening_time" class="form-label">وقت الافتتاح</label>
                                    <input type="time" class="form-control" id="opening_time" name="opening_time" 
                                           value="{{ old('opening_time', $radiologyCenter->opening_time?->format('H:i')) }}">
                                    @error('opening_time')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="closing_time" class="form-label">وقت الإغلاق</label>
                                    <input type="time" class="form-control" id="closing_time" name="closing_time" 
                                           value="{{ old('closing_time', $radiologyCenter->closing_time?->format('H:i')) }}">
                                    @error('closing_time')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">خط العرض</label>
                                    <input type="number" step="any" class="form-control" id="latitude" name="latitude" 
                                           value="{{ old('latitude', $radiologyCenter->latitude) }}" placeholder="24.7136">
                                    @error('latitude')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">خط الطول</label>
                                    <input type="number" step="any" class="form-control" id="longitude" name="longitude" 
                                           value="{{ old('longitude', $radiologyCenter->longitude) }}" placeholder="46.6753">
                                    @error('longitude')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">الخدمات والإعدادات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="home_service" name="home_service" 
                                           value="1" {{ old('home_service', $radiologyCenter->home_service) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="home_service">
                                        خدمة منزلية
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="emergency_service" name="emergency_service" 
                                           value="1" {{ old('emergency_service', $radiologyCenter->emergency_service) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="emergency_service">
                                        خدمة طوارئ
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="accepts_insurance" name="accepts_insurance" 
                                           value="1" {{ old('accepts_insurance', $radiologyCenter->accepts_insurance) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="accepts_insurance">
                                        يقبل التأمين
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row" id="home_service_details" style="{{ old('home_service', $radiologyCenter->home_service) ? '' : 'display: none;' }}">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="home_service_fee" class="form-label">رسوم الخدمة المنزلية (ريال)</label>
                                    <input type="number" step="0.01" class="form-control" id="home_service_fee" name="home_service_fee" 
                                           value="{{ old('home_service_fee', $radiologyCenter->home_service_fee) }}">
                                    @error('home_service_fee')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="home_service_radius_km" class="form-label">نطاق الخدمة المنزلية (كم)</label>
                                    <input type="number" step="0.1" class="form-control" id="home_service_radius_km" name="home_service_radius_km" 
                                           value="{{ old('home_service_radius_km', $radiologyCenter->home_service_radius_km) }}">
                                    @error('home_service_radius_km')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_verified" name="is_verified" 
                                           value="1" {{ old('is_verified', $radiologyCenter->is_verified) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_verified">
                                        مركز محقق
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" {{ old('is_active', $radiologyCenter->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        مركز نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.radiology.index') }}" class="btn btn-secondary">
                                <i class="ri-arrow-left-line me-1"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line me-1"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // إظهار/إخفاء تفاصيل الخدمة المنزلية
    $('#home_service').change(function() {
        if ($(this).is(':checked')) {
            $('#home_service_details').show();
        } else {
            $('#home_service_details').hide();
        }
    });
});
</script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-user-plus me-2"></i>
                        إضافة مريض جديد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.patients.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.patients.store') }}" method="POST" id="patient-form">
                        @csrf
                        
                        <div class="row">
                            <!-- البيانات الأساسية -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">البيانات الأساسية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="phone" name="phone" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="national_id" class="form-label">الرقم القومي</label>
                                            <input type="text" class="form-control" id="national_id" name="national_id">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- البيانات الشخصية -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">البيانات الشخصية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="birth_date" class="form-label">تاريخ الميلاد <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                                            <select class="form-select" id="gender" name="gender" required>
                                                <option value="">اختر الجنس</option>
                                                <option value="male">ذكر</option>
                                                <option value="female">أنثى</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="blood_type" class="form-label">فصيلة الدم</label>
                                            <select class="form-select" id="blood_type" name="blood_type">
                                                <option value="">اختر فصيلة الدم</option>
                                                <option value="A+">A+</option>
                                                <option value="A-">A-</option>
                                                <option value="B+">B+</option>
                                                <option value="B-">B-</option>
                                                <option value="AB+">AB+</option>
                                                <option value="AB-">AB-</option>
                                                <option value="O+">O+</option>
                                                <option value="O-">O-</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- التاريخ الطبي -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">التاريخ الطبي</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="medical_history" class="form-label">التاريخ المرضي</label>
                                            <textarea class="form-control" id="medical_history" name="medical_history" rows="3"></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="allergies" class="form-label">الحساسية</label>
                                            <textarea class="form-control" id="allergies" name="allergies" rows="3"></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="chronic_diseases" class="form-label">الأمراض المزمنة</label>
                                            <textarea class="form-control" id="chronic_diseases" name="chronic_diseases" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- جهة الاتصال في الطوارئ -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">جهة الاتصال في الطوارئ</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="emergency_contact_name" class="form-label">اسم جهة الاتصال <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="emergency_contact_phone" class="form-label">رقم هاتف جهة الاتصال <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.patients.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ المريض
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            $('#patient-form').on('submit', function(e) {
                e.preventDefault();
                
                // إظهار مؤشر التحميل
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);
                
                // إرسال البيانات
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ البيانات');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

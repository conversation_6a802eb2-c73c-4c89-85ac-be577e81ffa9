@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-users me-2"></i>
                        إدارة المرضى
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.patients.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة مريض جديد
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0" id="total-patients">{{ \App\Models\Patient::count() }}</h3>
                                            <p class="mb-0">إجمالي المرضى</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-users fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0" id="chronic-patients">{{ \App\Models\Patient::where('is_chronic_patient', true)->count() }}</h3>
                                            <p class="mb-0">المرضى المزمنين</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-heart-rate-monitor fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0" id="new-patients">{{ \App\Models\Patient::whereMonth('created_at', now()->month)->count() }}</h3>
                                            <p class="mb-0">مرضى جدد هذا الشهر</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-user-plus fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0" id="active-prescriptions">{{ \App\Models\Prescription::whereIn('status', ['active', 'partially_filled'])->count() }}</h3>
                                            <p class="mb-0">الوصفات النشطة</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-prescription fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- البحث والفلاتر -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="search-patients" placeholder="البحث برقم المريض، الاسم، أو رقم الهاتف...">
                                <button class="btn btn-outline-secondary" type="button" id="search-btn">
                                    <i class="ti ti-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filter-chronic">
                                <option value="">جميع المرضى</option>
                                <option value="1">المرضى المزمنين فقط</option>
                                <option value="0">المرضى العاديين فقط</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filter-gender">
                                <option value="">جميع الأجناس</option>
                                <option value="male">ذكور</option>
                                <option value="female">إناث</option>
                            </select>
                        </div>
                    </div>

                    <!-- جدول المرضى -->
                    <div class="table-responsive">
                        {!! $dataTable->table(['class' => 'table table-striped table-hover']) !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    {!! $dataTable->scripts() !!}
    
    <script>
        $(document).ready(function() {
            // البحث في المرضى
            $('#search-btn').on('click', function() {
                var searchTerm = $('#search-patients').val();
                if (searchTerm.length > 0) {
                    window.LaravelDataTables['patients-table'].search(searchTerm).draw();
                }
            });

            $('#search-patients').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#search-btn').click();
                }
            });

            // فلترة المرضى المزمنين
            $('#filter-chronic').on('change', function() {
                var value = $(this).val();
                window.LaravelDataTables['patients-table'].column(7).search(value).draw();
            });

            // فلترة الجنس
            $('#filter-gender').on('change', function() {
                var value = $(this).val();
                window.LaravelDataTables['patients-table'].column(5).search(value).draw();
            });

            // تحديث الإحصائيات كل 30 ثانية
            setInterval(function() {
                $.get('{{ route("admin.patients.statistics") }}', function(data) {
                    $('#total-patients').text(data.total_patients);
                    $('#chronic-patients').text(data.chronic_patients);
                    $('#new-patients').text(data.new_patients_this_month);
                    $('#active-prescriptions').text(data.active_prescriptions);
                });
            }, 30000);
        });
    </script>
@endpush

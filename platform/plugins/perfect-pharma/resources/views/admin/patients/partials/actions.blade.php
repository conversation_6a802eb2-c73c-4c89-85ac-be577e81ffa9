<div class="dropdown">
    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="ti ti-dots-vertical"></i>
    </button>
    <ul class="dropdown-menu">
        <li>
            <a class="dropdown-item" href="{{ route('admin.patients.show', $item->id) }}">
                <i class="ti ti-eye me-2"></i>
                عرض التفاصيل
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="{{ route('admin.patients.edit', $item->id) }}">
                <i class="ti ti-edit me-2"></i>
                تعديل
            </a>
        </li>
        <li><hr class="dropdown-divider"></li>
        <li>
            <a class="dropdown-item text-danger" href="#" onclick="deletePatient({{ $item->id }})">
                <i class="ti ti-trash me-2"></i>
                حذف
            </a>
        </li>
    </ul>
</div>

<script>
function deletePatient(patientId) {
    if (confirm('هل أنت متأكد من حذف هذا المريض؟')) {
        $.ajax({
            url: '{{ route("patients.destroy", ":id") }}'.replace(':id', patientId),
            method: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.error) {
                    Botble.showError(response.message);
                } else {
                    Botble.showSuccess(response.message);
                    location.reload();
                }
            },
            error: function() {
                Botble.showError('حدث خطأ أثناء حذف المريض');
            }
        });
    }
}
</script>

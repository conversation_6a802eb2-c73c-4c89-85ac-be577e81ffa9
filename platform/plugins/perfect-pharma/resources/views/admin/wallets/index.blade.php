@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-wallet me-2"></i>
                        إدارة المحافظ المالية
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم المحفظة</th>
                                    <th>المستخدم</th>
                                    <th>الرصيد</th>
                                    <th>العملة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($wallets as $wallet)
                                    <tr>
                                        <td>{{ $wallet->wallet_number }}</td>
                                        <td>{{ $wallet->user->name ?? 'غير محدد' }}</td>
                                        <td>{{ number_format($wallet->balance, 2) }}</td>
                                        <td>{{ $wallet->currency }}</td>
                                        <td>
                                            @if($wallet->is_active)
                                                <span class="badge bg-success">نشطة</span>
                                            @else
                                                <span class="badge bg-danger">غير نشطة</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.wallets.show', $wallet->id) }}" class="btn btn-sm btn-primary">عرض</a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد محافظ مالية</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

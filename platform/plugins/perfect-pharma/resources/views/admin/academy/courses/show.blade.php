@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-school me-2"></i>
                        تفاصيل الدورة التدريبية
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.quizzes.index', $course->id) }}" class="btn btn-warning me-2">
                            <i class="ti ti-help me-1"></i>
                            الاختبارات
                        </a>
                        <a href="{{ route('admin.academy.courses.progress.course', $course->id) }}" class="btn btn-success me-2">
                            <i class="ti ti-chart-bar me-1"></i>
                            تتبع التقدم
                        </a>
                        <a href="{{ route('admin.academy.courses.index') }}" class="btn btn-secondary me-2">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                        <a href="{{ route('admin.academy.courses.edit', $course->id ?? 1) }}" class="btn btn-primary">
                            <i class="ti ti-edit me-1"></i>
                            تعديل الدورة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- معلومات الدورة -->
                        <div class="col-md-8">
                            <h5>{{ $course->title }}</h5>
                            <p class="text-muted">{{ $course->description }}</p>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <strong>المدرب:</strong> {{ $course->instructor_name }}<br>
                                    <strong>المدة:</strong> {{ $course->duration_hours }} ساعة<br>
                                    <strong>المستوى:</strong> {{ $course->difficulty_name }}
                                </div>
                                <div class="col-md-6">
                                    <strong>السعر:</strong> {{ number_format($course->price) }} جنيه<br>
                                    <strong>الفئة:</strong> {{ $course->category_name }}<br>
                                    <strong>النوع:</strong> {{ $course->type_name }}
                                </div>
                            </div>

                            <!-- دروس الدورة -->
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0">
                                        <i class="ti ti-list me-2"></i>
                                        دروس الدورة ({{ $course->contents->count() }})
                                    </h6>
                                    <a href="{{ route('admin.academy.courses.contents.index', $course->id) }}" class="btn btn-sm btn-primary">
                                        <i class="ti ti-settings me-1"></i>
                                        إدارة الدروس
                                    </a>
                                </div>
                                <div class="card-body">
                                    @if($course->contents->count() > 0)
                                        <div class="list-group">
                                            @foreach($course->contents->take(5) as $content)
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <div class="d-flex align-items-center">
                                                        <i class="{{ $content->icon }} me-2 text-{{ $content->color }}"></i>
                                                        <div>
                                                            <strong>{{ $content->title }}</strong>
                                                            @if($content->duration_minutes)
                                                                <small class="text-muted d-block">{{ $content->duration_minutes }} دقيقة</small>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <span class="badge bg-{{ $content->status === 'published' ? 'success' : 'warning' }}">
                                                        {{ $content->status_name }}
                                                    </span>
                                                </div>
                                            @endforeach
                                        </div>
                                        @if($course->contents->count() > 5)
                                            <div class="text-center mt-3">
                                                <a href="{{ route('admin.academy.courses.contents.index', $course->id) }}" class="btn btn-outline-primary">
                                                    عرض جميع الدروس ({{ $course->contents->count() }})
                                                </a>
                                            </div>
                                        @endif
                                    @else
                                        <div class="text-center py-4">
                                            <i class="ti ti-file-plus fs-1 text-muted mb-3"></i>
                                            <p class="text-muted">لا توجد دروس في هذه الدورة بعد</p>
                                            <a href="{{ route('admin.academy.courses.contents.create', $course->id) }}" class="btn btn-primary">
                                                <i class="ti ti-plus me-1"></i>
                                                إضافة أول درس
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات الدورة -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">إحصائيات الدورة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h4 class="text-primary mb-1">{{ $course->contents->count() }}</h4>
                                                <small class="text-muted">الدروس</small>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h4 class="text-success mb-1">{{ $course->getEnrollmentCount() }}</h4>
                                                <small class="text-muted">المسجلين</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="border rounded p-3">
                                                <h4 class="text-warning mb-1">{{ number_format($course->getCompletionRate(), 1) }}%</h4>
                                                <small class="text-muted">معدل الإكمال</small>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h4 class="text-info mb-1">{{ $course->contents->where('status', 'published')->count() }}</h4>
                                                <small class="text-muted">دروس منشورة</small>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h4 class="text-purple mb-1">{{ $course->quizzes->count() ?? 0 }}</h4>
                                                <small class="text-muted">الاختبارات</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="border rounded p-3">
                                                <h4 class="text-danger mb-1">{{ $course->enrollments->where('status', 'in_progress')->count() ?? 0 }}</h4>
                                                <small class="text-muted">جاري التعلم</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

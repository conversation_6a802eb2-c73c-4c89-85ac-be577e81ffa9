@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-school me-2"></i>
                        الدورات التدريبية
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة دورة جديدة
                        </a>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card-body border-bottom">
                    <div class="row">
                        <div class="col-md-4">
                            <select class="form-select" id="category-filter">
                                <option value="">جميع الفئات</option>
                                @php
                                    $categories = \Botble\PerfectPharma\Models\AcademyCourse::getCategories();
                                @endphp
                                @foreach($categories as $key => $name)
                                    <option value="{{ $key }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="status-filter">
                                <option value="">جميع الحالات</option>
                                <option value="published">منشورة</option>
                                <option value="draft">مسودة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="search-input" placeholder="البحث في الدورات...">
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>عنوان الدورة</th>
                                    <th>الفئة</th>
                                    <th>الدروس</th>
                                    <th>المدة</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>مميزة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($courses as $course)
                                    <tr>
                                        <td>{{ $course->title }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ $course->category_name }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $course->contents_count ?? 0 }}</span>
                                        </td>
                                        <td>{{ $course->duration_hours }} ساعة</td>
                                        <td>{{ number_format($course->price) }} جنيه</td>
                                        <td>
                                            @if($course->status === 'published')
                                                <span class="badge bg-success">منشورة</span>
                                            @else
                                                <span class="badge bg-warning">مسودة</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($course->is_featured)
                                                <span class="badge bg-primary">مميزة</span>
                                            @else
                                                <span class="badge bg-secondary">عادية</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.show', $course->id) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.contents.index', $course->id) }}">
                                                            <i class="ti ti-list me-2"></i>
                                                            إدارة الدروس
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.edit', $course->id) }}">
                                                            <i class="ti ti-edit me-2"></i>
                                                            تعديل
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">لا توجد دورات تدريبية</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // فلترة الجدول
            function filterTable() {
                const categoryFilter = $('#category-filter').val().toLowerCase();
                const statusFilter = $('#status-filter').val().toLowerCase();
                const searchText = $('#search-input').val().toLowerCase();

                $('tbody tr').each(function() {
                    const row = $(this);
                    const title = row.find('td:first').text().toLowerCase();
                    const category = row.find('td:nth-child(2) .badge').text().toLowerCase();
                    const status = row.find('td:nth-child(6) .badge').text().toLowerCase();

                    let showRow = true;

                    // فلترة حسب الفئة
                    if (categoryFilter && !category.includes(categoryFilter)) {
                        showRow = false;
                    }

                    // فلترة حسب الحالة
                    if (statusFilter) {
                        if (statusFilter === 'published' && !status.includes('منشورة')) {
                            showRow = false;
                        }
                        if (statusFilter === 'draft' && !status.includes('مسودة')) {
                            showRow = false;
                        }
                    }

                    // فلترة حسب النص
                    if (searchText && !title.includes(searchText)) {
                        showRow = false;
                    }

                    row.toggle(showRow);
                });
            }

            // ربط الأحداث
            $('#category-filter, #status-filter').on('change', filterTable);
            $('#search-input').on('keyup', filterTable);
        });
    </script>
@endpush

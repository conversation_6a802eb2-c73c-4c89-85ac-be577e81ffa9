@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة دورة تدريبية جديدة
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.academy.courses.store') }}" method="POST" id="course-form">
                        @csrf
                        
                        <div class="row">
                            <!-- معلومات الدورة الأساسية -->
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات الدورة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">عنوان الدورة <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف الدورة <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="description" name="description" rows="5" required></textarea>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="duration_hours" class="form-label">مدة الدورة (ساعة) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="duration_hours" name="duration_hours" min="1" required>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="price" class="form-label">سعر الدورة (جنيه) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" required>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="instructor_name" class="form-label">اسم المدرب <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="instructor_name" name="instructor_name" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="instructor_bio" class="form-label">نبذة عن المدرب</label>
                                            <textarea class="form-control" id="instructor_bio" name="instructor_bio" rows="3"></textarea>
                                        </div>

                                        <div class="mb-3">
                                            <label for="learning_objectives" class="form-label">أهداف التعلم</label>
                                            <textarea class="form-control" id="learning_objectives" name="learning_objectives" rows="4" placeholder="اكتب كل هدف في سطر منفصل"></textarea>
                                            <small class="form-text text-muted">اكتب كل هدف في سطر منفصل</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="max_students" class="form-label">الحد الأقصى للطلاب</label>
                                            <input type="number" class="form-control" id="max_students" name="max_students" min="1">
                                            <small class="form-text text-muted">اتركه فارغاً إذا لم يكن هناك حد أقصى</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إعدادات الدورة -->
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات الدورة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">حالة الدورة</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="draft">مسودة</option>
                                                <option value="published">منشورة</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                                <label class="form-check-label" for="is_featured">
                                                    دورة مميزة
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="has_certificate" name="has_certificate" value="1">
                                                <label class="form-check-label" for="has_certificate">
                                                    تمنح شهادة
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3" id="passing_score_field" style="display: none;">
                                            <label for="passing_score" class="form-label">درجة النجاح (%)</label>
                                            <input type="number" class="form-control" id="passing_score" name="passing_score" min="0" max="100" value="70">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="category" class="form-label">فئة الدورة</label>
                                            <select class="form-select" id="category" name="category">
                                                <option value="">اختر الفئة</option>
                                                @php
                                                    $categories = \Botble\PerfectPharma\Models\AcademyCourse::getCategories();
                                                @endphp
                                                @foreach($categories as $key => $name)
                                                    <option value="{{ $key }}">{{ $name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="type" class="form-label">نوع المحتوى</label>
                                            <select class="form-select" id="type" name="type">
                                                <option value="mixed">مختلط</option>
                                                <option value="video">فيديو</option>
                                                <option value="pdf">PDF</option>
                                                <option value="live">مباشر</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="difficulty_level" class="form-label">مستوى الصعوبة</label>
                                            <select class="form-select" id="difficulty_level" name="difficulty_level">
                                                <option value="beginner">مبتدئ</option>
                                                <option value="intermediate">متوسط</option>
                                                <option value="advanced">متقدم</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="language" class="form-label">لغة الدورة</label>
                                            <select class="form-select" id="language" name="language">
                                                <option value="ar">العربية</option>
                                                <option value="en">الإنجليزية</option>
                                                <option value="both">العربية والإنجليزية</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- معاينة الدورة -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معاينة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="course-preview">
                                            <div class="course-image bg-light rounded mb-3" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                                                <i class="ti ti-photo fs-1 text-muted"></i>
                                            </div>
                                            <h6 class="course-title text-muted">عنوان الدورة</h6>
                                            <p class="course-description text-muted small">وصف الدورة سيظهر هنا...</p>
                                            <div class="course-meta">
                                                <small class="text-muted">
                                                    <i class="ti ti-clock me-1"></i>
                                                    <span class="duration">0</span> ساعة
                                                </small>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="ti ti-currency-pound me-1"></i>
                                                    <span class="price">0</span> جنيه
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.academy.courses.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ الدورة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // تحديث المعاينة عند تغيير البيانات
            function updatePreview() {
                const title = $('#title').val() || 'عنوان الدورة';
                const description = $('#description').val() || 'وصف الدورة سيظهر هنا...';
                const duration = $('#duration_hours').val() || '0';
                const price = $('#price').val() || '0';
                
                $('.course-title').text(title);
                $('.course-description').text(description.substring(0, 100) + (description.length > 100 ? '...' : ''));
                $('.duration').text(duration);
                $('.price').text(parseFloat(price).toLocaleString());
            }
            
            $('#title, #description, #duration_hours, #price').on('input', updatePreview);

            // إظهار/إخفاء حقل درجة النجاح
            $('#has_certificate').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#passing_score_field').show();
                } else {
                    $('#passing_score_field').hide();
                }
            });
            
            // إرسال النموذج
            $('#course-form').on('submit', function(e) {
                e.preventDefault();
                
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);
                
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            } else {
                                window.location.href = '{{ route("admin.academy.courses.index") }}';
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ الدورة');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

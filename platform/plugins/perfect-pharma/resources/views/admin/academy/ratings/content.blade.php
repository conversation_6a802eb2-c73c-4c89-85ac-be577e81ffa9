@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-star fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_ratings'] }}</h3>
                                    <small>إجمالي التقييمات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-thumb-up fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['helpful_ratings'] }}</h3>
                                    <small>تقييمات مفيدة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-chart-line fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['average_rating'], 1) }}</h3>
                                    <small>متوسط التقييم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة التقييمات -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-star me-2"></i>
                        تقييمات المحتوى
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.ratings.analytics') }}" class="btn btn-info me-2">
                            <i class="ti ti-chart-pie me-1"></i>
                            التحليلات
                        </a>
                        <a href="{{ route('admin.academy.ratings.export', ['type' => 'content']) }}" class="btn btn-success">
                            <i class="ti ti-download me-1"></i>
                            تصدير
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" placeholder="البحث..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="course_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الدورات</option>
                                    @foreach($courses as $course)
                                        <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                            {{ $course->title }}
                                        </option>
                                    @endforeach
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="rating" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع التقييمات</option>
                                    <option value="5" {{ request('rating') === '5' ? 'selected' : '' }}>5 نجوم</option>
                                    <option value="4" {{ request('rating') === '4' ? 'selected' : '' }}>4 نجوم</option>
                                    <option value="3" {{ request('rating') === '3' ? 'selected' : '' }}>3 نجوم</option>
                                    <option value="2" {{ request('rating') === '2' ? 'selected' : '' }}>2 نجوم</option>
                                    <option value="1" {{ request('rating') === '1' ? 'selected' : '' }}>1 نجمة</option>
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                                @if(request('course_id'))
                                    <input type="hidden" name="course_id" value="{{ request('course_id') }}">
                                @endif
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="helpful" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع التقييمات</option>
                                    <option value="yes" {{ request('helpful') === 'yes' ? 'selected' : '' }}>مفيدة</option>
                                    <option value="no" {{ request('helpful') === 'no' ? 'selected' : '' }}>غير مفيدة</option>
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                                @if(request('course_id'))
                                    <input type="hidden" name="course_id" value="{{ request('course_id') }}">
                                @endif
                                @if(request('rating'))
                                    <input type="hidden" name="rating" value="{{ request('rating') }}">
                                @endif
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الطالب</th>
                                    <th>الدورة</th>
                                    <th>المحتوى</th>
                                    <th>التقييم</th>
                                    <th>التعليق</th>
                                    <th>مفيد</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($ratings as $rating)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ substr($rating->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $rating->user->first_name }} {{ $rating->user->last_name }}</strong>
                                                    <br><small class="text-muted">{{ $rating->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ $rating->content->course->title }}</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="ti ti-{{ $rating->content->type === 'video' ? 'video' : ($rating->content->type === 'text' ? 'file-text' : 'file') }} me-2 text-primary"></i>
                                                <div>
                                                    <strong>{{ $rating->content->title }}</strong>
                                                    <br><small class="text-muted">{{ $rating->content->type_name ?? $rating->content->type }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="ti ti-star{{ $i <= $rating->rating ? '-filled text-warning' : ' text-muted' }}"></i>
                                                @endfor
                                                <span class="ms-2">{{ $rating->rating }}/5</span>
                                            </div>
                                        </td>
                                        <td>
                                            @if($rating->comment)
                                                <div class="text-truncate" style="max-width: 200px;" title="{{ $rating->comment }}">
                                                    {{ $rating->comment }}
                                                </div>
                                            @else
                                                <span class="text-muted">لا يوجد تعليق</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $rating->is_helpful ? 'success' : 'secondary' }}">
                                                {{ $rating->is_helpful ? 'مفيد' : 'غير مفيد' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $rating->formatted_date }}</span>
                                            <br><small class="text-muted">{{ $rating->time_ago }}</small>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.contents.show', [$rating->content->course_id, $rating->content->id]) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض المحتوى
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item text-danger" onclick="deleteRating({{ $rating->id }})">
                                                            <i class="ti ti-trash me-2"></i>
                                                            حذف
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-star fs-1 mb-3"></i>
                                                <p>لا توجد تقييمات للمحتوى</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($ratings->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $ratings->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function deleteRating(ratingId) {
            if (confirm('هل أنت متأكد من حذف هذا التقييم؟')) {
                $.ajax({
                    url: `/admin/perfect-pharma/academy/ratings/content/${ratingId}`,
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء حذف التقييم');
                    }
                });
            }
        }
    </script>
@endpush

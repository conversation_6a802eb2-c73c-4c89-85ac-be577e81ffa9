@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات عامة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-star fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $generalStats['total_course_ratings'] }}</h3>
                                    <small>تقييمات الدورات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-file-text fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $generalStats['total_content_ratings'] }}</h3>
                                    <small>تقييمات المحتوى</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-chart-line fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ number_format($generalStats['average_course_rating'], 1) }}</h3>
                                    <small>متوسط تقييم الدورات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-chart-bar fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ number_format($generalStats['average_content_rating'], 1) }}</h3>
                                    <small>متوسط تقييم المحتوى</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلتر الدورة -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row align-items-end">
                        <div class="col-md-4">
                            <label for="course_id" class="form-label">اختر دورة للتحليل التفصيلي</label>
                            <select name="course_id" id="course_id" class="form-select">
                                <option value="">جميع الدورات</option>
                                @foreach($courses as $course)
                                    <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                        {{ $course->title }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-search me-1"></i>
                                تحليل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- توزيع التقييمات -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">توزيع التقييمات</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="ratingDistributionChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- التقييمات الشهرية -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">التقييمات الشهرية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyRatingsChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- أفضل الدورات تقييماً -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">أفضل الدورات تقييماً</h5>
                </div>
                <div class="card-body">
                    @if(count($topRatedCourses) > 0)
                        <div class="list-group list-group-flush">
                            @foreach($topRatedCourses as $course)
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ $course['title'] }}</strong>
                                        <br><small class="text-muted">{{ $course['total_ratings'] }} تقييم</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="d-flex align-items-center">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="ti ti-star{{ $i <= $course['average_rating'] ? '-filled text-warning' : ' text-muted' }}"></i>
                                            @endfor
                                        </div>
                                        <span class="badge bg-primary">{{ $course['average_rating'] }}/5</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center text-muted py-4">
                            <i class="ti ti-star fs-1 mb-3"></i>
                            <p>لا توجد دورات بتقييمات كافية</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- تحليلات الدورة المحددة -->
        @if($courseAnalytics)
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">تحليل دورة: {{ $courseAnalytics['course']->title }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6 text-center">
                                <h4 class="text-primary">{{ $courseAnalytics['ratings_summary']['average_rating'] }}</h4>
                                <small class="text-muted">متوسط التقييم</small>
                            </div>
                            <div class="col-6 text-center">
                                <h4 class="text-success">{{ $courseAnalytics['ratings_summary']['total_reviews'] }}</h4>
                                <small class="text-muted">إجمالي التقييمات</small>
                            </div>
                        </div>

                        <h6 class="mb-3">توزيع التقييمات:</h6>
                        @foreach($courseAnalytics['ratings_summary']['distribution']['counts'] as $rating => $count)
                            <div class="d-flex align-items-center mb-2">
                                <span class="me-2">{{ $rating }} نجوم</span>
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    <div class="progress-bar" style="width: {{ $courseAnalytics['ratings_summary']['distribution']['percentages'][$rating] }}%"></div>
                                </div>
                                <span class="text-muted">{{ $count }}</span>
                            </div>
                        @endforeach

                        @if(count($courseAnalytics['top_content']) > 0)
                            <h6 class="mt-4 mb-3">أفضل المحتوى:</h6>
                            @foreach($courseAnalytics['top_content'] as $content)
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <strong>{{ $content['title'] }}</strong>
                                        <br><small class="text-muted">{{ $content['type'] }}</small>
                                    </div>
                                    <span class="badge bg-success">{{ $content['average_rating'] }}/5</span>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection

@push('footer')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // رسم بياني لتوزيع التقييمات
        const ratingDistributionCtx = document.getElementById('ratingDistributionChart').getContext('2d');
        const ratingDistributionChart = new Chart(ratingDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['5 نجوم', '4 نجوم', '3 نجوم', '2 نجوم', '1 نجمة'],
                datasets: [{
                    data: [
                        @foreach($ratingDistribution as $rating => $data)
                            {{ $data['total'] }},
                        @endforeach
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#17a2b8',
                        '#ffc107',
                        '#fd7e14',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني للتقييمات الشهرية
        const monthlyRatingsCtx = document.getElementById('monthlyRatingsChart').getContext('2d');
        const monthlyRatingsChart = new Chart(monthlyRatingsCtx, {
            type: 'line',
            data: {
                labels: [
                    @foreach($monthlyRatings as $month)
                        '{{ $month['month_name'] }}',
                    @endforeach
                ],
                datasets: [{
                    label: 'تقييمات الدورات',
                    data: [
                        @foreach($monthlyRatings as $month)
                            {{ $month['course_ratings'] }},
                        @endforeach
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'تقييمات المحتوى',
                    data: [
                        @foreach($monthlyRatings as $month)
                            {{ $month['content_ratings'] }},
                        @endforeach
                    ],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-star fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_ratings'] }}</h3>
                                    <small>إجمالي التقييمات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['approved_ratings'] }}</h3>
                                    <small>معتمدة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-clock fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['pending_ratings'] }}</h3>
                                    <small>في الانتظار</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-chart-line fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['average_rating'], 1) }}</h3>
                                    <small>متوسط التقييم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة التقييمات -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-star me-2"></i>
                        تقييمات الدورات
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.ratings.analytics') }}" class="btn btn-info me-2">
                            <i class="ti ti-chart-pie me-1"></i>
                            التحليلات
                        </a>
                        <a href="{{ route('admin.academy.ratings.export', ['type' => 'course']) }}" class="btn btn-success">
                            <i class="ti ti-download me-1"></i>
                            تصدير
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" placeholder="البحث..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="course_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الدورات</option>
                                    @foreach($courses as $course)
                                        <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                            {{ $course->title }}
                                        </option>
                                    @endforeach
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="rating" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع التقييمات</option>
                                    <option value="5" {{ request('rating') === '5' ? 'selected' : '' }}>5 نجوم</option>
                                    <option value="4" {{ request('rating') === '4' ? 'selected' : '' }}>4 نجوم</option>
                                    <option value="3" {{ request('rating') === '3' ? 'selected' : '' }}>3 نجوم</option>
                                    <option value="2" {{ request('rating') === '2' ? 'selected' : '' }}>2 نجوم</option>
                                    <option value="1" {{ request('rating') === '1' ? 'selected' : '' }}>1 نجمة</option>
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                                @if(request('course_id'))
                                    <input type="hidden" name="course_id" value="{{ request('course_id') }}">
                                @endif
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>معتمد</option>
                                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                                @if(request('course_id'))
                                    <input type="hidden" name="course_id" value="{{ request('course_id') }}">
                                @endif
                                @if(request('rating'))
                                    <input type="hidden" name="rating" value="{{ request('rating') }}">
                                @endif
                            </form>
                        </div>
                    </div>

                    <!-- إجراءات مجمعة -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button class="btn btn-success btn-sm" onclick="bulkApprove()" disabled id="bulkApproveBtn">
                                    <i class="ti ti-check me-1"></i>
                                    الموافقة على المحدد
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="bulkDelete()" disabled id="bulkDeleteBtn">
                                    <i class="ti ti-trash me-1"></i>
                                    حذف المحدد
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>الطالب</th>
                                    <th>الدورة</th>
                                    <th>التقييم</th>
                                    <th>المراجعة</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($ratings as $rating)
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="rating-checkbox" value="{{ $rating->id }}">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ substr($rating->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $rating->user->first_name }} {{ $rating->user->last_name }}</strong>
                                                    <br><small class="text-muted">{{ $rating->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ $rating->course->title }}</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="ti ti-star{{ $i <= $rating->rating ? '-filled text-warning' : ' text-muted' }}"></i>
                                                @endfor
                                                <span class="ms-2">{{ $rating->rating }}/5</span>
                                            </div>
                                        </td>
                                        <td>
                                            @if($rating->review)
                                                <div class="text-truncate" style="max-width: 200px;" title="{{ $rating->review }}">
                                                    {{ $rating->review }}
                                                </div>
                                            @else
                                                <span class="text-muted">لا توجد مراجعة</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $rating->is_approved ? 'success' : 'warning' }}">
                                                {{ $rating->is_approved ? 'معتمد' : 'في الانتظار' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $rating->formatted_date }}</span>
                                            <br><small class="text-muted">{{ $rating->time_ago }}</small>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    @if(!$rating->is_approved)
                                                        <li>
                                                            <button class="dropdown-item text-success" onclick="approveRating({{ $rating->id }})">
                                                                <i class="ti ti-check me-2"></i>
                                                                الموافقة
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item text-warning" onclick="rejectRating({{ $rating->id }})">
                                                                <i class="ti ti-x me-2"></i>
                                                                الرفض
                                                            </button>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                    @endif
                                                    <li>
                                                        <button class="dropdown-item text-danger" onclick="deleteRating({{ $rating->id }})">
                                                            <i class="ti ti-trash me-2"></i>
                                                            حذف
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-star fs-1 mb-3"></i>
                                                <p>لا توجد تقييمات</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($ratings->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $ratings->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.rating-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateBulkButtons();
        }

        function updateBulkButtons() {
            const checkedBoxes = document.querySelectorAll('.rating-checkbox:checked');
            const bulkApproveBtn = document.getElementById('bulkApproveBtn');
            const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
            
            if (checkedBoxes.length > 0) {
                bulkApproveBtn.disabled = false;
                bulkDeleteBtn.disabled = false;
            } else {
                bulkApproveBtn.disabled = true;
                bulkDeleteBtn.disabled = true;
            }
        }

        // إضافة مستمع للأحداث لجميع checkboxes
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('.rating-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBulkButtons);
            });
        });

        function approveRating(ratingId) {
            $.ajax({
                url: `/admin/perfect-pharma/academy/ratings/courses/${ratingId}/approve`,
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء الموافقة على التقييم');
                }
            });
        }

        function rejectRating(ratingId) {
            $.ajax({
                url: `/admin/perfect-pharma/academy/ratings/courses/${ratingId}/reject`,
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                },
                error: function() {
                    Botble.showError('حدث خطأ أثناء رفض التقييم');
                }
            });
        }

        function deleteRating(ratingId) {
            if (confirm('هل أنت متأكد من حذف هذا التقييم؟')) {
                $.ajax({
                    url: `/admin/perfect-pharma/academy/ratings/courses/${ratingId}`,
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء حذف التقييم');
                    }
                });
            }
        }

        function bulkApprove() {
            const checkedBoxes = document.querySelectorAll('.rating-checkbox:checked');
            const ratingIds = Array.from(checkedBoxes).map(cb => cb.value);
            
            if (ratingIds.length === 0) return;
            
            if (confirm(`هل تريد الموافقة على ${ratingIds.length} تقييم؟`)) {
                $.ajax({
                    url: '{{ route("admin.academy.ratings.courses.bulk-approve") }}',
                    method: 'POST',
                    data: {
                        rating_ids: ratingIds,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء الموافقة على التقييمات');
                    }
                });
            }
        }

        function bulkDelete() {
            const checkedBoxes = document.querySelectorAll('.rating-checkbox:checked');
            const ratingIds = Array.from(checkedBoxes).map(cb => cb.value);
            
            if (ratingIds.length === 0) return;
            
            if (confirm(`هل أنت متأكد من حذف ${ratingIds.length} تقييم؟ لا يمكن التراجع عن هذا الإجراء.`)) {
                $.ajax({
                    url: '{{ route("admin.academy.ratings.courses.bulk-delete") }}',
                    method: 'POST',
                    data: {
                        rating_ids: ratingIds,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء حذف التقييمات');
                    }
                });
            }
        }
    </script>
@endpush

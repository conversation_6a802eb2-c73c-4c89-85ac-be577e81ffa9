@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات عامة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-certificate fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_certificates'] }}</h3>
                                    <small>إجمالي الشهادات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['active_certificates'] }}</h3>
                                    <small>شهادات نشطة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-ban fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['revoked_certificates'] }}</h3>
                                    <small>شهادات ملغاة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-clock fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['expired_certificates'] }}</h3>
                                    <small>شهادات منتهية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-calendar fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['certificates_this_month'] }}</h3>
                                    <small>هذا الشهر</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-calendar-year fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['certificates_this_year'] }}</h3>
                                    <small>هذا العام</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الشهادات الشهرية -->
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">الشهادات الصادرة شهرياً</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyCertificatesChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- أكثر الدورات إصداراً للشهادات -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">أكثر الدورات إصداراً للشهادات</h5>
                    </div>
                    <div class="card-body">
                        @if($topCourses->count() > 0)
                            <div class="list-group list-group-flush">
                                @foreach($topCourses as $course)
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>{{ $course->course->title ?? 'دورة محذوفة' }}</strong>
                                        </div>
                                        <span class="badge bg-primary">{{ $course->certificates_count }} شهادة</span>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center text-muted py-4">
                                <i class="ti ti-certificate fs-1 mb-3"></i>
                                <p>لا توجد شهادات صادرة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- توزيع الدرجات -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">توزيع الدرجات</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="scoreDistributionChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- إحصائيات إضافية -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">إحصائيات إضافية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-success">{{ number_format(($stats['active_certificates'] / max($stats['total_certificates'], 1)) * 100, 1) }}%</h4>
                                    <small class="text-muted">نسبة الشهادات النشطة</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-info">{{ number_format($stats['certificates_this_month'] / max(now()->day, 1), 1) }}</h4>
                                    <small class="text-muted">متوسط الشهادات اليومية</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-3">
                                    <h4 class="text-warning">{{ $stats['certificates_this_year'] > 0 ? number_format($stats['certificates_this_year'] / 12, 1) : 0 }}</h4>
                                    <small class="text-muted">متوسط الشهادات الشهرية</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-3">
                                    <h4 class="text-primary">{{ $topCourses->count() }}</h4>
                                    <small class="text-muted">دورات تصدر شهادات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">روابط سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="{{ route('admin.academy.certificates.index') }}" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="ti ti-list me-2"></i>
                                    جميع الشهادات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.academy.certificates.create') }}" class="btn btn-outline-success w-100 mb-2">
                                    <i class="ti ti-plus me-2"></i>
                                    إصدار شهادة جديدة
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.academy.courses.index') }}" class="btn btn-outline-info w-100 mb-2">
                                    <i class="ti ti-book me-2"></i>
                                    إدارة الدورات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.academy.enrollments.index') }}" class="btn btn-outline-warning w-100 mb-2">
                                    <i class="ti ti-users me-2"></i>
                                    إدارة التسجيلات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // رسم بياني للشهادات الشهرية
        const monthlyCertificatesCtx = document.getElementById('monthlyCertificatesChart').getContext('2d');
        const monthlyCertificatesChart = new Chart(monthlyCertificatesCtx, {
            type: 'line',
            data: {
                labels: [
                    @foreach($monthlyCertificates as $month)
                        '{{ $month['month_name'] }}',
                    @endforeach
                ],
                datasets: [{
                    label: 'الشهادات الصادرة',
                    data: [
                        @foreach($monthlyCertificates as $month)
                            {{ $month['count'] }},
                        @endforeach
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني لتوزيع الدرجات
        const scoreDistributionCtx = document.getElementById('scoreDistributionChart').getContext('2d');
        const scoreDistributionChart = new Chart(scoreDistributionCtx, {
            type: 'bar',
            data: {
                labels: [
                    @foreach($scoreDistribution as $range)
                        '{{ $range['range'] }}%',
                    @endforeach
                ],
                datasets: [{
                    label: 'عدد الشهادات',
                    data: [
                        @foreach($scoreDistribution as $range)
                            {{ $range['count'] }},
                        @endforeach
                    ],
                    backgroundColor: [
                        '#dc3545', '#fd7e14', '#ffc107', '#28a745', '#20c997',
                        '#17a2b8', '#6f42c1', '#e83e8c', '#6c757d', '#343a40'
                    ]
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
@endpush

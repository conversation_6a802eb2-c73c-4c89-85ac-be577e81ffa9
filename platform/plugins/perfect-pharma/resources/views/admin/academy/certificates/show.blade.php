@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-certificate me-2"></i>
                        تفاصيل الشهادة: {{ $certificate->certificate_number }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.certificates.index') }}" class="btn btn-secondary me-2">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                        @if($certificate->isActive())
                            <a href="{{ $certificate->download_url }}" target="_blank" class="btn btn-success me-2">
                                <i class="ti ti-download me-1"></i>
                                تحميل الشهادة
                            </a>
                        @endif
                        @if(!$certificate->isRevoked())
                            <button class="btn btn-danger" onclick="revokeCertificate()">
                                <i class="ti ti-ban me-1"></i>
                                إلغاء الشهادة
                            </button>
                        @endif
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- معلومات الشهادة -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">معلومات الشهادة</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="200"><strong>رقم الشهادة:</strong></td>
                                            <td>{{ $certificate->certificate_number }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>رمز التحقق:</strong></td>
                                            <td>
                                                <code>{{ $certificate->verification_code ?? 'غير متوفر' }}</code>
                                                @if($certificate->verification_code)
                                                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('{{ $certificate->verification_code }}')">
                                                        <i class="ti ti-copy"></i>
                                                    </button>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الطالب:</strong></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-2">
                                                        <span class="avatar-initial bg-primary rounded-circle">
                                                            {{ substr($certificate->user->name, 0, 1) }}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <strong>{{ $certificate->user->first_name }} {{ $certificate->user->last_name }}</strong>
                                                        <br><small class="text-muted">{{ $certificate->user->email }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الدورة:</strong></td>
                                            <td>
                                                <a href="{{ route('admin.academy.courses.show', $certificate->course->id) }}" class="text-decoration-none">
                                                    <strong>{{ $certificate->course->title }}</strong>
                                                </a>
                                                <br><small class="text-muted">{{ $certificate->course->instructor_name ?? 'غير محدد' }}</small>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الدرجة النهائية:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $certificate->final_score >= 90 ? 'success' : ($certificate->final_score >= 70 ? 'primary' : 'warning') }} fs-6">
                                                    {{ number_format($certificate->final_score ?? 0, 1) }}%
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإصدار:</strong></td>
                                            <td>
                                                @if($certificate->issued_date)
                                                    {{ $certificate->issued_date->format('Y-m-d H:i') }}
                                                    <br><small class="text-muted">{{ $certificate->issued_date->diffForHumans() }}</small>
                                                @else
                                                    <span class="text-muted">غير محدد</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الانتهاء:</strong></td>
                                            <td>
                                                @if($certificate->expiry_date)
                                                    {{ $certificate->expiry_date->format('Y-m-d') }}
                                                    @if($certificate->expiry_date->isPast())
                                                        <span class="badge bg-danger ms-2">منتهية الصلاحية</span>
                                                    @else
                                                        <span class="badge bg-success ms-2">سارية</span>
                                                    @endif
                                                @else
                                                    <span class="text-muted">بدون انتهاء</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $certificate->status === 'active' ? 'success' : 'danger' }} fs-6">
                                                    {{ $certificate->status_name }}
                                                </span>
                                            </td>
                                        </tr>
                                        @if($certificate->certificate_url)
                                            <tr>
                                                <td><strong>رابط الشهادة:</strong></td>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="certificateUrl" value="{{ $certificate->certificate_url }}" readonly>
                                                        <button class="btn btn-outline-primary" onclick="copyToClipboard('{{ $certificate->certificate_url }}')">
                                                            <i class="ti ti-copy"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            @if($certificate->metadata)
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات إضافية</h5>
                                    </div>
                                    <div class="card-body">
                                        <pre class="bg-light p-3 rounded">{{ json_encode($certificate->metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- معاينة الشهادة -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">معاينة الشهادة</h5>
                                </div>
                                <div class="card-body text-center">
                                    <div class="certificate-preview border rounded p-4 bg-light">
                                        <div class="mb-3">
                                            <i class="ti ti-certificate fs-1 text-warning"></i>
                                        </div>
                                        <h6 class="text-primary">شهادة إتمام الدورة</h6>
                                        <hr>
                                        <p class="mb-2"><strong>{{ $certificate->user->first_name }} {{ $certificate->user->last_name }}</strong></p>
                                        <p class="text-muted small mb-2">أكمل بنجاح دورة</p>
                                        <p class="mb-2"><strong>{{ $certificate->course->title }}</strong></p>
                                        <hr>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <small class="text-muted d-block">الدرجة</small>
                                                <strong>{{ number_format($certificate->final_score ?? 0, 1) }}%</strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">التاريخ</small>
                                                <strong>{{ $certificate->issued_date?->format('Y-m-d') ?? 'غير محدد' }}</strong>
                                            </div>
                                        </div>
                                        <hr>
                                        <small class="text-muted">{{ $certificate->certificate_number }}</small>
                                    </div>
                                    
                                    @if($certificate->isActive())
                                        <div class="mt-3">
                                            <a href="{{ $certificate->download_url }}" target="_blank" class="btn btn-primary btn-sm">
                                                <i class="ti ti-download me-1"></i>
                                                تحميل PDF
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- إحصائيات سريعة -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title">إحصائيات سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-12 mb-3">
                                            <div class="border rounded p-2">
                                                <h6 class="text-primary mb-0">{{ $certificate->course->enrollments->count() ?? 0 }}</h6>
                                                <small class="text-muted">إجمالي المسجلين</small>
                                            </div>
                                        </div>
                                        <div class="col-12 mb-3">
                                            <div class="border rounded p-2">
                                                <h6 class="text-success mb-0">{{ $certificate->course->certificates->where('status', 'active')->count() ?? 0 }}</h6>
                                                <small class="text-muted">شهادات صادرة</small>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="border rounded p-2">
                                                <h6 class="text-info mb-0">{{ number_format($certificate->course->average_rating ?? 0, 1) }}</h6>
                                                <small class="text-muted">تقييم الدورة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                Botble.showSuccess('تم نسخ النص');
            }, function() {
                // Fallback للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                Botble.showSuccess('تم نسخ النص');
            });
        }

        function revokeCertificate() {
            if (confirm('هل أنت متأكد من إلغاء هذه الشهادة؟ لا يمكن التراجع عن هذا الإجراء.')) {
                $.ajax({
                    url: '{{ route("admin.academy.certificates.revoke", $certificate->id) }}',
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء إلغاء الشهادة');
                    }
                });
            }
        }
    </script>
@endpush

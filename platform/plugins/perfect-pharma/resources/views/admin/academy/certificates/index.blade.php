@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-certificate me-2"></i>
                        إدارة الشهادات
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" placeholder="البحث برقم الشهادة أو اسم الطالب..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <form method="GET">
                                <select name="course_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الدورات</option>
                                    @foreach($courses ?? [] as $course)
                                        <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                            {{ $course->title }}
                                        </option>
                                    @endforeach
                                </select>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <form method="GET">
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    @foreach($statuses ?? [] as $key => $status)
                                        <option value="{{ $key }}" {{ request('status') === $key ? 'selected' : '' }}>
                                            {{ $status }}
                                        </option>
                                    @endforeach
                                </select>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم الشهادة</th>
                                    <th>الطالب</th>
                                    <th>الدورة</th>
                                    <th>الدرجة</th>
                                    <th>تاريخ الإصدار</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($certificates as $certificate)
                                    <tr>
                                        <td>
                                            <strong>{{ $certificate->certificate_number }}</strong>
                                            <br><small class="text-muted">{{ $certificate->verification_code }}</small>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ substr($certificate->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $certificate->user->name }}</strong>
                                                    <br><small class="text-muted">{{ $certificate->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ $certificate->course->title }}</strong>
                                            <br><small class="text-muted">{{ $certificate->course->instructor_name }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                {{ $certificate->formatted_grade }}
                                            </span>
                                            <br><small class="text-muted">{{ $certificate->grade_level }}</small>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $certificate->issued_at?->format('Y-m-d') ?? 'غير محدد' }}</span>
                                            @if($certificate->issued_at)
                                                <br><small class="text-muted">{{ $certificate->issued_at->diffForHumans() }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $certificate->status === 'active' ? 'success' : ($certificate->status === 'revoked' ? 'danger' : 'warning') }}">
                                                {{ $certificate->status_name }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.certificates.show', $certificate->id) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض التفاصيل
                                                        </a>
                                                    </li>
                                                    @if($certificate->isActive())
                                                        <li>
                                                            <a class="dropdown-item" href="{{ $certificate->download_url }}" target="_blank">
                                                                <i class="ti ti-download me-2"></i>
                                                                تحميل الشهادة
                                                            </a>
                                                        </li>
                                                    @endif
                                                    @if(!$certificate->isRevoked())
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <button class="dropdown-item text-danger" onclick="revokeCertificate({{ $certificate->id }})">
                                                                <i class="ti ti-ban me-2"></i>
                                                                إلغاء الشهادة
                                                            </button>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-certificate fs-1 mb-3"></i>
                                                <p>لا توجد شهادات</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if(isset($certificates) && $certificates->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $certificates->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function revokeCertificate(certificateId) {
            if (confirm('هل أنت متأكد من إلغاء هذه الشهادة؟ لا يمكن التراجع عن هذا الإجراء.')) {
                $.ajax({
                    url: `/admin/perfect-pharma/academy/certificates/${certificateId}/revoke`,
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء إلغاء الشهادة');
                    }
                });
            }
        }
    </script>
@endpush

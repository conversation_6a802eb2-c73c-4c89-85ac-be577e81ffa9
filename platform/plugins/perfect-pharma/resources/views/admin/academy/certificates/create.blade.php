@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-certificate me-2"></i>
                        إصدار شهادة جديدة
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.certificates.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.academy.certificates.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">الطالب <span class="text-danger">*</span></label>
                                    <select class="form-select" id="user_id" name="user_id" required>
                                        <option value="">اختر الطالب</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="course_id" class="form-label">الدورة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="course_id" name="course_id" required>
                                        <option value="">اختر الدورة</option>
                                        @foreach($courses as $course)
                                            <option value="{{ $course->id }}" {{ old('course_id') == $course->id ? 'selected' : '' }}>
                                                {{ $course->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('course_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="final_score" class="form-label">الدرجة النهائية (%)</label>
                                    <input type="number" class="form-control" id="final_score" name="final_score" 
                                           min="0" max="100" step="0.1" value="{{ old('final_score') }}" 
                                           placeholder="مثال: 85.5">
                                    @error('final_score')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">اتركه فارغاً إذا لم تكن هناك درجة محددة</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="issued_date" class="form-label">تاريخ الإصدار</label>
                                    <input type="date" class="form-control" id="issued_date" name="issued_date" 
                                           value="{{ old('issued_date', now()->format('Y-m-d')) }}">
                                    @error('issued_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">سيتم استخدام التاريخ الحالي إذا ترك فارغاً</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expiry_date" class="form-label">تاريخ الانتهاء</label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date" 
                                           value="{{ old('expiry_date') }}">
                                    @error('expiry_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">اتركه فارغاً إذا كانت الشهادة بدون انتهاء</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> سيتم إنشاء رقم شهادة ورمز تحقق فريدين تلقائياً عند الإصدار.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.academy.certificates.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-certificate me-1"></i>
                                        إصدار الشهادة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- معاينة الشهادة -->
    <div class="row mt-4" id="certificatePreview" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">معاينة الشهادة</h5>
                </div>
                <div class="card-body">
                    <div class="certificate-preview border rounded p-4 bg-light text-center">
                        <div class="mb-3">
                            <i class="ti ti-certificate fs-1 text-warning"></i>
                        </div>
                        <h4 class="text-primary mb-3">شهادة إتمام الدورة</h4>
                        <hr>
                        <p class="mb-2">هذا يشهد أن</p>
                        <h5 class="text-primary mb-3" id="previewStudentName">اسم الطالب</h5>
                        <p class="mb-2">قد أكمل بنجاح دورة</p>
                        <h5 class="text-success mb-3" id="previewCourseName">اسم الدورة</h5>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted d-block">الدرجة</small>
                                <strong id="previewScore">-</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted d-block">تاريخ الإصدار</small>
                                <strong id="previewDate">{{ now()->format('Y-m-d') }}</strong>
                            </div>
                        </div>
                        <hr>
                        <small class="text-muted">رقم الشهادة: سيتم إنشاؤه تلقائياً</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // تحديث المعاينة عند تغيير البيانات
            $('#user_id, #course_id, #final_score, #issued_date').on('change', updatePreview);
            
            function updatePreview() {
                const userId = $('#user_id').val();
                const courseId = $('#course_id').val();
                const score = $('#final_score').val();
                const date = $('#issued_date').val();
                
                if (userId && courseId) {
                    const studentName = $('#user_id option:selected').text().split(' (')[0];
                    const courseName = $('#course_id option:selected').text();
                    
                    $('#previewStudentName').text(studentName);
                    $('#previewCourseName').text(courseName);
                    $('#previewScore').text(score ? score + '%' : 'غير محدد');
                    $('#previewDate').text(date || '{{ now()->format("Y-m-d") }}');
                    
                    $('#certificatePreview').show();
                } else {
                    $('#certificatePreview').hide();
                }
            }
            
            // التحقق من وجود شهادة مسبقة
            $('#user_id, #course_id').on('change', function() {
                const userId = $('#user_id').val();
                const courseId = $('#course_id').val();
                
                if (userId && courseId) {
                    // يمكن إضافة AJAX call للتحقق من وجود شهادة مسبقة
                    // وإظهار تحذير للمستخدم
                }
            });
        });
    </script>
@endpush

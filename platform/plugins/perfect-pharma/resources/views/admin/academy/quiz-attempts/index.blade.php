@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-clipboard-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_attempts'] }}</h3>
                                    <small>إجمالي المحاولات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['completed_attempts'] }}</h3>
                                    <small>محاولات مكتملة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-clock fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['in_progress_attempts'] }}</h3>
                                    <small>قيد التنفيذ</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-chart-line fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['average_score'], 1) }}%</h3>
                                    <small>متوسط الدرجات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة المحاولات -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-clipboard-check me-2"></i>
                        محاولات الاختبارات
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.quiz-attempts.analytics') }}" class="btn btn-info">
                            <i class="ti ti-chart-pie me-1"></i>
                            التحليلات
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" placeholder="البحث..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="course_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الدورات</option>
                                    @foreach($courses as $course)
                                        <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                            {{ $course->title }}
                                        </option>
                                    @endforeach
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="quiz_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الاختبارات</option>
                                    @foreach($quizzes as $quiz)
                                        <option value="{{ $quiz->id }}" {{ request('quiz_id') == $quiz->id ? 'selected' : '' }}>
                                            {{ $quiz->title }}
                                        </option>
                                    @endforeach
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                                @if(request('course_id'))
                                    <input type="hidden" name="course_id" value="{{ request('course_id') }}">
                                @endif
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="GET">
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>مكتمل</option>
                                    <option value="in_progress" {{ request('status') === 'in_progress' ? 'selected' : '' }}>قيد التنفيذ</option>
                                    <option value="abandoned" {{ request('status') === 'abandoned' ? 'selected' : '' }}>متروك</option>
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                                @if(request('course_id'))
                                    <input type="hidden" name="course_id" value="{{ request('course_id') }}">
                                @endif
                                @if(request('quiz_id'))
                                    <input type="hidden" name="quiz_id" value="{{ request('quiz_id') }}">
                                @endif
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الطالب</th>
                                    <th>الاختبار</th>
                                    <th>الدورة</th>
                                    <th>الدرجة</th>
                                    <th>الحالة</th>
                                    <th>وقت البداية</th>
                                    <th>وقت الانتهاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($attempts as $attempt)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ substr($attempt->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $attempt->user->first_name }} {{ $attempt->user->last_name }}</strong>
                                                    <br><small class="text-muted">{{ $attempt->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ $attempt->quiz->title }}</strong>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.academy.courses.show', $attempt->quiz->course->id) }}" class="text-decoration-none">
                                                {{ $attempt->quiz->course->title }}
                                            </a>
                                        </td>
                                        <td>
                                            @if($attempt->status === 'completed')
                                                <span class="badge bg-{{ $attempt->score >= 60 ? 'success' : 'danger' }} fs-6">
                                                    {{ number_format($attempt->score, 1) }}%
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $attempt->status === 'completed' ? 'success' : ($attempt->status === 'in_progress' ? 'primary' : 'warning') }}">
                                                {{ $attempt->status_name ?? $attempt->status }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $attempt->started_at?->format('Y-m-d H:i') ?? $attempt->created_at->format('Y-m-d H:i') }}</span>
                                        </td>
                                        <td>
                                            @if($attempt->completed_at)
                                                <span class="text-muted">{{ $attempt->completed_at->format('Y-m-d H:i') }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.quiz-attempts.show', $attempt->id) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض التفاصيل
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.quizzes.show', [$attempt->quiz->course_id, $attempt->quiz_id]) }}">
                                                            <i class="ti ti-clipboard-check me-2"></i>
                                                            عرض الاختبار
                                                        </a>
                                                    </li>
                                                    @if($attempt->status === 'completed' && $attempt->score >= 60)
                                                        <li>
                                                            <a class="dropdown-item text-success" href="#">
                                                                <i class="ti ti-certificate me-2"></i>
                                                                إصدار شهادة
                                                            </a>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-clipboard-check fs-1 mb-3"></i>
                                                <p>لا توجد محاولات اختبارات</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($attempts->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $attempts->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

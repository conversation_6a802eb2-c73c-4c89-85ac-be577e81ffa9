@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-clipboard-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_quizzes'] }}</h3>
                                    <small>إجمالي الاختبارات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-calendar fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['recent_quizzes'] }}</h3>
                                    <small>اختبارات حديثة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['quizzes_with_questions'] }}</h3>
                                    <small>اختبارات بأسئلة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-help fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_questions'] }}</h3>
                                    <small>إجمالي الأسئلة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الاختبارات -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-clipboard-check me-2"></i>
                        جميع الاختبارات
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.quiz-attempts.analytics') }}" class="btn btn-info me-2">
                            <i class="ti ti-chart-pie me-1"></i>
                            تحليلات الاختبارات
                        </a>
                        <a href="{{ route('admin.academy.courses.index') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة اختبار جديد
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" placeholder="البحث..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <form method="GET">
                                <select name="course_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الدورات</option>
                                    @foreach($courses as $course)
                                        <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                            {{ $course->title }}
                                        </option>
                                    @endforeach
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاختبار</th>
                                    <th>الدورة</th>
                                    <th>عدد الأسئلة</th>
                                    <th>المدة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($quizzes as $quiz)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $quiz->title }}</strong>
                                                @if($quiz->description)
                                                    <br><small class="text-muted">{{ Str::limit($quiz->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.academy.courses.show', $quiz->course->id) }}" class="text-decoration-none">
                                                {{ $quiz->course->title }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $quiz->questions_count ?? 0 }} سؤال</span>
                                        </td>
                                        <td>
                                            @if($quiz->time_limit_minutes)
                                                <span class="text-muted">{{ $quiz->time_limit_minutes }} دقيقة</span>
                                            @else
                                                <span class="text-muted">بدون حد زمني</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $quiz->created_at->format('Y-m-d') }}</span>
                                            <br><small class="text-muted">{{ $quiz->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.quizzes.show', [$quiz->course_id, $quiz->id]) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض الاختبار
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.quizzes.questions.index', [$quiz->course_id, $quiz->id]) }}">
                                                            <i class="ti ti-help me-2"></i>
                                                            إدارة الأسئلة
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.quizzes.edit', [$quiz->course_id, $quiz->id]) }}">
                                                            <i class="ti ti-edit me-2"></i>
                                                            تعديل
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.quizzes.statistics', [$quiz->course_id, $quiz->id]) }}">
                                                            <i class="ti ti-chart-bar me-2"></i>
                                                            الإحصائيات
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-clipboard-check fs-1 mb-3"></i>
                                                <p>لا توجد اختبارات</p>
                                                <a href="{{ route('admin.academy.courses.index') }}" class="btn btn-primary">
                                                    <i class="ti ti-plus me-1"></i>
                                                    إضافة اختبار جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if($quizzes->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $quizzes->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

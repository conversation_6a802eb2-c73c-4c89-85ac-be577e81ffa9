@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة اختبار جديد للدورة: {{ $course->title }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.quizzes.index', $course->id) }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للاختبارات
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.academy.courses.quizzes.store', $course->id) }}" method="POST" id="quiz-form">
                        @csrf
                        
                        <div class="row">
                            <!-- معلومات الاختبار الأساسية -->
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات الاختبار</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">عنوان الاختبار <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف الاختبار</label>
                                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="وصف مختصر عن الاختبار..."></textarea>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="passing_score" class="form-label">درجة النجاح (%) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="passing_score" name="passing_score" min="1" max="100" value="70" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="max_attempts" class="form-label">عدد المحاولات المسموحة <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="max_attempts" name="max_attempts" min="1" max="10" value="3" required>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="time_limit_minutes" class="form-label">الوقت المحدد (بالدقائق)</label>
                                            <input type="number" class="form-control" id="time_limit_minutes" name="time_limit_minutes" min="1" placeholder="اتركه فارغاً لوقت غير محدود">
                                            <small class="form-text text-muted">اتركه فارغاً إذا كنت لا تريد تحديد وقت للاختبار</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إعدادات الاختبار -->
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات الاختبار</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="randomize_questions" name="randomize_questions" value="1">
                                                <label class="form-check-label" for="randomize_questions">
                                                    ترتيب عشوائي للأسئلة
                                                </label>
                                                <small class="form-text text-muted d-block">سيتم عرض الأسئلة بترتيب عشوائي لكل طالب</small>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="show_results_immediately" name="show_results_immediately" value="1" checked>
                                                <label class="form-check-label" for="show_results_immediately">
                                                    عرض النتائج فوراً
                                                </label>
                                                <small class="form-text text-muted d-block">سيتم عرض النتيجة للطالب فور انتهاء الاختبار</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- معاينة الاختبار -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معاينة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="quiz-preview">
                                            <div class="d-flex align-items-center mb-3">
                                                <i class="ti ti-help me-2 fs-3 text-warning"></i>
                                                <div>
                                                    <h6 class="preview-title mb-0">عنوان الاختبار</h6>
                                                    <small class="preview-description text-muted">وصف الاختبار</small>
                                                </div>
                                            </div>
                                            <div class="quiz-info">
                                                <div class="row text-center">
                                                    <div class="col-6 mb-2">
                                                        <div class="border rounded p-2">
                                                            <small class="text-muted d-block">درجة النجاح</small>
                                                            <strong class="preview-passing-score text-success">70%</strong>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 mb-2">
                                                        <div class="border rounded p-2">
                                                            <small class="text-muted d-block">المحاولات</small>
                                                            <strong class="preview-max-attempts text-warning">3</strong>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="border rounded p-2">
                                                            <small class="text-muted d-block">الوقت المحدد</small>
                                                            <strong class="preview-time-limit text-info">غير محدود</strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.academy.courses.quizzes.index', $course->id) }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ الاختبار
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // تحديث المعاينة عند تغيير البيانات
            function updatePreview() {
                const title = $('#title').val() || 'عنوان الاختبار';
                const description = $('#description').val() || 'وصف الاختبار';
                const passingScore = $('#passing_score').val() || '70';
                const maxAttempts = $('#max_attempts').val() || '3';
                const timeLimit = $('#time_limit_minutes').val();
                
                $('.preview-title').text(title);
                $('.preview-description').text(description.substring(0, 50) + (description.length > 50 ? '...' : ''));
                $('.preview-passing-score').text(passingScore + '%');
                $('.preview-max-attempts').text(maxAttempts);
                
                if (timeLimit) {
                    const hours = Math.floor(timeLimit / 60);
                    const minutes = timeLimit % 60;
                    let timeText = '';
                    
                    if (hours > 0) {
                        timeText = hours + ' ساعة';
                        if (minutes > 0) {
                            timeText += ' و ' + minutes + ' دقيقة';
                        }
                    } else {
                        timeText = minutes + ' دقيقة';
                    }
                    
                    $('.preview-time-limit').text(timeText);
                } else {
                    $('.preview-time-limit').text('غير محدود');
                }
            }
            
            $('#title, #description, #passing_score, #max_attempts, #time_limit_minutes').on('input', updatePreview);
            
            // إرسال النموذج
            $('#quiz-form').on('submit', function(e) {
                e.preventDefault();
                
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);
                
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            } else {
                                window.location.href = '{{ route("admin.academy.courses.quizzes.index", $course->id) }}';
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ الاختبار');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

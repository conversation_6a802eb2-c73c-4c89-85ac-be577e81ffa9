@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-folder me-2"></i>
                        إدارة ملفات الأكاديمية
                    </h4>
                    <div class="card-actions">
                        <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="ti ti-upload me-1"></i>
                            رفع ملف جديد
                        </button>
                        <button class="btn btn-info" onclick="refreshFiles()">
                            <i class="ti ti-refresh me-1"></i>
                            تحديث
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="fileTypeFilter" onchange="filterFiles()">
                                <option value="all">جميع الملفات</option>
                                <option value="videos">الفيديوهات</option>
                                <option value="documents">المستندات</option>
                                <option value="images">الصور</option>
                            </select>
                        </div>
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchFiles" placeholder="البحث في الملفات..." onkeyup="searchFiles()">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchFiles()">
                                    <i class="ti ti-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- قائمة الملفات -->
                    <div id="filesContainer">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل الملفات...</p>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div id="paginationContainer" class="d-flex justify-content-center mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal رفع الملفات -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">رفع ملف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- رفع فيديو -->
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="ti ti-video fs-1 text-primary mb-3"></i>
                                    <h6>رفع فيديو</h6>
                                    <p class="text-muted small">MP4, AVI, MOV, WMV<br>حد أقصى: 500MB</p>
                                    <input type="file" id="videoFile" accept="video/*" style="display: none;" onchange="uploadFile('video')">
                                    <button class="btn btn-primary btn-sm" onclick="document.getElementById('videoFile').click()">
                                        <i class="ti ti-upload me-1"></i>
                                        اختر فيديو
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- رفع مستند -->
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="ti ti-file-text fs-1 text-success mb-3"></i>
                                    <h6>رفع مستند</h6>
                                    <p class="text-muted small">PDF, DOC, DOCX, PPT<br>حد أقصى: 50MB</p>
                                    <input type="file" id="documentFile" accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt" style="display: none;" onchange="uploadFile('document')">
                                    <button class="btn btn-success btn-sm" onclick="document.getElementById('documentFile').click()">
                                        <i class="ti ti-upload me-1"></i>
                                        اختر مستند
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- رفع صورة -->
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="ti ti-photo fs-1 text-info mb-3"></i>
                                    <h6>رفع صورة</h6>
                                    <p class="text-muted small">JPG, PNG, GIF, SVG<br>حد أقصى: 10MB</p>
                                    <input type="file" id="imageFile" accept="image/*" style="display: none;" onchange="uploadFile('image')">
                                    <button class="btn btn-info btn-sm" onclick="document.getElementById('imageFile').click()">
                                        <i class="ti ti-upload me-1"></i>
                                        اختر صورة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- شريط التقدم -->
                    <div id="uploadProgress" class="mt-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <p class="text-center mt-2 mb-0">جاري الرفع...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal معلومات الملف -->
    <div class="modal fade" id="fileInfoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معلومات الملف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="fileInfoContent">
                    <!-- سيتم ملء المحتوى بـ JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="copyUrlBtn">نسخ الرابط</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        let currentPage = 1;
        let currentFilter = 'all';
        let currentSearch = '';

        // تحميل الملفات عند تحميل الصفحة
        $(document).ready(function() {
            loadFiles();
        });

        function loadFiles(page = 1) {
            currentPage = page;
            
            $.ajax({
                url: '{{ route("admin.academy.files.list") }}',
                method: 'GET',
                data: {
                    type: currentFilter,
                    page: page,
                    per_page: 12,
                    search: currentSearch
                },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        displayFiles(response.data.files);
                        displayPagination(response.data.pagination);
                    }
                },
                error: function() {
                    $('#filesContainer').html(`
                        <div class="text-center py-5">
                            <i class="ti ti-alert-circle fs-1 text-danger mb-3"></i>
                            <p class="text-muted">حدث خطأ أثناء تحميل الملفات</p>
                            <button class="btn btn-primary" onclick="loadFiles()">إعادة المحاولة</button>
                        </div>
                    `);
                }
            });
        }

        function displayFiles(files) {
            if (files.length === 0) {
                $('#filesContainer').html(`
                    <div class="text-center py-5">
                        <i class="ti ti-folder-x fs-1 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد ملفات</p>
                    </div>
                `);
                return;
            }

            let html = '<div class="row">';
            
            files.forEach(file => {
                const fileIcon = getFileIcon(file.mime_type);
                const fileSize = formatFileSize(file.size);
                const fileName = file.name.length > 20 ? file.name.substring(0, 20) + '...' : file.name;
                
                html += `
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="${fileIcon} fs-1 mb-3"></i>
                                <h6 class="card-title" title="${file.name}">${fileName}</h6>
                                <p class="text-muted small">${fileSize}</p>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="showFileInfo('${file.path}')">
                                        <i class="ti ti-info-circle"></i>
                                    </button>
                                    <a href="${file.url}" target="_blank" class="btn btn-outline-success">
                                        <i class="ti ti-eye"></i>
                                    </a>
                                    <a href="${file.url}" download class="btn btn-outline-info">
                                        <i class="ti ti-download"></i>
                                    </a>
                                    <button class="btn btn-outline-danger" onclick="deleteFile('${file.path}')">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            $('#filesContainer').html(html);
        }

        function displayPagination(pagination) {
            if (pagination.last_page <= 1) {
                $('#paginationContainer').html('');
                return;
            }

            let html = '<nav><ul class="pagination">';
            
            // Previous
            if (pagination.current_page > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadFiles(${pagination.current_page - 1})">السابق</a></li>`;
            }
            
            // Pages
            for (let i = 1; i <= pagination.last_page; i++) {
                const active = i === pagination.current_page ? 'active' : '';
                html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadFiles(${i})">${i}</a></li>`;
            }
            
            // Next
            if (pagination.current_page < pagination.last_page) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadFiles(${pagination.current_page + 1})">التالي</a></li>`;
            }
            
            html += '</ul></nav>';
            $('#paginationContainer').html(html);
        }

        function getFileIcon(mimeType) {
            if (mimeType.startsWith('video/')) return 'ti ti-video text-primary';
            if (mimeType.startsWith('image/')) return 'ti ti-photo text-info';
            if (mimeType.includes('pdf')) return 'ti ti-file-type-pdf text-danger';
            if (mimeType.includes('word') || mimeType.includes('document')) return 'ti ti-file-type-doc text-primary';
            if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'ti ti-file-type-ppt text-warning';
            if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'ti ti-file-type-xls text-success';
            return 'ti ti-file text-secondary';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function filterFiles() {
            currentFilter = $('#fileTypeFilter').val();
            loadFiles(1);
        }

        function searchFiles() {
            currentSearch = $('#searchFiles').val();
            loadFiles(1);
        }

        function refreshFiles() {
            loadFiles(currentPage);
        }

        function uploadFile(type) {
            const fileInput = document.getElementById(type + 'File');
            const file = fileInput.files[0];
            
            if (!file) return;

            const formData = new FormData();
            formData.append(type, file);

            // إظهار شريط التقدم
            $('#uploadProgress').show();
            $('.progress-bar').css('width', '0%');

            $.ajax({
                url: `{{ route('admin.academy.files.upload-video') }}`.replace('video', type),
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function(evt) {
                        if (evt.lengthComputable) {
                            const percentComplete = (evt.loaded / evt.total) * 100;
                            $('.progress-bar').css('width', percentComplete + '%');
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    $('#uploadProgress').hide();
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        $('#uploadModal').modal('hide');
                        fileInput.value = '';
                        loadFiles(1);
                    }
                },
                error: function() {
                    $('#uploadProgress').hide();
                    Botble.showError('حدث خطأ أثناء رفع الملف');
                }
            });
        }

        function showFileInfo(path) {
            $.ajax({
                url: '{{ route("admin.academy.files.info") }}',
                method: 'GET',
                data: { path: path },
                success: function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        const file = response.data;
                        let content = `
                            <table class="table table-borderless">
                                <tr><td><strong>اسم الملف:</strong></td><td>${file.path.split('/').pop()}</td></tr>
                                <tr><td><strong>الحجم:</strong></td><td>${formatFileSize(file.size)}</td></tr>
                                <tr><td><strong>النوع:</strong></td><td>${file.mime_type}</td></tr>
                                <tr><td><strong>آخر تعديل:</strong></td><td>${file.last_modified}</td></tr>
                        `;
                        
                        if (file.dimensions) {
                            content += `<tr><td><strong>الأبعاد:</strong></td><td>${file.dimensions.width} × ${file.dimensions.height}</td></tr>`;
                        }
                        
                        if (file.duration) {
                            content += `<tr><td><strong>المدة:</strong></td><td>${file.duration} ثانية</td></tr>`;
                        }
                        
                        content += `<tr><td><strong>الرابط:</strong></td><td><input type="text" class="form-control" id="fileUrl" value="${file.url}" readonly></td></tr>`;
                        content += '</table>';
                        
                        $('#fileInfoContent').html(content);
                        $('#fileInfoModal').modal('show');
                    }
                }
            });
        }

        function deleteFile(path) {
            if (confirm('هل أنت متأكد من حذف هذا الملف؟ لا يمكن التراجع عن هذا الإجراء.')) {
                $.ajax({
                    url: '{{ route("admin.academy.files.delete") }}',
                    method: 'DELETE',
                    data: {
                        path: path,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            loadFiles(currentPage);
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء حذف الملف');
                    }
                });
            }
        }

        // نسخ الرابط
        $('#copyUrlBtn').click(function() {
            const urlInput = document.getElementById('fileUrl');
            urlInput.select();
            document.execCommand('copy');
            Botble.showSuccess('تم نسخ الرابط');
        });
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة فئة دورات جديدة
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.categories.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.academy.categories.store') }}" method="POST" id="category-form">
                        @csrf
                        
                        <div class="row">
                            <!-- معلومات الفئة الأساسية -->
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات الفئة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">اسم الفئة <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف الفئة</label>
                                            <textarea class="form-control" id="description" name="description" rows="4" placeholder="وصف مختصر عن هذه الفئة..."></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="parent_id" class="form-label">الفئة الأب</label>
                                            <select class="form-select" id="parent_id" name="parent_id">
                                                <option value="">فئة رئيسية</option>
                                                @foreach($parentCategories as $parent)
                                                    <option value="{{ $parent->id }}">{{ $parent->name }}</option>
                                                @endforeach
                                            </select>
                                            <small class="form-text text-muted">اختر فئة أب لإنشاء فئة فرعية</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إعدادات الفئة -->
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات الفئة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="icon" class="form-label">أيقونة الفئة</label>
                                            <select class="form-select" id="icon" name="icon">
                                                <option value="">بدون أيقونة</option>
                                                @foreach($icons as $iconClass => $iconName)
                                                    <option value="{{ $iconClass }}">{{ $iconName }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="color" class="form-label">لون الفئة</label>
                                            <select class="form-select" id="color" name="color">
                                                @foreach($colors as $colorKey => $colorName)
                                                    <option value="{{ $colorKey }}" {{ $colorKey === 'primary' ? 'selected' : '' }}>{{ $colorName }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="order_index" class="form-label">ترتيب الفئة</label>
                                            <input type="number" class="form-control" id="order_index" name="order_index" min="0" value="0">
                                            <small class="form-text text-muted">الرقم الأصغر يظهر أولاً</small>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                                <label class="form-check-label" for="is_active">
                                                    فئة نشطة
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                                <label class="form-check-label" for="is_featured">
                                                    فئة مميزة
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- معاينة الفئة -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معاينة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="category-preview">
                                            <div class="d-flex align-items-center mb-3">
                                                <i class="preview-icon ti ti-category me-2 fs-3"></i>
                                                <div>
                                                    <h6 class="preview-name mb-0">اسم الفئة</h6>
                                                    <small class="preview-description text-muted">وصف الفئة</small>
                                                </div>
                                            </div>
                                            <span class="preview-badge badge bg-primary">0 دورة</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.academy.categories.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ الفئة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // تحديث المعاينة عند تغيير البيانات
            function updatePreview() {
                const name = $('#name').val() || 'اسم الفئة';
                const description = $('#description').val() || 'وصف الفئة';
                const icon = $('#icon').val() || 'ti ti-category';
                const color = $('#color').val() || 'primary';
                
                $('.preview-name').text(name);
                $('.preview-description').text(description.substring(0, 50) + (description.length > 50 ? '...' : ''));
                $('.preview-icon').attr('class', `preview-icon ${icon} me-2 fs-3`);
                $('.preview-badge').attr('class', `preview-badge badge bg-${color}`);
            }
            
            $('#name, #description, #icon, #color').on('change input', updatePreview);
            
            // إرسال النموذج
            $('#category-form').on('submit', function(e) {
                e.preventDefault();
                
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);
                
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            } else {
                                window.location.href = '{{ route("admin.academy.categories.index") }}';
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ الفئة');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

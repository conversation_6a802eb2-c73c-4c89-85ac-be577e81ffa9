@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-category me-2"></i>
                        فئات الدورات التدريبية
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.categories.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة فئة جديدة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الفئة</th>
                                    <th>الوصف</th>
                                    <th>عدد الدورات</th>
                                    <th>الترتيب</th>
                                    <th>الحالة</th>
                                    <th>مميزة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($categories as $category)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($category->icon)
                                                    <i class="{{ $category->icon }} me-2 fs-4"></i>
                                                @endif
                                                <div>
                                                    <strong>{{ $category->name }}</strong>
                                                    @if($category->parent)
                                                        <br><small class="text-muted">{{ $category->parent->name }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ Str::limit($category->description, 50) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge {{ $category->color_class }}">{{ $category->courses_count }}</span>
                                        </td>
                                        <td>{{ $category->order_index }}</td>
                                        <td>
                                            @if($category->is_active)
                                                <span class="badge bg-success">نشطة</span>
                                            @else
                                                <span class="badge bg-secondary">غير نشطة</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($category->is_featured)
                                                <span class="badge bg-warning">مميزة</span>
                                            @else
                                                <span class="badge bg-light text-dark">عادية</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.categories.show', $category->id) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.categories.edit', $category->id) }}">
                                                            <i class="ti ti-edit me-2"></i>
                                                            تعديل
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item text-danger" onclick="deleteCategory({{ $category->id }})">
                                                            <i class="ti ti-trash me-2"></i>
                                                            حذف
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-category fs-1 mb-3"></i>
                                                <p>لا توجد فئات دورات</p>
                                                <a href="{{ route('admin.academy.categories.create') }}" class="btn btn-primary">
                                                    إضافة فئة جديدة
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($categories->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $categories->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function deleteCategory(categoryId) {
            if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
                $.ajax({
                    url: `/admin/perfect-pharma/academy/categories/${categoryId}`,
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء حذف الفئة');
                    }
                });
            }
        }
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-users fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_students'] }}</h3>
                                    <small>إجمالي الطلاب</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-book fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_courses'] }}</h3>
                                    <small>إجمالي الدورات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['completed_courses'] }}</h3>
                                    <small>دورات مكتملة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-progress fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['average_progress'], 1) }}%</h3>
                                    <small>متوسط التقدم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نظرة عامة على التقدم -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-progress me-2"></i>
                        نظرة عامة على تقدم الطلاب
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.progress.analytics') }}" class="btn btn-info me-2">
                            <i class="ti ti-chart-line me-1"></i>
                            التحليلات التفصيلية
                        </a>
                        <a href="{{ route('admin.academy.enrollments.index') }}" class="btn btn-primary">
                            <i class="ti ti-users me-1"></i>
                            إدارة التسجيلات
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- التسجيلات الحديثة -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3">التسجيلات الحديثة</h5>
                            
                            @if($recentEnrollments->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>الطالب</th>
                                                <th>الدورة</th>
                                                <th>التقدم</th>
                                                <th>الحالة</th>
                                                <th>تاريخ التسجيل</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($recentEnrollments as $enrollment)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar avatar-sm me-2">
                                                                <span class="avatar-initial bg-primary rounded-circle">
                                                                    {{ substr($enrollment->user->name, 0, 1) }}
                                                                </span>
                                                            </div>
                                                            <div>
                                                                <strong>{{ $enrollment->user->first_name }} {{ $enrollment->user->last_name }}</strong>
                                                                <br><small class="text-muted">{{ $enrollment->user->email }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('admin.academy.courses.show', $enrollment->course->id) }}" class="text-decoration-none">
                                                            <strong>{{ $enrollment->course->title }}</strong>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="progress me-2" style="width: 100px; height: 8px;">
                                                                <div class="progress-bar" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                                            </div>
                                                            <span class="text-muted">{{ number_format($enrollment->progress_percentage, 1) }}%</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{{ $enrollment->status === 'completed' ? 'success' : ($enrollment->status === 'in_progress' ? 'primary' : 'secondary') }}">
                                                            {{ $enrollment->status_name ?? $enrollment->status }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="text-muted">{{ $enrollment->created_at->format('Y-m-d') }}</span>
                                                        <br><small class="text-muted">{{ $enrollment->created_at->diffForHumans() }}</small>
                                                    </td>
                                                    <td>
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                                <i class="ti ti-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li>
                                                                    <a class="dropdown-item" href="{{ route('admin.academy.courses.progress.course', $enrollment->course_id) }}">
                                                                        <i class="ti ti-eye me-2"></i>
                                                                        عرض التقدم
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="{{ route('admin.academy.courses.show', $enrollment->course_id) }}">
                                                                        <i class="ti ti-book me-2"></i>
                                                                        عرض الدورة
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="ti ti-users fs-1 mb-3"></i>
                                        <p>لا توجد تسجيلات حديثة</p>
                                        <a href="{{ route('admin.academy.enrollments.create') }}" class="btn btn-primary">
                                            <i class="ti ti-plus me-1"></i>
                                            تسجيل طالب جديد
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- روابط سريعة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">روابط سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="{{ route('admin.academy.courses.index') }}" class="btn btn-outline-primary w-100 mb-2">
                                                <i class="ti ti-book me-2"></i>
                                                إدارة الدورات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('admin.academy.enrollments.index') }}" class="btn btn-outline-success w-100 mb-2">
                                                <i class="ti ti-users me-2"></i>
                                                إدارة التسجيلات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('admin.academy.certificates.index') }}" class="btn btn-outline-warning w-100 mb-2">
                                                <i class="ti ti-certificate me-2"></i>
                                                إدارة الشهادات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ route('admin.academy.progress.analytics') }}" class="btn btn-outline-info w-100 mb-2">
                                                <i class="ti ti-chart-line me-2"></i>
                                                التحليلات التفصيلية
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

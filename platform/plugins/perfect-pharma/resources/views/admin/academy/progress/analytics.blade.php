@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات عامة -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-chart-pie me-2"></i>
                        تحليلات التقدم للدورة: {{ $course->title }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.progress.course', $course->id) }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة لقائمة الطلاب
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-primary">{{ $totalEnrollments }}</h3>
                                <p class="text-muted">إجمالي المسجلين</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-success">{{ $completedEnrollments }}</h3>
                                <p class="text-muted">مكتملة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-info">{{ number_format($averageCompletionTime ?? 0, 1) }}</h3>
                                <p class="text-muted">متوسط أيام الإكمال</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-warning">{{ $totalEnrollments > 0 ? number_format(($completedEnrollments / $totalEnrollments) * 100, 1) : 0 }}%</h3>
                                <p class="text-muted">معدل الإكمال</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تحليل المحتوى -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-list me-2"></i>
                        تحليل أداء المحتوى
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>المحتوى</th>
                                    <th>المشاهدات</th>
                                    <th>الإكمال</th>
                                    <th>معدل الإكمال</th>
                                    <th>متوسط الوقت</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($contentAnalytics as $analytics)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="ti ti-{{ $analytics['content']->type === 'video' ? 'video' : ($analytics['content']->type === 'text' ? 'file-text' : 'file') }} me-2 text-primary"></i>
                                                <div>
                                                    <strong>{{ $analytics['content']->title }}</strong>
                                                    <br><small class="text-muted">{{ $analytics['content']->type_name }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $analytics['total_views'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ $analytics['completed'] }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 80px;">
                                                    <div class="progress-bar bg-{{ $analytics['completion_rate'] >= 70 ? 'success' : ($analytics['completion_rate'] >= 40 ? 'warning' : 'danger') }}" 
                                                         style="width: {{ $analytics['completion_rate'] }}%"></div>
                                                </div>
                                                <span class="text-muted">{{ number_format($analytics['completion_rate'], 1) }}%</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ number_format($analytics['average_time'], 0) }} دقيقة</span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <i class="ti ti-file-x fs-1 text-muted mb-3"></i>
                                            <p class="text-muted">لا يوجد محتوى للتحليل</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- تحليل الاختبارات -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-help me-2"></i>
                        تحليل الاختبارات
                    </h5>
                </div>
                <div class="card-body">
                    @forelse($quizAnalytics as $analytics)
                        <div class="mb-4 p-3 border rounded">
                            <h6 class="mb-3">{{ $analytics['quiz']->title }}</h6>
                            
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <h5 class="text-primary mb-0">{{ $analytics['total_attempts'] }}</h5>
                                        <small class="text-muted">المحاولات</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2">
                                        <h5 class="text-info mb-0">{{ $analytics['unique_students'] }}</h5>
                                        <small class="text-muted">الطلاب</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted">معدل النجاح</small>
                                    <small class="text-muted">{{ number_format($analytics['pass_rate'], 1) }}%</small>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-{{ $analytics['pass_rate'] >= 70 ? 'success' : ($analytics['pass_rate'] >= 50 ? 'warning' : 'danger') }}" 
                                         style="width: {{ $analytics['pass_rate'] }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted">متوسط النتيجة</small>
                                    <small class="text-muted">{{ number_format($analytics['average_score'], 1) }}%</small>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-info" style="width: {{ $analytics['average_score'] }}%"></div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="ti ti-help-circle fs-1 text-muted mb-3"></i>
                            <p class="text-muted">لا توجد اختبارات للتحليل</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- رسم بياني للتقدم -->
        <div class="col-12 mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-chart-line me-2"></i>
                        توزيع مستويات التقدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h4 class="text-danger">{{ $totalEnrollments - $completedEnrollments }}</h4>
                                <p class="text-muted mb-0">غير مكتمل</p>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-danger" style="width: {{ $totalEnrollments > 0 ? (($totalEnrollments - $completedEnrollments) / $totalEnrollments) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h4 class="text-warning">0</h4>
                                <p class="text-muted mb-0">25-50%</p>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-warning" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h4 class="text-info">0</h4>
                                <p class="text-muted mb-0">50-75%</p>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-info" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h4 class="text-success">{{ $completedEnrollments }}</h4>
                                <p class="text-muted mb-0">مكتمل 100%</p>
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-success" style="width: {{ $totalEnrollments > 0 ? ($completedEnrollments / $totalEnrollments) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- توصيات التحسين -->
        <div class="col-12 mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-bulb me-2"></i>
                        توصيات التحسين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if($totalEnrollments > 0 && ($completedEnrollments / $totalEnrollments) < 0.5)
                            <div class="col-md-6">
                                <div class="alert alert-warning">
                                    <h6><i class="ti ti-alert-triangle me-2"></i>معدل إكمال منخفض</h6>
                                    <p class="mb-0">معدل الإكمال أقل من 50%. يُنصح بمراجعة محتوى الدورة وتحسين تجربة التعلم.</p>
                                </div>
                            </div>
                        @endif
                        
                        @if(count($quizAnalytics) > 0)
                            @php
                                $lowPerformingQuizzes = collect($quizAnalytics)->where('pass_rate', '<', 60);
                            @endphp
                            @if($lowPerformingQuizzes->count() > 0)
                                <div class="col-md-6">
                                    <div class="alert alert-danger">
                                        <h6><i class="ti ti-help-circle me-2"></i>اختبارات صعبة</h6>
                                        <p class="mb-0">{{ $lowPerformingQuizzes->count() }} اختبار(ات) لديها معدل نجاح أقل من 60%. يُنصح بمراجعة الأسئلة.</p>
                                    </div>
                                </div>
                            @endif
                        @endif
                        
                        @if($totalEnrollments > 0 && ($completedEnrollments / $totalEnrollments) >= 0.8)
                            <div class="col-md-6">
                                <div class="alert alert-success">
                                    <h6><i class="ti ti-check me-2"></i>أداء ممتاز</h6>
                                    <p class="mb-0">معدل إكمال عالي! الدورة تحقق نتائج ممتازة مع الطلاب.</p>
                                </div>
                            </div>
                        @endif
                        
                        @if(count($contentAnalytics) > 0)
                            @php
                                $lowEngagementContent = collect($contentAnalytics)->where('completion_rate', '<', 40);
                            @endphp
                            @if($lowEngagementContent->count() > 0)
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <h6><i class="ti ti-eye-off me-2"></i>محتوى يحتاج تحسين</h6>
                                        <p class="mb-0">{{ $lowEngagementContent->count() }} محتوى لديه معدل إكمال أقل من 40%. يُنصح بمراجعة وتحسين هذا المحتوى.</p>
                                    </div>
                                </div>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

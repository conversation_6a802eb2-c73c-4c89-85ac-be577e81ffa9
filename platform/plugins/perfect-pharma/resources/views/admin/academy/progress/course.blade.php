@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-users fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_enrolled'] }}</h3>
                                    <small>إجمالي المسجلين</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['completed'] }}</h3>
                                    <small>مكتملة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-clock fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['in_progress'] }}</h3>
                                    <small>جاري</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-chart-line fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['average_progress'], 1) }}%</h3>
                                    <small>متوسط التقدم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الطلاب -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-chart-bar me-2"></i>
                        تقدم الطلاب في الدورة: {{ $course->title }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.progress.analytics', $course->id) }}" class="btn btn-info me-2">
                            <i class="ti ti-chart-pie me-1"></i>
                            التحليلات
                        </a>
                        <a href="{{ route('admin.academy.courses.progress.export', $course->id) }}" class="btn btn-success me-2">
                            <i class="ti ti-download me-1"></i>
                            تصدير
                        </a>
                        <a href="{{ route('admin.academy.courses.show', $course->id) }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للدورة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" placeholder="البحث بالاسم أو البريد الإلكتروني..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <select name="status" class="form-select me-2" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="enrolled" {{ request('status') === 'enrolled' ? 'selected' : '' }}>مسجل</option>
                                    <option value="in_progress" {{ request('status') === 'in_progress' ? 'selected' : '' }}>جاري</option>
                                    <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>مكتمل</option>
                                    <option value="dropped" {{ request('status') === 'dropped' ? 'selected' : '' }}>منسحب</option>
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الطالب</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>التقدم</th>
                                    <th>الوقت المستغرق</th>
                                    <th>آخر نشاط</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($enrollments as $enrollment)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ substr($enrollment->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $enrollment->user->name }}</strong>
                                                    <br><small class="text-muted">{{ $enrollment->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $enrollment->enrollment_date->format('Y-m-d') }}</span>
                                            <br><small>{{ $enrollment->duration_since_enrollment }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $enrollment->status_color }}">
                                                {{ $enrollment->status_name }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 100px;">
                                                    <div class="progress-bar bg-{{ $enrollment->progress_percentage >= 100 ? 'success' : ($enrollment->progress_percentage >= 50 ? 'warning' : 'primary') }}" 
                                                         style="width: {{ $enrollment->progress_percentage }}%"></div>
                                                </div>
                                                <span class="text-muted">{{ number_format($enrollment->progress_percentage, 1) }}%</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $enrollment->formatted_total_time_spent }}</span>
                                        </td>
                                        <td>
                                            @if($enrollment->progress->isNotEmpty())
                                                <span class="text-muted">{{ $enrollment->progress->max('last_accessed_at')?->diffForHumans() ?? 'لا يوجد' }}</span>
                                            @else
                                                <span class="text-muted">لم يبدأ</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.progress.student', [$course->id, $enrollment->user_id]) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض التفاصيل
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item" onclick="resetProgress({{ $enrollment->user_id }})">
                                                            <i class="ti ti-refresh me-2"></i>
                                                            إعادة تعيين التقدم
                                                        </button>
                                                    </li>
                                                    @if($enrollment->canReceiveCertificate())
                                                        <li>
                                                            <button class="dropdown-item text-success" onclick="issueCertificate({{ $enrollment->user_id }})">
                                                                <i class="ti ti-certificate me-2"></i>
                                                                إصدار شهادة
                                                            </button>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-users fs-1 mb-3"></i>
                                                <p>لا يوجد طلاب مسجلين في هذه الدورة</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($enrollments->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $enrollments->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function resetProgress(userId) {
            if (confirm('هل أنت متأكد من إعادة تعيين تقدم هذا الطالب؟ سيتم حذف جميع البيانات المرتبطة بتقدمه.')) {
                $.ajax({
                    url: `/admin/perfect-pharma/academy/courses/{{ $course->id }}/progress/student/${userId}/reset`,
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء إعادة تعيين التقدم');
                    }
                });
            }
        }

        function issueCertificate(userId) {
            if (confirm('هل تريد إصدار شهادة لهذا الطالب؟')) {
                $.ajax({
                    url: `/admin/perfect-pharma/academy/courses/{{ $course->id }}/certificates/issue`,
                    method: 'POST',
                    data: {
                        user_id: userId,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء إصدار الشهادة');
                    }
                });
            }
        }
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات عامة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-users fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $generalStats['total_enrollments'] }}</h3>
                                    <small>إجمالي التسجيلات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $generalStats['completed_enrollments'] }}</h3>
                                    <small>تسجيلات مكتملة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-clock fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $generalStats['active_enrollments'] }}</h3>
                                    <small>تسجيلات نشطة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-book fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $generalStats['total_courses'] }}</h3>
                                    <small>إجمالي الدورات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-user fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $generalStats['total_students'] }}</h3>
                                    <small>إجمالي الطلاب</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-dark text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-percentage fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ number_format($generalStats['completion_rate'], 1) }}%</h3>
                                    <small>معدل الإكمال</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- التسجيلات والإكمالات الشهرية -->
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">التسجيلات والإكمالات الشهرية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyProgressChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- توزيع التقدم -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">توزيع التقدم</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="progressDistributionChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- أكثر الدورات شعبية -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">أكثر الدورات شعبية</h5>
                    </div>
                    <div class="card-body">
                        @if($popularCourses->count() > 0)
                            <div class="list-group list-group-flush">
                                @foreach($popularCourses as $course)
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>{{ $course->course->title ?? 'دورة محذوفة' }}</strong>
                                        </div>
                                        <span class="badge bg-primary">{{ $course->enrollments_count }} تسجيل</span>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center text-muted py-4">
                                <i class="ti ti-book fs-1 mb-3"></i>
                                <p>لا توجد تسجيلات</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- أكثر الدورات إكمالاً -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">أكثر الدورات إكمالاً</h5>
                    </div>
                    <div class="card-body">
                        @if($topCompletedCourses->count() > 0)
                            <div class="list-group list-group-flush">
                                @foreach($topCompletedCourses as $course)
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>{{ $course->course->title ?? 'دورة محذوفة' }}</strong>
                                            <br><small class="text-muted">{{ $course->completed_enrollments }}/{{ $course->total_enrollments }} مكتمل</small>
                                        </div>
                                        <span class="badge bg-success">{{ number_format($course->completion_rate, 1) }}%</span>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center text-muted py-4">
                                <i class="ti ti-certificate fs-1 mb-3"></i>
                                <p>لا توجد دورات مكتملة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">روابط سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="{{ route('admin.academy.progress.overview') }}" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="ti ti-eye me-2"></i>
                                    نظرة عامة على التقدم
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.academy.enrollments.index') }}" class="btn btn-outline-success w-100 mb-2">
                                    <i class="ti ti-users me-2"></i>
                                    إدارة التسجيلات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.academy.courses.index') }}" class="btn btn-outline-info w-100 mb-2">
                                    <i class="ti ti-book me-2"></i>
                                    إدارة الدورات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.academy.certificates.index') }}" class="btn btn-outline-warning w-100 mb-2">
                                    <i class="ti ti-certificate me-2"></i>
                                    إدارة الشهادات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // رسم بياني للتسجيلات والإكمالات الشهرية
        const monthlyProgressCtx = document.getElementById('monthlyProgressChart').getContext('2d');
        const monthlyProgressChart = new Chart(monthlyProgressCtx, {
            type: 'line',
            data: {
                labels: [
                    @foreach($monthlyEnrollments as $month)
                        '{{ $month['month_name'] }}',
                    @endforeach
                ],
                datasets: [{
                    label: 'التسجيلات',
                    data: [
                        @foreach($monthlyEnrollments as $month)
                            {{ $month['count'] }},
                        @endforeach
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'الإكمالات',
                    data: [
                        @foreach($monthlyCompletions as $month)
                            {{ $month['count'] }},
                        @endforeach
                    ],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني لتوزيع التقدم
        const progressDistributionCtx = document.getElementById('progressDistributionChart').getContext('2d');
        const progressDistributionChart = new Chart(progressDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['0-25%', '25-50%', '50-75%', '75-100%'],
                datasets: [{
                    data: [
                        {{ $progressDistribution['0-25%'] }},
                        {{ $progressDistribution['25-50%'] }},
                        {{ $progressDistribution['50-75%'] }},
                        {{ $progressDistribution['75-100%'] }}
                    ],
                    backgroundColor: [
                        '#dc3545',
                        '#ffc107',
                        '#17a2b8',
                        '#28a745'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
@endpush

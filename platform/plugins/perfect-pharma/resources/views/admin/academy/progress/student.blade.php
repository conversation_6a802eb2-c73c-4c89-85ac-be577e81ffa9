@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- معلومات الطالب والدورة -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-user me-2"></i>
                        تقدم الطالب: {{ $user->name }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.progress.course', $course->id) }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة لقائمة الطلاب
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>معلومات الطالب</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التسجيل:</strong></td>
                                    <td>{{ $enrollment->enrollment_date->format('Y-m-d H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $enrollment->status_color }}">
                                            {{ $enrollment->status_name }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>إحصائيات التقدم</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>نسبة الإكمال:</strong></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 100px;">
                                                <div class="progress-bar bg-{{ $enrollment->progress_percentage >= 100 ? 'success' : 'primary' }}" 
                                                     style="width: {{ $enrollment->progress_percentage }}%"></div>
                                            </div>
                                            <span>{{ number_format($enrollment->progress_percentage, 1) }}%</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>الوقت المستغرق:</strong></td>
                                    <td>{{ $enrollment->formatted_total_time_spent }}</td>
                                </tr>
                                @if($enrollment->completion_date)
                                    <tr>
                                        <td><strong>تاريخ الإكمال:</strong></td>
                                        <td>{{ $enrollment->completion_date->format('Y-m-d H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>مدة الإكمال:</strong></td>
                                        <td>{{ $enrollment->completion_time }}</td>
                                    </tr>
                                @endif
                                <tr>
                                    <td><strong>شهادة صادرة:</strong></td>
                                    <td>
                                        @if($enrollment->certificate_issued)
                                            <span class="badge bg-success">نعم</span>
                                        @else
                                            <span class="badge bg-secondary">لا</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تقدم المحتوى -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-list me-2"></i>
                        تقدم المحتوى
                    </h5>
                </div>
                <div class="card-body">
                    @forelse($detailedProgress as $item)
                        <div class="d-flex align-items-center mb-3 p-3 border rounded">
                            <div class="me-3">
                                <i class="ti ti-{{ $item['content']->type === 'video' ? 'video' : ($item['content']->type === 'text' ? 'file-text' : 'file') }} fs-4 text-{{ $item['progress'] ? ($item['progress']->isCompleted() ? 'success' : 'warning') : 'muted' }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $item['content']->title }}</h6>
                                <p class="text-muted mb-2">{{ Str::limit($item['content']->description, 100) }}</p>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-3" style="width: 150px;">
                                        <div class="progress-bar bg-{{ $item['progress_percentage'] >= 100 ? 'success' : 'primary' }}" 
                                             style="width: {{ $item['progress_percentage'] }}%"></div>
                                    </div>
                                    <span class="text-muted me-3">{{ number_format($item['progress_percentage'], 1) }}%</span>
                                    @if($item['time_spent'] > 0)
                                        <small class="text-muted">
                                            <i class="ti ti-clock me-1"></i>
                                            {{ $item['time_spent'] }} دقيقة
                                        </small>
                                    @endif
                                </div>
                            </div>
                            <div>
                                <span class="badge bg-{{ $item['progress'] ? ($item['progress']->isCompleted() ? 'success' : ($item['progress']->isInProgress() ? 'warning' : 'secondary')) : 'secondary' }}">
                                    {{ $item['progress'] ? $item['progress']->status_name : 'لم يبدأ' }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="ti ti-file-x fs-1 text-muted mb-3"></i>
                            <p class="text-muted">لا يوجد محتوى في هذه الدورة</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- نتائج الاختبارات -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-help me-2"></i>
                        نتائج الاختبارات
                    </h5>
                </div>
                <div class="card-body">
                    @forelse($quizResults as $result)
                        <div class="mb-3 p-3 border rounded">
                            <h6 class="mb-2">{{ $result['quiz']->title }}</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted d-block">المحاولات</small>
                                    <strong>{{ $result['attempts_count'] }}/{{ $result['quiz']->max_attempts }}</strong>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">أفضل نتيجة</small>
                                    @if($result['best_score'] !== null)
                                        <strong class="text-{{ $result['passed'] ? 'success' : 'danger' }}">
                                            {{ number_format($result['best_score'], 1) }}%
                                        </strong>
                                    @else
                                        <strong class="text-muted">لم يحاول</strong>
                                    @endif
                                </div>
                            </div>
                            @if($result['last_attempt'])
                                <div class="mt-2">
                                    <small class="text-muted">
                                        آخر محاولة: {{ $result['last_attempt']->created_at->diffForHumans() }}
                                    </small>
                                </div>
                            @endif
                            <div class="mt-2">
                                <span class="badge bg-{{ $result['passed'] ? 'success' : ($result['attempts_count'] > 0 ? 'danger' : 'secondary') }}">
                                    {{ $result['passed'] ? 'نجح' : ($result['attempts_count'] > 0 ? 'رسب' : 'لم يحاول') }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="ti ti-help-circle fs-1 text-muted mb-3"></i>
                            <p class="text-muted">لا توجد اختبارات في هذه الدورة</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-settings me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-warning" onclick="resetProgress()">
                            <i class="ti ti-refresh me-1"></i>
                            إعادة تعيين التقدم
                        </button>
                        
                        @if($enrollment->canReceiveCertificate())
                            <button class="btn btn-success" onclick="issueCertificate()">
                                <i class="ti ti-certificate me-1"></i>
                                إصدار شهادة
                            </button>
                        @endif
                        
                        @if($enrollment->status !== 'completed')
                            <button class="btn btn-primary" onclick="markAsCompleted()">
                                <i class="ti ti-check me-1"></i>
                                تحديد كمكتمل
                            </button>
                        @endif
                        
                        @if($enrollment->status !== 'dropped')
                            <button class="btn btn-danger" onclick="dropStudent()">
                                <i class="ti ti-user-x me-1"></i>
                                إلغاء التسجيل
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function resetProgress() {
            if (confirm('هل أنت متأكد من إعادة تعيين تقدم هذا الطالب؟ سيتم حذف جميع البيانات المرتبطة بتقدمه.')) {
                $.ajax({
                    url: '{{ route("admin.academy.courses.progress.reset", [$course->id, $user->id]) }}',
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء إعادة تعيين التقدم');
                    }
                });
            }
        }

        function issueCertificate() {
            if (confirm('هل تريد إصدار شهادة لهذا الطالب؟')) {
                $.ajax({
                    url: '/admin/perfect-pharma/academy/courses/{{ $course->id }}/certificates/issue',
                    method: 'POST',
                    data: {
                        user_id: {{ $user->id }},
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء إصدار الشهادة');
                    }
                });
            }
        }

        function markAsCompleted() {
            if (confirm('هل تريد تحديد هذا الطالب كمكتمل للدورة؟')) {
                $.ajax({
                    url: '{{ route("admin.academy.courses.progress.update", [$course->id, $user->id]) }}',
                    method: 'PUT',
                    data: {
                        action: 'mark_completed',
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء تحديث الحالة');
                    }
                });
            }
        }

        function dropStudent() {
            if (confirm('هل أنت متأكد من إلغاء تسجيل هذا الطالب؟')) {
                $.ajax({
                    url: '{{ route("admin.academy.courses.progress.update", [$course->id, $user->id]) }}',
                    method: 'PUT',
                    data: {
                        action: 'drop',
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء تحديث الحالة');
                    }
                });
            }
        }
    </script>
@endpush

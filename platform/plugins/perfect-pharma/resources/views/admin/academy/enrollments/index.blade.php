@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-users fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_enrollments'] }}</h3>
                                    <small>إجمالي التسجيلات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-check fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['active_enrollments'] }}</h3>
                                    <small>تسجيلات نشطة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-certificate fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['completed_enrollments'] }}</h3>
                                    <small>دورات مكتملة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-user fs-1 me-3"></i>
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_students'] }}</h3>
                                    <small>إجمالي الطلاب</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة التسجيلات -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-users me-2"></i>
                        تسجيل الطلاب
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.enrollments.bulk') }}" class="btn btn-info me-2">
                            <i class="ti ti-users-plus me-1"></i>
                            تسجيل مجمع
                        </a>
                        <a href="{{ route('admin.academy.enrollments.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            تسجيل طالب جديد
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" placeholder="البحث..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <form method="GET">
                                <select name="course_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الدورات</option>
                                    @foreach($courses as $course)
                                        <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                            {{ $course->title }}
                                        </option>
                                    @endforeach
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                            </form>
                        </div>
                        <div class="col-md-4">
                            <form method="GET">
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>مكتمل</option>
                                    <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>معلق</option>
                                </select>
                                @if(request('search'))
                                    <input type="hidden" name="search" value="{{ request('search') }}">
                                @endif
                                @if(request('course_id'))
                                    <input type="hidden" name="course_id" value="{{ request('course_id') }}">
                                @endif
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الطالب</th>
                                    <th>الدورة</th>
                                    <th>التقدم</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($enrollments as $enrollment)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ substr($enrollment->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $enrollment->user->first_name }} {{ $enrollment->user->last_name }}</strong>
                                                    <br><small class="text-muted">{{ $enrollment->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.academy.courses.show', $enrollment->course->id) }}" class="text-decoration-none">
                                                <strong>{{ $enrollment->course->title }}</strong>
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 100px; height: 8px;">
                                                    <div class="progress-bar" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                                </div>
                                                <span class="text-muted">{{ number_format($enrollment->progress_percentage, 1) }}%</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $enrollment->status === 'completed' ? 'success' : ($enrollment->status === 'active' ? 'primary' : 'warning') }}">
                                                {{ $enrollment->status_name ?? $enrollment->status }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $enrollment->enrolled_at?->format('Y-m-d') ?? $enrollment->created_at->format('Y-m-d') }}</span>
                                            <br><small class="text-muted">{{ $enrollment->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.progress.course', $enrollment->course_id) }}">
                                                            <i class="ti ti-progress me-2"></i>
                                                            عرض التقدم
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.show', $enrollment->course_id) }}">
                                                            <i class="ti ti-book me-2"></i>
                                                            عرض الدورة
                                                        </a>
                                                    </li>
                                                    @if($enrollment->status === 'completed')
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('admin.academy.certificates.index') }}?user_id={{ $enrollment->user_id }}&course_id={{ $enrollment->course_id }}">
                                                                <i class="ti ti-certificate me-2"></i>
                                                                عرض الشهادة
                                                            </a>
                                                        </li>
                                                    @endif
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item text-warning" onclick="suspendEnrollment({{ $enrollment->id }})">
                                                            <i class="ti ti-pause me-2"></i>
                                                            تعليق التسجيل
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-users fs-1 mb-3"></i>
                                                <p>لا توجد تسجيلات</p>
                                                <a href="{{ route('admin.academy.enrollments.create') }}" class="btn btn-primary">
                                                    <i class="ti ti-plus me-1"></i>
                                                    تسجيل طالب جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($enrollments->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $enrollments->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function suspendEnrollment(enrollmentId) {
            if (confirm('هل أنت متأكد من تعليق هذا التسجيل؟')) {
                // يمكن إضافة AJAX call هنا لتعليق التسجيل
                console.log('تعليق التسجيل:', enrollmentId);
            }
        }
    </script>
@endpush

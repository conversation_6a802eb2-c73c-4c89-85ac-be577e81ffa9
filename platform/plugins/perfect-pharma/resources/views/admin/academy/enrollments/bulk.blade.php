@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-users-plus me-2"></i>
                        تسجيل مجمع للطلاب
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.enrollments.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.academy.enrollments.bulk.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="course_id" class="form-label">الدورة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="course_id" name="course_id" required>
                                        <option value="">اختر الدورة</option>
                                        @foreach($courses as $course)
                                            <option value="{{ $course->id }}" {{ old('course_id') == $course->id ? 'selected' : '' }}>
                                                {{ $course->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('course_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">حالة التسجيل <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>نشط</option>
                                        <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                        <option value="suspended" {{ old('status') == 'suspended' ? 'selected' : '' }}>معلق</option>
                                    </select>
                                    @error('status')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="user_emails" class="form-label">عناوين البريد الإلكتروني للطلاب <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="user_emails" name="user_emails" rows="10" required placeholder="أدخل عنوان بريد إلكتروني واحد في كل سطر...">{{ old('user_emails') }}</textarea>
                                    @error('user_emails')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        أدخل عنوان بريد إلكتروني واحد في كل سطر. مثال:<br>
                                        <EMAIL><br>
                                        <EMAIL><br>
                                        <EMAIL>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <i class="ti ti-alert-triangle me-2"></i>
                                    <strong>تنبيه:</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>سيتم التحقق من وجود كل عنوان بريد إلكتروني في النظام</li>
                                        <li>سيتم تجاهل العناوين غير الموجودة وإظهار تقرير بالأخطاء</li>
                                        <li>سيتم تجاهل الطلاب المسجلين مسبقاً في نفس الدورة</li>
                                        <li>يمكن تسجيل حتى 100 طالب في المرة الواحدة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.academy.enrollments.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="button" class="btn btn-info" onclick="validateEmails()">
                                        <i class="ti ti-check me-1"></i>
                                        التحقق من العناوين
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-users-plus me-1"></i>
                                        تسجيل الطلاب
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- معاينة التسجيل المجمع -->
    <div class="row mt-4" id="bulkPreview" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">معاينة التسجيل المجمع</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">معلومات الدورة</h6>
                            <p class="mb-1"><strong>اسم الدورة:</strong> <span id="previewCourseName">-</span></p>
                            <p class="mb-1"><strong>حالة التسجيل:</strong> <span id="previewStatus">-</span></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">إحصائيات</h6>
                            <p class="mb-1"><strong>عدد العناوين:</strong> <span id="previewEmailCount">0</span></p>
                            <p class="mb-1"><strong>عناوين صحيحة:</strong> <span id="previewValidEmails">0</span></p>
                            <p class="mb-1"><strong>عناوين غير صحيحة:</strong> <span id="previewInvalidEmails">0</span></p>
                        </div>
                    </div>
                    
                    <div class="mt-3" id="emailsList" style="display: none;">
                        <h6>قائمة العناوين:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="valid-emails">
                                    <h6 class="text-success">عناوين صحيحة:</h6>
                                    <ul id="validEmailsList" class="list-unstyled"></ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="invalid-emails">
                                    <h6 class="text-danger">عناوين غير صحيحة:</h6>
                                    <ul id="invalidEmailsList" class="list-unstyled"></ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // تحديث المعاينة عند تغيير البيانات
            $('#course_id, #status, #user_emails').on('change input', updatePreview);
            
            function updatePreview() {
                const courseId = $('#course_id').val();
                const status = $('#status').val();
                const emails = $('#user_emails').val();
                
                if (courseId && status && emails) {
                    const courseName = $('#course_id option:selected').text();
                    const statusText = $('#status option:selected').text();
                    
                    $('#previewCourseName').text(courseName);
                    $('#previewStatus').text(statusText);
                    
                    // تحليل العناوين
                    const emailLines = emails.split('\n').filter(line => line.trim() !== '');
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    
                    let validEmails = [];
                    let invalidEmails = [];
                    
                    emailLines.forEach(line => {
                        const email = line.trim();
                        if (emailRegex.test(email)) {
                            validEmails.push(email);
                        } else if (email !== '') {
                            invalidEmails.push(email);
                        }
                    });
                    
                    $('#previewEmailCount').text(emailLines.length);
                    $('#previewValidEmails').text(validEmails.length);
                    $('#previewInvalidEmails').text(invalidEmails.length);
                    
                    $('#bulkPreview').show();
                } else {
                    $('#bulkPreview').hide();
                }
            }
        });
        
        function validateEmails() {
            const emails = $('#user_emails').val();
            const emailLines = emails.split('\n').filter(line => line.trim() !== '');
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            let validEmails = [];
            let invalidEmails = [];
            
            emailLines.forEach(line => {
                const email = line.trim();
                if (emailRegex.test(email)) {
                    validEmails.push(email);
                } else if (email !== '') {
                    invalidEmails.push(email);
                }
            });
            
            // عرض النتائج
            $('#validEmailsList').empty();
            $('#invalidEmailsList').empty();
            
            validEmails.forEach(email => {
                $('#validEmailsList').append(`<li><i class="ti ti-check text-success me-1"></i>${email}</li>`);
            });
            
            invalidEmails.forEach(email => {
                $('#invalidEmailsList').append(`<li><i class="ti ti-x text-danger me-1"></i>${email}</li>`);
            });
            
            $('#emailsList').show();
            
            if (invalidEmails.length > 0) {
                alert(`تم العثور على ${invalidEmails.length} عنوان بريد إلكتروني غير صحيح. يرجى مراجعة القائمة أدناه.`);
            } else {
                alert(`جميع العناوين صحيحة! (${validEmails.length} عنوان)`);
            }
        }
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-user-plus me-2"></i>
                        تسجيل طالب جديد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.enrollments.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.academy.enrollments.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">الطالب <span class="text-danger">*</span></label>
                                    <select class="form-select" id="user_id" name="user_id" required>
                                        <option value="">اختر الطالب</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="course_id" class="form-label">الدورة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="course_id" name="course_id" required>
                                        <option value="">اختر الدورة</option>
                                        @foreach($courses as $course)
                                            <option value="{{ $course->id }}" {{ old('course_id') == $course->id ? 'selected' : '' }}>
                                                {{ $course->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('course_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">حالة التسجيل <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>نشط</option>
                                        <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                        <option value="suspended" {{ old('status') == 'suspended' ? 'selected' : '' }}>معلق</option>
                                    </select>
                                    @error('status')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="enrolled_at" class="form-label">تاريخ التسجيل</label>
                                    <input type="date" class="form-control" id="enrolled_at" name="enrolled_at" 
                                           value="{{ old('enrolled_at', now()->format('Y-m-d')) }}">
                                    @error('enrolled_at')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">سيتم استخدام التاريخ الحالي إذا ترك فارغاً</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> سيتم التحقق من عدم وجود تسجيل مسبق للطالب في نفس الدورة.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.academy.enrollments.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-user-plus me-1"></i>
                                        تسجيل الطالب
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- معاينة التسجيل -->
    <div class="row mt-4" id="enrollmentPreview" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">معاينة التسجيل</h5>
                </div>
                <div class="card-body">
                    <div class="enrollment-preview border rounded p-4 bg-light">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">معلومات الطالب</h6>
                                <p class="mb-1"><strong>الاسم:</strong> <span id="previewStudentName">-</span></p>
                                <p class="mb-1"><strong>البريد الإلكتروني:</strong> <span id="previewStudentEmail">-</span></p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">معلومات الدورة</h6>
                                <p class="mb-1"><strong>اسم الدورة:</strong> <span id="previewCourseName">-</span></p>
                                <p class="mb-1"><strong>حالة التسجيل:</strong> <span id="previewStatus">-</span></p>
                                <p class="mb-1"><strong>تاريخ التسجيل:</strong> <span id="previewDate">-</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // تحديث المعاينة عند تغيير البيانات
            $('#user_id, #course_id, #status, #enrolled_at').on('change', updatePreview);
            
            function updatePreview() {
                const userId = $('#user_id').val();
                const courseId = $('#course_id').val();
                const status = $('#status').val();
                const date = $('#enrolled_at').val();
                
                if (userId && courseId) {
                    const studentText = $('#user_id option:selected').text();
                    const studentName = studentText.split(' (')[0];
                    const studentEmail = studentText.match(/\(([^)]+)\)/)?.[1] || '';
                    const courseName = $('#course_id option:selected').text();
                    const statusText = $('#status option:selected').text();
                    
                    $('#previewStudentName').text(studentName);
                    $('#previewStudentEmail').text(studentEmail);
                    $('#previewCourseName').text(courseName);
                    $('#previewStatus').text(statusText);
                    $('#previewDate').text(date || '{{ now()->format("Y-m-d") }}');
                    
                    $('#enrollmentPreview').show();
                } else {
                    $('#enrollmentPreview').hide();
                }
            }
            
            // التحقق من وجود تسجيل مسبق
            $('#user_id, #course_id').on('change', function() {
                const userId = $('#user_id').val();
                const courseId = $('#course_id').val();
                
                if (userId && courseId) {
                    // يمكن إضافة AJAX call للتحقق من وجود تسجيل مسبق
                    // وإظهار تحذير للمستخدم
                }
            });
        });
    </script>
@endpush

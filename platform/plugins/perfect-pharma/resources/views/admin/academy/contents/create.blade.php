@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة درس جديد للدورة: {{ $course->title }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.contents.index', $course->id) }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للدروس
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.academy.courses.contents.store', $course->id) }}" method="POST" id="content-form">
                        @csrf
                        
                        <div class="row">
                            <!-- معلومات الدرس الأساسية -->
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات الدرس</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">عنوان الدرس <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف الدرس</label>
                                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="وصف مختصر عن محتوى الدرس..."></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="type" class="form-label">نوع المحتوى <span class="text-danger">*</span></label>
                                            <select class="form-select" id="type" name="type" required>
                                                <option value="">اختر نوع المحتوى</option>
                                                @foreach($contentTypes as $key => $name)
                                                    <option value="{{ $key }}">{{ $name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        
                                        <!-- حقول المحتوى الديناميكية -->
                                        <div id="content-fields">
                                            <!-- سيتم إضافة الحقول هنا حسب نوع المحتوى -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إعدادات الدرس -->
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات الدرس</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="duration_minutes" class="form-label">مدة الدرس (بالدقائق)</label>
                                            <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" min="1">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="order" class="form-label">ترتيب الدرس</label>
                                            <input type="number" class="form-control" id="order" name="order" min="0">
                                            <small class="form-text text-muted">اتركه فارغاً للترتيب التلقائي</small>
                                        </div>
                                        
                                        <!-- تم حذف حقل الحالة لأن الجدول لا يحتوي على عمود status -->
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_free" name="is_free" value="1">
                                                <label class="form-check-label" for="is_free">
                                                    درس مجاني
                                                </label>
                                                <small class="form-text text-muted d-block">يمكن للطلاب مشاهدته بدون اشتراك</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- معاينة الدرس -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معاينة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="content-preview">
                                            <div class="d-flex align-items-center mb-3">
                                                <i class="preview-icon ti ti-file me-2 fs-3"></i>
                                                <div>
                                                    <h6 class="preview-title mb-0">عنوان الدرس</h6>
                                                    <small class="preview-description text-muted">وصف الدرس</small>
                                                </div>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <span class="preview-type badge bg-secondary">نوع المحتوى</span>
                                                <span class="preview-duration badge bg-info">0 دقيقة</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.academy.courses.contents.index', $course->id) }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ الدرس
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // تحديث المعاينة عند تغيير البيانات
            function updatePreview() {
                const title = $('#title').val() || 'عنوان الدرس';
                const description = $('#description').val() || 'وصف الدرس';
                const contentType = $('#content_type option:selected').text() || 'نوع المحتوى';
                const duration = $('#duration_minutes').val() || '0';
                const type = $('#type').val();
                
                $('.preview-title').text(title);
                $('.preview-description').text(description.substring(0, 50) + (description.length > 50 ? '...' : ''));
                $('.preview-type').text(contentType);
                $('.preview-duration').text(duration + ' دقيقة');
                
                // تحديث الأيقونة حسب النوع
                let icon = 'ti ti-file';
                let color = 'secondary';
                
                switch(type) {
                    case 'video':
                        icon = 'ti ti-video';
                        color = 'primary';
                        break;
                    case 'pdf':
                        icon = 'ti ti-file-text';
                        color = 'danger';
                        break;
                    case 'text':
                        icon = 'ti ti-notes';
                        color = 'info';
                        break;
                    case 'quiz':
                        icon = 'ti ti-help';
                        color = 'warning';
                        break;
                    case 'assignment':
                        icon = 'ti ti-clipboard';
                        color = 'success';
                        break;
                }
                
                $('.preview-icon').attr('class', `preview-icon ${icon} me-2 fs-3 text-${color}`);
                $('.preview-type').attr('class', `preview-type badge bg-${color}`);
            }
            
            // تحديث حقول المحتوى حسب النوع
            function updateContentFields() {
                const contentType = $('#type').val();
                const fieldsContainer = $('#content-fields');
                
                fieldsContainer.empty();
                
                if (contentType === 'video') {
                    fieldsContainer.html(`
                        <div class="mb-3">
                            <label for="content_url" class="form-label">رابط الفيديو</label>
                            <input type="url" class="form-control mb-2" id="content_url" name="content_url" placeholder="https://youtube.com/watch?v=...">
                            <small class="form-text text-muted">يدعم YouTube, Vimeo, أو رابط مباشر</small>
                        </div>
                        <div class="text-center mb-2">
                            <span class="text-muted">أو</span>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رفع ملف فيديو</label>
                            <div class="d-flex gap-2">
                                <input type="text" class="form-control" id="video_file_display" name="video_file" placeholder="لم يتم اختيار ملف" readonly>
                                <button type="button" class="btn btn-outline-primary" onclick="openFileManager('video')">
                                    <i class="ti ti-folder-open me-1"></i>
                                    اختر ملف
                                </button>
                            </div>
                        </div>
                    `);
                } else if (contentType === 'pdf') {
                    fieldsContainer.html(`
                        <div class="mb-3">
                            <label for="content_url" class="form-label">رابط ملف PDF</label>
                            <input type="url" class="form-control" id="content_url" name="content_url" placeholder="https://example.com/file.pdf">
                        </div>
                    `);
                } else if (contentType === 'text') {
                    fieldsContainer.html(`
                        <div class="mb-3">
                            <label for="text_content" class="form-label">محتوى الدرس</label>
                            <textarea class="form-control" id="text_content" name="text_content" rows="8" placeholder="اكتب محتوى الدرس هنا..."></textarea>
                        </div>
                    `);
                } else if (contentType === 'quiz') {
                    fieldsContainer.html(`
                        <div class="alert alert-info">
                            <i class="ti ti-info-circle me-2"></i>
                            سيتم إنشاء الاختبار بعد حفظ الدرس
                        </div>
                    `);
                } else if (contentType === 'assignment') {
                    fieldsContainer.html(`
                        <div class="mb-3">
                            <label for="text_content" class="form-label">تعليمات الواجب</label>
                            <textarea class="form-control" id="text_content" name="text_content" rows="6" placeholder="اكتب تعليمات الواجب هنا..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="content_url" class="form-label">رابط ملف الواجب (اختياري)</label>
                            <input type="url" class="form-control" id="content_url" name="content_url" placeholder="https://example.com/assignment.pdf">
                        </div>
                    `);
                }
            }
            
            $('#title, #description, #duration_minutes').on('input', updatePreview);
            $('#type').on('change', function() {
                updateContentFields();
                updatePreview();
            });
            
            // إرسال النموذج
            $('#content-form').on('submit', function(e) {
                e.preventDefault();
                
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);
                
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ الدرس');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });

        // فتح مدير الملفات
        function openFileManager(type) {
            // فتح نافذة جديدة لمدير الملفات
            const fileManagerUrl = '{{ route("admin.academy.files.index") }}?select_mode=true&type=' + type;
            const popup = window.open(fileManagerUrl, 'fileManager', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            // الاستماع لرسائل من النافذة المنبثقة
            window.addEventListener('message', function(event) {
                if (event.data.action === 'fileSelected') {
                    const fileUrl = event.data.url;
                    const fileName = event.data.name;

                    if (type === 'video') {
                        $('#video_file_display').val(fileName);
                        $('#video_file_display').attr('data-url', fileUrl);
                    } else if (type === 'document') {
                        $('#document_file_display').val(fileName);
                        $('#document_file_display').attr('data-url', fileUrl);
                    }

                    popup.close();
                }
            });
        }
    </script>
@endpush

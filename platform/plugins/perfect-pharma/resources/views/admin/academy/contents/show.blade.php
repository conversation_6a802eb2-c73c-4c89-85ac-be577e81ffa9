@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="{{ $content->icon }} me-2"></i>
                        {{ $content->title }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.contents.index', $course->id) }}" class="btn btn-secondary me-2">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للدروس
                        </a>
                        <a href="{{ route('admin.academy.courses.contents.edit', [$course->id, $content->id]) }}" class="btn btn-primary">
                            <i class="ti ti-edit me-1"></i>
                            تعديل الدرس
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- معلومات الدرس -->
                        <div class="col-md-8">
                            <div class="mb-4">
                                <h5>{{ $content->title }}</h5>
                                @if($content->description)
                                    <p class="text-muted">{{ $content->description }}</p>
                                @endif
                            </div>
                            
                            <!-- محتوى الدرس -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">
                                        <i class="{{ $content->icon }} me-2"></i>
                                        محتوى الدرس
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @if($content->type === 'video')
                                        <div class="video-container mb-3">
                                            @if($content->content_url)
                                                <div class="alert alert-info">
                                                    <i class="ti ti-video me-2"></i>
                                                    <strong>رابط الفيديو:</strong>
                                                    <a href="{{ $content->content_url }}" target="_blank" class="ms-2">
                                                        {{ $content->content_url }}
                                                    </a>
                                                </div>
                                            @else
                                                <div class="alert alert-warning">
                                                    <i class="ti ti-alert-triangle me-2"></i>
                                                    لم يتم تحديد رابط الفيديو بعد
                                                </div>
                                            @endif
                                        </div>
                                        
                                    @elseif($content->type === 'pdf')
                                        <div class="pdf-container mb-3">
                                            @if($content->content_url)
                                                <div class="alert alert-info">
                                                    <i class="ti ti-file-text me-2"></i>
                                                    <strong>ملف PDF:</strong>
                                                    <a href="{{ $content->content_url }}" target="_blank" class="ms-2">
                                                        عرض الملف
                                                    </a>
                                                </div>
                                            @else
                                                <div class="alert alert-warning">
                                                    <i class="ti ti-alert-triangle me-2"></i>
                                                    لم يتم رفع ملف PDF بعد
                                                </div>
                                            @endif
                                        </div>
                                        
                                    @elseif($content->type === 'text')
                                        <div class="text-content">
                                            @if($content->text_content)
                                                <div class="content-text p-3 bg-light rounded">
                                                    {!! nl2br(e($content->text_content)) !!}
                                                </div>
                                            @else
                                                <div class="alert alert-warning">
                                                    <i class="ti ti-alert-triangle me-2"></i>
                                                    لم يتم إضافة محتوى نصي بعد
                                                </div>
                                            @endif
                                        </div>
                                        
                                    @elseif($content->type === 'quiz')
                                        <div class="quiz-container">
                                            <div class="alert alert-primary">
                                                <i class="ti ti-help me-2"></i>
                                                <strong>اختبار تفاعلي</strong>
                                                <p class="mb-0 mt-2">سيتم عرض الاختبار للطلاب في هذا القسم</p>
                                            </div>
                                            <!-- هنا يمكن إضافة نظام إدارة الأسئلة لاحقاً -->
                                        </div>
                                        
                                    @elseif($content->type === 'assignment')
                                        <div class="assignment-container">
                                            @if($content->text_content)
                                                <div class="assignment-instructions mb-3">
                                                    <h6>تعليمات الواجب:</h6>
                                                    <div class="content-text p-3 bg-light rounded">
                                                        {!! nl2br(e($content->text_content)) !!}
                                                    </div>
                                                </div>
                                            @endif
                                            
                                            @if($content->content_url)
                                                <div class="assignment-file">
                                                    <div class="alert alert-info">
                                                        <i class="ti ti-paperclip me-2"></i>
                                                        <strong>ملف مرفق:</strong>
                                                        <a href="{{ $content->content_url }}" target="_blank" class="ms-2">
                                                            تحميل الملف
                                                        </a>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <!-- معلومات إضافية -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">معلومات الدرس</h6>
                                </div>
                                <div class="card-body">
                                    <div class="info-item mb-3">
                                        <strong>نوع المحتوى:</strong>
                                        <span class="badge bg-{{ $content->color }} ms-2">{{ $content->content_type_name }}</span>
                                    </div>
                                    
                                    @if($content->duration_minutes)
                                        <div class="info-item mb-3">
                                            <strong>المدة:</strong>
                                            <span class="ms-2">{{ $content->duration_minutes }} دقيقة</span>
                                        </div>
                                    @endif
                                    
                                    <div class="info-item mb-3">
                                        <strong>الترتيب:</strong>
                                        <span class="ms-2">{{ $content->order }}</span>
                                    </div>
                                    
                                    <div class="info-item mb-3">
                                        <strong>نوع الوصول:</strong>
                                        @if($content->is_free)
                                            <span class="badge bg-success ms-2">مجاني</span>
                                        @else
                                            <span class="badge bg-warning ms-2">مدفوع</span>
                                        @endif
                                    </div>
                                    
                                    <div class="info-item mb-3">
                                        <strong>الحالة:</strong>
                                        <span class="badge bg-success ms-2">{{ $content->status_name }}</span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <strong>تاريخ الإنشاء:</strong>
                                        <small class="text-muted d-block">{{ $content->created_at->format('Y-m-d H:i') }}</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إحصائيات الدرس -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">إحصائيات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h5 class="text-primary mb-1">{{ $content->progress->count() }}</h5>
                                                <small class="text-muted">مشاهدة</small>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="border rounded p-3">
                                                <h5 class="text-success mb-1">{{ $content->progress->where('status', 'completed')->count() }}</h5>
                                                <small class="text-muted">مكتمل</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

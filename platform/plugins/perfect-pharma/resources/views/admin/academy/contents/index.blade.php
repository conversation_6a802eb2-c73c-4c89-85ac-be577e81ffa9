@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-list me-2"></i>
                        دروس الدورة: {{ $course->title }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.show', $course->id) }}" class="btn btn-secondary me-2">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للدورة
                        </a>
                        <a href="{{ route('admin.academy.courses.contents.create', $course->id) }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة درس جديد
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الترتيب</th>
                                    <th>الدرس</th>
                                    <th>النوع</th>
                                    <th>المدة</th>
                                    <th>الحالة</th>
                                    <th>مجاني</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="sortable-contents">
                                @forelse($contents as $content)
                                    <tr data-id="{{ $content->id }}">
                                        <td>
                                            <span class="drag-handle" style="cursor: move;">
                                                <i class="ti ti-grip-vertical"></i>
                                            </span>
                                            {{ $content->order }}
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="{{ $content->icon }} me-2 text-{{ $content->color }} fs-4"></i>
                                                <div>
                                                    <strong>{{ $content->title }}</strong>
                                                    @if($content->description)
                                                        <br><small class="text-muted">{{ Str::limit($content->description, 50) }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $content->color }}">{{ $content->content_type_name }}</span>
                                        </td>
                                        <td>
                                            @if($content->duration_minutes)
                                                {{ $content->duration_minutes }} دقيقة
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-success">منشور</span>
                                        </td>
                                        <td>
                                            @if($content->is_free)
                                                <span class="badge bg-info">مجاني</span>
                                            @else
                                                <span class="badge bg-light text-dark">مدفوع</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.contents.show', [$course->id, $content->id]) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.academy.courses.contents.edit', [$course->id, $content->id]) }}">
                                                            <i class="ti ti-edit me-2"></i>
                                                            تعديل
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item text-danger" onclick="deleteContent({{ $content->id }})">
                                                            <i class="ti ti-trash me-2"></i>
                                                            حذف
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-file-plus fs-1 mb-3"></i>
                                                <p>لا توجد دروس في هذه الدورة</p>
                                                <a href="{{ route('admin.academy.courses.contents.create', $course->id) }}" class="btn btn-primary">
                                                    إضافة أول درس
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($contents->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $contents->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        $(document).ready(function() {
            // تفعيل السحب والإفلات لإعادة الترتيب
            const sortable = new Sortable(document.getElementById('sortable-contents'), {
                handle: '.drag-handle',
                animation: 150,
                onEnd: function(evt) {
                    const items = [];
                    $('#sortable-contents tr').each(function(index) {
                        const id = $(this).data('id');
                        if (id) {
                            items.push({
                                id: id,
                                order: index + 1
                            });
                        }
                    });
                    
                    // إرسال الترتيب الجديد للخادم
                    $.ajax({
                        url: '{{ route("admin.academy.courses.contents.reorder", $course->id) }}',
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            contents: items
                        },
                        success: function(response) {
                            if (!response.error) {
                                Botble.showSuccess('تم إعادة ترتيب الدروس بنجاح');
                            }
                        }
                    });
                }
            });
        });

        // تم حذف دالة toggleContent لأن الجدول لا يحتوي على عمود status

        function deleteContent(contentId) {
            if (confirm('هل أنت متأكد من حذف هذا الدرس؟')) {
                $.ajax({
                    url: `/admin/perfect-pharma/academy/courses/{{ $course->id }}/contents/${contentId}`,
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            location.reload();
                        }
                    },
                    error: function() {
                        Botble.showError('حدث خطأ أثناء حذف الدرس');
                    }
                });
            }
        }
    </script>
@endpush

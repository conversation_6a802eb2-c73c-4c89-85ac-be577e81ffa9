@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-edit me-2"></i>
                        تعديل الدرس: {{ $content->title }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.academy.courses.contents.index', $course->id) }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للدروس
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.academy.courses.contents.update', [$course->id, $content->id]) }}" method="POST" id="content-form">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- معلومات الدرس الأساسية -->
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات الدرس</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">عنوان الدرس <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" value="{{ $content->title }}" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف الدرس</label>
                                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="وصف مختصر عن محتوى الدرس...">{{ $content->description }}</textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="type" class="form-label">نوع المحتوى <span class="text-danger">*</span></label>
                                            <select class="form-select" id="type" name="type" required>
                                                <option value="">اختر نوع المحتوى</option>
                                                @foreach($contentTypes as $key => $name)
                                                    <option value="{{ $key }}" {{ $content->type === $key ? 'selected' : '' }}>{{ $name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        
                                        <!-- حقول المحتوى الديناميكية -->
                                        <div id="content-fields">
                                            @if($content->type === 'video')
                                                <div class="mb-3">
                                                    <label for="content_url" class="form-label">رابط الفيديو</label>
                                                    <input type="url" class="form-control" id="content_url" name="content_url" value="{{ $content->content_url }}" placeholder="https://youtube.com/watch?v=...">
                                                    <small class="form-text text-muted">يدعم YouTube, Vimeo, أو رابط مباشر</small>
                                                </div>
                                            @elseif($content->type === 'pdf')
                                                <div class="mb-3">
                                                    <label for="content_url" class="form-label">رابط ملف PDF</label>
                                                    <input type="url" class="form-control" id="content_url" name="content_url" value="{{ $content->content_url }}" placeholder="https://example.com/file.pdf">
                                                </div>
                                            @elseif($content->type === 'text')
                                                <div class="mb-3">
                                                    <label for="text_content" class="form-label">محتوى الدرس</label>
                                                    <textarea class="form-control" id="text_content" name="text_content" rows="8" placeholder="اكتب محتوى الدرس هنا...">{{ $content->text_content }}</textarea>
                                                </div>
                                            @elseif($content->type === 'quiz')
                                                <div class="alert alert-info">
                                                    <i class="ti ti-info-circle me-2"></i>
                                                    يمكن إدارة أسئلة الاختبار من صفحة منفصلة
                                                </div>
                                            @elseif($content->type === 'assignment')
                                                <div class="mb-3">
                                                    <label for="text_content" class="form-label">تعليمات الواجب</label>
                                                    <textarea class="form-control" id="text_content" name="text_content" rows="6" placeholder="اكتب تعليمات الواجب هنا...">{{ $content->text_content }}</textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="content_url" class="form-label">رابط ملف الواجب (اختياري)</label>
                                                    <input type="url" class="form-control" id="content_url" name="content_url" value="{{ $content->content_url }}" placeholder="https://example.com/assignment.pdf">
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إعدادات الدرس -->
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات الدرس</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="duration_minutes" class="form-label">مدة الدرس (بالدقائق)</label>
                                            <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" value="{{ $content->duration_minutes }}" min="1">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="order" class="form-label">ترتيب الدرس</label>
                                            <input type="number" class="form-control" id="order" name="order" value="{{ $content->order }}" min="0">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_free" name="is_free" value="1" {{ $content->is_free ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_free">
                                                    درس مجاني
                                                </label>
                                                <small class="form-text text-muted d-block">يمكن للطلاب مشاهدته بدون اشتراك</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- معاينة الدرس -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معاينة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="content-preview">
                                            <div class="d-flex align-items-center mb-3">
                                                <i class="preview-icon {{ $content->icon }} me-2 fs-3 text-{{ $content->color }}"></i>
                                                <div>
                                                    <h6 class="preview-title mb-0">{{ $content->title }}</h6>
                                                    <small class="preview-description text-muted">{{ $content->description ?: 'وصف الدرس' }}</small>
                                                </div>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <span class="preview-type badge bg-{{ $content->color }}">{{ $content->content_type_name }}</span>
                                                <span class="preview-duration badge bg-info">{{ $content->duration_minutes ?: 0 }} دقيقة</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.academy.courses.contents.index', $course->id) }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // تحديث المعاينة عند تغيير البيانات
            function updatePreview() {
                const title = $('#title').val() || 'عنوان الدرس';
                const description = $('#description').val() || 'وصف الدرس';
                const contentType = $('#type option:selected').text() || 'نوع المحتوى';
                const duration = $('#duration_minutes').val() || '0';
                const type = $('#type').val();
                
                $('.preview-title').text(title);
                $('.preview-description').text(description.substring(0, 50) + (description.length > 50 ? '...' : ''));
                $('.preview-type').text(contentType);
                $('.preview-duration').text(duration + ' دقيقة');
                
                // تحديث الأيقونة حسب النوع
                let icon = 'ti ti-file';
                let color = 'secondary';
                
                switch(type) {
                    case 'video':
                        icon = 'ti ti-video';
                        color = 'primary';
                        break;
                    case 'pdf':
                        icon = 'ti ti-file-text';
                        color = 'danger';
                        break;
                    case 'text':
                        icon = 'ti ti-notes';
                        color = 'info';
                        break;
                    case 'quiz':
                        icon = 'ti ti-help';
                        color = 'warning';
                        break;
                    case 'assignment':
                        icon = 'ti ti-clipboard';
                        color = 'success';
                        break;
                }
                
                $('.preview-icon').attr('class', `preview-icon ${icon} me-2 fs-3 text-${color}`);
                $('.preview-type').attr('class', `preview-type badge bg-${color}`);
            }
            
            // تحديث حقول المحتوى حسب النوع
            function updateContentFields() {
                const contentType = $('#type').val();
                const fieldsContainer = $('#content-fields');
                
                // احتفظ بالقيم الحالية
                const currentUrl = $('#content_url').val() || '';
                const currentText = $('#text_content').val() || '';
                
                fieldsContainer.empty();
                
                if (contentType === 'video') {
                    fieldsContainer.html(`
                        <div class="mb-3">
                            <label for="content_url" class="form-label">رابط الفيديو</label>
                            <input type="url" class="form-control" id="content_url" name="content_url" value="${currentUrl}" placeholder="https://youtube.com/watch?v=...">
                            <small class="form-text text-muted">يدعم YouTube, Vimeo, أو رابط مباشر</small>
                        </div>
                    `);
                } else if (contentType === 'pdf') {
                    fieldsContainer.html(`
                        <div class="mb-3">
                            <label for="content_url" class="form-label">رابط ملف PDF</label>
                            <input type="url" class="form-control" id="content_url" name="content_url" value="${currentUrl}" placeholder="https://example.com/file.pdf">
                        </div>
                    `);
                } else if (contentType === 'text') {
                    fieldsContainer.html(`
                        <div class="mb-3">
                            <label for="text_content" class="form-label">محتوى الدرس</label>
                            <textarea class="form-control" id="text_content" name="text_content" rows="8" placeholder="اكتب محتوى الدرس هنا...">${currentText}</textarea>
                        </div>
                    `);
                } else if (contentType === 'quiz') {
                    fieldsContainer.html(`
                        <div class="alert alert-info">
                            <i class="ti ti-info-circle me-2"></i>
                            يمكن إدارة أسئلة الاختبار من صفحة منفصلة
                        </div>
                    `);
                } else if (contentType === 'assignment') {
                    fieldsContainer.html(`
                        <div class="mb-3">
                            <label for="text_content" class="form-label">تعليمات الواجب</label>
                            <textarea class="form-control" id="text_content" name="text_content" rows="6" placeholder="اكتب تعليمات الواجب هنا...">${currentText}</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="content_url" class="form-label">رابط ملف الواجب (اختياري)</label>
                            <input type="url" class="form-control" id="content_url" name="content_url" value="${currentUrl}" placeholder="https://example.com/assignment.pdf">
                        </div>
                    `);
                }
            }
            
            $('#title, #description, #duration_minutes').on('input', updatePreview);
            $('#type').on('change', function() {
                updateContentFields();
                updatePreview();
            });
            
            // إرسال النموذج
            $('#content-form').on('submit', function(e) {
                e.preventDefault();
                
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);
                
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'PUT',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ التغييرات');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

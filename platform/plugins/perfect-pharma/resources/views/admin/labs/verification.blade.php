@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-shield-check me-2"></i>
                        طلبات التحقق من المعامل
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.labs.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    @if($labs->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-vcenter table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم المعمل</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>رقم الترخيص</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($labs as $lab)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-3 bg-primary-lt">
                                                        <i class="ti ti-flask"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ $lab->name }}</div>
                                                        <div class="text-muted small">{{ $lab->lab_code }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $lab->email }}</td>
                                            <td>{{ $lab->phone }}</td>
                                            <td>{{ $lab->license_number }}</td>
                                            <td>{{ $lab->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.labs.show', $lab->id) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="ti ti-eye me-1"></i>
                                                        عرض
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-success verify-btn" 
                                                            data-id="{{ $lab->id }}"
                                                            data-name="{{ $lab->name }}">
                                                        <i class="ti ti-check me-1"></i>
                                                        تحقق
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-danger reject-btn" 
                                                            data-id="{{ $lab->id }}"
                                                            data-name="{{ $lab->name }}">
                                                        <i class="ti ti-x me-1"></i>
                                                        رفض
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-center mt-4">
                            {{ $labs->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-shield-check fs-1 text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد طلبات تحقق</h5>
                            <p class="text-muted">جميع المعامل محققة أو لا توجد طلبات جديدة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // التحقق من المعمل
            $('.verify-btn').on('click', function() {
                const labId = $(this).data('id');
                const labName = $(this).data('name');
                
                if (confirm(`هل أنت متأكد من تحقق المعمل "${labName}"؟`)) {
                    $.ajax({
                        url: `/admin/perfect-pharma/labs/${labId}/verify`,
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.error) {
                                Botble.showError(response.message);
                            } else {
                                Botble.showSuccess(response.message);
                                location.reload();
                            }
                        },
                        error: function() {
                            Botble.showError('حدث خطأ أثناء التحقق من المعمل');
                        }
                    });
                }
            });
            
            // رفض المعمل
            $('.reject-btn').on('click', function() {
                const labId = $(this).data('id');
                const labName = $(this).data('name');
                
                if (confirm(`هل أنت متأكد من رفض المعمل "${labName}"؟`)) {
                    $.ajax({
                        url: `/admin/perfect-pharma/labs/${labId}/reject`,
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.error) {
                                Botble.showError(response.message);
                            } else {
                                Botble.showSuccess(response.message);
                                location.reload();
                            }
                        },
                        error: function() {
                            Botble.showError('حدث خطأ أثناء رفض المعمل');
                        }
                    });
                }
            });
        });
    </script>
@endpush

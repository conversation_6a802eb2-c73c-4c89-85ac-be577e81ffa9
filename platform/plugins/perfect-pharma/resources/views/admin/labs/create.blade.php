@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة معمل جديد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.labs.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.labs.store') }}" method="POST" id="lab-form">
                        @csrf

                        <div class="row">
                            <!-- البيانات الأساسية -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">البيانات الأساسية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">اسم المعمل <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="phone" name="phone" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="license_number" class="form-label">رقم الترخيص <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="license_number" name="license_number" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف المعمل</label>
                                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- بيانات الموقع -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">بيانات الموقع</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="address" class="form-label">العنوان <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                                        </div>

                                        <div class="row">
                                            <div class="col-6">
                                                <div class="mb-3">
                                                    <label for="latitude" class="form-label">خط العرض</label>
                                                    <input type="number" class="form-control" id="latitude" name="latitude" step="0.00000001">
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="mb-3">
                                                    <label for="longitude" class="form-label">خط الطول</label>
                                                    <input type="number" class="form-control" id="longitude" name="longitude" step="0.00000001">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-6">
                                                <div class="mb-3">
                                                    <label for="opening_time" class="form-label">وقت الفتح</label>
                                                    <input type="time" class="form-control" id="opening_time" name="opening_time">
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="mb-3">
                                                    <label for="closing_time" class="form-label">وقت الإغلاق</label>
                                                    <input type="time" class="form-control" id="closing_time" name="closing_time">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- الخدمات والتحاليل -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">الخدمات</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="home_collection" name="home_collection" value="1">
                                                <label class="form-check-label" for="home_collection">
                                                    خدمة السحب من المنزل
                                                </label>
                                            </div>
                                        </div>

                                        <div id="collection-options" style="display: none;">
                                            <div class="mb-3">
                                                <label for="collection_fee" class="form-label">رسوم السحب من المنزل</label>
                                                <input type="number" class="form-control" id="collection_fee" name="collection_fee" step="0.01" min="0">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- التحاليل المتاحة -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">التحاليل المتاحة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">أنواع التحاليل</label>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="available_tests[]" value="تحليل دم شامل" id="blood_test">
                                                        <label class="form-check-label" for="blood_test">تحليل دم شامل</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="available_tests[]" value="تحليل بول" id="urine_test">
                                                        <label class="form-check-label" for="urine_test">تحليل بول</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="available_tests[]" value="تحليل سكر" id="sugar_test">
                                                        <label class="form-check-label" for="sugar_test">تحليل سكر</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="available_tests[]" value="تحليل كوليسترول" id="cholesterol_test">
                                                        <label class="form-check-label" for="cholesterol_test">تحليل كوليسترول</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="available_tests[]" value="تحليل وظائف كبد" id="liver_test">
                                                        <label class="form-check-label" for="liver_test">تحليل وظائف كبد</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="available_tests[]" value="تحليل وظائف كلى" id="kidney_test">
                                                        <label class="form-check-label" for="kidney_test">تحليل وظائف كلى</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="available_tests[]" value="تحليل هرمونات" id="hormone_test">
                                                        <label class="form-check-label" for="hormone_test">تحليل هرمونات</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="available_tests[]" value="تحليل فيتامينات" id="vitamin_test">
                                                        <label class="form-check-label" for="vitamin_test">تحليل فيتامينات</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.labs.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ المعمل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // إظهار/إخفاء خيارات السحب من المنزل
            $('#home_collection').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#collection-options').show();
                } else {
                    $('#collection-options').hide();
                }
            });

            $('#lab-form').on('submit', function(e) {
                e.preventDefault();

                // إظهار مؤشر التحميل
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);

                // إرسال البيانات
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ البيانات');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-test-pipe me-2"></i>
                        إدارة المعامل
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>كود المعمل</th>
                                    <th>اسم المعمل</th>
                                    <th>العنوان</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($labs as $lab)
                                    <tr>
                                        <td>{{ $lab->lab_code }}</td>
                                        <td>{{ $lab->name }}</td>
                                        <td>{{ $lab->address }}</td>
                                        <td>{{ $lab->phone }}</td>
                                        <td>
                                            @if($lab->is_verified)
                                                <span class="badge bg-success">محقق</span>
                                            @else
                                                <span class="badge bg-warning">في انتظار التحقق</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.labs.show', $lab->id) }}" class="btn btn-sm btn-primary">عرض</a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد معامل مسجلة</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

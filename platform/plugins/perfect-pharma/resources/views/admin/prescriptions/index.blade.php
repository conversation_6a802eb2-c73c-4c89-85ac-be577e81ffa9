@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-prescription me-2"></i>
                        إدارة الوصفات الطبية
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم الوصفة</th>
                                    <th>المريض</th>
                                    <th>الطبيب</th>
                                    <th>تاريخ الوصفة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($prescriptions as $prescription)
                                    <tr>
                                        <td>{{ $prescription->prescription_number }}</td>
                                        <td>{{ $prescription->patient->customer->name ?? 'غير محدد' }}</td>
                                        <td>{{ $prescription->doctor->user->name ?? 'غير محدد' }}</td>
                                        <td>{{ $prescription->prescription_date }}</td>
                                        <td>
                                            @switch($prescription->status)
                                                @case('active')
                                                    <span class="badge bg-success">نشطة</span>
                                                    @break
                                                @case('completed')
                                                    <span class="badge bg-primary">مكتملة</span>
                                                    @break
                                                @case('expired')
                                                    <span class="badge bg-danger">منتهية</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary">{{ $prescription->status }}</span>
                                            @endswitch
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.prescriptions.show', $prescription->id) }}" class="btn btn-sm btn-primary">عرض</a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد وصفات طبية</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة مستشفى خيري جديد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.charity-hospitals.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.charity-hospitals.store') }}" method="POST" id="hospital-form">
                        @csrf
                        
                        <div class="row">
                            <!-- معلومات المستشفى الأساسية -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات المستشفى</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">اسم المستشفى <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف المستشفى</label>
                                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="address" class="form-label">العنوان <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                                    <input type="email" class="form-control" id="email" name="email">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- معلومات إضافية -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات إضافية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="license_number" class="form-label">رقم الترخيص</label>
                                            <input type="text" class="form-control" id="license_number" name="license_number">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="capacity" class="form-label">السعة (عدد الأسرة)</label>
                                            <input type="number" class="form-control" id="capacity" name="capacity" min="1">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="specialties" class="form-label">التخصصات المتاحة</label>
                                            <textarea class="form-control" id="specialties" name="specialties" rows="3" placeholder="مثال: طب الأطفال، الجراحة العامة، الباطنة"></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="website" class="form-label">الموقع الإلكتروني</label>
                                            <input type="url" class="form-control" id="website" name="website">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- إعدادات التبرع -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات التبرع</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="accepts_donations" name="accepts_donations" value="1" checked>
                                                <label class="form-check-label" for="accepts_donations">
                                                    يقبل التبرعات
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="donation_goal" class="form-label">هدف التبرع (جنيه)</label>
                                            <input type="number" class="form-control" id="donation_goal" name="donation_goal" min="0" step="0.01">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="donation_purpose" class="form-label">الغرض من التبرع</label>
                                            <textarea class="form-control" id="donation_purpose" name="donation_purpose" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- الحالة والتحقق -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">الحالة والتحقق</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">حالة المستشفى</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="active">نشط</option>
                                                <option value="inactive">غير نشط</option>
                                                <option value="pending">في انتظار المراجعة</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_verified" name="is_verified" value="1">
                                                <label class="form-check-label" for="is_verified">
                                                    مستشفى محقق
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                                <label class="form-check-label" for="is_featured">
                                                    مستشفى مميز
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">ملاحظات إدارية</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.charity-hospitals.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ المستشفى
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            $('#hospital-form').on('submit', function(e) {
                e.preventDefault();
                
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);
                
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            } else {
                                window.location.href = '{{ route("admin.charity-hospitals.index") }}';
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ المستشفى');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

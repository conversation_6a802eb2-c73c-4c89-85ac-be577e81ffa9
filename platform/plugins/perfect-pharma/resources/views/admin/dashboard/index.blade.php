@extends(BaseHelper::getAdminMasterLayoutTemplate())

@push('header')
    @include('plugins/perfect-pharma::layouts.currency-meta')
@endpush

@section('content')
    <div class="row">
        <!-- الإحصائيات الأساسية -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h3 class="mb-0">{{ number_format($stats['total_patients']) }}</h3>
                                    <p class="mb-0">إجمالي المرضى</p>
                                    <small class="opacity-75">+{{ $stats['new_patients_this_month'] }} هذا الشهر</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <i class="ti ti-users fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h3 class="mb-0">{{ number_format($stats['total_doctors']) }}</h3>
                                    <p class="mb-0">الأطباء المسجلين</p>
                                    <small class="opacity-75">{{ $stats['pending_doctors'] }} في انتظار التحقق</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <i class="ti ti-stethoscope fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h3 class="mb-0">{{ number_format($stats['total_pharmacies']) }}</h3>
                                    <p class="mb-0">الصيدليات المسجلة</p>
                                    <small class="opacity-75">{{ $stats['pending_pharmacies'] }} في انتظار التحقق</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <i class="ti ti-building-store fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h3 class="mb-0">{{ number_format($financialStats['this_month_prescriptions']) }}</h3>
                                    <p class="mb-0">وصفات هذا الشهر</p>
                                    <small class="opacity-75">{{ $financialStats['completed_prescriptions'] }} مكتملة</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <i class="ti ti-prescription fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات الطبية -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">الإحصائيات الطبية</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-primary rounded p-2">
                                        <i class="ti ti-prescription text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-0">{{ number_format($stats['total_prescriptions']) }}</h5>
                                    <small class="text-muted">إجمالي الوصفات</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="badge bg-success">{{ $stats['active_prescriptions'] }} نشطة</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-info rounded p-2">
                                        <i class="ti ti-flask text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-0">{{ number_format($stats['total_lab_tests']) }}</h5>
                                    <small class="text-muted">إجمالي التحاليل</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="badge bg-warning">{{ $stats['pending_lab_tests'] }} معلقة</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-success rounded p-2">
                                        <i class="ti ti-calendar text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-0">{{ number_format($stats['new_patients_this_month']) }}</h5>
                                    <small class="text-muted">مرضى جدد هذا الشهر</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="badge bg-primary">{{ $stats['new_prescriptions_this_month'] }} وصفة</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-warning rounded p-2">
                                        <i class="ti ti-clock text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-0">{{ number_format($financialStats['pending_prescriptions']) }}</h5>
                                    <small class="text-muted">وصفات معلقة</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="badge bg-info">{{ format_price($financialStats['total_prescriptions_value']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الأنشطة الحديثة -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">الأنشطة الحديثة</h4>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        @foreach($recentActivities as $activity)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-{{ $activity['color'] }}">
                                    <i class="{{ $activity['icon'] }}"></i>
                                </div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">{{ $activity['title'] }}</h6>
                                    <p class="text-muted mb-1">{{ $activity['subtitle'] }}</p>
                                    <small class="text-muted">{{ $activity['time']->diffForHumans() }}</small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">نمو المرضى الجدد</h4>
                </div>
                <div class="card-body">
                    <canvas id="patientsChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">حالات الوصفات</h4>
                </div>
                <div class="card-body">
                    <canvas id="prescriptionsChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- توزيع المستخدمين -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">توزيع المستخدمين</h4>
                </div>
                <div class="card-body">
                    <canvas id="usersChart" height="300"></canvas>
                </div>
            </div>
        </div>


    </div>
@endsection

@push('footer')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // رسم بياني لنمو المرضى
        const patientsCtx = document.getElementById('patientsChart').getContext('2d');
        new Chart(patientsCtx, {
            type: 'line',
            data: {
                labels: {!! json_encode(array_column($chartData['patients_growth'], 'month')) !!},
                datasets: [{
                    label: 'المرضى الجدد',
                    data: {!! json_encode(array_column($chartData['patients_growth'], 'count')) !!},
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // رسم بياني لحالات الوصفات
        const prescriptionsCtx = document.getElementById('prescriptionsChart').getContext('2d');
        new Chart(prescriptionsCtx, {
            type: 'pie',
            data: {
                labels: {!! json_encode(array_column($chartData['prescription_status'], 'status')) !!},
                datasets: [{
                    data: {!! json_encode(array_column($chartData['prescription_status'], 'count')) !!},
                    backgroundColor: ['#3b82f6', '#10b981', '#ef4444']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // رسم بياني لتوزيع المستخدمين
        const usersCtx = document.getElementById('usersChart').getContext('2d');
        new Chart(usersCtx, {
            type: 'doughnut',
            data: {
                labels: {!! json_encode(array_column($chartData['users_distribution'], 'name')) !!},
                datasets: [{
                    data: {!! json_encode(array_column($chartData['users_distribution'], 'count')) !!},
                    backgroundColor: {!! json_encode(array_column($chartData['users_distribution'], 'color')) !!}
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });



        // تحديث البيانات كل دقيقة
        setInterval(function() {
            fetch('{{ route("admin.pharma.dashboard.stats") }}')
                .then(response => response.json())
                .then(data => {
                    // تحديث الإحصائيات هنا
                    console.log('تم تحديث البيانات');
                });
        }, 60000);
    </script>
@endpush

@push('header')
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -35px;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
    </style>
@endpush

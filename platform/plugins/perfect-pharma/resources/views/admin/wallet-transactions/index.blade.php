@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-credit-card me-2"></i>
                        المعاملات المالية
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم المعاملة</th>
                                    <th>المحفظة</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($transactions as $transaction)
                                    <tr>
                                        <td>{{ $transaction->transaction_id }}</td>
                                        <td>{{ $transaction->wallet->wallet_number ?? 'غير محدد' }}</td>
                                        <td>
                                            @switch($transaction->type)
                                                @case('credit')
                                                    <span class="badge bg-success">إيداع</span>
                                                    @break
                                                @case('debit')
                                                    <span class="badge bg-danger">سحب</span>
                                                    @break
                                                @case('transfer_in')
                                                    <span class="badge bg-info">تحويل وارد</span>
                                                    @break
                                                @case('transfer_out')
                                                    <span class="badge bg-warning">تحويل صادر</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary">{{ $transaction->type }}</span>
                                            @endswitch
                                        </td>
                                        <td>{{ number_format($transaction->amount, 2) }} جنيه</td>
                                        <td>
                                            @switch($transaction->status)
                                                @case('completed')
                                                    <span class="badge bg-success">مكتملة</span>
                                                    @break
                                                @case('pending')
                                                    <span class="badge bg-warning">معلقة</span>
                                                    @break
                                                @case('failed')
                                                    <span class="badge bg-danger">فاشلة</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary">{{ $transaction->status }}</span>
                                            @endswitch
                                        </td>
                                        <td>{{ $transaction->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            <a href="{{ route('admin.wallet-transactions.show', $transaction->id) }}" class="btn btn-sm btn-primary">عرض</a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد معاملات مالية</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

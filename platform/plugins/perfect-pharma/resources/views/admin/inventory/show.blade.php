@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- معلومات المنتج الأساسية -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="ti ti-package me-2"></i>
                            {{ $inventory->product->name }}
                        </h4>
                        <div class="card-actions">
                            <a href="{{ route('admin.inventory.edit', $inventory->id) }}" class="btn btn-warning me-2">
                                <i class="ti ti-edit me-1"></i>
                                تعديل
                            </a>
                            <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left me-1"></i>
                                العودة
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">الصيدلية:</td>
                                    <td><span class="badge bg-info">{{ $inventory->pharmacy->name }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">رقم الدفعة:</td>
                                    <td>{{ $inventory->batch_number ?? 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ الإنتاج:</td>
                                    <td>{{ $inventory->manufacturing_date ? $inventory->manufacturing_date->format('Y-m-d') : 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ انتهاء الصلاحية:</td>
                                    <td>
                                        @if($inventory->expiry_date)
                                            {{ $inventory->expiry_date->format('Y-m-d') }}
                                            @if($inventory->days_to_expiry !== null)
                                                @if($inventory->days_to_expiry < 0)
                                                    <span class="badge bg-danger">منتهي منذ {{ abs($inventory->days_to_expiry) }} يوم</span>
                                                @elseif($inventory->days_to_expiry <= 30)
                                                    <span class="badge bg-warning">{{ $inventory->days_to_expiry }} يوم متبقي</span>
                                                @else
                                                    <span class="badge bg-success">{{ $inventory->days_to_expiry }} يوم متبقي</span>
                                                @endif
                                            @endif
                                        @else
                                            <span class="text-muted">لا ينتهي</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">ظروف التخزين:</td>
                                    <td><span class="badge bg-light text-dark">{{ $inventory->storage_condition_label }}</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">موقع التخزين:</td>
                                    <td>{{ $inventory->storage_location ?? 'غير محدد' }}</td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">المورد:</td>
                                    <td>{{ $inventory->supplier_name ?? 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">سعر التكلفة:</td>
                                    <td class="text-success fw-bold">{{ number_format($inventory->cost_price, 2) }} ريال</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">سعر البيع:</td>
                                    <td class="text-primary fw-bold">{{ number_format($inventory->selling_price, 2) }} ريال</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">هامش الربح:</td>
                                    <td>
                                        @if($inventory->profit_margin > 0)
                                            <span class="text-success fw-bold">{{ number_format($inventory->profit_margin, 2) }}%</span>
                                        @else
                                            <span class="text-danger">0%</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">يتطلب وصفة:</td>
                                    <td>
                                        @if($inventory->is_prescription_required)
                                            <span class="badge bg-warning">نعم</span>
                                        @else
                                            <span class="badge bg-success">لا</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الحالة:</td>
                                    <td>
                                        @if($inventory->is_active)
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-danger">غير نشط</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    @if($inventory->notes)
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <strong>ملاحظات:</strong> {{ $inventory->notes }}
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- إحصائيات المخزون -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-chart-bar me-2"></i>
                        إحصائيات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h3 class="text-primary mb-1">{{ number_format($inventory->quantity_in_stock) }}</h3>
                                <small class="text-muted">الكمية الإجمالية</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h3 class="text-success mb-1">{{ number_format($inventory->available_quantity) }}</h3>
                                <small class="text-muted">الكمية المتاحة</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h3 class="text-warning mb-1">{{ number_format($inventory->reserved_quantity) }}</h3>
                                <small class="text-muted">الكمية المحجوزة</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h3 class="text-info mb-1">{{ number_format($inventory->minimum_stock_level) }}</h3>
                                <small class="text-muted">الحد الأدنى</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- شريط تقدم المخزون -->
                    <div class="mb-3">
                        <label class="form-label">مستوى المخزون</label>
                        <div class="progress" style="height: 20px;">
                            @php
                                $percentage = min(100, $inventory->stock_percentage);
                                $color = 'success';
                                if ($percentage <= 25) $color = 'danger';
                                elseif ($percentage <= 50) $color = 'warning';
                                elseif ($percentage <= 75) $color = 'info';
                            @endphp
                            <div class="progress-bar bg-{{ $color }}" style="width: {{ $percentage }}%">
                                {{ number_format($percentage, 1) }}%
                            </div>
                        </div>
                    </div>
                    
                    <!-- حالة المخزون -->
                    <div class="mb-3">
                        @php
                            $statusColors = [
                                'out_of_stock' => 'danger',
                                'low_stock' => 'warning',
                                'reorder_needed' => 'info',
                                'overstock' => 'secondary',
                                'normal' => 'success'
                            ];
                            $color = $statusColors[$inventory->stock_status] ?? 'secondary';
                        @endphp
                        <div class="alert alert-{{ $color }} text-center">
                            <strong>{{ $inventory->stock_status_label }}</strong>
                        </div>
                    </div>
                    
                    <!-- إجراءات سريعة -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" onclick="showAddStockModal()">
                            <i class="ti ti-plus me-1"></i>
                            إضافة مخزون
                        </button>
                        <button type="button" class="btn btn-warning" onclick="showRemoveStockModal()">
                            <i class="ti ti-minus me-1"></i>
                            خصم مخزون
                        </button>
                        <button type="button" class="btn btn-info" onclick="showAdjustStockModal()">
                            <i class="ti ti-edit me-1"></i>
                            تعديل المخزون
                        </button>
                        <a href="{{ route('admin.inventory.movements', $inventory->id) }}" class="btn btn-secondary">
                            <i class="ti ti-history me-1"></i>
                            حركات المخزون
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخر الحركات -->
        <div class="col-12 mt-4">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="ti ti-history me-2"></i>
                            آخر حركات المخزون
                        </h5>
                        <a href="{{ route('admin.inventory.movements', $inventory->id) }}" class="btn btn-sm btn-outline-primary">
                            عرض جميع الحركات
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($inventory->movements->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>الكمية</th>
                                        <th>قبل</th>
                                        <th>بعد</th>
                                        <th>السبب</th>
                                        <th>المستخدم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($inventory->movements->take(5) as $movement)
                                        <tr>
                                            <td>{{ $movement->formatted_date_time }}</td>
                                            <td>
                                                <span class="badge bg-{{ $movement->color }}">
                                                    <i class="{{ $movement->icon }} me-1"></i>
                                                    {{ $movement->type_label }}
                                                </span>
                                            </td>
                                            <td class="fw-bold">{{ number_format($movement->quantity) }}</td>
                                            <td>{{ number_format($movement->quantity_before) }}</td>
                                            <td>{{ number_format($movement->quantity_after) }}</td>
                                            <td>{{ $movement->reason }}</td>
                                            <td>{{ $movement->user->name ?? 'غير محدد' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="ti ti-history fs-1 text-muted mb-2"></i>
                            <p class="text-muted">لا توجد حركات مخزون</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- التنبيهات الحالية -->
        @if($inventory->alerts->count() > 0)
            <div class="col-12 mt-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="ti ti-bell me-2"></i>
                            التنبيهات الحالية
                        </h5>
                    </div>
                    <div class="card-body">
                        @foreach($inventory->alerts->take(5) as $alert)
                            <div class="alert alert-{{ $alert->color }} d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="{{ $alert->icon }} me-2"></i>
                                    <strong>{{ $alert->title }}</strong>
                                    <p class="mb-0">{{ $alert->message }}</p>
                                    <small class="text-muted">{{ $alert->time_ago }}</small>
                                </div>
                                @if(!$alert->is_resolved)
                                    <button type="button" class="btn btn-sm btn-success" onclick="resolveAlert({{ $alert->id }})">
                                        حل التنبيه
                                    </button>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Modal إضافة مخزون -->
    <div class="modal fade" id="addStockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ route('admin.inventory.add-stock', $inventory->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label required">الكمية</label>
                            <input type="number" name="quantity" class="form-control" min="1" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سعر التكلفة الجديد</label>
                            <input type="number" name="unit_cost" class="form-control" step="0.01" min="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">السبب</label>
                            <textarea name="reason" class="form-control" rows="3" required placeholder="سبب إضافة المخزون..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">إضافة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal خصم مخزون -->
    <div class="modal fade" id="removeStockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">خصم مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ route('admin.inventory.remove-stock', $inventory->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="alert alert-info">
                            الكمية المتاحة: <strong>{{ number_format($inventory->available_quantity) }}</strong>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">الكمية</label>
                            <input type="number" name="quantity" class="form-control" min="1" max="{{ $inventory->available_quantity }}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">السبب</label>
                            <textarea name="reason" class="form-control" rows="3" required placeholder="سبب خصم المخزون..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-warning">خصم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal تعديل مخزون -->
    <div class="modal fade" id="adjustStockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ route('admin.inventory.adjust-stock', $inventory->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="alert alert-info">
                            الكمية الحالية: <strong>{{ number_format($inventory->quantity_in_stock) }}</strong>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">الكمية الجديدة</label>
                            <input type="number" name="new_quantity" class="form-control" min="0" value="{{ $inventory->quantity_in_stock }}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">السبب</label>
                            <textarea name="reason" class="form-control" rows="3" required placeholder="سبب تعديل المخزون..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-info">تعديل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function showAddStockModal() {
            const modal = new bootstrap.Modal(document.getElementById('addStockModal'));
            modal.show();
        }

        function showRemoveStockModal() {
            const modal = new bootstrap.Modal(document.getElementById('removeStockModal'));
            modal.show();
        }

        function showAdjustStockModal() {
            const modal = new bootstrap.Modal(document.getElementById('adjustStockModal'));
            modal.show();
        }

        function resolveAlert(alertId) {
            if (confirm('هل أنت متأكد من حل هذا التنبيه؟')) {
                fetch(`/admin/inventory/alerts/${alertId}/resolve`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        resolution_notes: 'تم الحل من صفحة المنتج'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error === false) {
                        location.reload();
                    }
                });
            }
        }
    </script>
@endpush

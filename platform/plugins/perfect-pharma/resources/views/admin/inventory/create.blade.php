@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة منتج للمخزون
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.inventory.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <!-- معلومات أساسية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label required">الصيدلية</label>
                                    <select name="pharmacy_id" class="form-select" required>
                                        <option value="">اختر الصيدلية</option>
                                        @foreach($pharmacies as $pharmacy)
                                            <option value="{{ $pharmacy->id }}" {{ old('pharmacy_id') == $pharmacy->id ? 'selected' : '' }}>
                                                {{ $pharmacy->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('pharmacy_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label required">المنتج</label>
                                    <select name="product_id" class="form-select" required>
                                        <option value="">اختر المنتج</option>
                                        @foreach($products as $product)
                                            <option value="{{ $product->id }}" {{ old('product_id') == $product->id ? 'selected' : '' }}>
                                                {{ $product->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('product_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- معلومات الدفعة -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">رقم الدفعة</label>
                                    <input type="text" name="batch_number" class="form-control" 
                                           value="{{ old('batch_number') }}" placeholder="رقم الدفعة">
                                    @error('batch_number')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الإنتاج</label>
                                    <input type="date" name="manufacturing_date" class="form-control" 
                                           value="{{ old('manufacturing_date') }}">
                                    @error('manufacturing_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ انتهاء الصلاحية</label>
                                    <input type="date" name="expiry_date" class="form-control" 
                                           value="{{ old('expiry_date') }}">
                                    @error('expiry_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- الكميات -->
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label required">الكمية في المخزون</label>
                                    <input type="number" name="quantity_in_stock" class="form-control" 
                                           value="{{ old('quantity_in_stock', 0) }}" min="0" required>
                                    @error('quantity_in_stock')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label required">الحد الأدنى</label>
                                    <input type="number" name="minimum_stock_level" class="form-control" 
                                           value="{{ old('minimum_stock_level', 10) }}" min="1" required>
                                    @error('minimum_stock_level')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label required">الحد الأقصى</label>
                                    <input type="number" name="maximum_stock_level" class="form-control" 
                                           value="{{ old('maximum_stock_level', 1000) }}" min="1" required>
                                    @error('maximum_stock_level')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label required">مستوى إعادة الطلب</label>
                                    <input type="number" name="reorder_level" class="form-control" 
                                           value="{{ old('reorder_level', 20) }}" min="1" required>
                                    @error('reorder_level')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- الأسعار -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label required">سعر التكلفة</label>
                                    <input type="number" name="cost_price" class="form-control" 
                                           value="{{ old('cost_price') }}" step="0.01" min="0" required>
                                    @error('cost_price')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label required">سعر البيع</label>
                                    <input type="number" name="selling_price" class="form-control" 
                                           value="{{ old('selling_price') }}" step="0.01" min="0" required>
                                    @error('selling_price')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">اسم المورد</label>
                                    <input type="text" name="supplier_name" class="form-control" 
                                           value="{{ old('supplier_name') }}" placeholder="اسم المورد">
                                    @error('supplier_name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- معلومات التخزين -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">موقع التخزين</label>
                                    <input type="text" name="storage_location" class="form-control" 
                                           value="{{ old('storage_location') }}" placeholder="مثل: رف A1">
                                    @error('storage_location')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label required">ظروف التخزين</label>
                                    <select name="storage_condition" class="form-select" required>
                                        <option value="">اختر ظروف التخزين</option>
                                        <option value="room_temperature" {{ old('storage_condition') == 'room_temperature' ? 'selected' : '' }}>
                                            درجة حرارة الغرفة
                                        </option>
                                        <option value="refrigerated" {{ old('storage_condition') == 'refrigerated' ? 'selected' : '' }}>
                                            مبرد (2-8°C)
                                        </option>
                                        <option value="frozen" {{ old('storage_condition') == 'frozen' ? 'selected' : '' }}>
                                            مجمد (-18°C)
                                        </option>
                                        <option value="controlled" {{ old('storage_condition') == 'controlled' ? 'selected' : '' }}>
                                            ظروف خاصة
                                        </option>
                                    </select>
                                    @error('storage_condition')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" name="is_prescription_required" class="form-check-input" 
                                               value="1" {{ old('is_prescription_required') ? 'checked' : '' }}>
                                        <label class="form-check-label">
                                            يتطلب وصفة طبية
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3" 
                                              placeholder="ملاحظات إضافية...">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ المنتج
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        // حساب هامش الربح تلقائياً
        document.addEventListener('DOMContentLoaded', function() {
            const costPrice = document.querySelector('input[name="cost_price"]');
            const sellingPrice = document.querySelector('input[name="selling_price"]');
            
            function calculateMargin() {
                const cost = parseFloat(costPrice.value) || 0;
                const selling = parseFloat(sellingPrice.value) || 0;
                
                if (cost > 0) {
                    const margin = ((selling - cost) / cost * 100).toFixed(2);
                    console.log('هامش الربح: ' + margin + '%');
                }
            }
            
            costPrice.addEventListener('input', calculateMargin);
            sellingPrice.addEventListener('input', calculateMargin);
        });
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- معلومات المنتج -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            @if($inventory->product->image)
                                <img src="{{ RvMedia::getImageUrl($inventory->product->image, 'thumb') }}" 
                                     alt="{{ $inventory->product->name }}" class="rounded me-3" width="60" height="60">
                            @endif
                            <div>
                                <h4 class="mb-1">{{ $inventory->product->name }}</h4>
                                <p class="mb-0">
                                    <span class="badge bg-info me-2">{{ $inventory->pharmacy->name }}</span>
                                    <span class="text-muted">الكمية الحالية: </span>
                                    <span class="fw-bold text-primary">{{ number_format($inventory->quantity_in_stock) }}</span>
                                    <span class="text-muted"> | المتاح: </span>
                                    <span class="fw-bold text-success">{{ number_format($inventory->available_quantity) }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="card-actions">
                            <a href="{{ route('admin.inventory.show', $inventory->id) }}" class="btn btn-info me-2">
                                <i class="ti ti-eye me-1"></i>
                                عرض التفاصيل
                            </a>
                            <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-filter me-2"></i>
                        فلاتر الحركات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.inventory.movements', $inventory->id) }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">نوع الحركة</label>
                                <select name="type" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    @foreach($movementTypes as $key => $label)
                                        <option value="{{ $key }}" {{ request('type') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-search me-1"></i>
                                        بحث
                                    </button>
                                    <a href="{{ route('admin.inventory.movements', $inventory->id) }}" class="btn btn-secondary">
                                        <i class="ti ti-refresh me-1"></i>
                                        إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة الحركات -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="ti ti-history me-2"></i>
                            حركات المخزون
                        </h4>
                        <div class="card-actions">
                            <button type="button" class="btn btn-success me-2" onclick="showAddStockModal()">
                                <i class="ti ti-plus me-1"></i>
                                إضافة مخزون
                            </button>
                            <button type="button" class="btn btn-warning me-2" onclick="showRemoveStockModal()">
                                <i class="ti ti-minus me-1"></i>
                                خصم مخزون
                            </button>
                            <button type="button" class="btn btn-info" onclick="showAdjustStockModal()">
                                <i class="ti ti-edit me-1"></i>
                                تعديل المخزون
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    @if($movements->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الحركة</th>
                                        <th>التاريخ والوقت</th>
                                        <th>النوع</th>
                                        <th>الكمية</th>
                                        <th>قبل الحركة</th>
                                        <th>بعد الحركة</th>
                                        <th>التكلفة</th>
                                        <th>السبب</th>
                                        <th>المستخدم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($movements as $movement)
                                        <tr>
                                            <td>
                                                <span class="fw-bold">{{ $movement->movement_number }}</span>
                                            </td>
                                            <td>
                                                <div>
                                                    {{ $movement->formatted_date }}
                                                    <br><small class="text-muted">{{ $movement->formatted_time }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $movement->color }}">
                                                    <i class="{{ $movement->icon }} me-1"></i>
                                                    {{ $movement->type_label }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="fw-bold {{ $movement->isPositive() ? 'text-success' : 'text-danger' }}">
                                                    {{ $movement->isPositive() ? '+' : '-' }}{{ number_format($movement->quantity) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-muted">{{ number_format($movement->quantity_before) }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-bold">{{ number_format($movement->quantity_after) }}</span>
                                            </td>
                                            <td>
                                                @if($movement->total_cost)
                                                    <div>
                                                        <span class="fw-bold">{{ number_format($movement->total_cost, 2) }} ريال</span>
                                                        @if($movement->unit_cost)
                                                            <br><small class="text-muted">{{ number_format($movement->unit_cost, 2) }} ريال/وحدة</small>
                                                        @endif
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="text-wrap">{{ $movement->reason }}</span>
                                                @if($movement->notes)
                                                    <br><small class="text-muted">{{ $movement->notes }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div>
                                                    {{ $movement->user->name ?? 'غير محدد' }}
                                                    @if($movement->user)
                                                        <br><small class="text-muted">{{ $movement->user->email }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="card-footer">
                            {{ $movements->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-history fs-1 text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد حركات مخزون</h5>
                            <p class="text-muted">لم يتم تسجيل أي حركات لهذا المنتج بعد</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة مخزون -->
    <div class="modal fade" id="addStockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ route('admin.inventory.add-stock', $inventory->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label required">الكمية</label>
                            <input type="number" name="quantity" class="form-control" min="1" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سعر التكلفة الجديد</label>
                            <input type="number" name="unit_cost" class="form-control" step="0.01" min="0" 
                                   value="{{ $inventory->cost_price }}">
                            <small class="text-muted">اتركه فارغاً للاحتفاظ بالسعر الحالي</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">السبب</label>
                            <textarea name="reason" class="form-control" rows="3" required placeholder="سبب إضافة المخزون..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">إضافة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal خصم مخزون -->
    <div class="modal fade" id="removeStockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">خصم مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ route('admin.inventory.remove-stock', $inventory->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="alert alert-info">
                            الكمية المتاحة: <strong>{{ number_format($inventory->available_quantity) }}</strong>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">الكمية</label>
                            <input type="number" name="quantity" class="form-control" min="1" max="{{ $inventory->available_quantity }}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">السبب</label>
                            <textarea name="reason" class="form-control" rows="3" required placeholder="سبب خصم المخزون..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-warning">خصم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal تعديل مخزون -->
    <div class="modal fade" id="adjustStockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ route('admin.inventory.adjust-stock', $inventory->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="alert alert-info">
                            الكمية الحالية: <strong>{{ number_format($inventory->quantity_in_stock) }}</strong>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">الكمية الجديدة</label>
                            <input type="number" name="new_quantity" class="form-control" min="0" value="{{ $inventory->quantity_in_stock }}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label required">السبب</label>
                            <textarea name="reason" class="form-control" rows="3" required placeholder="سبب تعديل المخزون..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-info">تعديل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function showAddStockModal() {
            const modal = new bootstrap.Modal(document.getElementById('addStockModal'));
            modal.show();
        }

        function showRemoveStockModal() {
            const modal = new bootstrap.Modal(document.getElementById('removeStockModal'));
            modal.show();
        }

        function showAdjustStockModal() {
            const modal = new bootstrap.Modal(document.getElementById('adjustStockModal'));
            modal.show();
        }

        // تحديث تلقائي للصفحة كل 5 دقائق
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات التنبيهات -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $alertStats['total_alerts'] }}</h4>
                                    <small>إجمالي التنبيهات</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-bell fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $alertStats['unread_alerts'] }}</h4>
                                    <small>غير مقروءة</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-mail fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $alertStats['critical_alerts'] }}</h4>
                                    <small>حرجة</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-alert-triangle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $alertStats['stock_alerts'] }}</h4>
                                    <small>تنبيهات المخزون</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-package fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $alertStats['expiry_alerts'] }}</h4>
                                    <small>تنبيهات الصلاحية</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-calendar-x fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $alertStats['today_alerts'] }}</h4>
                                    <small>تنبيهات اليوم</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-calendar fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-filter me-2"></i>
                        فلاتر التنبيهات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.inventory.alerts') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">الصيدلية</label>
                                <select name="pharmacy_id" class="form-select">
                                    <option value="">جميع الصيدليات</option>
                                    @foreach($pharmacies as $pharmacy)
                                        <option value="{{ $pharmacy->id }}" {{ request('pharmacy_id') == $pharmacy->id ? 'selected' : '' }}>
                                            {{ $pharmacy->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">نوع التنبيه</label>
                                <select name="type" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    @foreach($alertTypes as $key => $label)
                                        <option value="{{ $key }}" {{ request('type') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">الأولوية</label>
                                <select name="priority" class="form-select">
                                    <option value="">جميع الأولويات</option>
                                    @foreach($priorities as $key => $label)
                                        <option value="{{ $key }}" {{ request('priority') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="unread" {{ request('status') == 'unread' ? 'selected' : '' }}>غير مقروءة</option>
                                    <option value="unresolved" {{ request('status') == 'unresolved' ? 'selected' : '' }}>غير محلولة</option>
                                    <option value="resolved" {{ request('status') == 'resolved' ? 'selected' : '' }}>محلولة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-search me-1"></i>
                                        بحث
                                    </button>
                                    <a href="{{ route('admin.inventory.alerts') }}" class="btn btn-secondary">
                                        <i class="ti ti-refresh me-1"></i>
                                        إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة التنبيهات -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="ti ti-bell me-2"></i>
                            تنبيهات المخزون
                        </h4>
                        <div class="card-actions">
                            <a href="{{ route('admin.inventory.index') }}" class="btn btn-primary">
                                <i class="ti ti-package me-1"></i>
                                إدارة المخزون
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    @if($alerts->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>التنبيه</th>
                                        <th>المنتج</th>
                                        <th>الصيدلية</th>
                                        <th>النوع</th>
                                        <th>الأولوية</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($alerts as $alert)
                                        <tr class="{{ !$alert->is_read ? 'table-warning' : '' }}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="{{ $alert->icon }} text-{{ $alert->color }} me-2"></i>
                                                    <div>
                                                        <h6 class="mb-0">{{ $alert->title }}</h6>
                                                        <small class="text-muted">{{ $alert->message }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="fw-bold">{{ $alert->product->name }}</span>
                                                    @if($alert->current_quantity !== null)
                                                        <br><small class="text-muted">الكمية: {{ $alert->current_quantity }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $alert->pharmacy->name }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $alert->color }}">{{ $alert->type_label }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $alert->priority_color }}">{{ $alert->priority_label }}</span>
                                            </td>
                                            <td>
                                                <div>
                                                    {{ $alert->formatted_date }}
                                                    <br><small class="text-muted">{{ $alert->time_ago }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    @if($alert->is_resolved)
                                                        <span class="badge bg-success">محلول</span>
                                                        @if($alert->resolved_at)
                                                            <br><small class="text-muted">{{ $alert->resolved_at->format('Y-m-d') }}</small>
                                                        @endif
                                                    @else
                                                        <span class="badge bg-warning">غير محلول</span>
                                                    @endif
                                                    
                                                    @if(!$alert->is_read)
                                                        <br><span class="badge bg-danger">غير مقروء</span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    @if(!$alert->is_read)
                                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                onclick="markAsRead({{ $alert->id }})" title="تحديد كمقروء">
                                                            <i class="ti ti-eye"></i>
                                                        </button>
                                                    @endif
                                                    
                                                    @if(!$alert->is_resolved)
                                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                                onclick="resolveAlert({{ $alert->id }})" title="حل التنبيه">
                                                            <i class="ti ti-check"></i>
                                                        </button>
                                                    @endif
                                                    
                                                    @if($alert->pharmacyInventory)
                                                        <a href="{{ route('admin.inventory.show', $alert->pharmacyInventory->id) }}" 
                                                           class="btn btn-sm btn-outline-info" title="عرض المنتج">
                                                            <i class="ti ti-package"></i>
                                                        </a>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="card-footer">
                            {{ $alerts->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-bell-off fs-1 text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد تنبيهات</h5>
                            <p class="text-muted">لا توجد تنبيهات مخزون حالياً</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal حل التنبيه -->
    <div class="modal fade" id="resolveAlertModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حل التنبيه</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="resolveAlertForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">ملاحظات الحل</label>
                            <textarea name="resolution_notes" class="form-control" rows="3" 
                                      placeholder="اكتب ملاحظات حول كيفية حل هذا التنبيه..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">حل التنبيه</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function markAsRead(alertId) {
            fetch(`/admin/inventory/alerts/${alertId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.error === false) {
                    location.reload();
                }
            });
        }

        function resolveAlert(alertId) {
            const form = document.getElementById('resolveAlertForm');
            form.action = `/admin/inventory/alerts/${alertId}/resolve`;
            
            const modal = new bootstrap.Modal(document.getElementById('resolveAlertModal'));
            modal.show();
        }

        // تحديث تلقائي للتنبيهات كل دقيقة
        setInterval(function() {
            location.reload();
        }, 60000);
    </script>
@endpush

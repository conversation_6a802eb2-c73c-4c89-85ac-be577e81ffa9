@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title">
                            <i class="ti ti-edit me-2"></i>
                            تعديل المخزون: {{ $inventory->product->name }}
                        </h4>
                        <div class="card-actions">
                            <a href="{{ route('admin.inventory.show', $inventory->id) }}" class="btn btn-info me-2">
                                <i class="ti ti-eye me-1"></i>
                                عرض التفاصيل
                            </a>
                            <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- معلومات المنتج -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    @if($inventory->product->image)
                                        <img src="{{ RvMedia::getImageUrl($inventory->product->image, 'thumb') }}" 
                                             alt="{{ $inventory->product->name }}" class="rounded me-3" width="60" height="60">
                                    @endif
                                    <div>
                                        <h5 class="mb-1">{{ $inventory->product->name }}</h5>
                                        <p class="mb-0">
                                            <strong>الصيدلية:</strong> {{ $inventory->pharmacy->name }} |
                                            <strong>الكمية الحالية:</strong> {{ number_format($inventory->quantity_in_stock) }} |
                                            <strong>المتاح:</strong> {{ number_format($inventory->available_quantity) }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('admin.inventory.update', $inventory->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- معلومات الدفعة -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">رقم الدفعة</label>
                                    <input type="text" name="batch_number" class="form-control" 
                                           value="{{ old('batch_number', $inventory->batch_number) }}" placeholder="رقم الدفعة">
                                    @error('batch_number')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الإنتاج</label>
                                    <input type="date" name="manufacturing_date" class="form-control" 
                                           value="{{ old('manufacturing_date', $inventory->manufacturing_date?->format('Y-m-d')) }}">
                                    @error('manufacturing_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ انتهاء الصلاحية</label>
                                    <input type="date" name="expiry_date" class="form-control" 
                                           value="{{ old('expiry_date', $inventory->expiry_date?->format('Y-m-d')) }}">
                                    @error('expiry_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- مستويات المخزون -->
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label required">الحد الأدنى</label>
                                    <input type="number" name="minimum_stock_level" class="form-control" 
                                           value="{{ old('minimum_stock_level', $inventory->minimum_stock_level) }}" min="1" required>
                                    @error('minimum_stock_level')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label required">الحد الأقصى</label>
                                    <input type="number" name="maximum_stock_level" class="form-control" 
                                           value="{{ old('maximum_stock_level', $inventory->maximum_stock_level) }}" min="1" required>
                                    @error('maximum_stock_level')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label required">مستوى إعادة الطلب</label>
                                    <input type="number" name="reorder_level" class="form-control" 
                                           value="{{ old('reorder_level', $inventory->reorder_level) }}" min="1" required>
                                    @error('reorder_level')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">الكمية الحالية</label>
                                    <input type="number" class="form-control" value="{{ $inventory->quantity_in_stock }}" readonly>
                                    <small class="text-muted">لتعديل الكمية، استخدم أزرار الإجراءات في صفحة التفاصيل</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- الأسعار -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label required">سعر التكلفة</label>
                                    <input type="number" name="cost_price" class="form-control" 
                                           value="{{ old('cost_price', $inventory->cost_price) }}" step="0.01" min="0" required>
                                    @error('cost_price')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label required">سعر البيع</label>
                                    <input type="number" name="selling_price" class="form-control" 
                                           value="{{ old('selling_price', $inventory->selling_price) }}" step="0.01" min="0" required>
                                    @error('selling_price')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">هامش الربح</label>
                                    <input type="text" id="profit_margin" class="form-control" readonly>
                                    <small class="text-muted">يتم حسابه تلقائياً</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- معلومات إضافية -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">اسم المورد</label>
                                    <input type="text" name="supplier_name" class="form-control" 
                                           value="{{ old('supplier_name', $inventory->supplier_name) }}" placeholder="اسم المورد">
                                    @error('supplier_name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">موقع التخزين</label>
                                    <input type="text" name="storage_location" class="form-control" 
                                           value="{{ old('storage_location', $inventory->storage_location) }}" placeholder="مثل: رف A1">
                                    @error('storage_location')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label required">ظروف التخزين</label>
                                    <select name="storage_condition" class="form-select" required>
                                        <option value="">اختر ظروف التخزين</option>
                                        <option value="room_temperature" {{ old('storage_condition', $inventory->storage_condition) == 'room_temperature' ? 'selected' : '' }}>
                                            درجة حرارة الغرفة
                                        </option>
                                        <option value="refrigerated" {{ old('storage_condition', $inventory->storage_condition) == 'refrigerated' ? 'selected' : '' }}>
                                            مبرد (2-8°C)
                                        </option>
                                        <option value="frozen" {{ old('storage_condition', $inventory->storage_condition) == 'frozen' ? 'selected' : '' }}>
                                            مجمد (-18°C)
                                        </option>
                                        <option value="controlled" {{ old('storage_condition', $inventory->storage_condition) == 'controlled' ? 'selected' : '' }}>
                                            ظروف خاصة
                                        </option>
                                    </select>
                                    @error('storage_condition')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3" 
                                              placeholder="ملاحظات إضافية...">{{ old('notes', $inventory->notes) }}</textarea>
                                    @error('notes')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الإعدادات</label>
                                    <div class="form-check">
                                        <input type="checkbox" name="is_prescription_required" class="form-check-input" 
                                               value="1" {{ old('is_prescription_required', $inventory->is_prescription_required) ? 'checked' : '' }}>
                                        <label class="form-check-label">
                                            يتطلب وصفة طبية
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="is_active" class="form-check-input" 
                                               value="1" {{ old('is_active', $inventory->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label">
                                            نشط
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إحصائية -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">معلومات إحصائية</h6>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <small class="text-muted">تاريخ الإضافة:</small>
                                                <div>{{ $inventory->created_at->format('Y-m-d H:i') }}</div>
                                            </div>
                                            <div class="col-md-3">
                                                <small class="text-muted">آخر تحديث:</small>
                                                <div>{{ $inventory->updated_at->format('Y-m-d H:i') }}</div>
                                            </div>
                                            <div class="col-md-3">
                                                <small class="text-muted">عدد الحركات:</small>
                                                <div>{{ $inventory->movements->count() }} حركة</div>
                                            </div>
                                            <div class="col-md-3">
                                                <small class="text-muted">قيمة المخزون:</small>
                                                <div class="text-success fw-bold">{{ number_format($inventory->quantity_in_stock * $inventory->cost_price, 2) }} ريال</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.inventory.show', $inventory->id) }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const costPrice = document.querySelector('input[name="cost_price"]');
            const sellingPrice = document.querySelector('input[name="selling_price"]');
            const profitMargin = document.getElementById('profit_margin');
            
            function calculateMargin() {
                const cost = parseFloat(costPrice.value) || 0;
                const selling = parseFloat(sellingPrice.value) || 0;
                
                if (cost > 0) {
                    const margin = ((selling - cost) / cost * 100).toFixed(2);
                    profitMargin.value = margin + '%';
                    
                    // تغيير لون الحقل حسب هامش الربح
                    if (margin < 10) {
                        profitMargin.className = 'form-control text-danger';
                    } else if (margin < 20) {
                        profitMargin.className = 'form-control text-warning';
                    } else {
                        profitMargin.className = 'form-control text-success';
                    }
                } else {
                    profitMargin.value = '0%';
                    profitMargin.className = 'form-control';
                }
            }
            
            // حساب هامش الربح عند تحميل الصفحة
            calculateMargin();
            
            // حساب هامش الربح عند تغيير الأسعار
            costPrice.addEventListener('input', calculateMargin);
            sellingPrice.addEventListener('input', calculateMargin);
            
            // التحقق من صحة المستويات
            const minLevel = document.querySelector('input[name="minimum_stock_level"]');
            const maxLevel = document.querySelector('input[name="maximum_stock_level"]');
            const reorderLevel = document.querySelector('input[name="reorder_level"]');
            
            function validateLevels() {
                const min = parseInt(minLevel.value) || 0;
                const max = parseInt(maxLevel.value) || 0;
                const reorder = parseInt(reorderLevel.value) || 0;
                
                if (min >= max) {
                    maxLevel.setCustomValidity('الحد الأقصى يجب أن يكون أكبر من الحد الأدنى');
                } else {
                    maxLevel.setCustomValidity('');
                }
                
                if (reorder < min) {
                    reorderLevel.setCustomValidity('مستوى إعادة الطلب يجب أن يكون أكبر من أو يساوي الحد الأدنى');
                } else {
                    reorderLevel.setCustomValidity('');
                }
            }
            
            minLevel.addEventListener('input', validateLevels);
            maxLevel.addEventListener('input', validateLevels);
            reorderLevel.addEventListener('input', validateLevels);
            
            // التحقق الأولي
            validateLevels();
        });
    </script>
@endpush

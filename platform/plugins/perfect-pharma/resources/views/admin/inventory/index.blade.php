@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ number_format($stats['total_products']) }}</h4>
                                    <small>إجمالي المنتجات</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-package fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ number_format($stats['total_value'], 2) }}</h4>
                                    <small>قيمة المخزون</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-currency-dollar fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['low_stock_count'] }}</h4>
                                    <small>مخزون منخفض</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-alert-triangle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['out_of_stock_count'] }}</h4>
                                    <small>نفد المخزون</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-x-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['expired_count'] }}</h4>
                                    <small>منتهي الصلاحية</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-calendar-x fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['expiring_soon_count'] }}</h4>
                                    <small>ينتهي قريباً</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="ti ti-clock fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.inventory.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">الصيدلية</label>
                                <select name="pharmacy_id" class="form-select">
                                    <option value="">جميع الصيدليات</option>
                                    @foreach($pharmacies as $pharmacy)
                                        <option value="{{ $pharmacy->id }}" {{ request('pharmacy_id') == $pharmacy->id ? 'selected' : '' }}>
                                            {{ $pharmacy->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">البحث في المنتجات</label>
                                <input type="text" name="product_search" class="form-control" 
                                       placeholder="اسم المنتج..." value="{{ request('product_search') }}">
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">حالة المخزون</label>
                                <select name="stock_status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    @foreach($stockStatuses as $key => $label)
                                        <option value="{{ $key }}" {{ request('stock_status') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">ظروف التخزين</label>
                                <select name="storage_condition" class="form-select">
                                    <option value="">جميع الظروف</option>
                                    @foreach($storageConditions as $key => $label)
                                        <option value="{{ $key }}" {{ request('storage_condition') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-search me-1"></i>
                                        بحث
                                    </button>
                                    <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-refresh me-1"></i>
                                        إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة المخزون -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="ti ti-package me-2"></i>
                            إدارة المخزون
                        </h4>
                        <div class="card-actions">
                            <a href="{{ route('admin.inventory.alerts') }}" class="btn btn-warning me-2">
                                <i class="ti ti-bell me-1"></i>
                                التنبيهات
                            </a>
                            <a href="{{ route('admin.inventory.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                إضافة منتج
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    @if($inventory->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الصيدلية</th>
                                        <th>رقم الدفعة</th>
                                        <th>الكمية</th>
                                        <th>المتاح</th>
                                        <th>حالة المخزون</th>
                                        <th>تاريخ الانتهاء</th>
                                        <th>ظروف التخزين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($inventory as $item)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($item->product->image)
                                                        <img src="{{ RvMedia::getImageUrl($item->product->image, 'thumb') }}" 
                                                             alt="{{ $item->product->name }}" class="rounded me-2" width="40" height="40">
                                                    @endif
                                                    <div>
                                                        <h6 class="mb-0">{{ $item->product->name }}</h6>
                                                        <small class="text-muted">{{ $item->product->sku ?? 'لا يوجد رمز' }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $item->pharmacy->name }}</span>
                                            </td>
                                            <td>
                                                <span class="text-muted">{{ $item->batch_number ?? 'غير محدد' }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-bold">{{ number_format($item->quantity_in_stock) }}</span>
                                                @if($item->reserved_quantity > 0)
                                                    <br><small class="text-warning">(محجوز: {{ $item->reserved_quantity }})</small>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">{{ number_format($item->available_quantity) }}</span>
                                            </td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'out_of_stock' => 'danger',
                                                        'low_stock' => 'warning',
                                                        'reorder_needed' => 'info',
                                                        'overstock' => 'secondary',
                                                        'normal' => 'success'
                                                    ];
                                                    $color = $statusColors[$item->stock_status] ?? 'secondary';
                                                @endphp
                                                <span class="badge bg-{{ $color }}">{{ $item->stock_status_label }}</span>
                                            </td>
                                            <td>
                                                @if($item->expiry_date)
                                                    <div>
                                                        {{ $item->expiry_date->format('Y-m-d') }}
                                                        @if($item->days_to_expiry !== null)
                                                            <br>
                                                            @if($item->days_to_expiry < 0)
                                                                <small class="text-danger">(منتهي منذ {{ abs($item->days_to_expiry) }} يوم)</small>
                                                            @elseif($item->days_to_expiry <= 30)
                                                                <small class="text-warning">({{ $item->days_to_expiry }} يوم متبقي)</small>
                                                            @else
                                                                <small class="text-success">({{ $item->days_to_expiry }} يوم متبقي)</small>
                                                            @endif
                                                        @endif
                                                    </div>
                                                @else
                                                    <span class="text-muted">لا ينتهي</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">{{ $item->storage_condition_label }}</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.inventory.show', $item->id) }}" 
                                                       class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.inventory.edit', $item->id) }}" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <a href="{{ route('admin.inventory.movements', $item->id) }}" 
                                                       class="btn btn-sm btn-outline-info" title="حركات المخزون">
                                                        <i class="ti ti-history"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="card-footer">
                            {{ $inventory->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-package fs-1 text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات في المخزون</h5>
                            <p class="text-muted">ابدأ بإضافة منتجات جديدة للمخزون</p>
                            <a href="{{ route('admin.inventory.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                إضافة منتج جديد
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        // تحديث تلقائي للإحصائيات كل 5 دقائق
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
@endpush

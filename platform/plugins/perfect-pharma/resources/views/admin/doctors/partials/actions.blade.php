<div class="dropdown">
    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="ti ti-dots-vertical"></i>
    </button>
    <ul class="dropdown-menu">
        <li>
            <a class="dropdown-item" href="{{ route('admin.doctors.show', $item->id) }}">
                <i class="ti ti-eye me-2"></i>
                عرض التفاصيل
            </a>
        </li>
        @if(!$item->is_verified)
            <li>
                <a class="dropdown-item text-success" href="#" onclick="verifyDoctor({{ $item->id }})">
                    <i class="ti ti-shield-check me-2"></i>
                    تحقق من الطبيب
                </a>
            </li>
        @endif
        <li>
            <a class="dropdown-item" href="{{ route('admin.doctors.edit', $item->id) }}">
                <i class="ti ti-edit me-2"></i>
                تعديل
            </a>
        </li>
        <li><hr class="dropdown-divider"></li>
        <li>
            <a class="dropdown-item text-danger" href="#" onclick="deleteDoctor({{ $item->id }})">
                <i class="ti ti-trash me-2"></i>
                حذف
            </a>
        </li>
    </ul>
</div>

<script>
function verifyDoctor(doctorId) {
    if (confirm('هل أنت متأكد من تحقق هذا الطبيب؟')) {
        $.post('{{ route("doctors.verify", ":id") }}'.replace(':id', doctorId), {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.error) {
                Botble.showError(response.message);
            } else {
                Botble.showSuccess(response.message);
                location.reload();
            }
        })
        .fail(function() {
            Botble.showError('حدث خطأ أثناء التحقق من الطبيب');
        });
    }
}

function deleteDoctor(doctorId) {
    if (confirm('هل أنت متأكد من حذف هذا الطبيب؟')) {
        $.ajax({
            url: '{{ route("doctors.destroy", ":id") }}'.replace(':id', doctorId),
            method: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.error) {
                    Botble.showError(response.message);
                } else {
                    Botble.showSuccess(response.message);
                    location.reload();
                }
            },
            error: function() {
                Botble.showError('حدث خطأ أثناء حذف الطبيب');
            }
        });
    }
}
</script>

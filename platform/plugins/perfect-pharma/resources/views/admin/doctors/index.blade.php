@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-stethoscope me-2"></i>
                        إدارة الأطباء
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $doctors->total() }}</h3>
                                            <p class="mb-0">إجمالي الأطباء</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-stethoscope fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $doctors->where('is_verified', true)->count() }}</h3>
                                            <p class="mb-0">أطباء محققين</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-shield-check fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $doctors->where('is_verified', false)->count() }}</h3>
                                            <p class="mb-0">في انتظار التحقق</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-clock fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $doctors->where('is_active', true)->count() }}</h3>
                                            <p class="mb-0">أطباء نشطين</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-user-check fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الأطباء -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>كود الطبيب</th>
                                    <th>اسم الطبيب</th>
                                    <th>التخصص</th>
                                    <th>رقم الترخيص</th>
                                    <th>رسوم الكشف</th>
                                    <th>الحالة</th>
                                    <th>التحقق</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($doctors as $doctor)
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary">{{ $doctor->doctor_code }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ substr($doctor->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ $doctor->user->name }}</h6>
                                                    <small class="text-muted">{{ $doctor->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $doctor->specialization }}</span>
                                        </td>
                                        <td>{{ $doctor->license_number }}</td>
                                        <td>
                                            @if($doctor->consultation_fee)
                                                {{ number_format($doctor->consultation_fee) }} جنيه
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($doctor->is_active)
                                                <span class="badge bg-success">نشط</span>
                                            @else
                                                <span class="badge bg-danger">غير نشط</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($doctor->is_verified)
                                                <span class="badge bg-success">
                                                    <i class="ti ti-shield-check me-1"></i>
                                                    محقق
                                                </span>
                                            @else
                                                <span class="badge bg-warning">
                                                    <i class="ti ti-clock me-1"></i>
                                                    في انتظار التحقق
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.doctors.show', $doctor->id) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض التفاصيل
                                                        </a>
                                                    </li>
                                                    @if(!$doctor->is_verified)
                                                        <li>
                                                            <a class="dropdown-item text-success" href="#" onclick="verifyDoctor({{ $doctor->id }})">
                                                                <i class="ti ti-shield-check me-2"></i>
                                                                تحقق من الطبيب
                                                            </a>
                                                        </li>
                                                    @endif
                                                    <li>
                                                        <a class="dropdown-item text-primary" href="{{ route('admin.doctors.edit', $doctor->id) }}">
                                                            <i class="ti ti-edit me-2"></i>
                                                            تعديل
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="empty-state">
                                                <i class="ti ti-stethoscope fs-1 text-muted mb-3"></i>
                                                <h5 class="text-muted">لا توجد أطباء مسجلين</h5>
                                                <p class="text-muted">سيتم عرض الأطباء هنا عند تسجيلهم في النظام</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($doctors->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            {{ $doctors->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function verifyDoctor(doctorId) {
            if (confirm('هل أنت متأكد من تحقق هذا الطبيب؟')) {
                $.post('{{ route("admin.doctors.verify", ":id") }}'.replace(':id', doctorId), {
                    _token: '{{ csrf_token() }}'
                })
                .done(function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                })
                .fail(function() {
                    Botble.showError('حدث خطأ أثناء التحقق من الطبيب');
                });
            }
        }
    </script>
@endpush

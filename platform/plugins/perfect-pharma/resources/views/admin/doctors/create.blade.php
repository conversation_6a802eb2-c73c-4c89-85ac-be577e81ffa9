@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        إضافة طبيب جديد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.doctors.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.doctors.store') }}" method="POST" id="doctor-form">
                        @csrf

                        <div class="row">
                            <!-- البيانات الأساسية -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">البيانات الأساسية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="phone" name="phone" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="license_number" class="form-label">رقم الترخيص <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="license_number" name="license_number" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- البيانات المهنية -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">البيانات المهنية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="specialization" class="form-label">التخصص <span class="text-danger">*</span></label>
                                            <select class="form-select" id="specialization" name="specialization" required>
                                                <option value="">اختر التخصص</option>
                                                <option value="طب عام">طب عام</option>
                                                <option value="طب باطني">طب باطني</option>
                                                <option value="طب أطفال">طب أطفال</option>
                                                <option value="طب نساء وولادة">طب نساء وولادة</option>
                                                <option value="طب عيون">طب عيون</option>
                                                <option value="طب أنف وأذن وحنجرة">طب أنف وأذن وحنجرة</option>
                                                <option value="طب جراحة">طب جراحة</option>
                                                <option value="طب قلب">طب قلب</option>
                                                <option value="طب عظام">طب عظام</option>
                                                <option value="طب نفسي">طب نفسي</option>
                                                <option value="طب جلدية">طب جلدية</option>
                                                <option value="طب أسنان">طب أسنان</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="qualification" class="form-label">المؤهل العلمي</label>
                                            <input type="text" class="form-control" id="qualification" name="qualification" placeholder="مثال: بكالوريوس طب وجراحة">
                                        </div>

                                        <div class="mb-3">
                                            <label for="consultation_fee" class="form-label">رسوم الكشف</label>
                                            <input type="number" class="form-control" id="consultation_fee" name="consultation_fee" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- بيانات العيادة -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">بيانات العيادة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="clinic_address" class="form-label">عنوان العيادة</label>
                                            <textarea class="form-control" id="clinic_address" name="clinic_address" rows="3"></textarea>
                                        </div>

                                        <div class="mb-3">
                                            <label for="clinic_phone" class="form-label">هاتف العيادة</label>
                                            <input type="tel" class="form-control" id="clinic_phone" name="clinic_phone">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- أوقات العمل -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">أوقات العمل</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="work_start_time" class="form-label">وقت بداية العمل</label>
                                            <input type="time" class="form-control" id="work_start_time" name="work_start_time">
                                        </div>

                                        <div class="mb-3">
                                            <label for="work_end_time" class="form-label">وقت نهاية العمل</label>
                                            <input type="time" class="form-control" id="work_end_time" name="work_end_time">
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">أيام العمل</label>
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="work_days[]" value="sunday" id="sunday">
                                                        <label class="form-check-label" for="sunday">الأحد</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="work_days[]" value="monday" id="monday">
                                                        <label class="form-check-label" for="monday">الاثنين</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="work_days[]" value="tuesday" id="tuesday">
                                                        <label class="form-check-label" for="tuesday">الثلاثاء</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="work_days[]" value="wednesday" id="wednesday">
                                                        <label class="form-check-label" for="wednesday">الأربعاء</label>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="work_days[]" value="thursday" id="thursday">
                                                        <label class="form-check-label" for="thursday">الخميس</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="work_days[]" value="friday" id="friday">
                                                        <label class="form-check-label" for="friday">الجمعة</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="work_days[]" value="saturday" id="saturday">
                                                        <label class="form-check-label" for="saturday">السبت</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">نبذة عن الطبيب</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="bio" class="form-label">السيرة الذاتية</label>
                                            <textarea class="form-control" id="bio" name="bio" rows="4" placeholder="اكتب نبذة عن الطبيب وخبراته..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.doctors.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ الطبيب
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            $('#doctor-form').on('submit', function(e) {
                e.preventDefault();

                // إظهار مؤشر التحميل
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="ti ti-loader-2 me-1 animate-spin"></i>جاري الحفظ...').prop('disabled', true);

                // إرسال البيانات
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.error) {
                            Botble.showError(response.message);
                        } else {
                            Botble.showSuccess(response.message);
                            if (response.data && response.data.redirect) {
                                window.location.href = response.data.redirect;
                            }
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                            for (const field in errors) {
                                errorMessage += '- ' + errors[field][0] + '\n';
                            }
                            Botble.showError(errorMessage);
                        } else {
                            Botble.showError('حدث خطأ أثناء حفظ البيانات');
                        }
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endpush

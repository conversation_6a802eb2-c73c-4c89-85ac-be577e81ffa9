@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-shield-check me-2"></i>
                        طلبات التحقق من الأطباء
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.doctors.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    @if($doctors->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-vcenter table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم الطبيب</th>
                                        <th>التخصص</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>رقم الترخيص</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($doctors as $doctor)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-3 bg-primary-lt">
                                                        <i class="ti ti-user-md"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ $doctor->user->name ?? 'غير محدد' }}</div>
                                                        <div class="text-muted small">{{ $doctor->doctor_code }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $doctor->specialization }}</td>
                                            <td>{{ $doctor->user->email ?? 'غير محدد' }}</td>
                                            <td>{{ $doctor->user->phone ?? 'غير محدد' }}</td>
                                            <td>{{ $doctor->license_number }}</td>
                                            <td>{{ $doctor->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.doctors.show', $doctor->id) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="ti ti-eye me-1"></i>
                                                        عرض
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-success verify-btn" 
                                                            data-id="{{ $doctor->id }}"
                                                            data-name="{{ $doctor->user->name ?? 'غير محدد' }}">
                                                        <i class="ti ti-check me-1"></i>
                                                        تحقق
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-danger reject-btn" 
                                                            data-id="{{ $doctor->id }}"
                                                            data-name="{{ $doctor->user->name ?? 'غير محدد' }}">
                                                        <i class="ti ti-x me-1"></i>
                                                        رفض
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-center mt-4">
                            {{ $doctors->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-shield-check fs-1 text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد طلبات تحقق</h5>
                            <p class="text-muted">جميع الأطباء محققون أو لا توجد طلبات جديدة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // التحقق من الطبيب
            $('.verify-btn').on('click', function() {
                const doctorId = $(this).data('id');
                const doctorName = $(this).data('name');
                
                if (confirm(`هل أنت متأكد من تحقق الطبيب "${doctorName}"؟`)) {
                    $.ajax({
                        url: `/admin/perfect-pharma/doctors/${doctorId}/verify`,
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.error) {
                                Botble.showError(response.message);
                            } else {
                                Botble.showSuccess(response.message);
                                location.reload();
                            }
                        },
                        error: function() {
                            Botble.showError('حدث خطأ أثناء التحقق من الطبيب');
                        }
                    });
                }
            });
            
            // رفض الطبيب
            $('.reject-btn').on('click', function() {
                const doctorId = $(this).data('id');
                const doctorName = $(this).data('name');
                
                if (confirm(`هل أنت متأكد من رفض الطبيب "${doctorName}"؟`)) {
                    $.ajax({
                        url: `/admin/perfect-pharma/doctors/${doctorId}/reject`,
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.error) {
                                Botble.showError(response.message);
                            } else {
                                Botble.showSuccess(response.message);
                                location.reload();
                            }
                        },
                        error: function() {
                            Botble.showError('حدث خطأ أثناء رفض الطبيب');
                        }
                    });
                }
            });
        });
    </script>
@endpush

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-building-store me-2"></i>
                        إدارة الصيدليات
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $pharmacies->total() }}</h3>
                                            <p class="mb-0">إجمالي الصيدليات</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-building-store fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $pharmacies->where('is_verified', true)->count() }}</h3>
                                            <p class="mb-0">صيدليات محققة</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-shield-check fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $pharmacies->where('home_delivery', true)->count() }}</h3>
                                            <p class="mb-0">توصيل منزلي</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-truck-delivery fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الصيدليات -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>كود الصيدلية</th>
                                    <th>اسم الصيدلية</th>
                                    <th>العنوان</th>
                                    <th>الهاتف</th>
                                    <th>التوصيل</th>
                                    <th>الحالة</th>
                                    <th>التحقق</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($pharmacies as $pharmacy)
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary">{{ $pharmacy->pharmacy_code }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-success rounded-circle">
                                                        {{ substr($pharmacy->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ $pharmacy->name }}</h6>
                                                    <small class="text-muted">{{ $pharmacy->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ Str::limit($pharmacy->address, 30) }}</small>
                                        </td>
                                        <td>{{ $pharmacy->phone }}</td>
                                        <td>
                                            @if($pharmacy->home_delivery)
                                                <span class="badge bg-success">
                                                    <i class="ti ti-truck me-1"></i>
                                                    متاح
                                                </span>
                                                @if($pharmacy->delivery_fee)
                                                    <br><small class="text-muted">{{ $pharmacy->delivery_fee }} جنيه</small>
                                                @endif
                                            @else
                                                <span class="badge bg-secondary">غير متاح</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($pharmacy->is_active)
                                                <span class="badge bg-success">نشطة</span>
                                            @else
                                                <span class="badge bg-danger">غير نشطة</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($pharmacy->is_verified)
                                                <span class="badge bg-success">
                                                    <i class="ti ti-shield-check me-1"></i>
                                                    محققة
                                                </span>
                                            @else
                                                <span class="badge bg-warning">
                                                    <i class="ti ti-clock me-1"></i>
                                                    في انتظار التحقق
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.pharmacies.show', $pharmacy->id) }}">
                                                            <i class="ti ti-eye me-2"></i>
                                                            عرض التفاصيل
                                                        </a>
                                                    </li>
                                                    @if(!$pharmacy->is_verified)
                                                        <li>
                                                            <a class="dropdown-item text-success" href="#" onclick="verifyPharmacy({{ $pharmacy->id }})">
                                                                <i class="ti ti-shield-check me-2"></i>
                                                                تحقق من الصيدلية
                                                            </a>
                                                        </li>
                                                    @endif
                                                    <li>
                                                        <a class="dropdown-item text-primary" href="{{ route('admin.pharmacies.edit', $pharmacy->id) }}">
                                                            <i class="ti ti-edit me-2"></i>
                                                            تعديل
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="empty-state">
                                                <i class="ti ti-building-store fs-1 text-muted mb-3"></i>
                                                <h5 class="text-muted">لا توجد صيدليات مسجلة</h5>
                                                <p class="text-muted">سيتم عرض الصيدليات هنا عند تسجيلها في النظام</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($pharmacies->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            {{ $pharmacies->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function verifyPharmacy(pharmacyId) {
            if (confirm('هل أنت متأكد من تحقق هذه الصيدلية؟')) {
                $.post('{{ route("admin.pharmacies.verify", ":id") }}'.replace(':id', pharmacyId), {
                    _token: '{{ csrf_token() }}'
                })
                .done(function(response) {
                    if (response.error) {
                        Botble.showError(response.message);
                    } else {
                        Botble.showSuccess(response.message);
                        location.reload();
                    }
                })
                .fail(function() {
                    Botble.showError('حدث خطأ أثناء التحقق من الصيدلية');
                });
            }
        }
    </script>
@endpush

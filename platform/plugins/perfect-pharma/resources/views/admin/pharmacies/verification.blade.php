@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-shield-check me-2"></i>
                        طلبات التحقق من الصيدليات
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.pharmacies.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    @if($pharmacies->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-vcenter table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم الصيدلية</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>رقم الترخيص</th>
                                        <th>العنوان</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pharmacies as $pharmacy)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-3 bg-primary-lt">
                                                        <i class="ti ti-building-store"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ $pharmacy->name }}</div>
                                                        <div class="text-muted small">{{ $pharmacy->pharmacy_code }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $pharmacy->email }}</td>
                                            <td>{{ $pharmacy->phone }}</td>
                                            <td>{{ $pharmacy->license_number }}</td>
                                            <td>{{ Str::limit($pharmacy->address, 50) }}</td>
                                            <td>{{ $pharmacy->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.pharmacies.show', $pharmacy->id) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="ti ti-eye me-1"></i>
                                                        عرض
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-success verify-btn" 
                                                            data-id="{{ $pharmacy->id }}"
                                                            data-name="{{ $pharmacy->name }}">
                                                        <i class="ti ti-check me-1"></i>
                                                        تحقق
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-danger reject-btn" 
                                                            data-id="{{ $pharmacy->id }}"
                                                            data-name="{{ $pharmacy->name }}">
                                                        <i class="ti ti-x me-1"></i>
                                                        رفض
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-center mt-4">
                            {{ $pharmacies->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-shield-check fs-1 text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد طلبات تحقق</h5>
                            <p class="text-muted">جميع الصيدليات محققة أو لا توجد طلبات جديدة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // التحقق من الصيدلية
            $('.verify-btn').on('click', function() {
                const pharmacyId = $(this).data('id');
                const pharmacyName = $(this).data('name');
                
                if (confirm(`هل أنت متأكد من تحقق الصيدلية "${pharmacyName}"؟`)) {
                    $.ajax({
                        url: `/admin/perfect-pharma/pharmacies/${pharmacyId}/verify`,
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.error) {
                                Botble.showError(response.message);
                            } else {
                                Botble.showSuccess(response.message);
                                location.reload();
                            }
                        },
                        error: function() {
                            Botble.showError('حدث خطأ أثناء التحقق من الصيدلية');
                        }
                    });
                }
            });
            
            // رفض الصيدلية
            $('.reject-btn').on('click', function() {
                const pharmacyId = $(this).data('id');
                const pharmacyName = $(this).data('name');
                
                if (confirm(`هل أنت متأكد من رفض الصيدلية "${pharmacyName}"؟`)) {
                    $.ajax({
                        url: `/admin/perfect-pharma/pharmacies/${pharmacyId}/reject`,
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.error) {
                                Botble.showError(response.message);
                            } else {
                                Botble.showSuccess(response.message);
                                location.reload();
                            }
                        },
                        error: function() {
                            Botble.showError('حدث خطأ أثناء رفض الصيدلية');
                        }
                    });
                }
            });
        });
    </script>
@endpush

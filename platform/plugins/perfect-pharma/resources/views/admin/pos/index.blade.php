@extends(BaseHelper::getAdminMasterLayoutTemplate())

@push('header')
    @include('plugins/perfect-pharma::layouts.currency-meta')
@endpush

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ number_format($todayStats['total_sales']) }}</h3>
                                    <p class="mb-0">مبيعات اليوم</p>
                                </div>
                                <i class="ti ti-shopping-cart fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ number_format($todayStats['total_amount'], 2) }}</h3>
                                    <p class="mb-0">إجمالي المبيعات ({{ get_application_currency()->symbol ?? 'ر.س' }})</p>
                                </div>
                                <i class="ti ti-currency-dollar fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ number_format($paymentStats['total_payments']) }}</h3>
                                    <p class="mb-0">المدفوعات</p>
                                </div>
                                <i class="ti ti-credit-card fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ number_format($todayStats['average_sale'], 2) }}</h3>
                                    <p class="mb-0">متوسط البيع ({{ get_application_currency()->symbol ?? 'ر.س' }})</p>
                                </div>
                                <i class="ti ti-chart-line fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الجلسة الحالية -->
        @if($currentSession)
            <div class="col-12 mb-4">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="ti ti-user-check me-2"></i>
                            الجلسة الحالية - {{ $currentSession->session_number }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>الصيدلية:</strong><br>
                                {{ $currentSession->pharmacy->name }}
                            </div>
                            <div class="col-md-3">
                                <strong>وقت الفتح:</strong><br>
                                {{ $currentSession->formatted_opened_at }}
                            </div>
                            <div class="col-md-3">
                                <strong>الرصيد الافتتاحي:</strong><br>
                                {{ format_price($currentSession->opening_balance) }}
                            </div>
                            <div class="col-md-3">
                                <strong>مدة الجلسة:</strong><br>
                                {{ $currentSession->session_duration }}
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <strong>عدد المبيعات:</strong><br>
                                {{ number_format($currentSession->total_sales) }}
                            </div>
                            <div class="col-md-3">
                                <strong>قيمة المبيعات:</strong><br>
                                {{ format_price($currentSession->total_sales_amount) }}
                            </div>
                            <div class="col-md-3">
                                <strong>المرتجعات:</strong><br>
                                {{ number_format($currentSession->total_returns) }}
                            </div>
                            <div class="col-md-3">
                                <strong>النقد المتوقع:</strong><br>
                                {{ format_price($currentSession->expected_cash) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- أزرار الإجراءات السريعة -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">الإجراءات السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.pos.cashier') }}" class="btn btn-primary btn-lg w-100">
                                <i class="ti ti-cash me-2"></i>
                                واجهة الكاشير
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.pos.sales.index') }}" class="btn btn-info btn-lg w-100">
                                <i class="ti ti-list me-2"></i>
                                إدارة المبيعات
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.pos.sales.reports') }}" class="btn btn-success btn-lg w-100">
                                <i class="ti ti-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            @if($currentSession)
                                <a href="{{ route('admin.pos.session.close') }}" class="btn btn-warning btn-lg w-100">
                                    <i class="ti ti-logout me-2"></i>
                                    إغلاق الجلسة
                                </a>
                            @else
                                <a href="{{ route('admin.pos.session.open') }}" class="btn btn-success btn-lg w-100">
                                    <i class="ti ti-login me-2"></i>
                                    فتح جلسة
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المنتجات الأكثر مبيعاً -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">المنتجات الأكثر مبيعاً</h5>
                </div>
                <div class="card-body">
                    @if(count($topProducts) > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>المبيعات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($topProducts as $product)
                                        <tr>
                                            <td>{{ $product['product_name'] }}</td>
                                            <td>{{ number_format($product['total_quantity']) }}</td>
                                            <td>{{ format_price($product['total_sales']) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="ti ti-package fs-1 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد بيانات مبيعات</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- إحصائيات طرق الدفع -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">طرق الدفع اليوم</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ number_format($paymentStats['cash_payments'], 2) }}</h4>
                                <p class="mb-0">نقدي</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ number_format($paymentStats['card_payments'], 2) }}</h4>
                                <p class="mb-0">بطاقة</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ number_format($paymentStats['wallet_payments'], 2) }}</h4>
                                <p class="mb-0">محفظة رقمية</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <h4 class="text-warning">{{ number_format($paymentStats['other_payments'], 2) }}</h4>
                                <p class="mb-0">أخرى</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخر المبيعات -->
        <div class="col-12 mt-4">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">آخر المبيعات</h5>
                        <a href="{{ route('admin.pos.sales.index') }}" class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم البيع</th>
                                    <th>التاريخ</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $recentSales = \Botble\PerfectPharma\Models\PosSale::with(['customer', 'cashier'])
                                                    ->orderBy('created_at', 'desc')
                                                    ->limit(5)
                                                    ->get();
                                @endphp
                                @forelse($recentSales as $sale)
                                    <tr>
                                        <td>
                                            <strong>{{ $sale->sale_number }}</strong>
                                        </td>
                                        <td>{{ $sale->formatted_sale_date }}</td>
                                        <td>{{ $sale->customer->name ?? 'عميل عادي' }}</td>
                                        <td>{{ format_price($sale->total_amount) }}</td>
                                        <td>
                                            <span class="badge bg-{{ $sale->status_color }}">
                                                {{ $sale->status_label }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('admin.pos.sales.show', $sale->id) }}" 
                                                   class="btn btn-outline-info" title="عرض">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.pos.sales.receipt', $sale->id) }}" 
                                                   class="btn btn-outline-primary" title="طباعة" target="_blank">
                                                    <i class="ti ti-printer"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <i class="ti ti-shopping-cart-off fs-1 text-muted"></i>
                                            <p class="text-muted mt-2">لا توجد مبيعات</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(function() {
            fetch('{{ route("admin.pos.dashboard.stats") }}')
                .then(response => response.json())
                .then(data => {
                    // تحديث الإحصائيات في الواجهة
                    console.log('Stats updated:', data);
                })
                .catch(error => console.error('Error updating stats:', error));
        }, 30000);
    </script>
@endpush

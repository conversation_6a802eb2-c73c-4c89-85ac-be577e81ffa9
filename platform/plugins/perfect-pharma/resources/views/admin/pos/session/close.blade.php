@extends(BaseHelper::getAdminMasterLayoutTemplate())

@push('header')
    @include('plugins/perfect-pharma::layouts.currency-meta')
@endpush

@section('content')
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="ti ti-logout me-2"></i>
                        إغلاق جلسة كاشير - {{ $currentSession->session_number }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- معلومات الجلسة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ number_format($currentSession->total_sales) }}</h3>
                                    <p class="mb-0">عدد المبيعات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ number_format($currentSession->total_sales_amount, 2) }}</h3>
                                    <p class="mb-0">قيمة المبيعات ({{ get_application_currency()->symbol ?? 'ر.س' }})</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ number_format($currentSession->total_returns) }}</h3>
                                    <p class="mb-0">عدد المرتجعات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ $currentSession->session_duration }}</h3>
                                    <p class="mb-0">مدة الجلسة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل الجلسة -->
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h6 class="card-title">تفاصيل الجلسة</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>الكاشير:</strong><br>
                                    {{ $currentSession->cashier->name }}
                                </div>
                                <div class="col-md-3">
                                    <strong>الصيدلية:</strong><br>
                                    {{ $currentSession->pharmacy->name }}
                                </div>
                                <div class="col-md-3">
                                    <strong>وقت الفتح:</strong><br>
                                    {{ $currentSession->formatted_opened_at }}
                                </div>
                                <div class="col-md-3">
                                    <strong>الرصيد الافتتاحي:</strong><br>
                                    {{ format_price($currentSession->opening_balance) }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- حساب النقد المتوقع -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">النقد المتوقع</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الرصيد الافتتاحي:</span>
                                        <span>{{ format_price($currentSession->opening_balance) }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المبيعات النقدية:</span>
                                        <span class="text-success">+{{ format_price($currentSession->cash_sales ?? 0) }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المرتجعات النقدية:</span>
                                        <span class="text-danger">-{{ format_price($currentSession->cash_returns ?? 0) }}</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>النقد المتوقع:</span>
                                        <span class="text-primary">{{ format_price($currentSession->expected_cash) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">ملخص المدفوعات</h6>
                                </div>
                                <div class="card-body">
                                    @php
                                        $paymentStats = \Botble\PerfectPharma\Models\PosPayment::whereHas('sale', function($q) use ($currentSession) {
                                            $q->where('cashier_id', $currentSession->cashier_id)
                                              ->whereBetween('sale_date', [$currentSession->opened_at, now()]);
                                        })->approved()->get()->groupBy('payment_method');
                                    @endphp
                                    
                                    @foreach(['cash' => 'نقدي', 'card' => 'بطاقة', 'wallet' => 'محفظة', 'other' => 'أخرى'] as $method => $label)
                                        @php
                                            $amount = $paymentStats->get($method, collect())->sum('amount');
                                        @endphp
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>{{ $label }}:</span>
                                            <span>{{ format_price($amount) }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج الإغلاق -->
                    <form action="{{ route('admin.pos.session.update') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label required">الرصيد الختامي الفعلي ({{ get_application_currency()->symbol ?? 'ر.س' }})</label>
                                    <input type="number" name="closing_balance" class="form-control" 
                                           value="{{ old('closing_balance') }}" step="0.01" min="0" required>
                                    <small class="text-muted">المبلغ النقدي الموجود فعلياً في الدرج</small>
                                    @error('closing_balance')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الفرق</label>
                                    <input type="text" id="difference-display" class="form-control" readonly>
                                    <small class="text-muted">الفرق بين المتوقع والفعلي</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات الإغلاق</label>
                            <textarea name="closing_notes" class="form-control" rows="4" 
                                      placeholder="أي ملاحظات خاصة بإغلاق الجلسة، أسباب الفروقات، مشاكل حدثت...">{{ old('closing_notes') }}</textarea>
                            @error('closing_notes')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- تحذيرات -->
                        <div id="difference-warning" class="alert alert-warning" style="display: none;">
                            <h6 class="alert-heading">تنبيه: يوجد فرق في الرصيد</h6>
                            <p class="mb-0">يرجى التأكد من عد النقود مرة أخرى وتوضيح سبب الفرق في الملاحظات.</p>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.pos.cashier') }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left me-1"></i>
                                العودة للكاشير
                            </a>
                            <button type="submit" class="btn btn-warning" id="close-session-btn">
                                <i class="ti ti-logout me-1"></i>
                                إغلاق الجلسة
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- تعليمات الإغلاق -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-checklist me-2"></i>
                        خطوات إغلاق الجلسة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>قبل الإغلاق:</h6>
                            <ul class="list-unstyled">
                                <li><i class="ti ti-square-check text-success me-2"></i>تأكد من إنهاء جميع المبيعات المعلقة</li>
                                <li><i class="ti ti-square-check text-success me-2"></i>راجع جميع المرتجعات</li>
                                <li><i class="ti ti-square-check text-success me-2"></i>تأكد من طباعة جميع الفواتير</li>
                                <li><i class="ti ti-square-check text-success me-2"></i>احفظ نسخة من التقارير</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>عند الإغلاق:</h6>
                            <ul class="list-unstyled">
                                <li><i class="ti ti-square-check text-success me-2"></i>عد النقود في الدرج بدقة</li>
                                <li><i class="ti ti-square-check text-success me-2"></i>تأكد من تطابق الأرقام</li>
                                <li><i class="ti ti-square-check text-success me-2"></i>سجل أي فروقات مع السبب</li>
                                <li><i class="ti ti-square-check text-success me-2"></i>احفظ النقود في المكان الآمن</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            var expectedCash = {{ $currentSession->expected_cash }};
            
            // حساب الفرق عند تغيير الرصيد الختامي
            $('input[name="closing_balance"]').on('input', function() {
                var closingBalance = parseFloat($(this).val()) || 0;
                var difference = closingBalance - expectedCash;
                
                $('#difference-display').val(window.formatPrice ? window.formatPrice(difference) : difference.toFixed(2));
                
                // تغيير لون الحقل حسب الفرق
                if (Math.abs(difference) > 0.01) {
                    $('#difference-display').removeClass('text-success').addClass('text-danger');
                    $('#difference-warning').show();
                    
                    if (difference > 0) {
                        $('#difference-warning .alert-heading').text('تنبيه: زيادة في الرصيد');
                        $('#difference-warning p').text('الرصيد الفعلي أكبر من المتوقع بمقدار ' + (window.formatPrice ? window.formatPrice(difference) : difference.toFixed(2)) + '.');
                    } else {
                        $('#difference-warning .alert-heading').text('تنبيه: نقص في الرصيد');
                        $('#difference-warning p').text('الرصيد الفعلي أقل من المتوقع بمقدار ' + (window.formatPrice ? window.formatPrice(Math.abs(difference)) : Math.abs(difference).toFixed(2)) + '.');
                    }
                } else {
                    $('#difference-display').removeClass('text-danger').addClass('text-success');
                    $('#difference-warning').hide();
                }
            });

            // تأكيد الإغلاق
            $('#close-session-btn').on('click', function(e) {
                var closingBalance = parseFloat($('input[name="closing_balance"]').val()) || 0;
                var difference = Math.abs(closingBalance - expectedCash);
                
                if (difference > 0.01) {
                    if (!confirm('يوجد فرق في الرصيد بمقدار ' + (window.formatPrice ? window.formatPrice(difference) : difference.toFixed(2)) + '. هل أنت متأكد من الإغلاق؟')) {
                        e.preventDefault();
                        return false;
                    }
                }
                
                if (!confirm('هل أنت متأكد من إغلاق الجلسة؟ لن تتمكن من التراجع عن هذا الإجراء.')) {
                    e.preventDefault();
                    return false;
                }
            });

            // تعيين الرصيد المتوقع كقيمة افتراضية
            if (!$('input[name="closing_balance"]').val()) {
                $('input[name="closing_balance"]').val(expectedCash.toFixed(2)).trigger('input');
            }
        });
    </script>
@endpush

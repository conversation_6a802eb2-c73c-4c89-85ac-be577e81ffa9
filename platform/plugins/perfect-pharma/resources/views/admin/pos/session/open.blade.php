@extends(BaseHelper::getAdminMasterLayoutTemplate())

@push('header')
    @include('plugins/perfect-pharma::layouts.currency-meta')
@endpush

@section('content')
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="ti ti-login me-2"></i>
                        فتح جلسة كاشير
                    </h4>
                </div>
                
                <div class="card-body">
                    @if($currentSession)
                        <div class="alert alert-warning">
                            <h5 class="alert-heading">
                                <i class="ti ti-alert-triangle me-2"></i>
                                يوجد جلسة مفتوحة بالفعل
                            </h5>
                            <p class="mb-3">
                                لديك جلسة كاشير مفتوحة برقم: <strong>{{ $currentSession->session_number }}</strong>
                                <br>تم فتحها في: {{ $currentSession->formatted_opened_at }}
                                <br>الصيدلية: {{ $currentSession->pharmacy->name }}
                            </p>
                            <div class="d-flex gap-2">
                                <a href="{{ route('admin.pos.cashier') }}" class="btn btn-primary">
                                    <i class="ti ti-cash me-1"></i>
                                    الذهاب لواجهة الكاشير
                                </a>
                                <a href="{{ route('admin.pos.session.close') }}" class="btn btn-warning">
                                    <i class="ti ti-logout me-1"></i>
                                    إغلاق الجلسة الحالية
                                </a>
                            </div>
                        </div>
                    @else
                        <form action="{{ route('admin.pos.session.store') }}" method="POST">
                            @csrf
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label required">الصيدلية</label>
                                        <select name="pharmacy_id" class="form-select" required>
                                            <option value="">اختر الصيدلية</option>
                                            @foreach($pharmacies as $pharmacy)
                                                <option value="{{ $pharmacy->id }}" {{ old('pharmacy_id') == $pharmacy->id ? 'selected' : '' }}>
                                                    {{ $pharmacy->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('pharmacy_id')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label required">الرصيد الافتتاحي ({{ get_application_currency()->symbol ?? 'ر.س' }})</label>
                                        <input type="number" name="opening_balance" class="form-control" 
                                               value="{{ old('opening_balance', '0.00') }}" step="0.01" min="0" required>
                                        <small class="text-muted">المبلغ النقدي الموجود في الدرج في بداية الجلسة</small>
                                        @error('opening_balance')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">ملاحظات الفتح</label>
                                <textarea name="opening_notes" class="form-control" rows="3" 
                                          placeholder="أي ملاحظات خاصة بفتح الجلسة...">{{ old('opening_notes') }}</textarea>
                                @error('opening_notes')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">معلومات الجلسة</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>الكاشير:</strong><br>
                                            {{ Auth::user()->name }}
                                        </div>
                                        <div class="col-md-4">
                                            <strong>التاريخ:</strong><br>
                                            {{ now()->format('Y-m-d') }}
                                        </div>
                                        <div class="col-md-4">
                                            <strong>الوقت:</strong><br>
                                            {{ now()->format('H:i') }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end gap-2 mt-4">
                                <a href="{{ route('admin.pos.index') }}" class="btn btn-secondary">
                                    <i class="ti ti-arrow-left me-1"></i>
                                    العودة
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="ti ti-login me-1"></i>
                                    فتح الجلسة
                                </button>
                            </div>
                        </form>
                    @endif
                </div>
            </div>

            <!-- نصائح وإرشادات -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-info-circle me-2"></i>
                        نصائح مهمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>قبل فتح الجلسة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="ti ti-check text-success me-2"></i>تأكد من عد النقود في الدرج</li>
                                <li><i class="ti ti-check text-success me-2"></i>تحقق من اتصال الطابعة</li>
                                <li><i class="ti ti-check text-success me-2"></i>تأكد من اتصال الإنترنت</li>
                                <li><i class="ti ti-check text-success me-2"></i>راجع المنتجات المتوفرة</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>أثناء الجلسة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="ti ti-alert-circle text-warning me-2"></i>احتفظ بجميع الإيصالات</li>
                                <li><i class="ti ti-alert-circle text-warning me-2"></i>تأكد من دقة المبالغ</li>
                                <li><i class="ti ti-alert-circle text-warning me-2"></i>راجع تواريخ انتهاء الصلاحية</li>
                                <li><i class="ti ti-alert-circle text-warning me-2"></i>سجل أي مشاكل تحدث</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        $(document).ready(function() {
            // التحقق من الرصيد الافتتاحي
            $('input[name="opening_balance"]').on('input', function() {
                var value = parseFloat($(this).val()) || 0;
                if (value < 0) {
                    $(this).val(0);
                }
            });

            // تحديد الصيدلية تلقائياً إذا كان هناك واحدة فقط
            var pharmacySelect = $('select[name="pharmacy_id"]');
            if (pharmacySelect.find('option').length === 2) { // واحدة فارغة + واحدة فعلية
                pharmacySelect.find('option:last').prop('selected', true);
            }
        });
    </script>
@endpush

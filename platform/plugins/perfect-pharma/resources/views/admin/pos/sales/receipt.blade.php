<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ $sale->sale_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            direction: rtl;
        }
        
        .receipt {
            width: 80mm;
            margin: 0 auto;
            padding: 10px;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .pharmacy-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .pharmacy-info {
            font-size: 10px;
            color: #666;
        }
        
        .sale-info {
            margin-bottom: 15px;
            border-bottom: 1px dashed #ccc;
            padding-bottom: 10px;
        }
        
        .sale-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .items-table {
            width: 100%;
            margin-bottom: 15px;
            border-collapse: collapse;
        }
        
        .items-table th,
        .items-table td {
            padding: 5px 2px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        .items-table th {
            font-weight: bold;
            border-bottom: 2px solid #333;
        }
        
        .item-name {
            font-size: 11px;
            max-width: 120px;
            word-wrap: break-word;
        }
        
        .totals {
            border-top: 2px solid #333;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        
        .totals div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .total-amount {
            font-size: 14px;
            font-weight: bold;
            border-top: 1px solid #333;
            padding-top: 5px;
            margin-top: 5px;
        }
        
        .payment-info {
            border-top: 1px dashed #ccc;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        
        .footer {
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px dashed #ccc;
            padding-top: 10px;
        }
        
        .qr-code {
            text-align: center;
            margin: 10px 0;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .receipt {
                width: 100%;
                margin: 0;
                padding: 5px;
            }
            
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- رأس الفاتورة -->
        <div class="header">
            <div class="pharmacy-name">{{ $sale->pharmacy->name }}</div>
            <div class="pharmacy-info">
                {{ $sale->pharmacy->address }}<br>
                هاتف: {{ $sale->pharmacy->phone }}<br>
                @if($sale->pharmacy->email)
                    البريد: {{ $sale->pharmacy->email }}<br>
                @endif
                الرقم الضريبي: {{ $sale->pharmacy->tax_number ?? 'غير محدد' }}
            </div>
        </div>

        <!-- معلومات البيع -->
        <div class="sale-info">
            <div>
                <span>رقم الفاتورة:</span>
                <span><strong>{{ $sale->sale_number }}</strong></span>
            </div>
            <div>
                <span>التاريخ:</span>
                <span>{{ $sale->sale_date->format('Y-m-d') }}</span>
            </div>
            <div>
                <span>الوقت:</span>
                <span>{{ $sale->sale_date->format('H:i') }}</span>
            </div>
            <div>
                <span>الكاشير:</span>
                <span>{{ $sale->cashier->name }}</span>
            </div>
            @if($sale->customer)
                <div>
                    <span>العميل:</span>
                    <span>{{ $sale->customer->name }}</span>
                </div>
                @if($sale->customer->phone)
                    <div>
                        <span>الهاتف:</span>
                        <span>{{ $sale->customer->phone }}</span>
                    </div>
                @endif
            @endif
        </div>

        <!-- عناصر البيع -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 50%">المنتج</th>
                    <th style="width: 15%">الكمية</th>
                    <th style="width: 20%">السعر</th>
                    <th style="width: 15%">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                @foreach($sale->items as $item)
                    <tr>
                        <td class="item-name">
                            {{ $item->product_name }}
                            @if($item->batch_number)
                                <br><small style="color: #666;">دفعة: {{ $item->batch_number }}</small>
                            @endif
                            @if($item->expiry_date)
                                <br><small style="color: #666;">انتهاء: {{ $item->expiry_date->format('Y-m-d') }}</small>
                            @endif
                        </td>
                        <td style="text-align: center;">{{ $item->quantity }}</td>
                        <td style="text-align: center;">{{ number_format($item->unit_price, 2) }}</td>
                        <td style="text-align: center;">{{ number_format($item->total_price, 2) }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        <!-- المجاميع -->
        <div class="totals">
            <div>
                <span>المجموع الفرعي:</span>
                <span>{{ number_format($sale->subtotal, 2) }} ريال</span>
            </div>
            @if($sale->discount_amount > 0)
                <div>
                    <span>الخصم:</span>
                    <span>-{{ number_format($sale->discount_amount, 2) }} ريال</span>
                </div>
            @endif
            @if($sale->tax_amount > 0)
                <div>
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>{{ number_format($sale->tax_amount, 2) }} ريال</span>
                </div>
            @endif
            <div class="total-amount">
                <span>الإجمالي:</span>
                <span>{{ number_format($sale->total_amount, 2) }} ريال</span>
            </div>
        </div>

        <!-- معلومات الدفع -->
        <div class="payment-info">
            @foreach($sale->payments as $payment)
                <div>
                    <span>{{ $payment->payment_method_label }}:</span>
                    <span>{{ number_format($payment->amount, 2) }} ريال</span>
                </div>
            @endforeach
            @if($sale->change_amount > 0)
                <div style="border-top: 1px solid #ccc; padding-top: 5px; margin-top: 5px;">
                    <span>الباقي:</span>
                    <span><strong>{{ number_format($sale->change_amount, 2) }} ريال</strong></span>
                </div>
            @endif
        </div>

        <!-- رمز QR (اختياري) -->
        <div class="qr-code">
            <!-- يمكن إضافة رمز QR هنا -->
            <div style="border: 1px solid #ccc; width: 60px; height: 60px; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 10px;">
                QR Code
            </div>
        </div>

        <!-- ذيل الفاتورة -->
        <div class="footer">
            <div>شكراً لتسوقكم معنا</div>
            <div>{{ $sale->pharmacy->name }}</div>
            <div style="margin-top: 5px;">
                تم الطباعة في: {{ now()->format('Y-m-d H:i') }}
            </div>
            @if($sale->notes)
                <div style="margin-top: 5px; font-style: italic;">
                    ملاحظات: {{ $sale->notes }}
                </div>
            @endif
        </div>
    </div>

    <!-- أزرار الطباعة (لا تظهر في الطباعة) -->
    <div class="no-print" style="text-align: center; margin: 20px; padding: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; font-size: 14px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
            طباعة الفاتورة
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; font-size: 14px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
            إغلاق
        </button>
        <a href="{{ route('admin.pos.sales.show', $sale->id) }}" style="padding: 10px 20px; font-size: 14px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;">
            عرض التفاصيل
        </a>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() {
        //     window.print();
        // };
    </script>
</body>
</html>

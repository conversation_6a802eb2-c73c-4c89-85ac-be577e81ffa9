@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['total_sales']) }}</h3>
                                    <p class="mb-0">إجمالي المبيعات</p>
                                </div>
                                <i class="ti ti-shopping-cart fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['total_amount'], 2) }}</h3>
                                    <p class="mb-0">قيمة المبيعات (ريال)</p>
                                </div>
                                <i class="ti ti-currency-dollar fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['completed_sales']) }}</h3>
                                    <p class="mb-0">مبيعات مكتملة</p>
                                </div>
                                <i class="ti ti-check fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ number_format($stats['pending_sales']) }}</h3>
                                    <p class="mb-0">مبيعات معلقة</p>
                                </div>
                                <i class="ti ti-clock fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.pos.sales.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">الصيدلية</label>
                                <select name="pharmacy_id" class="form-select">
                                    <option value="">جميع الصيدليات</option>
                                    @foreach($pharmacies as $pharmacy)
                                        <option value="{{ $pharmacy->id }}" {{ request('pharmacy_id') == $pharmacy->id ? 'selected' : '' }}>
                                            {{ $pharmacy->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">الكاشير</label>
                                <select name="cashier_id" class="form-select">
                                    <option value="">جميع الكاشيرين</option>
                                    @foreach($cashiers as $cashier)
                                        <option value="{{ $cashier->id }}" {{ request('cashier_id') == $cashier->id ? 'selected' : '' }}>
                                            {{ $cashier->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">حالة البيع</label>
                                <select name="status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    @foreach($statuses as $key => $label)
                                        <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">حالة الدفع</label>
                                <select name="payment_status" class="form-select">
                                    <option value="">جميع حالات الدفع</option>
                                    @foreach($paymentStatuses as $key => $label)
                                        <option value="{{ $key }}" {{ request('payment_status') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-search me-1"></i>
                                        بحث
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <a href="{{ route('admin.pos.sales.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-refresh me-1"></i>
                                        إعادة تعيين
                                    </a>
                                    <a href="{{ route('admin.pos.sales.reports') }}" class="btn btn-success">
                                        <i class="ti ti-chart-bar me-1"></i>
                                        التقارير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة المبيعات -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="ti ti-list me-2"></i>
                            قائمة المبيعات
                        </h4>
                        <div class="card-actions">
                            <a href="{{ route('admin.pos.cashier') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                بيع جديد
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    @if($sales->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم البيع</th>
                                        <th>التاريخ والوقت</th>
                                        <th>الكاشير</th>
                                        <th>العميل</th>
                                        <th>العناصر</th>
                                        <th>المبلغ</th>
                                        <th>حالة البيع</th>
                                        <th>حالة الدفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sales as $sale)
                                        <tr>
                                            <td>
                                                <strong>{{ $sale->sale_number }}</strong>
                                            </td>
                                            <td>
                                                <div>
                                                    {{ $sale->formatted_sale_date }}
                                                    <br><small class="text-muted">{{ $sale->formatted_sale_time }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    {{ $sale->cashier->name }}
                                                    <br><small class="text-muted">{{ $sale->pharmacy->name }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                {{ $sale->customer->name ?? 'عميل عادي' }}
                                                @if($sale->customer)
                                                    <br><small class="text-muted">{{ $sale->customer->phone }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $sale->items_count }} عنصر</span>
                                                <br><small class="text-muted">{{ $sale->total_quantity }} قطعة</small>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ number_format($sale->total_amount, 2) }} ريال</strong>
                                                    @if($sale->discount_amount > 0)
                                                        <br><small class="text-muted">خصم: {{ number_format($sale->discount_amount, 2) }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $sale->status_color }}">
                                                    {{ $sale->status_label }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $sale->payment_status_color }}">
                                                    {{ $sale->payment_status_label }}
                                                </span>
                                                @if($sale->remaining_amount > 0)
                                                    <br><small class="text-muted">متبقي: {{ number_format($sale->remaining_amount, 2) }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('admin.pos.sales.show', $sale->id) }}" 
                                                       class="btn btn-outline-info" title="عرض التفاصيل">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.pos.sales.receipt', $sale->id) }}" 
                                                       class="btn btn-outline-primary" title="طباعة الفاتورة" target="_blank">
                                                        <i class="ti ti-printer"></i>
                                                    </a>
                                                    @if($sale->canBeRefunded())
                                                        <a href="{{ route('admin.pos.sales.refund', $sale->id) }}" 
                                                           class="btn btn-outline-warning" title="إرجاع">
                                                            <i class="ti ti-arrow-back-up"></i>
                                                        </a>
                                                    @endif
                                                    @if($sale->canBeCancelled())
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                title="إلغاء" onclick="cancelSale({{ $sale->id }})">
                                                            <i class="ti ti-x"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="card-footer">
                            {{ $sales->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="ti ti-shopping-cart-off fs-1 text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مبيعات</h5>
                            <p class="text-muted">لم يتم العثور على مبيعات تطابق معايير البحث</p>
                            <a href="{{ route('admin.pos.cashier') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>
                                إنشاء بيع جديد
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إلغاء البيع -->
    <div class="modal fade" id="cancel-sale-modal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إلغاء البيع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="cancel-sale-form" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label required">سبب الإلغاء</label>
                            <textarea name="cancellation_reason" class="form-control" rows="3" 
                                      placeholder="يرجى توضيح سبب إلغاء البيع..." required></textarea>
                        </div>
                        <div class="alert alert-warning">
                            <strong>تنبيه:</strong> سيتم إرجاع جميع المنتجات للمخزون عند إلغاء البيع.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">تأكيد الإلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        function cancelSale(saleId) {
            $('#cancel-sale-form').attr('action', `/admin/perfect-pharma/pos/sales/${saleId}/cancel`);
            $('#cancel-sale-modal').modal('show');
        }

        $(document).ready(function() {
            // تحديث الصفحة كل دقيقتين
            setInterval(function() {
                if (!$('.modal').hasClass('show')) {
                    location.reload();
                }
            }, 120000);

            // تأكيد إلغاء البيع
            $('#cancel-sale-form').on('submit', function(e) {
                if (!confirm('هل أنت متأكد من إلغاء هذا البيع؟')) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    </script>
@endpush

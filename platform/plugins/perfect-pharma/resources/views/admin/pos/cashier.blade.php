@extends(BaseHelper::getAdminMasterLayoutTemplate())

@push('header')
    <style>
        .pos-container {
            height: calc(100vh - 120px);
            overflow: hidden;
        }
        
        .product-search {
            position: sticky;
            top: 0;
            z-index: 100;
            background: white;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .products-grid {
            height: calc(100vh - 300px);
            overflow-y: auto;
            padding: 15px;
        }
        
        .product-card {
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        
        .product-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .cart-container {
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
            border-left: 1px solid #dee2e6;
        }
        
        .cart-header {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .cart-items {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        
        .cart-footer {
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        
        .cart-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            background: white;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .quantity-controls button {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 1px solid #dee2e6;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .quantity-controls input {
            width: 60px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            height: 30px;
        }
        
        .total-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .payment-method {
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .payment-method.active {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .customer-info {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
        }
    </style>
@endpush

@section('content')
    <div class="pos-container">
        <div class="row h-100">
            <!-- قسم المنتجات -->
            <div class="col-md-8">
                <!-- شريط البحث -->
                <div class="product-search">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="ti ti-search"></i>
                                </span>
                                <input type="text" id="product-search" class="form-control" 
                                       placeholder="ابحث عن منتج بالاسم أو الرمز..." autocomplete="off">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select id="category-filter" class="form-select">
                                <option value="">جميع الفئات</option>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                    </div>
                    
                    <!-- نتائج البحث -->
                    <div id="search-results" class="mt-3" style="display: none;">
                        <div class="card">
                            <div class="card-body p-2">
                                <div id="search-results-list"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شبكة المنتجات -->
                <div class="products-grid">
                    <div class="row" id="products-container">
                        @foreach($products as $product)
                            @php
                                $inventory = $product->pharmacyInventory->first();
                            @endphp
                            @if($inventory && $inventory->available_quantity > 0)
                                <div class="col-md-4 col-lg-3 mb-3">
                                    <div class="card product-card" 
                                         data-product-id="{{ $product->id }}"
                                         data-product-name="{{ $product->name }}"
                                         data-product-price="{{ $inventory->selling_price }}"
                                         data-product-stock="{{ $inventory->available_quantity }}"
                                         data-inventory-id="{{ $inventory->id }}"
                                         data-batch-number="{{ $inventory->batch_number }}"
                                         data-expiry-date="{{ $inventory->expiry_date?->format('Y-m-d') }}"
                                         data-prescription-required="{{ $inventory->is_prescription_required ? 'true' : 'false' }}">
                                        <div class="card-body p-2">
                                            @if($product->image)
                                                <img src="{{ RvMedia::getImageUrl($product->image, 'thumb') }}" 
                                                     alt="{{ $product->name }}" class="img-fluid mb-2" style="height: 60px; object-fit: cover;">
                                            @endif
                                            <h6 class="card-title mb-1" style="font-size: 0.9rem;">{{ Str::limit($product->name, 30) }}</h6>
                                            <p class="card-text mb-1">
                                                <strong class="text-success">{{ number_format($inventory->selling_price, 2) }} ريال</strong>
                                            </p>
                                            <p class="card-text mb-0">
                                                <small class="text-muted">المتاح: {{ $inventory->available_quantity }}</small>
                                            </p>
                                            @if($inventory->is_prescription_required)
                                                <span class="badge bg-warning badge-sm">يتطلب وصفة</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- قسم السلة والدفع -->
            <div class="col-md-4">
                <div class="cart-container">
                    <!-- رأس السلة -->
                    <div class="cart-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">سلة التسوق</h5>
                            <button type="button" class="btn btn-sm btn-outline-danger" id="clear-cart">
                                <i class="ti ti-trash"></i> مسح الكل
                            </button>
                        </div>
                        
                        <!-- معلومات الجلسة -->
                        <div class="mt-2">
                            <small class="text-muted">
                                الجلسة: {{ $currentSession->session_number }} | 
                                الصيدلية: {{ $currentSession->pharmacy->name }}
                            </small>
                        </div>
                    </div>

                    <!-- عناصر السلة -->
                    <div class="cart-items">
                        <div id="cart-items-container">
                            <div class="text-center py-5" id="empty-cart-message">
                                <i class="ti ti-shopping-cart-off fs-1 text-muted"></i>
                                <p class="text-muted mt-2">السلة فارغة</p>
                                <p class="text-muted">اختر المنتجات لإضافتها</p>
                            </div>
                        </div>
                    </div>

                    <!-- ذيل السلة - الدفع -->
                    <div class="cart-footer">
                        <!-- معلومات العميل -->
                        <div class="mb-3">
                            <label class="form-label">العميل (اختياري)</label>
                            <select id="customer-select" class="form-select">
                                <option value="">عميل عادي</option>
                                @foreach($customers as $customer)
                                    <option value="{{ $customer->id }}">{{ $customer->name }} - {{ $customer->phone }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- ملخص المبالغ -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal-display">0.00 ريال</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>الخصم:</span>
                                <span id="discount-display">0.00 ريال</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>الضريبة:</span>
                                <span id="tax-display">0.00 ريال</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>الإجمالي:</strong>
                                <strong class="total-display" id="total-display">0.00 ريال</strong>
                            </div>
                        </div>

                        <!-- طرق الدفع -->
                        <div class="mb-3">
                            <label class="form-label">طريقة الدفع</label>
                            <div class="payment-methods">
                                <div class="payment-method active" data-method="cash">
                                    <i class="ti ti-cash fs-3"></i>
                                    <div>نقدي</div>
                                </div>
                                <div class="payment-method" data-method="card">
                                    <i class="ti ti-credit-card fs-3"></i>
                                    <div>بطاقة</div>
                                </div>
                                <div class="payment-method" data-method="wallet">
                                    <i class="ti ti-wallet fs-3"></i>
                                    <div>محفظة</div>
                                </div>
                                <div class="payment-method" data-method="insurance">
                                    <i class="ti ti-shield-check fs-3"></i>
                                    <div>تأمين</div>
                                </div>
                            </div>
                        </div>

                        <!-- مبلغ الدفع -->
                        <div class="mb-3">
                            <label class="form-label">المبلغ المدفوع</label>
                            <input type="number" id="payment-amount" class="form-control" step="0.01" min="0">
                            <div class="mt-1">
                                <small class="text-muted">الباقي: <span id="change-display">0.00 ريال</span></small>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success btn-lg" id="complete-sale" disabled>
                                <i class="ti ti-check me-2"></i>
                                إتمام البيع
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="hold-sale" disabled>
                                <i class="ti ti-clock me-2"></i>
                                تعليق البيع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد البيع -->
    <div class="modal fade" id="sale-confirmation-modal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد البيع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="sale-summary"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" id="confirm-sale">تأكيد البيع</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('header')
    @include('plugins/perfect-pharma::layouts.currency-meta')
@endpush

@push('footer')
    <script src="{{ asset('vendor/core/plugins/perfect-pharma/js/pos-cashier.js') }}"></script>
@endpush

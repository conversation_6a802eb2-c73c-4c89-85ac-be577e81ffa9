@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-0">
                <i class="fas fa-percentage"></i>
                إدارة خصومات المنتجات
            </h4>
            <p class="text-muted">تحديد نسب الخصم لكل نوع مستخدم على المنتجات</p>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4" id="statisticsRow">
        <!-- سيتم تحميل الإحصائيات هنا -->
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter"></i>
                فلاتر البحث
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.perfect-pharma.product-discounts.index') }}" id="filterForm">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="البحث بالاسم..." 
                               value="{{ request('search') }}">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">حالة الخصومات</label>
                        <select name="discount_status" class="form-select">
                            <option value="">جميع المنتجات</option>
                            <option value="enabled" {{ request('discount_status') === 'enabled' ? 'selected' : '' }}>مفعلة</option>
                            <option value="disabled" {{ request('discount_status') === 'disabled' ? 'selected' : '' }}>معطلة</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" onclick="showBulkActionsModal()">
                                <i class="fas fa-cogs"></i> إجراءات مجمعة
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المنتجات -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i>
                    المنتجات ({{ $products->total() }})
                </h5>
                <div>
                    <button type="button" class="btn btn-info btn-sm" onclick="loadStatistics()">
                        <i class="fas fa-chart-bar"></i>
                        تحديث الإحصائيات
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            @if($products->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>المنتج</th>
                                <th>السعر الأساسي</th>
                                <th>حالة الخصومات</th>
                                <th>خصومات أنواع المستخدمين</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($products as $product)
                            <tr>
                                <td>
                                    <input type="checkbox" name="product_ids[]" value="{{ $product->id }}" class="form-check-input product-checkbox">
                                </td>
                                
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($product->image)
                                            <img src="{{ RvMedia::getImageUrl($product->image, 'thumb') }}" 
                                                 alt="{{ $product->name }}" class="me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        @else
                                            <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-0">{{ $product->name }}</h6>
                                            <small class="text-muted">SKU: {{ $product->sku }}</small>
                                        </div>
                                    </div>
                                </td>
                                
                                <td>
                                    <div>
                                        <strong>{{ format_price($product->price) }}</strong>
                                        @if($product->sale_price && $product->sale_price < $product->price)
                                            <br><small class="text-success">عرض: {{ format_price($product->sale_price) }}</small>
                                        @endif
                                    </div>
                                </td>
                                
                                <td>
                                    @if($product->enable_user_type_discounts)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> مفعلة
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times"></i> معطلة
                                        </span>
                                    @endif
                                </td>
                                
                                <td>
                                    @if($product->enable_user_type_discounts)
                                        <div class="discount-summary">
                                            @foreach($userTypes as $userType)
                                                @php
                                                    $field = $userType->name . '_discount_percentage';
                                                    $discount = $product->$field ?? 0;
                                                @endphp
                                                @if($discount > 0)
                                                    <small class="badge bg-info me-1">
                                                        {{ $userType->display_name }}: {{ $discount }}%
                                                    </small>
                                                @endif
                                            @endforeach
                                        </div>
                                    @else
                                        <span class="text-muted">غير مفعلة</span>
                                    @endif
                                </td>
                                
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.perfect-pharma.product-discounts.edit', $product->id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="تعديل الخصومات">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="previewPrices({{ $product->id }})" title="معاينة الأسعار">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        @if($product->enable_user_type_discounts)
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="toggleDiscounts({{ $product->id }}, false)" title="إلغاء الخصومات">
                                                <i class="fas fa-toggle-off"></i>
                                            </button>
                                        @else
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="toggleDiscounts({{ $product->id }}, true)" title="تفعيل الخصومات">
                                                <i class="fas fa-toggle-on"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $products->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد منتجات</h5>
                    <p class="text-muted">لم يتم العثور على أي منتجات تطابق معايير البحث.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Modal للإجراءات المجمعة -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cogs"></i>
                    إجراءات مجمعة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bulkActionsForm">
                    <div class="mb-3">
                        <label class="form-label">الإجراء</label>
                        <select name="action" id="bulkAction" class="form-select" required>
                            <option value="">اختر الإجراء</option>
                            <option value="enable_discounts">تفعيل الخصومات المتدرجة</option>
                            <option value="disable_discounts">إلغاء الخصومات المتدرجة</option>
                            <option value="set_discounts">تحديد خصومات مخصصة</option>
                            <option value="apply_defaults">تطبيق الخصومات الافتراضية</option>
                        </select>
                    </div>

                    <!-- خصومات مخصصة -->
                    <div id="customDiscountsSection" class="d-none">
                        <h6 class="mb-3">نسب الخصم لكل نوع مستخدم:</h6>
                        <div class="row">
                            @foreach($userTypes as $userType)
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{{ $userType->display_name }}</label>
                                <div class="input-group">
                                    <input type="number" name="{{ $userType->name }}_discount_percentage" 
                                           class="form-control" min="0" max="100" step="0.01" 
                                           placeholder="0.00">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> سيتم تطبيق الإجراء على المنتجات المحددة فقط.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkAction()">
                    <i class="fas fa-check"></i>
                    تطبيق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal لمعاينة الأسعار -->
<div class="modal fade" id="pricePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye"></i>
                    معاينة الأسعار
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="pricePreviewContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<style>
.discount-summary .badge {
    font-size: 0.7rem;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.btn-group .btn {
    margin-right: 2px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}
</style>

<script>
// تحميل الإحصائيات
function loadStatistics() {
    fetch('{{ route("admin.perfect-pharma.product-discounts.statistics") }}')
        .then(response => response.json())
        .then(data => {
            updateStatisticsDisplay(data);
        })
        .catch(error => {
            console.error('Error loading statistics:', error);
        });
}

function updateStatisticsDisplay(data) {
    const statsRow = document.getElementById('statisticsRow');
    const generalStats = data.general_stats;
    
    statsRow.innerHTML = `
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">${generalStats.total_products}</h4>
                            <p class="mb-0">إجمالي المنتجات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">${generalStats.products_with_discounts}</h4>
                            <p class="mb-0">بخصومات مفعلة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">${generalStats.products_without_discounts}</h4>
                            <p class="mb-0">بدون خصومات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">${Math.round((generalStats.products_with_discounts / generalStats.total_products) * 100)}%</h4>
                            <p class="mb-0">نسبة التفعيل</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-pie fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// تحديد/إلغاء تحديد جميع المنتجات
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.product-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// عرض modal الإجراءات المجمعة
function showBulkActionsModal() {
    const selectedProducts = document.querySelectorAll('.product-checkbox:checked');
    
    if (selectedProducts.length === 0) {
        alert('يرجى تحديد منتج واحد على الأقل');
        return;
    }
    
    new bootstrap.Modal(document.getElementById('bulkActionsModal')).show();
}

// تغيير نوع الإجراء المجمع
document.getElementById('bulkAction').addEventListener('change', function() {
    const customSection = document.getElementById('customDiscountsSection');
    
    if (this.value === 'set_discounts') {
        customSection.classList.remove('d-none');
    } else {
        customSection.classList.add('d-none');
    }
});

// تنفيذ الإجراء المجمع
function executeBulkAction() {
    const selectedProducts = Array.from(document.querySelectorAll('.product-checkbox:checked'))
                                  .map(cb => cb.value);
    
    if (selectedProducts.length === 0) {
        alert('يرجى تحديد منتج واحد على الأقل');
        return;
    }
    
    const formData = new FormData(document.getElementById('bulkActionsForm'));
    formData.append('product_ids', JSON.stringify(selectedProducts));
    
    const action = formData.get('action');
    let url;
    
    if (action === 'apply_defaults') {
        url = '{{ route("admin.perfect-pharma.product-discounts.apply-defaults") }}';
    } else {
        url = '{{ route("admin.perfect-pharma.product-discounts.bulk-update") }}';
    }
    
    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(Object.fromEntries(formData))
    })
    .then(response => response.json())
    .then(data => {
        if (data.error === false) {
            bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// تفعيل/إلغاء الخصومات لمنتج واحد
function toggleDiscounts(productId, enable) {
    fetch('{{ route("admin.perfect-pharma.product-discounts.bulk-update") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            product_ids: [productId],
            action: enable ? 'enable_discounts' : 'disable_discounts'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error === false) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// معاينة الأسعار
function previewPrices(productId) {
    fetch(`{{ route("admin.perfect-pharma.product-discounts.preview", "") }}/${productId}`)
        .then(response => response.json())
        .then(data => {
            displayPricePreview(data);
            new bootstrap.Modal(document.getElementById('pricePreviewModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل معاينة الأسعار');
        });
}

function displayPricePreview(data) {
    const content = document.getElementById('pricePreviewContent');
    const product = data.product;
    const prices = data.price_preview;
    
    let html = `
        <div class="mb-4">
            <h6>${product.name}</h6>
            <p class="text-muted">السعر الأساسي: ${product.price} | حالة الخصومات: ${product.enable_discounts ? 'مفعلة' : 'معطلة'}</p>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>نوع المستخدم</th>
                        <th>السعر الأساسي</th>
                        <th>نسبة الخصم</th>
                        <th>السعر النهائي</th>
                        <th>الوفورات</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    prices.forEach(price => {
        html += `
            <tr>
                <td><strong>${price.user_type}</strong></td>
                <td>${price.sale_price || price.original_price}</td>
                <td>${price.discount_percentage}%</td>
                <td><strong class="text-success">${price.final_price}</strong></td>
                <td class="text-info">${price.savings}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    content.innerHTML = html;
}

// تحميل الإحصائيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
});
</script>
@endsection

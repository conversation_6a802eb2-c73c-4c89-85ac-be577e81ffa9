@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">
                        <i class="fas fa-percentage"></i>
                        تعديل خصومات المنتج
                    </h4>
                    <p class="text-muted">{{ $product->name }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.perfect-pharma.product-discounts.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات المنتج -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        معلومات المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($product->image)
                            <img src="{{ RvMedia::getImageUrl($product->image, 'medium') }}" 
                                 alt="{{ $product->name }}" class="img-fluid rounded" style="max-height: 200px;">
                        @else
                            <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                    </div>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td>{{ $product->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>SKU:</strong></td>
                            <td>{{ $product->sku }}</td>
                        </tr>
                        <tr>
                            <td><strong>السعر الأساسي:</strong></td>
                            <td>{{ format_price($product->price) }}</td>
                        </tr>
                        @if($product->sale_price && $product->sale_price < $product->price)
                        <tr>
                            <td><strong>سعر العرض:</strong></td>
                            <td class="text-success">{{ format_price($product->sale_price) }}</td>
                        </tr>
                        @endif
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                @if($product->status == 'published')
                                    <span class="badge bg-success">منشور</span>
                                @else
                                    <span class="badge bg-secondary">مسودة</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- معاينة الأسعار -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye"></i>
                        معاينة الأسعار
                    </h5>
                </div>
                <div class="card-body" id="pricePreview">
                    <!-- سيتم تحديث المحتوى بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- نموذج تعديل الخصومات -->
        <div class="col-lg-8">
            <form action="{{ route('admin.perfect-pharma.product-discounts.update', $product->id) }}" 
                  method="POST" id="discountForm">
                @csrf
                @method('PUT')

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs"></i>
                            إعدادات الخصومات
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- تفعيل/إلغاء الخصومات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" 
                                           name="enable_user_type_discounts" id="enableDiscounts"
                                           {{ $product->enable_user_type_discounts ? 'checked' : '' }}>
                                    <label class="form-check-label" for="enableDiscounts">
                                        <strong>تفعيل الخصومات المتدرجة لهذا المنتج</strong>
                                    </label>
                                </div>
                                <small class="text-muted">
                                    عند التفعيل، سيحصل كل نوع مستخدم على خصم حسب النسبة المحددة أدناه
                                </small>
                            </div>
                        </div>

                        <!-- نسب الخصم -->
                        <div id="discountRatesSection" class="{{ !$product->enable_user_type_discounts ? 'd-none' : '' }}">
                            <h6 class="mb-3">نسب الخصم لكل نوع مستخدم:</h6>
                            
                            <div class="row">
                                @foreach($userTypes as $userType)
                                @php
                                    $field = $userType->name . '_discount_percentage';
                                    $currentValue = $currentDiscounts[$userType->name] ?? 0;
                                @endphp
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-user-tag"></i>
                                        {{ $userType->display_name }}
                                        <small class="text-muted">(افتراضي: {{ $userType->default_discount_percentage }}%)</small>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" name="{{ $field }}" 
                                               class="form-control discount-input" 
                                               min="0" max="100" step="0.01" 
                                               value="{{ $currentValue }}"
                                               placeholder="0.00"
                                               data-user-type="{{ $userType->name }}">
                                        <span class="input-group-text">%</span>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                onclick="setDefaultDiscount('{{ $userType->name }}', {{ $userType->default_discount_percentage }})"
                                                title="تطبيق القيمة الافتراضية">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <!-- أزرار سريعة -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                onclick="applyDefaultDiscounts()">
                                            <i class="fas fa-magic"></i>
                                            تطبيق القيم الافتراضية
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" 
                                                onclick="clearAllDiscounts()">
                                            <i class="fas fa-eraser"></i>
                                            مسح جميع الخصومات
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm" 
                                                onclick="copyFromSimilarProduct()">
                                            <i class="fas fa-copy"></i>
                                            نسخ من منتج مشابه
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات التسعير -->
                        <div class="mb-3">
                            <label class="form-label">ملاحظات التسعير</label>
                            <textarea name="pricing_notes" class="form-control" rows="3" 
                                      placeholder="ملاحظات اختيارية حول تسعير هذا المنتج...">{{ $product->pricing_notes }}</textarea>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ التغييرات
                                </button>
                                <button type="button" class="btn btn-info" onclick="previewChanges()">
                                    <i class="fas fa-eye"></i>
                                    معاينة التغييرات
                                </button>
                            </div>
                            <div>
                                <a href="{{ route('admin.perfect-pharma.product-discounts.index') }}" 
                                   class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.discount-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.btn-group .btn {
    margin-right: 0;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.table td {
    padding: 0.5rem;
    border-top: 1px solid #dee2e6;
}

.input-group .btn {
    border-left: none;
}
</style>

<script>
// تفعيل/إلغاء قسم الخصومات
document.getElementById('enableDiscounts').addEventListener('change', function() {
    const section = document.getElementById('discountRatesSection');
    if (this.checked) {
        section.classList.remove('d-none');
    } else {
        section.classList.add('d-none');
    }
    updatePricePreview();
});

// تحديث معاينة الأسعار عند تغيير القيم
document.querySelectorAll('.discount-input').forEach(input => {
    input.addEventListener('input', updatePricePreview);
});

// تطبيق خصم افتراضي لنوع مستخدم معين
function setDefaultDiscount(userType, defaultValue) {
    const input = document.querySelector(`input[data-user-type="${userType}"]`);
    if (input) {
        input.value = defaultValue;
        updatePricePreview();
    }
}

// تطبيق جميع القيم الافتراضية
function applyDefaultDiscounts() {
    const userTypeDefaults = @json($userTypes->pluck('default_discount_percentage', 'name'));
    
    Object.entries(userTypeDefaults).forEach(([userType, defaultValue]) => {
        setDefaultDiscount(userType, defaultValue);
    });
}

// مسح جميع الخصومات
function clearAllDiscounts() {
    document.querySelectorAll('.discount-input').forEach(input => {
        input.value = 0;
    });
    updatePricePreview();
}

// نسخ من منتج مشابه
function copyFromSimilarProduct() {
    // يمكن تطوير هذه الوظيفة لاحقاً
    alert('هذه الوظيفة قيد التطوير');
}

// معاينة التغييرات
function previewChanges() {
    updatePricePreview();
    
    // التمرير إلى قسم المعاينة
    document.getElementById('pricePreview').scrollIntoView({ 
        behavior: 'smooth' 
    });
}

// تحديث معاينة الأسعار
function updatePricePreview() {
    const enableDiscounts = document.getElementById('enableDiscounts').checked;
    const basePrice = {{ $product->sale_price ?: $product->price }};
    const originalPrice = {{ $product->price }};
    
    let html = '<div class="price-preview">';
    html += '<h6 class="mb-3">معاينة الأسعار:</h6>';
    
    if (!enableDiscounts) {
        html += '<div class="alert alert-info">';
        html += '<i class="fas fa-info-circle"></i> ';
        html += 'الخصومات المتدرجة معطلة - جميع المستخدمين يرون نفس السعر';
        html += '</div>';
        html += '<div class="text-center">';
        html += '<h4 class="text-primary">' + formatPrice(basePrice) + '</h4>';
        html += '</div>';
    } else {
        html += '<div class="table-responsive">';
        html += '<table class="table table-sm table-striped">';
        html += '<thead><tr><th>نوع المستخدم</th><th>نسبة الخصم</th><th>السعر النهائي</th><th>الوفورات</th></tr></thead>';
        html += '<tbody>';
        
        // المرضى (بدون خصم)
        html += '<tr>';
        html += '<td><strong>مريض</strong></td>';
        html += '<td>-</td>';
        html += '<td>' + formatPrice(basePrice) + '</td>';
        html += '<td>-</td>';
        html += '</tr>';
        
        // الأنواع الأخرى
        @foreach($userTypes as $userType)
        @if($userType->name !== 'patient')
        const {{ $userType->name }}Discount = parseFloat(document.querySelector('input[data-user-type="{{ $userType->name }}"]').value) || 0;
        const {{ $userType->name }}FinalPrice = basePrice - (basePrice * {{ $userType->name }}Discount / 100);
        const {{ $userType->name }}Savings = basePrice - {{ $userType->name }}FinalPrice;
        
        html += '<tr>';
        html += '<td><strong>{{ $userType->display_name }}</strong></td>';
        html += '<td>';
        if ({{ $userType->name }}Discount > 0) {
            html += '<span class="badge bg-success">' + {{ $userType->name }}Discount + '%</span>';
        } else {
            html += '-';
        }
        html += '</td>';
        html += '<td>';
        if ({{ $userType->name }}Discount > 0) {
            html += '<strong class="text-success">' + formatPrice({{ $userType->name }}FinalPrice) + '</strong>';
        } else {
            html += formatPrice(basePrice);
        }
        html += '</td>';
        html += '<td>';
        if ({{ $userType->name }}Savings > 0) {
            html += '<span class="text-success">' + formatPrice({{ $userType->name }}Savings) + '</span>';
        } else {
            html += '-';
        }
        html += '</td>';
        html += '</tr>';
        @endif
        @endforeach
        
        html += '</tbody></table>';
        html += '</div>';
    }
    
    html += '</div>';
    
    document.getElementById('pricePreview').innerHTML = html;
}

// تنسيق السعر
function formatPrice(price) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(price);
}

// تحديث المعاينة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updatePricePreview();
});

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('discountForm').addEventListener('submit', function(e) {
    const discountInputs = document.querySelectorAll('.discount-input');
    let hasError = false;
    
    discountInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        if (value < 0 || value > 100) {
            hasError = true;
            input.classList.add('is-invalid');
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    if (hasError) {
        e.preventDefault();
        alert('يرجى التأكد من أن جميع نسب الخصم بين 0 و 100');
    }
});
</script>
@endsection

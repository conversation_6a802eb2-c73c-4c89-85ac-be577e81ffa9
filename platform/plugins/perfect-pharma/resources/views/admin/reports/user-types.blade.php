@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">تقرير أنواع المستخدمين</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard.index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.perfect-pharma.reports.index') }}">التقارير</a></li>
                        <li class="breadcrumb-item active">أنواع المستخدمين</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        @if(isset($userTypeStats) && $userTypeStats->count() > 0)
            @foreach($userTypeStats as $stat)
            <div class="col-xl-3 col-md-6">
                <div class="card card-animate">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1 overflow-hidden">
                                <p class="text-uppercase fw-medium text-muted text-truncate mb-0">
                                    {{ $stat->display_name ?? $stat->name }}
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                <h5 class="text-success fs-14 mb-0">
                                    <i class="ri-arrow-right-up-line fs-13 align-middle"></i>
                                    {{ number_format((($stat->verified_users / max($stat->total_users, 1)) * 100), 1) }}%
                                </h5>
                            </div>
                        </div>
                        <div class="d-flex align-items-end justify-content-between mt-4">
                            <div>
                                <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                    <span class="counter-value" data-target="{{ $stat->total_users }}">{{ $stat->total_users }}</span>
                                </h4>
                                <span class="badge bg-success-subtle text-success mb-0">
                                    <i class="ri-user-line align-middle"></i> إجمالي المستخدمين
                                </span>
                            </div>
                            <div class="avatar-sm flex-shrink-0">
                                <span class="avatar-title bg-success-subtle rounded fs-3">
                                    <i class="bx bx-user-check text-success"></i>
                                </span>
                            </div>
                        </div>

                        <!-- تفاصيل إضافية -->
                        <div class="mt-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <p class="text-muted mb-1">محقق</p>
                                    <h6 class="text-success">{{ $stat->verified_users }}</h6>
                                </div>
                                <div class="col-4">
                                    <p class="text-muted mb-1">معلق</p>
                                    <h6 class="text-warning">{{ $stat->pending_users }}</h6>
                                </div>
                                <div class="col-4">
                                    <p class="text-muted mb-1">جديد</p>
                                    <h6 class="text-info">{{ $stat->recent_users }}</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        @else
            <div class="col-12">
                <div class="alert alert-warning">
                    <i class="ri-alert-line me-2"></i>
                    لا توجد بيانات أنواع مستخدمين لعرضها
                </div>
            </div>
        @endif
    </div>

    <!-- جدول تفصيلي -->
    @if(isset($userTypeStats) && $userTypeStats->count() > 0)
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">تفاصيل أنواع المستخدمين</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>نوع المستخدم</th>
                                    <th>إجمالي المستخدمين</th>
                                    <th>محقق</th>
                                    <th>معلق</th>
                                    <th>نسبة التحقق</th>
                                    <th>خصم افتراضي</th>
                                    <th>جديد ({{ $period ?? 30 }} يوم)</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($userTypeStats as $stat)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-xs me-3">
                                                <span class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                                    {{ substr($stat->display_name ?? $stat->name, 0, 1) }}
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $stat->display_name ?? $stat->name }}</h6>
                                                <small class="text-muted">{{ $stat->name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary-subtle text-primary fs-12">
                                            {{ number_format($stat->total_users) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success-subtle text-success fs-12">
                                            {{ number_format($stat->verified_users) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning-subtle text-warning fs-12">
                                            {{ number_format($stat->pending_users) }}
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $verificationRate = $stat->total_users > 0 ? ($stat->verified_users / $stat->total_users) * 100 : 0;
                                        @endphp
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                 style="width: {{ $verificationRate }}%"
                                                 aria-valuenow="{{ $verificationRate }}"
                                                 aria-valuemin="0" aria-valuemax="100">
                                            </div>
                                        </div>
                                        <small class="text-muted">{{ number_format($verificationRate, 1) }}%</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info-subtle text-info fs-12">
                                            {{ $stat->default_discount_percentage ?? 0 }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary-subtle text-secondary fs-12">
                                            {{ number_format($stat->recent_users) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($stat->is_active)
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-danger">غير نشط</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

</div>
@endsection

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-0">
                <i class="fas fa-chart-bar"></i>
                التقارير والإحصائيات
            </h4>
            <p class="text-muted">نظرة شاملة على أداء منصة Perfect Pharma</p>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['total_users']) }}</h4>
                            <p class="mb-0">إجمالي المستخدمين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['verified_users']) }}</h4>
                            <p class="mb-0">مستخدمين محققين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['pending_verification']) }}</h4>
                            <p class="mb-0">قيد التحقق</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['active_user_types']) }}</h4>
                            <p class="mb-0">أنواع المستخدمين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['today_registrations']) }}</h4>
                            <p class="mb-0">تسجيلات اليوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card bg-dark text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['this_week_verifications']) }}</h4>
                            <p class="mb-0">تحققات الأسبوع</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-week fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i>
                        نشاط التسجيل والتحقق (آخر 30 يوم)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-pie-chart"></i>
                        توزيع حالات التحقق
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="verificationPieChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- تقارير سريعة -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users-cog"></i>
                        تقرير أنواع المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">إحصائيات مفصلة عن أنواع المستخدمين ونموهم</p>
                    <a href="{{ route('admin.perfect-pharma.reports.user-types') }}" class="btn btn-primary">
                        <i class="fas fa-eye"></i>
                        عرض التقرير
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-check"></i>
                        تقرير التحقق
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">تحليل شامل لعمليات التحقق وأوقات المعالجة</p>
                    <a href="{{ route('admin.perfect-pharma.reports.verification') }}" class="btn btn-success">
                        <i class="fas fa-eye"></i>
                        عرض التقرير
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area"></i>
                        تقرير النشاط
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">تحليل نشاط المستخدمين وساعات الذروة</p>
                    <a href="{{ route('admin.perfect-pharma.reports.activity') }}" class="btn btn-info">
                        <i class="fas fa-eye"></i>
                        عرض التقرير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- النشاط الأخير -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i>
                        النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    @if($recentActivity->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>نوع الحساب</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentActivity as $activity)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ substr($activity->customer->name, 0, 1) }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ $activity->customer->name }}</h6>
                                                    <small class="text-muted">{{ $activity->customer->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ $activity->userType->display_name }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($activity->is_verified)
                                                <span class="badge bg-success">محقق</span>
                                            @else
                                                <span class="badge bg-warning">قيد المراجعة</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $activity->created_at->format('Y-m-d H:i') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.perfect-pharma.verification.show', $activity->id) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا يوجد نشاط حديث</h5>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني للنشاط
    const activityCtx = document.getElementById('activityChart').getContext('2d');
    const chartData = @json($chartData);
    
    new Chart(activityCtx, {
        type: 'line',
        data: {
            labels: chartData.map(item => item.date),
            datasets: [{
                label: 'التسجيلات',
                data: chartData.map(item => item.registrations),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }, {
                label: 'التحققات',
                data: chartData.map(item => item.verifications),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // رسم بياني دائري للتحقق
    const pieCtx = document.getElementById('verificationPieChart').getContext('2d');
    
    new Chart(pieCtx, {
        type: 'doughnut',
        data: {
            labels: ['محقق', 'قيد المراجعة'],
            datasets: [{
                data: [{{ $stats['verified_users'] }}, {{ $stats['pending_verification'] }}],
                backgroundColor: ['#28a745', '#ffc107'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
@endsection

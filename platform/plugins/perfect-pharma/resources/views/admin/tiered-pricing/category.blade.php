@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-8">
            <!-- نموذج إضافة تسعير فئة جديد -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">إضافة تسعير فئة جديد</h4>
                    <p class="card-description">قم بتحديد نسب خصم خاصة لفئات المنتجات حسب نوع المستخدم</p>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.tiered-pricing.category.store') }}">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category_id">فئة المنتجات <span class="text-danger">*</span></label>
                                    <select class="form-control" id="category_id" name="category_id" required>
                                        <option value="">اختر الفئة</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">
                                                {{ $category->name }} ({{ $category->products_count }} منتج)
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="user_type_id">نوع المستخدم <span class="text-danger">*</span></label>
                                    <select class="form-control" id="user_type_id" name="user_type_id" required>
                                        <option value="">اختر نوع المستخدم</option>
                                        @foreach($userTypes as $userType)
                                            <option value="{{ $userType->id }}" 
                                                    data-default-discount="{{ $userType->default_discount_percentage }}">
                                                {{ $userType->display_name }} 
                                                (خصم افتراضي: {{ $userType->default_discount_percentage }}%)
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="discount_percentage">نسبة الخصم (%) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="discount_percentage" 
                                           name="discount_percentage" min="0" max="100" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="min_quantity">الحد الأدنى للكمية</label>
                                    <input type="number" class="form-control" id="min_quantity" 
                                           name="min_quantity" value="1" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="start_date">تاريخ البداية</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">تاريخ النهاية</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="ti ti-plus"></i> إضافة تسعير الفئة
                                        </button>
                                        <button type="button" class="btn btn-info" id="preview_category_btn">
                                            <i class="ti ti-eye"></i> معاينة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- معاينة تسعير الفئة -->
            <div class="card mt-4" id="category_preview" style="display: none;">
                <div class="card-header">
                    <h5 class="card-title">معاينة تسعير الفئة</h5>
                </div>
                <div class="card-body">
                    <div id="category_preview_content">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- نصائح وإرشادات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-info-circle"></i> نصائح مهمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>كيف يعمل تسعير الفئات؟</h6>
                        <ul class="mb-0">
                            <li>يتم تطبيق الخصم على جميع منتجات الفئة المحددة</li>
                            <li>التسعير المحدد للمنتج له أولوية أعلى من تسعير الفئة</li>
                            <li>يمكن تحديد فترة زمنية للتسعير</li>
                            <li>الحد الأدنى للكمية يحدد متى يبدأ تطبيق الخصم</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6>تنبيهات:</h6>
                        <ul class="mb-0">
                            <li>تأكد من صحة نسبة الخصم قبل الحفظ</li>
                            <li>التسعير الجديد سيؤثر على جميع منتجات الفئة</li>
                            <li>يمكن تعديل أو حذف التسعير لاحقاً</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول التسعيرات الحالية -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">تسعيرات الفئات الحالية</h4>
                </div>
                <div class="card-body">
                    @if($existingPricing->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الفئة</th>
                                        <th>نوع المستخدم</th>
                                        <th>نسبة الخصم</th>
                                        <th>الحد الأدنى للكمية</th>
                                        <th>فترة التطبيق</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($existingPricing as $pricing)
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ $pricing->category->name }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        {{ $pricing->category->products_count ?? 0 }} منتج
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-primary">
                                                    {{ $pricing->userType->display_name }}
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-success">{{ $pricing->discount_percentage }}%</strong>
                                            </td>
                                            <td>{{ $pricing->min_quantity }}</td>
                                            <td>
                                                @if($pricing->start_date || $pricing->end_date)
                                                    <small>
                                                        @if($pricing->start_date)
                                                            من: {{ $pricing->start_date }}<br>
                                                        @endif
                                                        @if($pricing->end_date)
                                                            إلى: {{ $pricing->end_date }}
                                                        @else
                                                            دائم
                                                        @endif
                                                    </small>
                                                @else
                                                    <span class="text-muted">دائم</span>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $isActive = $pricing->is_active;
                                                    $isExpired = $pricing->end_date && $pricing->end_date < now();
                                                    $isNotStarted = $pricing->start_date && $pricing->start_date > now();
                                                @endphp
                                                
                                                @if(!$isActive)
                                                    <span class="badge badge-secondary">معطل</span>
                                                @elseif($isExpired)
                                                    <span class="badge badge-danger">منتهي</span>
                                                @elseif($isNotStarted)
                                                    <span class="badge badge-warning">لم يبدأ</span>
                                                @else
                                                    <span class="badge badge-success">نشط</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-info edit-pricing-btn" 
                                                            title="تعديل"
                                                            data-id="{{ $pricing->id }}"
                                                            data-category="{{ $pricing->category_id }}"
                                                            data-user-type="{{ $pricing->user_type_id }}"
                                                            data-discount="{{ $pricing->discount_percentage }}"
                                                            data-min-quantity="{{ $pricing->min_quantity }}"
                                                            data-start-date="{{ $pricing->start_date }}"
                                                            data-end-date="{{ $pricing->end_date }}">
                                                        <i class="ti ti-edit"></i>
                                                    </button>
                                                    
                                                    @if($isActive)
                                                        <button class="btn btn-sm btn-warning toggle-status-btn" 
                                                                title="تعطيل"
                                                                data-id="{{ $pricing->id }}"
                                                                data-action="disable">
                                                            <i class="ti ti-toggle-left"></i>
                                                        </button>
                                                    @else
                                                        <button class="btn btn-sm btn-success toggle-status-btn" 
                                                                title="تفعيل"
                                                                data-id="{{ $pricing->id }}"
                                                                data-action="enable">
                                                            <i class="ti ti-toggle-right"></i>
                                                        </button>
                                                    @endif
                                                    
                                                    <button class="btn btn-sm btn-danger delete-pricing-btn" 
                                                            title="حذف"
                                                            data-id="{{ $pricing->id }}">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="ti ti-info-circle"></i> لا توجد تسعيرات فئات محددة حالياً
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // تحديث الخصم الافتراضي عند اختيار نوع المستخدم
    $('#user_type_id').change(function() {
        const defaultDiscount = $(this).find(':selected').data('default-discount');
        if (defaultDiscount && !$('#discount_percentage').val()) {
            $('#discount_percentage').val(defaultDiscount);
        }
    });

    // معاينة تسعير الفئة
    $('#preview_category_btn').click(function() {
        const categoryId = $('#category_id').val();
        const userTypeId = $('#user_type_id').val();
        const discountPercentage = $('#discount_percentage').val();
        
        if (!categoryId || !userTypeId || !discountPercentage) {
            alert('يرجى ملء جميع الحقول المطلوبة أولاً');
            return;
        }

        const categoryName = $('#category_id option:selected').text();
        const userTypeName = $('#user_type_id option:selected').text().split('(')[0].trim();
        
        const previewHtml = `
            <div class="alert alert-success">
                <h6>معاينة التسعير:</h6>
                <ul class="mb-0">
                    <li><strong>الفئة:</strong> ${categoryName}</li>
                    <li><strong>نوع المستخدم:</strong> ${userTypeName}</li>
                    <li><strong>نسبة الخصم:</strong> ${discountPercentage}%</li>
                    <li><strong>الحد الأدنى للكمية:</strong> ${$('#min_quantity').val()}</li>
                </ul>
            </div>
            <p class="text-info">
                <i class="ti ti-info-circle"></i> 
                سيتم تطبيق خصم ${discountPercentage}% على جميع منتجات فئة "${categoryName.split('(')[0].trim()}" 
                لعملاء نوع "${userTypeName}"
            </p>
        `;
        
        $('#category_preview_content').html(previewHtml);
        $('#category_preview').show();
    });

    // تعديل التسعير
    $('.edit-pricing-btn').click(function() {
        const data = $(this).data();
        
        $('#category_id').val(data.category);
        $('#user_type_id').val(data.userType);
        $('#discount_percentage').val(data.discount);
        $('#min_quantity').val(data.minQuantity);
        $('#start_date').val(data.startDate);
        $('#end_date').val(data.endDate);
        
        // تمرير للأعلى
        $('html, body').animate({
            scrollTop: 0
        }, 500);
        
        // تغيير نص الزر
        $('button[type="submit"]').html('<i class="ti ti-save"></i> تحديث التسعير');
    });

    // تفعيل/تعطيل التسعير
    $('.toggle-status-btn').click(function() {
        const id = $(this).data('id');
        const action = $(this).data('action');
        const btn = $(this);
        
        if (confirm(`هل أنت متأكد من ${action === 'enable' ? 'تفعيل' : 'تعطيل'} هذا التسعير؟`)) {
            // محاكاة العملية
            btn.prop('disabled', true);
            
            setTimeout(function() {
                if (action === 'enable') {
                    btn.removeClass('btn-success')
                       .addClass('btn-warning')
                       .attr('data-action', 'disable')
                       .attr('title', 'تعطيل')
                       .html('<i class="ti ti-toggle-left"></i>');
                    
                    btn.closest('tr').find('.badge-secondary')
                       .removeClass('badge-secondary')
                       .addClass('badge-success')
                       .text('نشط');
                } else {
                    btn.removeClass('btn-warning')
                       .addClass('btn-success')
                       .attr('data-action', 'enable')
                       .attr('title', 'تفعيل')
                       .html('<i class="ti ti-toggle-right"></i>');
                    
                    btn.closest('tr').find('.badge-success')
                       .removeClass('badge-success')
                       .addClass('badge-secondary')
                       .text('معطل');
                }
                
                btn.prop('disabled', false);
                
                // إظهار رسالة نجاح
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        تم ${action === 'enable' ? 'تفعيل' : 'تعطيل'} التسعير بنجاح
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                `;
                
                $('.card-body').first().prepend(alertHtml);
            }, 1000);
        }
    });

    // حذف التسعير
    $('.delete-pricing-btn').click(function() {
        const id = $(this).data('id');
        const row = $(this).closest('tr');
        
        if (confirm('هل أنت متأكد من حذف هذا التسعير؟ لا يمكن التراجع عن هذا الإجراء.')) {
            // محاكاة العملية
            row.fadeOut(500, function() {
                $(this).remove();
                
                // إظهار رسالة نجاح
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        تم حذف التسعير بنجاح
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                `;
                
                $('.card-body').first().prepend(alertHtml);
            });
        }
    });

    // التحقق من التواريخ
    $('#start_date, #end_date').change(function() {
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        
        if (startDate && endDate && startDate > endDate) {
            alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
            $(this).val('');
        }
    });
});
</script>
@endpush

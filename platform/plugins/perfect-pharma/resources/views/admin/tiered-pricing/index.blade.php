@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $statistics['total_tiered_products'] }}</h4>
                            <p class="mb-0">منتج بتسعير متدرج</p>
                        </div>
                        <div class="align-self-center">
                            <i class="ti ti-package fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $statistics['total_pricing_rules'] }}</h4>
                            <p class="mb-0">قاعدة تسعير نشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="ti ti-percentage fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $statistics['user_types_with_pricing'] }}</h4>
                            <p class="mb-0">نوع مستخدم بتسعير</p>
                        </div>
                        <div class="align-self-center">
                            <i class="ti ti-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ number_format($statistics['average_discount'], 1) }}%</h4>
                            <p class="mb-0">متوسط نسبة الخصم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="ti ti-discount fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- أنواع المستخدمين -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-users"></i> أنواع المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        @foreach($userTypes as $userType)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $userType->display_name }}</h6>
                                    <small class="text-muted">{{ $userType->description }}</small>
                                </div>
                                <div class="text-right">
                                    <span class="badge badge-primary">
                                        {{ $userType->default_discount_percentage }}% خصم
                                    </span>
                                    @if($userType->is_vendor)
                                        <br><small class="text-success">بائع</small>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- المنتجات مع التسعير المتدرج -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="ti ti-package"></i> المنتجات مع التسعير المتدرج
                    </h5>
                    <div>
                        <a href="{{ route('admin.tiered-pricing.bulk') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus"></i> تسعير مجمع
                        </a>
                        <a href="{{ route('admin.tiered-pricing.category') }}" class="btn btn-info btn-sm">
                            <i class="ti ti-category"></i> تسعير الفئات
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <form method="GET" class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" class="form-control" name="search" 
                                       value="{{ request('search') }}" placeholder="البحث في المنتجات...">
                            </div>
                            <div class="col-md-4">
                                <select class="form-control" name="category">
                                    <option value="">جميع الفئات</option>
                                    @foreach($products->pluck('categories')->flatten()->unique('id') as $category)
                                        <option value="{{ $category->id }}" 
                                                {{ request('category') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="ti ti-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- جدول المنتجات -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر الأساسي</th>
                                    <th>التسعير المتدرج</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($products as $product)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($product->image)
                                                    <img src="{{ RvMedia::getImageUrl($product->image, 'thumb') }}" 
                                                         alt="{{ $product->name }}" class="img-thumbnail mr-2" 
                                                         style="width: 40px; height: 40px;">
                                                @endif
                                                <div>
                                                    <h6 class="mb-0">{{ $product->name }}</h6>
                                                    <small class="text-muted">{{ $product->sku }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ number_format($product->price, 2) }} ج.م</strong>
                                        </td>
                                        <td>
                                            @if($product->has_tiered_pricing)
                                                <span class="badge badge-success">مفعل</span>
                                                <br>
                                                <small class="text-muted">
                                                    {{ $product->productTieredPricing->count() ?? 0 }} قاعدة تسعير
                                                </small>
                                            @else
                                                <span class="badge badge-secondary">غير مفعل</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($product->status == 'published')
                                                <span class="badge badge-success">منشور</span>
                                            @else
                                                <span class="badge badge-warning">{{ $product->status }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.tiered-pricing.product', $product->id) }}" 
                                                   class="btn btn-sm btn-primary" title="إدارة التسعير">
                                                    <i class="ti ti-settings"></i>
                                                </a>
                                                <button class="btn btn-sm btn-info preview-pricing-btn" 
                                                        title="معاينة الأسعار"
                                                        data-product-id="{{ $product->id }}"
                                                        data-product-name="{{ $product->name }}"
                                                        data-product-price="{{ $product->price }}">
                                                    <i class="ti ti-eye"></i>
                                                </button>
                                                @if($product->has_tiered_pricing)
                                                    <button class="btn btn-sm btn-warning toggle-pricing-btn" 
                                                            title="تعطيل التسعير المتدرج"
                                                            data-product-id="{{ $product->id }}"
                                                            data-action="disable">
                                                        <i class="ti ti-toggle-left"></i>
                                                    </button>
                                                @else
                                                    <button class="btn btn-sm btn-success toggle-pricing-btn" 
                                                            title="تفعيل التسعير المتدرج"
                                                            data-product-id="{{ $product->id }}"
                                                            data-action="enable">
                                                        <i class="ti ti-toggle-right"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <div class="alert alert-info mb-0">
                                                <i class="ti ti-info-circle"></i> لا توجد منتجات
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- الترقيم -->
                    @if($products->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $products->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal معاينة الأسعار -->
    <div class="modal fade" id="pricingPreviewModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معاينة أسعار المنتج</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="pricing_preview_content">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                    <a href="#" class="btn btn-primary" id="edit_pricing_btn">تعديل التسعير</a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // معاينة الأسعار
    $('.preview-pricing-btn').click(function() {
        const productId = $(this).data('product-id');
        const productName = $(this).data('product-name');
        const productPrice = $(this).data('product-price');
        
        $('#pricing_preview_content').html('<div class="text-center"><i class="ti ti-loader"></i> جاري التحميل...</div>');
        $('#edit_pricing_btn').attr('href', '{{ route("admin.tiered-pricing.product", ":id") }}'.replace(':id', productId));
        $('#pricingPreviewModal').modal('show');
        
        // محاكاة تحميل البيانات
        setTimeout(function() {
            const previewHtml = `
                <h6>${productName}</h6>
                <p><strong>السعر الأساسي:</strong> ${productPrice} ج.م</p>
                
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>نوع المستخدم</th>
                                <th>نسبة الخصم</th>
                                <th>السعر بعد الخصم</th>
                                <th>الوفورات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($userTypes as $userType)
                                <tr>
                                    <td>{{ $userType->display_name }}</td>
                                    <td>{{ $userType->default_discount_percentage }}%</td>
                                    <td>${(productPrice * (1 - {{ $userType->default_discount_percentage }} / 100)).toFixed(2)} ج.م</td>
                                    <td class="text-success">${(productPrice * {{ $userType->default_discount_percentage }} / 100).toFixed(2)} ج.م</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            `;
            
            $('#pricing_preview_content').html(previewHtml);
        }, 1000);
    });
    
    // تفعيل/تعطيل التسعير المتدرج
    $('.toggle-pricing-btn').click(function() {
        const productId = $(this).data('product-id');
        const action = $(this).data('action');
        const btn = $(this);
        
        if (confirm(`هل أنت متأكد من ${action === 'enable' ? 'تفعيل' : 'تعطيل'} التسعير المتدرج لهذا المنتج؟`)) {
            btn.prop('disabled', true);
            
            // محاكاة العملية
            setTimeout(function() {
                if (action === 'enable') {
                    btn.removeClass('btn-success')
                       .addClass('btn-warning')
                       .attr('data-action', 'disable')
                       .attr('title', 'تعطيل التسعير المتدرج')
                       .html('<i class="ti ti-toggle-left"></i>');
                    
                    btn.closest('tr').find('.badge-secondary')
                       .removeClass('badge-secondary')
                       .addClass('badge-success')
                       .text('مفعل');
                } else {
                    btn.removeClass('btn-warning')
                       .addClass('btn-success')
                       .attr('data-action', 'enable')
                       .attr('title', 'تفعيل التسعير المتدرج')
                       .html('<i class="ti ti-toggle-right"></i>');
                    
                    btn.closest('tr').find('.badge-success')
                       .removeClass('badge-success')
                       .addClass('badge-secondary')
                       .text('غير مفعل');
                }
                
                btn.prop('disabled', false);
                
                // إظهار رسالة نجاح
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        تم ${action === 'enable' ? 'تفعيل' : 'تعطيل'} التسعير المتدرج بنجاح
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                `;
                
                if ($('.alert').length === 0) {
                    $('.card-body').prepend(alertHtml);
                }
            }, 1000);
        }
    });
});
</script>
@endpush

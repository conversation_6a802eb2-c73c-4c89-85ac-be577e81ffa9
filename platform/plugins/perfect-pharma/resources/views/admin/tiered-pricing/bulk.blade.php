@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">التسعير المجمع للمنتجات</h4>
                    <p class="card-description">قم بتطبيق نسب خصم موحدة على مجموعة من المنتجات لنوع مستخدم محدد</p>
                </div>
                <div class="card-body">
                    <form id="bulkPricingForm" method="POST" action="{{ route('admin.tiered-pricing.bulk.store') }}">
                        @csrf
                        
                        <!-- اختيار نوع المستخدم -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="user_type_id">نوع المستخدم <span class="text-danger">*</span></label>
                                    <select class="form-control" id="user_type_id" name="user_type_id" required>
                                        <option value="">اختر نوع المستخدم</option>
                                        @foreach($userTypes as $userType)
                                            <option value="{{ $userType->id }}" 
                                                    data-default-discount="{{ $userType->default_discount_percentage }}">
                                                {{ $userType->display_name }} 
                                                (خصم افتراضي: {{ $userType->default_discount_percentage }}%)
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category_filter">فلترة حسب الفئة</label>
                                    <select class="form-control" id="category_filter">
                                        <option value="">جميع الفئات</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">
                                                {{ $category->name }} ({{ $category->products_count }} منتج)
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التسعير -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="discount_percentage">نسبة الخصم (%)</label>
                                    <input type="number" class="form-control" id="discount_percentage" 
                                           name="discount_percentage" min="0" max="100" step="0.01">
                                    <small class="form-text text-muted">اتركه فارغاً لاستخدام الخصم الافتراضي</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="min_quantity">الحد الأدنى للكمية</label>
                                    <input type="number" class="form-control" id="min_quantity" 
                                           name="min_quantity" value="1" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="max_quantity">الحد الأقصى للكمية</label>
                                    <input type="number" class="form-control" id="max_quantity" 
                                           name="max_quantity" min="1">
                                    <small class="form-text text-muted">اتركه فارغاً لعدم وجود حد أقصى</small>
                                </div>
                            </div>
                        </div>

                        <!-- فترة التطبيق -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">تاريخ البداية</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date">
                                    <small class="form-text text-muted">اتركه فارغاً للتطبيق الفوري</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">تاريخ النهاية</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                    <small class="form-text text-muted">اتركه فارغاً للتطبيق الدائم</small>
                                </div>
                            </div>
                        </div>

                        <!-- اختيار المنتجات -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">اختيار المنتجات</h5>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-sm btn-primary" id="select_all_btn">
                                        تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-secondary" id="deselect_all_btn">
                                        إلغاء تحديد الكل
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- البحث في المنتجات -->
                                <div class="form-group">
                                    <input type="text" class="form-control" id="product_search" 
                                           placeholder="البحث في المنتجات...">
                                </div>

                                <!-- قائمة المنتجات -->
                                <div class="row" id="products_list">
                                    @foreach($categories as $category)
                                        @if($category->products->count() > 0)
                                            <div class="col-md-12 category-section" data-category-id="{{ $category->id }}">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    {{ $category->name }} ({{ $category->products->count() }} منتج)
                                                </h6>
                                                <div class="row">
                                                    @foreach($category->products as $product)
                                                        <div class="col-md-6 col-lg-4 mb-3 product-item" 
                                                             data-product-name="{{ strtolower($product->name) }}"
                                                             data-product-sku="{{ strtolower($product->sku) }}">
                                                            <div class="card product-card">
                                                                <div class="card-body p-3">
                                                                    <div class="custom-control custom-checkbox">
                                                                        <input type="checkbox" class="custom-control-input product-checkbox" 
                                                                               id="product_{{ $product->id }}" 
                                                                               name="product_ids[]" 
                                                                               value="{{ $product->id }}">
                                                                        <label class="custom-control-label" for="product_{{ $product->id }}">
                                                                            <div class="d-flex align-items-center">
                                                                                @if($product->image)
                                                                                    <img src="{{ RvMedia::getImageUrl($product->image, 'thumb') }}" 
                                                                                         alt="{{ $product->name }}" 
                                                                                         class="img-thumbnail mr-2" 
                                                                                         style="width: 40px; height: 40px;">
                                                                                @endif
                                                                                <div>
                                                                                    <h6 class="mb-1">{{ $product->name }}</h6>
                                                                                    <small class="text-muted">{{ $product->sku }}</small>
                                                                                    <br>
                                                                                    <strong class="text-success">{{ number_format($product->price, 2) }} ج.م</strong>
                                                                                </div>
                                                                            </div>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>

                                <!-- عداد المنتجات المحددة -->
                                <div class="alert alert-info mt-3">
                                    <strong>المنتجات المحددة:</strong> <span id="selected_count">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- معاينة التسعير -->
                        <div class="card mt-4" id="pricing_preview" style="display: none;">
                            <div class="card-header">
                                <h5 class="card-title">معاينة التسعير</h5>
                            </div>
                            <div class="card-body">
                                <div id="preview_content">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-group mt-4">
                            <button type="button" class="btn btn-info" id="preview_btn">
                                <i class="ti ti-eye"></i> معاينة التسعير
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="ti ti-check"></i> تطبيق التسعير المجمع
                            </button>
                            <a href="{{ route('admin.tiered-pricing.index') }}" class="btn btn-secondary">
                                <i class="ti ti-arrow-left"></i> العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // تحديث الخصم الافتراضي عند اختيار نوع المستخدم
    $('#user_type_id').change(function() {
        const defaultDiscount = $(this).find(':selected').data('default-discount');
        if (defaultDiscount && !$('#discount_percentage').val()) {
            $('#discount_percentage').val(defaultDiscount);
        }
        updatePreview();
    });

    // فلترة المنتجات حسب الفئة
    $('#category_filter').change(function() {
        const categoryId = $(this).val();
        
        if (categoryId) {
            $('.category-section').hide();
            $(`.category-section[data-category-id="${categoryId}"]`).show();
        } else {
            $('.category-section').show();
        }
        
        updateSelectedCount();
    });

    // البحث في المنتجات
    $('#product_search').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        
        $('.product-item').each(function() {
            const productName = $(this).data('product-name');
            const productSku = $(this).data('product-sku');
            
            if (productName.includes(searchTerm) || productSku.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
        
        updateSelectedCount();
    });

    // تحديد/إلغاء تحديد جميع المنتجات
    $('#select_all_btn').click(function() {
        $('.product-checkbox:visible').prop('checked', true);
        updateSelectedCount();
        updatePreview();
    });

    $('#deselect_all_btn').click(function() {
        $('.product-checkbox').prop('checked', false);
        updateSelectedCount();
        updatePreview();
    });

    // تحديث العداد عند تغيير التحديد
    $(document).on('change', '.product-checkbox', function() {
        updateSelectedCount();
        updatePreview();
    });

    // معاينة التسعير
    $('#preview_btn').click(function() {
        updatePreview();
        $('#pricing_preview').show();
    });

    // تحديث إعدادات التسعير
    $('#discount_percentage, #min_quantity, #max_quantity').on('input', updatePreview);

    function updateSelectedCount() {
        const count = $('.product-checkbox:checked').length;
        $('#selected_count').text(count);
    }

    function updatePreview() {
        const selectedProducts = $('.product-checkbox:checked');
        const userTypeId = $('#user_type_id').val();
        const discountPercentage = $('#discount_percentage').val() || 0;
        
        if (selectedProducts.length === 0 || !userTypeId) {
            $('#pricing_preview').hide();
            return;
        }

        let previewHtml = `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>السعر الأصلي</th>
                            <th>نسبة الخصم</th>
                            <th>السعر الجديد</th>
                            <th>الوفورات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        selectedProducts.each(function() {
            const productCard = $(this).closest('.product-item');
            const productName = productCard.find('h6').text();
            const priceText = productCard.find('.text-success').text();
            const originalPrice = parseFloat(priceText.replace(/[^\d.]/g, ''));
            
            const discountAmount = originalPrice * (discountPercentage / 100);
            const newPrice = originalPrice - discountAmount;
            
            previewHtml += `
                <tr>
                    <td>${productName}</td>
                    <td>${originalPrice.toFixed(2)} ج.م</td>
                    <td>${discountPercentage}%</td>
                    <td><strong class="text-success">${newPrice.toFixed(2)} ج.م</strong></td>
                    <td><span class="text-primary">${discountAmount.toFixed(2)} ج.م</span></td>
                </tr>
            `;
        });

        previewHtml += `
                    </tbody>
                </table>
            </div>
            <div class="alert alert-success mt-3">
                <strong>ملخص:</strong> سيتم تطبيق خصم ${discountPercentage}% على ${selectedProducts.length} منتج
            </div>
        `;

        $('#preview_content').html(previewHtml);
    }

    // التحقق من صحة النموذج قبل الإرسال
    $('#bulkPricingForm').submit(function(e) {
        const selectedProducts = $('.product-checkbox:checked').length;
        const userTypeId = $('#user_type_id').val();
        
        if (!userTypeId) {
            e.preventDefault();
            alert('يرجى اختيار نوع المستخدم');
            return false;
        }
        
        if (selectedProducts === 0) {
            e.preventDefault();
            alert('يرجى اختيار منتج واحد على الأقل');
            return false;
        }
        
        if (!confirm(`هل أنت متأكد من تطبيق التسعير على ${selectedProducts} منتج؟`)) {
            e.preventDefault();
            return false;
        }
    });

    // تحديث العداد في البداية
    updateSelectedCount();
});
</script>

<style>
.product-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.product-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.product-checkbox:checked + label .product-card {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.category-section {
    margin-bottom: 2rem;
}

.custom-control-label {
    width: 100%;
}

#selected_count {
    font-weight: bold;
    color: #007bff;
}
</style>
@endpush

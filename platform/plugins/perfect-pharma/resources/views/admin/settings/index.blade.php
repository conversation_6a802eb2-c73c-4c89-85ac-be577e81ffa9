@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-settings me-2"></i>
                        إعدادات Perfect Pharma
                    </h4>
                </div>
                
                <div class="card-body">
                    <form action="{{ route('admin.pharma.settings.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <!-- الإعدادات العامة -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">الإعدادات العامة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="pharma_system_name" class="form-label">اسم النظام</label>
                                            <input type="text" class="form-control" id="pharma_system_name" name="pharma_system_name" value="{{ setting('pharma_system_name', 'Perfect Pharma') }}">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="pharma_system_description" class="form-label">وصف النظام</label>
                                            <textarea class="form-control" id="pharma_system_description" name="pharma_system_description" rows="3">{{ setting('pharma_system_description', 'نظام إدارة الصيدلة والرعاية الصحية المتكامل') }}</textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="pharma_contact_email" class="form-label">البريد الإلكتروني للتواصل</label>
                                            <input type="email" class="form-control" id="pharma_contact_email" name="pharma_contact_email" value="{{ setting('pharma_contact_email', '<EMAIL>') }}">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="pharma_contact_phone" class="form-label">رقم الهاتف للتواصل</label>
                                            <input type="tel" class="form-control" id="pharma_contact_phone" name="pharma_contact_phone" value="{{ setting('pharma_contact_phone', '+20123456789') }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إعدادات المحفظة -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات المحفظة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="wallet_min_charge_amount" class="form-label">الحد الأدنى للشحن</label>
                                            <input type="number" class="form-control" id="wallet_min_charge_amount" name="wallet_min_charge_amount" value="{{ setting('wallet_min_charge_amount', 10) }}" min="1">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="wallet_max_charge_amount" class="form-label">الحد الأقصى للشحن</label>
                                            <input type="number" class="form-control" id="wallet_max_charge_amount" name="wallet_max_charge_amount" value="{{ setting('wallet_max_charge_amount', 10000) }}" min="1">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="wallet_transfer_fee_percentage" class="form-label">نسبة رسوم التحويل (%)</label>
                                            <input type="number" class="form-control" id="wallet_transfer_fee_percentage" name="wallet_transfer_fee_percentage" value="{{ setting('wallet_transfer_fee_percentage', 1) }}" min="0" max="100" step="0.1">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="wallet_currency" class="form-label">العملة</label>
                                            <select class="form-select" id="wallet_currency" name="wallet_currency">
                                                <option value="EGP" {{ setting('wallet_currency', 'EGP') === 'EGP' ? 'selected' : '' }}>جنيه مصري (EGP)</option>
                                                <option value="USD" {{ setting('wallet_currency') === 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                                                <option value="EUR" {{ setting('wallet_currency') === 'EUR' ? 'selected' : '' }}>يورو (EUR)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- إعدادات الوصفات -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات الوصفات</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="prescription_default_expiry_days" class="form-label">مدة صلاحية الوصفة (أيام)</label>
                                            <input type="number" class="form-control" id="prescription_default_expiry_days" name="prescription_default_expiry_days" value="{{ setting('prescription_default_expiry_days', 30) }}" min="1">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="prescription_max_refills" class="form-label">الحد الأقصى لتجديد الوصفة</label>
                                            <input type="number" class="form-control" id="prescription_max_refills" name="prescription_max_refills" value="{{ setting('prescription_max_refills', 3) }}" min="1">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="prescription_require_doctor_verification" name="prescription_require_doctor_verification" value="1" {{ setting('prescription_require_doctor_verification', true) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="prescription_require_doctor_verification">
                                                    يتطلب تحقق من الطبيب
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إعدادات الأمان -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات الأمان</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="security_two_factor_required" name="security_two_factor_required" value="1" {{ setting('security_two_factor_required', false) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="security_two_factor_required">
                                                    المصادقة الثنائية مطلوبة
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="security_session_timeout_minutes" class="form-label">انتهاء الجلسة (دقائق)</label>
                                            <input type="number" class="form-control" id="security_session_timeout_minutes" name="security_session_timeout_minutes" value="{{ setting('security_session_timeout_minutes', 120) }}" min="5">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="security_max_login_attempts" class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                                            <input type="number" class="form-control" id="security_max_login_attempts" name="security_max_login_attempts" value="{{ setting('security_max_login_attempts', 5) }}" min="1">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

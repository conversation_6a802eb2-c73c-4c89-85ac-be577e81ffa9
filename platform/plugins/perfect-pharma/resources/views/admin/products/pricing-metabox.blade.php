<div class="perfect-pharma-pricing-metabox">
    <!-- تفعيل/إلغاء التسعير المتدرج -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" 
                       name="enable_user_type_discounts" id="enableUserTypeDiscounts"
                       value="1" {{ old('enable_user_type_discounts', $product->enable_user_type_discounts ?? false) ? 'checked' : '' }}>
                <label class="form-check-label" for="enableUserTypeDiscounts">
                    <strong>تفعيل التسعير المتدرج لهذا المنتج</strong>
                </label>
            </div>
            <small class="text-muted">
                عند التفعيل، سيحصل كل نوع مستخدم على خصم حسب النسبة المحددة أدناه
            </small>
        </div>
    </div>

    <!-- نسب الخصم -->
    <div id="discountRatesSection" class="{{ !old('enable_user_type_discounts', $product->enable_user_type_discounts ?? false) ? 'd-none' : '' }}">
        <h6 class="mb-3">نسب الخصم لكل نوع مستخدم:</h6>
        
        <div class="row">
            @foreach($userTypes as $userType)
            @php
                $field = $userType->name . '_discount_percentage';
                $currentValue = old($field, $product->$field ?? 0);
            @endphp
            <div class="col-md-6 mb-3">
                <label class="form-label">
                    <i class="ti ti-user-check"></i>
                    {{ $userType->display_name }}
                    <small class="text-muted">(افتراضي: {{ $userType->default_discount_percentage }}%)</small>
                </label>
                <div class="input-group">
                    <input type="number" name="{{ $field }}" 
                           class="form-control discount-input" 
                           min="0" max="100" step="0.01" 
                           value="{{ $currentValue }}"
                           placeholder="0.00"
                           data-user-type="{{ $userType->name }}"
                           data-default="{{ $userType->default_discount_percentage }}">
                    <span class="input-group-text">%</span>
                    <button type="button" class="btn btn-outline-secondary btn-sm" 
                            onclick="setDefaultDiscount('{{ $userType->name }}', {{ $userType->default_discount_percentage }})"
                            title="تطبيق القيمة الافتراضية">
                        <i class="ti ti-refresh"></i>
                    </button>
                </div>
            </div>
            @endforeach
        </div>

        <!-- أزرار سريعة -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" 
                            onclick="applyDefaultDiscounts()">
                        <i class="ti ti-wand"></i>
                        تطبيق القيم الافتراضية
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" 
                            onclick="clearAllDiscounts()">
                        <i class="ti ti-eraser"></i>
                        مسح جميع الخصومات
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" 
                            onclick="previewPricing()">
                        <i class="ti ti-eye"></i>
                        معاينة الأسعار
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- ملاحظات التسعير -->
    <div class="mb-3">
        <label class="form-label">ملاحظات التسعير</label>
        <textarea name="pricing_notes" class="form-control" rows="3" 
                  placeholder="ملاحظات اختيارية حول تسعير هذا المنتج...">{{ old('pricing_notes', $product->pricing_notes ?? '') }}</textarea>
    </div>

    <!-- معاينة الأسعار -->
    <div id="pricingPreview" class="mt-4" style="display: none;">
        <h6>معاينة الأسعار:</h6>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>نوع المستخدم</th>
                        <th>نسبة الخصم</th>
                        <th>السعر النهائي</th>
                        <th>الوفورات</th>
                    </tr>
                </thead>
                <tbody id="pricingPreviewBody">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
.perfect-pharma-pricing-metabox .discount-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.perfect-pharma-pricing-metabox .form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.perfect-pharma-pricing-metabox .btn-group .btn {
    margin-right: 0;
}

.perfect-pharma-pricing-metabox .input-group .btn {
    border-left: none;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل/إلغاء قسم الخصومات
    const enableCheckbox = document.getElementById('enableUserTypeDiscounts');
    const discountSection = document.getElementById('discountRatesSection');
    
    if (enableCheckbox) {
        enableCheckbox.addEventListener('change', function() {
            if (this.checked) {
                discountSection.classList.remove('d-none');
            } else {
                discountSection.classList.add('d-none');
            }
        });
    }

    // تحديث معاينة الأسعار عند تغيير القيم
    document.querySelectorAll('.discount-input').forEach(input => {
        input.addEventListener('input', updatePricingPreview);
    });
});

// تطبيق خصم افتراضي لنوع مستخدم معين
function setDefaultDiscount(userType, defaultValue) {
    const input = document.querySelector(`input[data-user-type="${userType}"]`);
    if (input) {
        input.value = defaultValue;
        updatePricingPreview();
    }
}

// تطبيق جميع القيم الافتراضية
function applyDefaultDiscounts() {
    document.querySelectorAll('.discount-input').forEach(input => {
        const defaultValue = input.getAttribute('data-default');
        if (defaultValue) {
            input.value = defaultValue;
        }
    });
    updatePricingPreview();
}

// مسح جميع الخصومات
function clearAllDiscounts() {
    document.querySelectorAll('.discount-input').forEach(input => {
        input.value = 0;
    });
    updatePricingPreview();
}

// معاينة التسعير
function previewPricing() {
    updatePricingPreview();
    const preview = document.getElementById('pricingPreview');
    preview.style.display = 'block';
    preview.scrollIntoView({ behavior: 'smooth' });
}

// تحديث معاينة الأسعار
function updatePricingPreview() {
    const enableDiscounts = document.getElementById('enableUserTypeDiscounts').checked;
    const priceInput = document.querySelector('input[name="price"]');
    const salePriceInput = document.querySelector('input[name="sale_price"]');
    
    if (!priceInput) return;
    
    const basePrice = parseFloat(salePriceInput?.value || priceInput.value || 0);
    const previewBody = document.getElementById('pricingPreviewBody');
    
    if (!enableDiscounts || basePrice <= 0) {
        document.getElementById('pricingPreview').style.display = 'none';
        return;
    }
    
    let html = '';
    
    // المرضى (بدون خصم)
    html += `
        <tr>
            <td><strong>مريض</strong></td>
            <td>-</td>
            <td>${formatPrice(basePrice)}</td>
            <td>-</td>
        </tr>
    `;
    
    // الأنواع الأخرى
    document.querySelectorAll('.discount-input').forEach(input => {
        const userType = input.getAttribute('data-user-type');
        const userTypeDisplay = input.closest('.col-md-6').querySelector('label').textContent.split('(')[0].trim();
        const discount = parseFloat(input.value) || 0;
        
        if (discount > 0) {
            const discountAmount = (basePrice * discount) / 100;
            const finalPrice = basePrice - discountAmount;
            
            html += `
                <tr>
                    <td><strong>${userTypeDisplay}</strong></td>
                    <td><span class="badge bg-success">${discount}%</span></td>
                    <td><strong class="text-success">${formatPrice(finalPrice)}</strong></td>
                    <td><span class="text-success">${formatPrice(discountAmount)}</span></td>
                </tr>
            `;
        } else {
            html += `
                <tr>
                    <td><strong>${userTypeDisplay}</strong></td>
                    <td>-</td>
                    <td>${formatPrice(basePrice)}</td>
                    <td>-</td>
                </tr>
            `;
        }
    });
    
    previewBody.innerHTML = html;
}

// تنسيق السعر
function formatPrice(price) {
    return window.CurrencyHelper ? window.CurrencyHelper.formatPrice(price) : price.toFixed(2);
}
</script>

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['total'] }}</h4>
                            <p class="mb-0">إجمالي الطلبات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['pending'] }}</h4>
                            <p class="mb-0">قيد المراجعة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['verified'] }}</h4>
                            <p class="mb-0">محقق</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['today'] }}</h4>
                            <p class="mb-0">طلبات اليوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter"></i>
                فلاتر البحث
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.perfect-pharma.verification.index') }}" id="filterForm">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                            <option value="verified" {{ request('status') === 'verified' ? 'selected' : '' }}>محقق</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">نوع المستخدم</label>
                        <select name="user_type" class="form-select">
                            <option value="">جميع الأنواع</option>
                            @foreach($userTypes as $userType)
                                <option value="{{ $userType->name }}" {{ request('user_type') === $userType->name ? 'selected' : '' }}>
                                    {{ $userType->display_name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="البحث بالاسم أو البريد الإلكتروني..." 
                               value="{{ request('search') }}">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i>
                    طلبات التحقق ({{ $verificationRequests->total() }})
                </h5>
                <div>
                    <a href="{{ route('admin.perfect-pharma.verification.export') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}" 
                       class="btn btn-success">
                        <i class="fas fa-download"></i>
                        تصدير التقرير
                    </a>
                    <button type="button" class="btn btn-secondary" onclick="location.reload()">
                        <i class="fas fa-sync"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول طلبات التحقق -->
    <div class="card">
        <div class="card-body">
            @if($verificationRequests->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>المستخدم</th>
                                <th>نوع الحساب</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>تاريخ التحقق</th>
                                <th>المستندات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($verificationRequests as $request)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-3">
                                            <div class="avatar-title bg-primary rounded-circle">
                                                {{ substr($request->customer->name, 0, 1) }}
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $request->customer->name }}</h6>
                                            <small class="text-muted">{{ $request->customer->email }}</small>
                                        </div>
                                    </div>
                                </td>
                                
                                <td>
                                    <span class="badge bg-info">
                                        {{ $request->userType->display_name }}
                                    </span>
                                </td>
                                
                                <td>
                                    @if($request->is_verified)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> محقق
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock"></i> قيد المراجعة
                                        </span>
                                    @endif
                                </td>
                                
                                <td>
                                    <small>{{ $request->created_at->format('Y-m-d') }}</small><br>
                                    <small class="text-muted">{{ $request->created_at->format('H:i') }}</small>
                                </td>
                                
                                <td>
                                    @if($request->verified_at)
                                        <small>{{ $request->verified_at->format('Y-m-d') }}</small><br>
                                        <small class="text-muted">{{ $request->verified_at->format('H:i') }}</small>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                
                                <td>
                                    @php
                                        $documents = $request->verification_documents ?? [];
                                        $documentCount = count($documents);
                                    @endphp
                                    
                                    @if($documentCount > 0)
                                        <span class="badge bg-primary">
                                            <i class="fas fa-file"></i> {{ $documentCount }} مستند
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-file-slash"></i> لا توجد مستندات
                                        </span>
                                    @endif
                                </td>
                                
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.perfect-pharma.verification.show', $request->id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        @if(!$request->is_verified)
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="approveRequest({{ $request->id }})" title="موافقة">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="rejectRequest({{ $request->id }})" title="رفض">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $verificationRequests->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد طلبات تحقق</h5>
                    <p class="text-muted">لم يتم العثور على أي طلبات تحقق تطابق معايير البحث.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Modal للموافقة -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check"></i>
                    تأكيد الموافقة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من الموافقة على طلب التحقق؟</p>
                <div class="mb-3">
                    <label class="form-label">ملاحظات (اختياري)</label>
                    <textarea class="form-control" id="approveNotes" rows="3" 
                              placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmApprove">
                    <i class="fas fa-check"></i>
                    موافقة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal للرفض -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-times"></i>
                    تأكيد الرفض
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رفض طلب التحقق؟</p>
                <div class="mb-3">
                    <label class="form-label">سبب الرفض <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="rejectReason" rows="3" 
                              placeholder="يرجى توضيح سبب الرفض..." required></textarea>
                    <div class="invalid-feedback">سبب الرفض مطلوب</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmReject">
                    <i class="fas fa-times"></i>
                    رفض
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.btn-group .btn {
    margin-right: 2px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}
</style>

<script>
let currentRequestId = null;

function approveRequest(requestId) {
    currentRequestId = requestId;
    document.getElementById('approveNotes').value = '';
    new bootstrap.Modal(document.getElementById('approveModal')).show();
}

function rejectRequest(requestId) {
    currentRequestId = requestId;
    document.getElementById('rejectReason').value = '';
    document.getElementById('rejectReason').classList.remove('is-invalid');
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

document.getElementById('confirmApprove').addEventListener('click', function() {
    if (!currentRequestId) return;
    
    const notes = document.getElementById('approveNotes').value;
    
    fetch(`/admin/perfect-pharma/verification/${currentRequestId}/approve`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ notes: notes })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error === false) {
            bootstrap.Modal.getInstance(document.getElementById('approveModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
});

document.getElementById('confirmReject').addEventListener('click', function() {
    if (!currentRequestId) return;
    
    const reason = document.getElementById('rejectReason').value.trim();
    const reasonField = document.getElementById('rejectReason');
    
    if (!reason) {
        reasonField.classList.add('is-invalid');
        return;
    }
    
    reasonField.classList.remove('is-invalid');
    
    fetch(`/admin/perfect-pharma/verification/${currentRequestId}/reject`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ reason: reason })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error === false) {
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
});

// تطبيق الفلاتر تلقائياً عند التغيير
document.querySelectorAll('#filterForm select').forEach(select => {
    select.addEventListener('change', function() {
        document.getElementById('filterForm').submit();
    });
});
</script>
@endsection

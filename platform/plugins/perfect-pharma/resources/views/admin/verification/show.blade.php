@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">
                        <i class="fas fa-user-check"></i>
                        تفاصيل طلب التحقق
                    </h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.perfect-pharma.verification.index') }}">طلبات التحقق</a>
                            </li>
                            <li class="breadcrumb-item active">تفاصيل الطلب</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('admin.perfect-pharma.verification.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات المستخدم -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user"></i>
                        معلومات المستخدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-lg mx-auto mb-3">
                            <div class="avatar-title bg-primary rounded-circle">
                                {{ substr($verificationRequest->customer->name, 0, 2) }}
                            </div>
                        </div>
                        <h5 class="mb-1">{{ $verificationRequest->customer->name }}</h5>
                        <p class="text-muted mb-0">{{ $verificationRequest->customer->email }}</p>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td class="fw-bold">نوع الحساب:</td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ $verificationRequest->userType->display_name }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">رقم الهاتف:</td>
                                <td>{{ $verificationRequest->customer->phone ?: 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">تاريخ التسجيل:</td>
                                <td>{{ $verificationRequest->created_at->format('Y-m-d H:i') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">حالة التحقق:</td>
                                <td>
                                    @if($verificationRequest->is_verified)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> محقق
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock"></i> قيد المراجعة
                                        </span>
                                    @endif
                                </td>
                            </tr>
                            @if($verificationRequest->verified_at)
                            <tr>
                                <td class="fw-bold">تاريخ التحقق:</td>
                                <td>{{ $verificationRequest->verified_at->format('Y-m-d H:i') }}</td>
                            </tr>
                            @endif
                            @if($verificationRequest->verification_notes)
                            <tr>
                                <td class="fw-bold">ملاحظات:</td>
                                <td>{{ $verificationRequest->verification_notes }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>

                    @if(!$verificationRequest->is_verified)
                    <div class="d-grid gap-2 mt-4">
                        <button type="button" class="btn btn-success" onclick="approveRequest()">
                            <i class="fas fa-check"></i>
                            موافقة على الطلب
                        </button>
                        <button type="button" class="btn btn-danger" onclick="rejectRequest()">
                            <i class="fas fa-times"></i>
                            رفض الطلب
                        </button>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- المستندات المرفوعة -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt"></i>
                        المستندات المرفوعة ({{ count($documents) }})
                    </h5>
                </div>
                <div class="card-body">
                    @if(count($documents) > 0)
                        <div class="row">
                            @foreach($documents as $documentType => $documentPath)
                            <div class="col-md-6 mb-4">
                                <div class="card border">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">
                                            @switch($documentType)
                                                @case('medical_license')
                                                    <i class="fas fa-certificate text-success"></i>
                                                    رخصة مزاولة المهنة الطبية
                                                    @break
                                                @case('pharmacy_license')
                                                    <i class="fas fa-certificate text-info"></i>
                                                    ترخيص الصيدلية
                                                    @break
                                                @case('lab_license')
                                                    <i class="fas fa-certificate text-warning"></i>
                                                    ترخيص المعمل
                                                    @break
                                                @case('hospital_license')
                                                    <i class="fas fa-certificate text-danger"></i>
                                                    ترخيص المستشفى
                                                    @break
                                                @case('clinic_license')
                                                    <i class="fas fa-certificate text-secondary"></i>
                                                    ترخيص العيادة
                                                    @break
                                                @case('government_id')
                                                @case('owner_id')
                                                @case('manager_id')
                                                @case('doctor_id')
                                                    <i class="fas fa-id-card text-primary"></i>
                                                    الهوية الشخصية
                                                    @break
                                                @default
                                                    <i class="fas fa-file text-dark"></i>
                                                    {{ $documentType }}
                                            @endswitch
                                        </h6>
                                    </div>
                                    <div class="card-body text-center">
                                        @php
                                            $extension = pathinfo($documentPath, PATHINFO_EXTENSION);
                                            $isPdf = strtolower($extension) === 'pdf';
                                            $isImage = in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif']);
                                        @endphp

                                        @if($isPdf)
                                            <i class="fas fa-file-pdf fa-4x text-danger mb-3"></i>
                                            <p class="mb-3">ملف PDF</p>
                                        @elseif($isImage)
                                            <i class="fas fa-file-image fa-4x text-primary mb-3"></i>
                                            <p class="mb-3">صورة</p>
                                        @else
                                            <i class="fas fa-file fa-4x text-secondary mb-3"></i>
                                            <p class="mb-3">ملف</p>
                                        @endif

                                        <div class="btn-group w-100" role="group">
                                            <a href="{{ route('admin.perfect-pharma.verification.view-document', [$verificationRequest->id, $documentType]) }}" 
                                               target="_blank" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <a href="{{ route('admin.perfect-pharma.verification.download-document', [$verificationRequest->id, $documentType]) }}" 
                                               class="btn btn-outline-success btn-sm">
                                                <i class="fas fa-download"></i>
                                                تحميل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مستندات مرفوعة</h5>
                            <p class="text-muted">لم يقم المستخدم برفع أي مستندات بعد.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- سجل الأنشطة -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i>
                        سجل الأنشطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء الطلب</h6>
                                <p class="text-muted mb-0">{{ $verificationRequest->created_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>

                        @if(count($documents) > 0)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم رفع المستندات</h6>
                                <p class="text-muted mb-0">{{ count($documents) }} مستند مرفوع</p>
                            </div>
                        </div>
                        @endif

                        @if($verificationRequest->verified_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم التحقق من الطلب</h6>
                                <p class="text-muted mb-0">{{ $verificationRequest->verified_at->format('Y-m-d H:i') }}</p>
                                @if($verificationRequest->verification_notes)
                                    <small class="text-muted">{{ $verificationRequest->verification_notes }}</small>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للموافقة -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check"></i>
                    تأكيد الموافقة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من الموافقة على طلب التحقق لـ <strong>{{ $verificationRequest->customer->name }}</strong>؟</p>
                <div class="mb-3">
                    <label class="form-label">ملاحظات (اختياري)</label>
                    <textarea class="form-control" id="approveNotes" rows="3" 
                              placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmApprove">
                    <i class="fas fa-check"></i>
                    موافقة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal للرفض -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-times"></i>
                    تأكيد الرفض
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رفض طلب التحقق لـ <strong>{{ $verificationRequest->customer->name }}</strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> سيتم حذف جميع المستندات المرفوعة عند الرفض.
                </div>
                <div class="mb-3">
                    <label class="form-label">سبب الرفض <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="rejectReason" rows="3" 
                              placeholder="يرجى توضيح سبب الرفض..." required></textarea>
                    <div class="invalid-feedback">سبب الرفض مطلوب</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmReject">
                    <i class="fas fa-times"></i>
                    رفض
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -21px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}
</style>

<script>
function approveRequest() {
    document.getElementById('approveNotes').value = '';
    new bootstrap.Modal(document.getElementById('approveModal')).show();
}

function rejectRequest() {
    document.getElementById('rejectReason').value = '';
    document.getElementById('rejectReason').classList.remove('is-invalid');
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

document.getElementById('confirmApprove').addEventListener('click', function() {
    const notes = document.getElementById('approveNotes').value;
    
    fetch(`{{ route('admin.perfect-pharma.verification.approve', $verificationRequest->id) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ notes: notes })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error === false) {
            bootstrap.Modal.getInstance(document.getElementById('approveModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
});

document.getElementById('confirmReject').addEventListener('click', function() {
    const reason = document.getElementById('rejectReason').value.trim();
    const reasonField = document.getElementById('rejectReason');
    
    if (!reason) {
        reasonField.classList.add('is-invalid');
        return;
    }
    
    reasonField.classList.remove('is-invalid');
    
    fetch(`{{ route('admin.perfect-pharma.verification.reject', $verificationRequest->id) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ reason: reason })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error === false) {
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
            window.location.href = '{{ route('admin.perfect-pharma.verification.index') }}';
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
});
</script>
@endsection

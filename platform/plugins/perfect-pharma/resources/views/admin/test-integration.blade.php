@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>🧪 اختبار التكامل بين Perfect Pharma و Ecommerce</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- اختبار المنتجات -->
                        <div class="col-md-6">
                            <h5>📦 المنتجات مع خصومات Perfect Pharma</h5>
                            @php
                                $products = \Botble\Ecommerce\Models\Product::where('enable_user_type_discounts', true)->take(5)->get();
                            @endphp
                            
                            @if($products->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>السعر</th>
                                                <th>خصم الأطباء</th>
                                                <th>خصم الصيدليات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($products as $product)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('products.edit', $product->id) }}" target="_blank">
                                                        {{ $product->name }}
                                                    </a>
                                                </td>
                                                <td>{{ format_price($product->price) }}</td>
                                                <td>{{ $product->doctor_discount_percentage ?? 0 }}%</td>
                                                <td>{{ $product->pharmacy_discount_percentage ?? 0 }}%</td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    لا توجد منتجات بخصومات مفعلة. 
                                    <a href="{{ route('admin.perfect-pharma.product-discounts.index') }}">إدارة الخصومات</a>
                                </div>
                            @endif
                        </div>

                        <!-- اختبار أنواع المستخدمين -->
                        <div class="col-md-6">
                            <h5>👥 أنواع المستخدمين</h5>
                            @php
                                $userTypes = \Botble\PerfectPharma\Models\UserType::where('is_active', true)->get();
                            @endphp
                            
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>النوع</th>
                                            <th>الخصم الافتراضي</th>
                                            <th>المستخدمين</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($userTypes as $userType)
                                        @php
                                            $usersCount = \Botble\PerfectPharma\Models\CustomerUserType::where('user_type_id', $userType->id)->count();
                                        @endphp
                                        <tr>
                                            <td>{{ $userType->display_name }}</td>
                                            <td>{{ $userType->default_discount_percentage }}%</td>
                                            <td>{{ $usersCount }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- اختبار التسعير -->
                    <div class="row">
                        <div class="col-12">
                            <h5>💰 اختبار التسعير المتدرج</h5>
                            @php
                                $testProduct = \Botble\Ecommerce\Models\Product::where('enable_user_type_discounts', true)->first();
                                $testCustomer = \Botble\Ecommerce\Models\Customer::whereHas('perfectPharmaUserType', function($q) {
                                    $q->where('is_verified', true);
                                })->first();
                            @endphp

                            @if($testProduct && $testCustomer)
                                @php
                                    $pricingService = app(\Botble\PerfectPharma\Services\Products\PerfectPharmaPriceService::class);
                                    $pricing = $pricingService->getPricingInfo($testProduct, $testCustomer);
                                @endphp

                                <div class="alert alert-success">
                                    <h6>✅ اختبار ناجح!</h6>
                                    <p><strong>المنتج:</strong> {{ $testProduct->name }}</p>
                                    <p><strong>العميل:</strong> {{ $testCustomer->name }} ({{ $pricing['user_type_display'] }})</p>
                                    <p><strong>السعر الأصلي:</strong> {{ format_price($pricing['sale_price']) }}</p>
                                    <p><strong>نسبة الخصم:</strong> {{ $pricing['discount_percentage'] }}%</p>
                                    <p><strong>السعر النهائي:</strong> {{ format_price($pricing['final_price']) }}</p>
                                    <p><strong>الوفورات:</strong> {{ format_price($pricing['discount_amount']) }}</p>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    لا توجد بيانات كافية للاختبار. تحتاج منتج بخصومات مفعلة وعميل محقق.
                                </div>
                            @endif
                        </div>
                    </div>

                    <hr>

                    <!-- روابط سريعة -->
                    <div class="row">
                        <div class="col-12">
                            <h5>🔗 روابط سريعة</h5>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.perfect-pharma.product-discounts.index') }}" class="btn btn-primary">
                                    <i class="fas fa-percentage"></i>
                                    إدارة خصومات المنتجات
                                </a>
                                <a href="{{ route('admin.perfect-pharma.verification.index') }}" class="btn btn-info">
                                    <i class="fas fa-user-check"></i>
                                    إدارة التحقق
                                </a>
                                <a href="{{ route('products.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-box"></i>
                                    إدارة المنتجات
                                </a>
                                <a href="{{ route('products.create') }}" class="btn btn-success">
                                    <i class="fas fa-plus"></i>
                                    إضافة منتج جديد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

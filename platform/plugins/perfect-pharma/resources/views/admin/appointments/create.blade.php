@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-plus me-2"></i>
                        حجز موعد جديد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.appointments.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.appointments.store') }}">
                        @csrf
                        
                        <div class="row">
                            <!-- معلومات المريض والطبيب -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المريض <span class="text-danger">*</span></label>
                                    <select name="patient_id" class="form-select" required>
                                        <option value="">اختر المريض</option>
                                        @foreach($patients ?? [] as $patient)
                                            <option value="{{ $patient->id }}" {{ old('patient_id') == $patient->id ? 'selected' : '' }}>
                                                {{ $patient->customer->name ?? 'مريض' }} - {{ $patient->customer->phone ?? '' }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('patient_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الطبيب <span class="text-danger">*</span></label>
                                    <select name="doctor_id" class="form-select" required>
                                        <option value="">اختر الطبيب</option>
                                        @foreach($doctors ?? [] as $doctor)
                                            <option value="{{ $doctor->id }}" {{ old('doctor_id') == $doctor->id ? 'selected' : '' }}>
                                                د. {{ $doctor->user->name ?? 'طبيب' }} - {{ $doctor->specialization ?? 'طب عام' }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('doctor_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- التاريخ والوقت -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الموعد <span class="text-danger">*</span></label>
                                    <input type="date" name="appointment_date" class="form-control" 
                                           value="{{ old('appointment_date') }}" min="{{ date('Y-m-d') }}" required>
                                    @error('appointment_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">وقت الموعد <span class="text-danger">*</span></label>
                                    <input type="time" name="appointment_time" class="form-control" 
                                           value="{{ old('appointment_time') }}" required>
                                    @error('appointment_time')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المدة (بالدقائق) <span class="text-danger">*</span></label>
                                    <select name="duration_minutes" class="form-select" required>
                                        <option value="">اختر المدة</option>
                                        <option value="15" {{ old('duration_minutes') == '15' ? 'selected' : '' }}>15 دقيقة</option>
                                        <option value="30" {{ old('duration_minutes') == '30' ? 'selected' : '' }}>30 دقيقة</option>
                                        <option value="45" {{ old('duration_minutes') == '45' ? 'selected' : '' }}>45 دقيقة</option>
                                        <option value="60" {{ old('duration_minutes') == '60' ? 'selected' : '' }}>ساعة</option>
                                        <option value="90" {{ old('duration_minutes') == '90' ? 'selected' : '' }}>ساعة ونصف</option>
                                        <option value="120" {{ old('duration_minutes') == '120' ? 'selected' : '' }}>ساعتان</option>
                                    </select>
                                    @error('duration_minutes')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع الموعد -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الموعد <span class="text-danger">*</span></label>
                                    <select name="type" class="form-select" required>
                                        <option value="">اختر النوع</option>
                                        <option value="consultation" {{ old('type') == 'consultation' ? 'selected' : '' }}>استشارة</option>
                                        <option value="follow_up" {{ old('type') == 'follow_up' ? 'selected' : '' }}>متابعة</option>
                                        <option value="emergency" {{ old('type') == 'emergency' ? 'selected' : '' }}>طوارئ</option>
                                        <option value="checkup" {{ old('type') == 'checkup' ? 'selected' : '' }}>فحص</option>
                                    </select>
                                    @error('type')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم هاتف المريض</label>
                                    <input type="tel" name="patient_phone" class="form-control" 
                                           value="{{ old('patient_phone') }}" placeholder="05xxxxxxxx">
                                    @error('patient_phone')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- السبب والملاحظات -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">سبب الزيارة <span class="text-danger">*</span></label>
                                    <textarea name="reason" class="form-control" rows="3" required 
                                              placeholder="اكتب سبب الزيارة...">{{ old('reason') }}</textarea>
                                    @error('reason')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات عامة</label>
                                    <textarea name="notes" class="form-control" rows="3" 
                                              placeholder="ملاحظات إضافية...">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_online" value="1" class="form-check-input" 
                                               id="is_online" {{ old('is_online') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_online">
                                            موعد عبر الإنترنت
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- منطقة عرض الفترات المتاحة -->
                        <div id="available-slots" class="row" style="display: none;">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0">الفترات المتاحة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="slots-container" class="d-flex flex-wrap gap-2">
                                            <!-- سيتم ملء الفترات هنا بواسطة JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.appointments.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حجز الموعد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.querySelector('input[name="appointment_date"]');
    const timeInput = document.querySelector('input[name="appointment_time"]');
    const doctorSelect = document.querySelector('select[name="doctor_id"]');
    const durationSelect = document.querySelector('select[name="duration_minutes"]');
    const availableSlotsDiv = document.getElementById('available-slots');
    const slotsContainer = document.getElementById('slots-container');

    function checkAvailability() {
        const date = dateInput.value;
        const time = timeInput.value;
        const doctorId = doctorSelect.value;
        const duration = durationSelect.value;

        if (date && time && doctorId && duration) {
            fetch('/admin/perfect-pharma/appointments/check-availability?' + new URLSearchParams({
                doctor_id: doctorId,
                date: date,
                time: time,
                duration: duration
            }))
            .then(response => response.json())
            .then(data => {
                if (!data.available) {
                    alert('تحذير: الطبيب غير متاح في هذا الوقت');
                    timeInput.style.borderColor = '#dc3545';
                } else {
                    timeInput.style.borderColor = '#28a745';
                }
            })
            .catch(error => {
                console.error('Error checking availability:', error);
            });
        }
    }

    function loadAvailableSlots() {
        const date = dateInput.value;
        const doctorId = doctorSelect.value;

        if (date && doctorId) {
            fetch(`/admin/perfect-pharma/appointments/get-available-slots?doctor_id=${doctorId}&date=${date}`)
            .then(response => response.json())
            .then(data => {
                if (data.slots && data.slots.length > 0) {
                    slotsContainer.innerHTML = '';
                    data.slots.forEach(slot => {
                        const slotButton = document.createElement('button');
                        slotButton.type = 'button';
                        slotButton.className = 'btn btn-outline-primary btn-sm';
                        slotButton.textContent = slot.time;
                        slotButton.onclick = function() {
                            timeInput.value = slot.time;
                            document.querySelectorAll('#slots-container .btn').forEach(btn => {
                                btn.classList.remove('btn-primary');
                                btn.classList.add('btn-outline-primary');
                            });
                            this.classList.remove('btn-outline-primary');
                            this.classList.add('btn-primary');
                        };
                        slotsContainer.appendChild(slotButton);
                    });
                    availableSlotsDiv.style.display = 'block';
                } else {
                    availableSlotsDiv.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading slots:', error);
                availableSlotsDiv.style.display = 'none';
            });
        } else {
            availableSlotsDiv.style.display = 'none';
        }
    }

    dateInput.addEventListener('change', function() {
        checkAvailability();
        loadAvailableSlots();
    });
    
    timeInput.addEventListener('change', checkAvailability);
    doctorSelect.addEventListener('change', function() {
        checkAvailability();
        loadAvailableSlots();
    });
    
    durationSelect.addEventListener('change', checkAvailability);
});
</script>
@endpush

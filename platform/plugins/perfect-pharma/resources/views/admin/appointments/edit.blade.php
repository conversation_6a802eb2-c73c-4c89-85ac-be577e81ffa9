@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-edit me-2"></i>
                        تعديل الموعد: {{ $appointment->appointment_number ?? '#' . $appointment->id }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.appointments.show', $appointment->id) }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.appointments.update', $appointment->id) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- معلومات المريض والطبيب -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المريض <span class="text-danger">*</span></label>
                                    <select name="patient_id" class="form-select" required>
                                        <option value="">اختر المريض</option>
                                        @foreach($patients ?? [] as $patient)
                                            <option value="{{ $patient->id }}" 
                                                {{ $appointment->patient_id == $patient->id ? 'selected' : '' }}>
                                                {{ $patient->customer->name ?? 'مريض' }} - {{ $patient->customer->phone ?? '' }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('patient_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الطبيب <span class="text-danger">*</span></label>
                                    <select name="doctor_id" class="form-select" required>
                                        <option value="">اختر الطبيب</option>
                                        @foreach($doctors ?? [] as $doctor)
                                            <option value="{{ $doctor->id }}" 
                                                {{ $appointment->doctor_id == $doctor->id ? 'selected' : '' }}>
                                                د. {{ $doctor->user->name ?? 'طبيب' }} - {{ $doctor->specialization ?? 'طب عام' }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('doctor_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- التاريخ والوقت -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الموعد <span class="text-danger">*</span></label>
                                    <input type="date" name="appointment_date" class="form-control" 
                                           value="{{ $appointment->appointment_datetime->format('Y-m-d') }}" required>
                                    @error('appointment_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">وقت الموعد <span class="text-danger">*</span></label>
                                    <input type="time" name="appointment_time" class="form-control" 
                                           value="{{ $appointment->appointment_datetime->format('H:i') }}" required>
                                    @error('appointment_time')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المدة (بالدقائق) <span class="text-danger">*</span></label>
                                    <select name="duration_minutes" class="form-select" required>
                                        <option value="15" {{ $appointment->duration_minutes == 15 ? 'selected' : '' }}>15 دقيقة</option>
                                        <option value="30" {{ $appointment->duration_minutes == 30 ? 'selected' : '' }}>30 دقيقة</option>
                                        <option value="45" {{ $appointment->duration_minutes == 45 ? 'selected' : '' }}>45 دقيقة</option>
                                        <option value="60" {{ $appointment->duration_minutes == 60 ? 'selected' : '' }}>ساعة</option>
                                        <option value="90" {{ $appointment->duration_minutes == 90 ? 'selected' : '' }}>ساعة ونصف</option>
                                        <option value="120" {{ $appointment->duration_minutes == 120 ? 'selected' : '' }}>ساعتان</option>
                                    </select>
                                    @error('duration_minutes')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع الموعد والحالة -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الموعد <span class="text-danger">*</span></label>
                                    <select name="type" class="form-select" required>
                                        <option value="consultation" {{ $appointment->type == 'consultation' ? 'selected' : '' }}>استشارة</option>
                                        <option value="follow_up" {{ $appointment->type == 'follow_up' ? 'selected' : '' }}>متابعة</option>
                                        <option value="emergency" {{ $appointment->type == 'emergency' ? 'selected' : '' }}>طوارئ</option>
                                        <option value="checkup" {{ $appointment->type == 'checkup' ? 'selected' : '' }}>فحص</option>
                                    </select>
                                    @error('type')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة الموعد</label>
                                    <select name="status" class="form-select">
                                        <option value="scheduled" {{ $appointment->status == 'scheduled' ? 'selected' : '' }}>مجدول</option>
                                        <option value="confirmed" {{ $appointment->status == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                                        <option value="completed" {{ $appointment->status == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                        <option value="cancelled" {{ $appointment->status == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                    </select>
                                    @error('status')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- الرسوم -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرسوم (ريال)</label>
                                    <input type="number" name="fee" class="form-control" step="0.01" min="0" 
                                           value="{{ $appointment->fee }}">
                                    @error('fee')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة الدفع</label>
                                    <select name="payment_status" class="form-select">
                                        <option value="pending" {{ $appointment->payment_status == 'pending' ? 'selected' : '' }}>معلق</option>
                                        <option value="paid" {{ $appointment->payment_status == 'paid' ? 'selected' : '' }}>مدفوع</option>
                                        <option value="refunded" {{ $appointment->payment_status == 'refunded' ? 'selected' : '' }}>مسترد</option>
                                    </select>
                                    @error('payment_status')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- السبب والملاحظات -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">سبب الزيارة <span class="text-danger">*</span></label>
                                    <textarea name="reason" class="form-control" rows="3" required>{{ $appointment->reason }}</textarea>
                                    @error('reason')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات عامة</label>
                                    <textarea name="notes" class="form-control" rows="3">{{ $appointment->notes }}</textarea>
                                    @error('notes')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.appointments.show', $appointment->id) }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من توفر الطبيب عند تغيير التاريخ أو الوقت
    const dateInput = document.querySelector('input[name="appointment_date"]');
    const timeInput = document.querySelector('input[name="appointment_time"]');
    const doctorSelect = document.querySelector('select[name="doctor_id"]');
    const durationSelect = document.querySelector('select[name="duration_minutes"]');

    function checkAvailability() {
        const date = dateInput.value;
        const time = timeInput.value;
        const doctorId = doctorSelect.value;
        const duration = durationSelect.value;

        if (date && time && doctorId && duration) {
            fetch('/admin/perfect-pharma/appointments/check-availability?' + new URLSearchParams({
                doctor_id: doctorId,
                date: date,
                time: time,
                duration: duration
            }))
            .then(response => response.json())
            .then(data => {
                if (!data.available) {
                    alert('تحذير: الطبيب غير متاح في هذا الوقت');
                }
            })
            .catch(error => {
                console.error('Error checking availability:', error);
            });
        }
    }

    dateInput.addEventListener('change', checkAvailability);
    timeInput.addEventListener('change', checkAvailability);
    doctorSelect.addEventListener('change', checkAvailability);
    durationSelect.addEventListener('change', checkAvailability);
});
</script>
@endpush

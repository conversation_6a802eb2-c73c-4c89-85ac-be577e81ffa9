@extends(BaseHelper::getAdminMasterLayoutTemplate())

@push('header')
<script src="{{ asset('vendor/core/plugins/perfect-pharma/js/appointments.js') }}"></script>
@endpush

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-calendar me-2"></i>
                        إدارة المواعيد
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.appointments.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i>
                            إضافة موعد جديد
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $totalAppointments ?? 0 }}</h3>
                                            <p class="mb-0">إجمالي المواعيد</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-calendar fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $todayAppointments ?? 0 }}</h3>
                                            <p class="mb-0">مواعيد اليوم</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-calendar-today fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $pendingAppointments ?? 0 }}</h3>
                                            <p class="mb-0">مواعيد معلقة</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-clock fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $completedAppointments ?? 0 }}</h3>
                                            <p class="mb-0">مواعيد مكتملة</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="ti ti-check fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر البحث -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <form method="GET" action="{{ route('admin.appointments.index') }}">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <label class="form-label">البحث</label>
                                                <input type="text" name="search" class="form-control" 
                                                       placeholder="اسم المريض أو الطبيب..." 
                                                       value="{{ request('search') }}">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">الحالة</label>
                                                <select name="status" class="form-select">
                                                    <option value="">جميع الحالات</option>
                                                    <option value="scheduled" {{ request('status') == 'scheduled' ? 'selected' : '' }}>مجدول</option>
                                                    <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                                                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                                    <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">من تاريخ</label>
                                                <input type="date" name="date_from" class="form-control" 
                                                       value="{{ request('date_from') }}">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">إلى تاريخ</label>
                                                <input type="date" name="date_to" class="form-control" 
                                                       value="{{ request('date_to') }}">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">&nbsp;</label>
                                                <div class="d-flex gap-2">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="ti ti-search me-1"></i>
                                                        بحث
                                                    </button>
                                                    <a href="{{ route('admin.appointments.index') }}" class="btn btn-secondary">
                                                        <i class="ti ti-refresh me-1"></i>
                                                        إعادة تعيين
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المواعيد -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الموعد</th>
                                    <th>المريض</th>
                                    <th>الطبيب</th>
                                    <th>التاريخ والوقت</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>الملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($appointments ?? [] as $appointment)
                                    <tr>
                                        <td>
                                            <strong>#{{ $appointment['appointment_number'] ?? 'APP-' . str_pad($loop->iteration, 4, '0', STR_PAD_LEFT) }}</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ substr($appointment['patient_name'] ?? 'مريض', 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $appointment['patient_name'] ?? 'مريض تجريبي' }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ $appointment['patient_phone'] ?? '05xxxxxxxx' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-success rounded-circle">
                                                        {{ substr($appointment['doctor_name'] ?? 'طبيب', 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $appointment['doctor_name'] ?? 'د. أحمد محمد' }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ $appointment['specialization'] ?? 'طب عام' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $appointment['appointment_date'] ?? now()->format('Y-m-d') }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $appointment['appointment_time'] ?? '10:00 AM' }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $appointment['type'] ?? 'استشارة' }}</span>
                                        </td>
                                        <td>
                                            @php
                                                $status = $appointment['status'] ?? 'scheduled';
                                                $statusClass = match($status) {
                                                    'confirmed' => 'bg-success',
                                                    'completed' => 'bg-primary',
                                                    'cancelled' => 'bg-danger',
                                                    default => 'bg-warning'
                                                };
                                                $statusText = match($status) {
                                                    'confirmed' => 'مؤكد',
                                                    'completed' => 'مكتمل',
                                                    'cancelled' => 'ملغي',
                                                    default => 'مجدول'
                                                };
                                            @endphp
                                            <span class="badge {{ $statusClass }}">{{ $statusText }}</span>
                                        </td>
                                        <td>
                                            <small>{{ $appointment['notes'] ?? 'لا توجد ملاحظات' }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.appointments.show', $appointment['id'] ?? 1) }}" 
                                                   class="btn btn-sm btn-info" title="عرض">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.appointments.edit', $appointment['id'] ?? 1) }}" 
                                                   class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                                @if(($appointment['status'] ?? 'scheduled') === 'scheduled')
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            onclick="confirmAppointment({{ $appointment['id'] ?? 1 }})" title="تأكيد">
                                                        <i class="ti ti-check"></i>
                                                    </button>
                                                @endif
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="cancelAppointment({{ $appointment['id'] ?? 1 }})" title="إلغاء">
                                                    <i class="ti ti-x"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-calendar-off fs-1 mb-2"></i>
                                                <p>لا توجد مواعيد</p>
                                                <a href="{{ route('admin.appointments.create') }}" class="btn btn-primary">
                                                    إضافة موعد جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if(isset($appointments) && method_exists($appointments, 'links'))
                        <div class="d-flex justify-content-center">
                            {{ $appointments->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
// تأكد من وجود CSRF token
if (!document.querySelector('meta[name="csrf-token"]')) {
    console.error('CSRF token not found');
}

function confirmAppointment(id) {
    if (confirm('هل تريد تأكيد هذا الموعد؟')) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            alert('خطأ: لم يتم العثور على رمز الأمان');
            return;
        }

        fetch(window.location.origin + '/admin/perfect-pharma/appointments/' + id + '/confirm', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert(data.message || 'تم تأكيد الموعد بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + (data.message || 'حدث خطأ غير متوقع'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال بالخادم');
        });
    }
}

function cancelAppointment(id) {
    if (confirm('هل تريد إلغاء هذا الموعد؟')) {
        const reason = prompt('سبب الإلغاء (اختياري):');
        const csrfToken = document.querySelector('meta[name="csrf-token"]');

        if (!csrfToken) {
            alert('خطأ: لم يتم العثور على رمز الأمان');
            return;
        }

        fetch(window.location.origin + '/admin/perfect-pharma/appointments/' + id + '/cancel', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                cancellation_reason: reason || ''
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert(data.message || 'تم إلغاء الموعد بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + (data.message || 'حدث خطأ غير متوقع'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال بالخادم');
        });
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('Appointments page loaded successfully');
});
</script>
@endpush

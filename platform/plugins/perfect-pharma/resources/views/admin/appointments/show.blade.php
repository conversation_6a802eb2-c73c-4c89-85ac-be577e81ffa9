@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-calendar me-2"></i>
                        تفاصيل الموعد: {{ $appointment->appointment_number ?? '#' . $appointment->id }}
                    </h4>
                    <div class="card-actions">
                        <a href="{{ route('admin.appointments.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                        @if(in_array($appointment->status, ['scheduled', 'confirmed']))
                            <a href="{{ route('admin.appointments.edit', $appointment->id) }}" class="btn btn-warning">
                                <i class="ti ti-edit me-1"></i>
                                تعديل
                            </a>
                        @endif
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- معلومات الموعد -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>معلومات الموعد</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم الموعد:</strong></td>
                                            <td>{{ $appointment->appointment_number ?? '#' . $appointment->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>التاريخ والوقت:</strong></td>
                                            <td>{{ $appointment->appointment_datetime->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المدة:</strong></td>
                                            <td>{{ $appointment->duration_minutes }} دقيقة</td>
                                        </tr>
                                        <tr>
                                            <td><strong>النوع:</strong></td>
                                            <td>
                                                <span class="badge bg-info">{{ $appointment->type_label ?? $appointment->type }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                @php
                                                    $statusClass = match($appointment->status) {
                                                        'confirmed' => 'bg-success',
                                                        'completed' => 'bg-primary',
                                                        'cancelled' => 'bg-danger',
                                                        default => 'bg-warning'
                                                    };
                                                @endphp
                                                <span class="badge {{ $statusClass }}">{{ $appointment->status_label ?? $appointment->status }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الرسوم:</strong></td>
                                            <td>{{ number_format($appointment->fee, 2) }} ريال</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات المريض -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>معلومات المريض</h5>
                                </div>
                                <div class="card-body">
                                    @if($appointment->patient && $appointment->patient->customer)
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="avatar avatar-lg me-3">
                                                <span class="avatar-initial bg-primary rounded-circle fs-4">
                                                    {{ substr($appointment->patient->customer->name, 0, 1) }}
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $appointment->patient->customer->name }}</h6>
                                                <small class="text-muted">{{ $appointment->patient->customer->email }}</small>
                                            </div>
                                        </div>
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>الهاتف:</strong></td>
                                                <td>{{ $appointment->patient->customer->phone ?? 'غير محدد' }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ الميلاد:</strong></td>
                                                <td>{{ $appointment->patient->customer->dob ?? 'غير محدد' }}</td>
                                            </tr>
                                        </table>
                                    @else
                                        <p class="text-muted">لا توجد معلومات للمريض</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <!-- معلومات الطبيب -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>معلومات الطبيب</h5>
                                </div>
                                <div class="card-body">
                                    @if($appointment->doctor && $appointment->doctor->user)
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="avatar avatar-lg me-3">
                                                <span class="avatar-initial bg-success rounded-circle fs-4">
                                                    {{ substr($appointment->doctor->user->name, 0, 1) }}
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">د. {{ $appointment->doctor->user->name }}</h6>
                                                <small class="text-muted">{{ $appointment->doctor->specialization ?? 'طب عام' }}</small>
                                            </div>
                                        </div>
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>التخصص:</strong></td>
                                                <td>{{ $appointment->doctor->specialization ?? 'طب عام' }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>رقم الترخيص:</strong></td>
                                                <td>{{ $appointment->doctor->license_number ?? 'غير محدد' }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>سنوات الخبرة:</strong></td>
                                                <td>{{ $appointment->doctor->experience_years ?? 0 }} سنة</td>
                                            </tr>
                                        </table>
                                    @else
                                        <p class="text-muted">لا توجد معلومات للطبيب</p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- الملاحظات والسبب -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>الملاحظات والتفاصيل</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>سبب الزيارة:</strong>
                                        <p class="mt-1">{{ $appointment->reason ?? 'غير محدد' }}</p>
                                    </div>
                                    
                                    @if($appointment->notes)
                                        <div class="mb-3">
                                            <strong>ملاحظات عامة:</strong>
                                            <p class="mt-1">{{ $appointment->notes }}</p>
                                        </div>
                                    @endif
                                    
                                    @if(isset($appointment->doctor_notes) && $appointment->doctor_notes)
                                        <div class="mb-3">
                                            <strong>ملاحظات الطبيب:</strong>
                                            <p class="mt-1">{{ $appointment->doctor_notes }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إجراءات الموعد -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>إجراءات الموعد</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group" role="group">
                                        @if($appointment->status === 'scheduled')
                                            <button type="button" class="btn btn-success" onclick="confirmAppointment({{ $appointment->id }})">
                                                <i class="ti ti-check me-1"></i>
                                                تأكيد الموعد
                                            </button>
                                        @endif
                                        
                                        @if(in_array($appointment->status, ['scheduled', 'confirmed']))
                                            <button type="button" class="btn btn-warning" onclick="rescheduleAppointment({{ $appointment->id }})">
                                                <i class="ti ti-calendar-event me-1"></i>
                                                إعادة جدولة
                                            </button>
                                            
                                            <button type="button" class="btn btn-danger" onclick="cancelAppointment({{ $appointment->id }})">
                                                <i class="ti ti-x me-1"></i>
                                                إلغاء الموعد
                                            </button>
                                        @endif
                                        
                                        @if($appointment->status === 'confirmed')
                                            <button type="button" class="btn btn-primary" onclick="completeAppointment({{ $appointment->id }})">
                                                <i class="ti ti-check-circle me-1"></i>
                                                إنهاء الموعد
                                            </button>
                                            
                                            <button type="button" class="btn btn-secondary" onclick="markNoShow({{ $appointment->id }})">
                                                <i class="ti ti-user-x me-1"></i>
                                                عدم حضور
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
function confirmAppointment(id) {
    if (confirm('هل تريد تأكيد هذا الموعد؟')) {
        fetch(`/admin/perfect-pharma/appointments/${id}/confirm`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تأكيد الموعد بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال بالخادم');
        });
    }
}

function cancelAppointment(id) {
    const reason = prompt('سبب الإلغاء (اختياري):');
    if (reason !== null) {
        fetch(`/admin/perfect-pharma/appointments/${id}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ cancellation_reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إلغاء الموعد بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال بالخادم');
        });
    }
}

function completeAppointment(id) {
    const notes = prompt('ملاحظات الطبيب (اختياري):');
    if (notes !== null) {
        fetch(`/admin/perfect-pharma/appointments/${id}/complete`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ doctor_notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إنهاء الموعد بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال بالخادم');
        });
    }
}

function markNoShow(id) {
    if (confirm('هل تريد تحديد عدم حضور المريض لهذا الموعد؟')) {
        fetch(`/admin/perfect-pharma/appointments/${id}/no-show`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تحديد عدم الحضور');
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال بالخادم');
        });
    }
}
</script>
@endpush

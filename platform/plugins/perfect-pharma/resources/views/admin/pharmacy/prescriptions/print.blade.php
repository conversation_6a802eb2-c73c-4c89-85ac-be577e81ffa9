<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال صرف دواء - {{ $dispensing->dispensing_number }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #fff;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        
        .receipt {
            max-width: 400px;
            margin: 0 auto;
            border: 2px solid #333;
            padding: 20px;
            background-color: #fff;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .pharmacy-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .pharmacy-info {
            font-size: 12px;
            color: #666;
        }
        
        .receipt-title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            background-color: #f0f0f0;
            padding: 10px;
            border: 1px solid #ddd;
        }
        
        .info-section {
            margin-bottom: 15px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 3px 0;
        }
        
        .info-label {
            font-weight: bold;
            min-width: 120px;
        }
        
        .info-value {
            flex: 1;
            text-align: left;
        }
        
        .medication-section {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 15px 0;
            background-color: #f9f9f9;
        }
        
        .medication-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
        }
        
        .price-section {
            border-top: 2px solid #333;
            padding-top: 10px;
            margin-top: 15px;
        }
        
        .total-price {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            background-color: #d4edda;
            padding: 10px;
            border: 2px solid #28a745;
            margin: 10px 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }
        
        .prescription-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .doctor-signature {
            margin-top: 30px;
            text-align: center;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            width: 200px;
            margin: 20px auto 5px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
            
            .receipt {
                border: 1px solid #000;
                box-shadow: none;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        .barcode {
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            letter-spacing: 2px;
            margin: 10px 0;
            padding: 5px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- رأس الإيصال -->
        <div class="header">
            <div class="pharmacy-name">{{ $dispensing->pharmacy->name }}</div>
            <div class="pharmacy-info">
                {{ $dispensing->pharmacy->address }}<br>
                هاتف: {{ $dispensing->pharmacy->phone }}<br>
                @if($dispensing->pharmacy->email)
                    البريد الإلكتروني: {{ $dispensing->pharmacy->email }}
                @endif
            </div>
        </div>
        
        <!-- عنوان الإيصال -->
        <div class="receipt-title">
            إيصال صرف دواء
        </div>
        
        <!-- معلومات الإيصال -->
        <div class="info-section">
            <div class="info-row">
                <span class="info-label">رقم الإيصال:</span>
                <span class="info-value">{{ $dispensing->dispensing_number }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">التاريخ:</span>
                <span class="info-value">{{ \Carbon\Carbon::parse($dispensing->dispensing_date)->format('Y-m-d') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">الوقت:</span>
                <span class="info-value">{{ $dispensing->created_at->format('H:i:s') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">الصيدلي:</span>
                <span class="info-value">{{ $dispensing->pharmacist->name }}</span>
            </div>
        </div>
        
        <!-- معلومات المريض -->
        <div class="info-section">
            <h4 style="margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">معلومات المريض</h4>
            <div class="info-row">
                <span class="info-label">الاسم:</span>
                <span class="info-value">{{ $dispensing->prescriptionMedication->prescription->patient->customer->name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">رقم المريض:</span>
                <span class="info-value">{{ $dispensing->prescriptionMedication->prescription->patient->patient_code }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">الهاتف:</span>
                <span class="info-value">{{ $dispensing->prescriptionMedication->prescription->patient->customer->phone }}</span>
            </div>
        </div>
        
        <!-- معلومات الوصفة الطبية -->
        <div class="prescription-info">
            <div class="info-row">
                <span class="info-label">رقم الوصفة:</span>
                <span class="info-value">{{ $dispensing->prescriptionMedication->prescription->prescription_number }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">الطبيب:</span>
                <span class="info-value">{{ $dispensing->prescriptionMedication->prescription->doctor->user->name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">التخصص:</span>
                <span class="info-value">{{ $dispensing->prescriptionMedication->prescription->doctor->specialization }}</span>
            </div>
        </div>
        
        <!-- معلومات الدواء -->
        <div class="medication-section">
            <div class="medication-name">{{ $dispensing->prescriptionMedication->medication_name }}</div>
            
            <div class="info-row">
                <span class="info-label">الجرعة:</span>
                <span class="info-value">{{ $dispensing->prescriptionMedication->dosage }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">التكرار:</span>
                <span class="info-value">{{ $dispensing->prescriptionMedication->frequency }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">الكمية المصروفة:</span>
                <span class="info-value">{{ $dispensing->quantity_dispensed }}</span>
            </div>
            
            @if($dispensing->prescriptionMedication->instructions)
                <div style="margin-top: 10px; padding: 8px; background-color: #e7f3ff; border-left: 4px solid #007bff;">
                    <strong>تعليمات الاستخدام:</strong><br>
                    {{ $dispensing->prescriptionMedication->instructions }}
                </div>
            @endif
        </div>
        
        <!-- معلومات السعر -->
        <div class="price-section">
            <div class="info-row">
                <span class="info-label">سعر الوحدة:</span>
                <span class="info-value">{{ number_format($dispensing->unit_price, 2) }} ج.م</span>
            </div>
            <div class="info-row">
                <span class="info-label">السعر الإجمالي:</span>
                <span class="info-value">{{ number_format($dispensing->total_price, 2) }} ج.م</span>
            </div>
            
            @if($dispensing->discount_amount > 0)
                <div class="info-row">
                    <span class="info-label">الخصم:</span>
                    <span class="info-value" style="color: #28a745;">{{ number_format($dispensing->discount_amount, 2) }} ج.م</span>
                </div>
            @endif
            
            <div class="total-price">
                المبلغ النهائي: {{ number_format($dispensing->final_price, 2) }} ج.م
            </div>
            
            <div class="info-row">
                <span class="info-label">طريقة الدفع:</span>
                <span class="info-value">
                    @php
                        $paymentMethods = [
                            'cash' => 'نقداً',
                            'wallet' => 'محفظة إلكترونية',
                            'insurance' => 'تأمين',
                            'credit_card' => 'بطاقة ائتمان'
                        ];
                    @endphp
                    {{ $paymentMethods[$dispensing->payment_method] ?? $dispensing->payment_method }}
                </span>
            </div>
        </div>
        
        <!-- ملاحظات الصيدلي -->
        @if($dispensing->pharmacist_notes)
            <div style="margin: 15px 0; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                <strong>ملاحظات الصيدلي:</strong><br>
                {{ $dispensing->pharmacist_notes }}
            </div>
        @endif
        
        <!-- الباركود -->
        <div class="barcode">
            {{ $dispensing->dispensing_number }}
        </div>
        
        <!-- توقيع الصيدلي -->
        <div class="doctor-signature">
            <div class="signature-line"></div>
            <div>توقيع الصيدلي</div>
        </div>
        
        <!-- تذييل الإيصال -->
        <div class="footer">
            <p>شكراً لثقتكم بنا</p>
            <p>للاستفسارات: {{ $dispensing->pharmacy->phone }}</p>
            <p style="font-size: 10px; margin-top: 10px;">
                تم الطباعة في: {{ now()->format('Y-m-d H:i:s') }}
            </p>
        </div>
    </div>
    
    <!-- أزرار التحكم -->
    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
            طباعة الإيصال
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
            إغلاق
        </button>
    </div>
    
    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            // تأخير قصير للتأكد من تحميل الصفحة كاملة
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>

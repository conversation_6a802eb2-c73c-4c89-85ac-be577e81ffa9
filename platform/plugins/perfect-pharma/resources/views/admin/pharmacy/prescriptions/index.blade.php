@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ trans('البحث عن الوصفات الطبية') }}</h4>
                    <p class="card-description">ابحث عن الوصفات الطبية باستخدام رقم هاتف المريض</p>
                </div>
                <div class="card-body">
                    <!-- نموذج البحث -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="patient_phone">رقم هاتف المريض</label>
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control" 
                                           id="patient_phone" 
                                           placeholder="أدخل رقم هاتف المريض (مثال: **********)"
                                           maxlength="15">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="button" id="search_btn">
                                            <i class="ti ti-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                                <small class="form-text text-muted">
                                    يمكنك إدخال رقم الهاتف بأي صيغة (مع أو بدون رمز الدولة)
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button class="btn btn-secondary" type="button" id="clear_btn">
                                        <i class="ti ti-refresh"></i> مسح
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- منطقة النتائج -->
                    <div id="search_results" style="display: none;">
                        <!-- معلومات المريض -->
                        <div id="patient_info" class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="ti ti-user"></i> معلومات المريض
                                </h5>
                            </div>
                            <div class="card-body" id="patient_details">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>

                        <!-- قائمة الوصفات -->
                        <div id="prescriptions_list">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- رسائل التنبيه -->
                    <div id="alert_container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لعرض تفاصيل الوصفة -->
    <div class="modal fade" id="prescriptionModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الوصفة الطبية</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="prescription_details">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                    <a href="#" class="btn btn-primary" id="view_prescription_btn">عرض الوصفة كاملة</a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // البحث عن الوصفات
    $('#search_btn').click(function() {
        const phone = $('#patient_phone').val().trim();
        
        if (!phone) {
            showAlert('يرجى إدخال رقم هاتف المريض', 'warning');
            return;
        }

        searchPrescriptions(phone);
    });

    // البحث عند الضغط على Enter
    $('#patient_phone').keypress(function(e) {
        if (e.which === 13) {
            $('#search_btn').click();
        }
    });

    // مسح النتائج
    $('#clear_btn').click(function() {
        $('#patient_phone').val('');
        $('#search_results').hide();
        $('#alert_container').empty();
    });

    function searchPrescriptions(phone) {
        // إظهار مؤشر التحميل
        $('#search_btn').prop('disabled', true).html('<i class="ti ti-loader"></i> جاري البحث...');
        $('#alert_container').empty();

        $.ajax({
            url: '{{ route("admin.pharmacy.prescriptions.search-by-phone") }}',
            method: 'POST',
            data: {
                phone: phone,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.error) {
                    showAlert(response.message, 'danger');
                    $('#search_results').hide();
                } else {
                    displayResults(response.data);
                    showAlert(response.message, 'success');
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'حدث خطأ أثناء البحث';
                showAlert(message, 'danger');
                $('#search_results').hide();
            },
            complete: function() {
                $('#search_btn').prop('disabled', false).html('<i class="ti ti-search"></i> بحث');
            }
        });
    }

    function displayResults(data) {
        // عرض معلومات المريض
        const patient = data.patient;
        const customer = patient.customer;
        
        $('#patient_details').html(`
            <div class="row">
                <div class="col-md-6">
                    <p><strong>الاسم:</strong> ${customer.name}</p>
                    <p><strong>رقم الهاتف:</strong> ${customer.phone}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${customer.email || 'غير محدد'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>رقم المريض:</strong> ${patient.patient_code}</p>
                    <p><strong>تاريخ الميلاد:</strong> ${patient.birth_date || 'غير محدد'}</p>
                    <p><strong>فصيلة الدم:</strong> ${patient.blood_type || 'غير محدد'}</p>
                </div>
            </div>
        `);

        // عرض الوصفات
        const prescriptions = data.prescriptions;
        let prescriptionsHtml = '';

        if (prescriptions.length === 0) {
            prescriptionsHtml = `
                <div class="alert alert-info">
                    <i class="ti ti-info-circle"></i> لا توجد وصفات طبية نشطة لهذا المريض
                </div>
            `;
        } else {
            prescriptions.forEach(function(prescription) {
                const statusClass = getStatusClass(prescription.status);
                const statusText = getStatusText(prescription.status);
                
                prescriptionsHtml += `
                    <div class="card mb-3">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-0">
                                        وصفة رقم: ${prescription.prescription_number}
                                        <span class="badge badge-${statusClass} ml-2">${statusText}</span>
                                    </h6>
                                    <small class="text-muted">
                                        الطبيب: ${prescription.doctor.user.name} | 
                                        التاريخ: ${prescription.prescription_date}
                                    </small>
                                </div>
                                <div class="col-md-4 text-right">
                                    <button class="btn btn-sm btn-primary view-prescription" 
                                            data-id="${prescription.id}">
                                        <i class="ti ti-eye"></i> عرض التفاصيل
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>التشخيص:</strong> ${prescription.diagnosis || 'غير محدد'}</p>
                                    <p><strong>الأعراض:</strong> ${prescription.symptoms || 'غير محدد'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>عدد الأدوية:</strong> ${prescription.medications.length}</p>
                                    <p><strong>تاريخ الانتهاء:</strong> ${prescription.expiry_date || 'غير محدد'}</p>
                                </div>
                            </div>
                            
                            <h6 class="mt-3">الأدوية:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>اسم الدواء</th>
                                            <th>الجرعة</th>
                                            <th>التكرار</th>
                                            <th>الكمية</th>
                                            <th>المصروف</th>
                                            <th>السعر للصيدلية</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;
                
                prescription.medications.forEach(function(medication) {
                    const medStatusClass = getStatusClass(medication.status);
                    const medStatusText = getStatusText(medication.status);
                    const remaining = medication.quantity - medication.dispensed_quantity;
                    
                    prescriptionsHtml += `
                        <tr>
                            <td>${medication.medication_name}</td>
                            <td>${medication.dosage}</td>
                            <td>${medication.frequency}</td>
                            <td>${medication.quantity}</td>
                            <td>${medication.dispensed_quantity} (متبقي: ${remaining})</td>
                            <td>
                                ${medication.pharmacy_price ? medication.pharmacy_price + ' ج.م' : 'غير محدد'}
                                ${medication.discount_percentage ? '<br><small class="text-success">خصم: ' + medication.discount_percentage + '%</small>' : ''}
                            </td>
                            <td><span class="badge badge-${medStatusClass}">${medStatusText}</span></td>
                        </tr>
                    `;
                });
                
                prescriptionsHtml += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            });
        }

        $('#prescriptions_list').html(prescriptionsHtml);
        $('#search_results').show();

        // ربط أحداث عرض التفاصيل
        $('.view-prescription').click(function() {
            const prescriptionId = $(this).data('id');
            window.open('{{ route("admin.pharmacy.prescriptions.show", ":id") }}'.replace(':id', prescriptionId), '_blank');
        });
    }

    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        $('#alert_container').html(alertHtml);
    }

    function getStatusClass(status) {
        const statusClasses = {
            'active': 'success',
            'partially_filled': 'warning',
            'completed': 'info',
            'expired': 'danger',
            'cancelled': 'secondary',
            'pending': 'primary',
            'partially_dispensed': 'warning'
        };
        return statusClasses[status] || 'secondary';
    }

    function getStatusText(status) {
        const statusTexts = {
            'active': 'نشطة',
            'partially_filled': 'مصروفة جزئياً',
            'completed': 'مكتملة',
            'expired': 'منتهية الصلاحية',
            'cancelled': 'ملغية',
            'pending': 'في الانتظار',
            'partially_dispensed': 'مصروف جزئياً'
        };
        return statusTexts[status] || status;
    }
});
</script>
@endpush

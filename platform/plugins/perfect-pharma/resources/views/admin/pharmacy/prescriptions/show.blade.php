@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <!-- معلومات الوصفة -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="ti ti-prescription"></i> 
                        وصفة طبية رقم: {{ $prescription->prescription_number }}
                        <span class="badge badge-light ml-2">{{ $prescription->status }}</span>
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>معلومات المريض:</h6>
                            <p><strong>الاسم:</strong> {{ $prescription->patient->customer->name }}</p>
                            <p><strong>رقم المريض:</strong> {{ $prescription->patient->patient_code }}</p>
                            <p><strong>الهاتف:</strong> {{ $prescription->patient->customer->phone }}</p>
                            <p><strong>فصيلة الدم:</strong> {{ $prescription->patient->blood_type ?? 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>معلومات الطبيب:</h6>
                            <p><strong>الاسم:</strong> {{ $prescription->doctor->user->name }}</p>
                            <p><strong>التخصص:</strong> {{ $prescription->doctor->specialization }}</p>
                            <p><strong>رقم الترخيص:</strong> {{ $prescription->doctor->license_number }}</p>
                            <p><strong>تاريخ الوصفة:</strong> {{ $prescription->prescription_date }}</p>
                        </div>
                    </div>
                    
                    @if($prescription->diagnosis)
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <h6>التشخيص:</h6>
                                <p class="bg-light p-3 rounded">{{ $prescription->diagnosis }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($prescription->symptoms)
                        <div class="row">
                            <div class="col-md-12">
                                <h6>الأعراض:</h6>
                                <p class="bg-light p-3 rounded">{{ $prescription->symptoms }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($prescription->notes)
                        <div class="row">
                            <div class="col-md-12">
                                <h6>ملاحظات الطبيب:</h6>
                                <p class="bg-light p-3 rounded">{{ $prescription->notes }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- حالة الوصفة -->
            @if(!$validation['is_valid'])
                <div class="alert alert-danger">
                    <h6><i class="ti ti-alert-triangle"></i> تحذيرات:</h6>
                    <ul class="mb-0">
                        @foreach($validation['errors'] as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- الأدوية -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="ti ti-pill"></i> الأدوية المطلوبة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم الدواء</th>
                                    <th>الجرعة</th>
                                    <th>التكرار</th>
                                    <th>المدة</th>
                                    <th>الكمية المطلوبة</th>
                                    <th>المصروف</th>
                                    <th>المتبقي</th>
                                    <th>السعر للصيدلية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($prescription->medications as $medication)
                                    @php
                                        $remaining = $medication->quantity - $medication->dispensed_quantity;
                                        $canDispense = $remaining > 0 && $validation['is_valid'];
                                    @endphp
                                    <tr>
                                        <td>
                                            <strong>{{ $medication->medication_name }}</strong>
                                            @if($medication->instructions)
                                                <br><small class="text-muted">{{ $medication->instructions }}</small>
                                            @endif
                                        </td>
                                        <td>{{ $medication->dosage }}</td>
                                        <td>{{ $medication->frequency }}</td>
                                        <td>{{ $medication->duration_days }} يوم</td>
                                        <td>{{ $medication->quantity }}</td>
                                        <td>{{ $medication->dispensed_quantity }}</td>
                                        <td>
                                            <span class="badge badge-{{ $remaining > 0 ? 'warning' : 'success' }}">
                                                {{ $remaining }}
                                            </span>
                                        </td>
                                        <td>
                                            @if(isset($medication->pharmacy_price))
                                                <strong>{{ number_format($medication->pharmacy_price, 2) }} ج.م</strong>
                                                @if($medication->discount_percentage > 0)
                                                    <br><small class="text-success">
                                                        خصم: {{ $medication->discount_percentage }}%
                                                        (السعر الأصلي: {{ number_format($medication->original_price, 2) }} ج.م)
                                                    </small>
                                                @endif
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $statusClass = match($medication->status) {
                                                    'pending' => 'primary',
                                                    'partially_dispensed' => 'warning',
                                                    'completed' => 'success',
                                                    default => 'secondary'
                                                };
                                                $statusText = match($medication->status) {
                                                    'pending' => 'في الانتظار',
                                                    'partially_dispensed' => 'مصروف جزئياً',
                                                    'completed' => 'مكتمل',
                                                    default => $medication->status
                                                };
                                            @endphp
                                            <span class="badge badge-{{ $statusClass }}">{{ $statusText }}</span>
                                        </td>
                                        <td>
                                            @if($canDispense)
                                                <button class="btn btn-sm btn-success dispense-btn" 
                                                        data-medication-id="{{ $medication->id }}"
                                                        data-medication-name="{{ $medication->medication_name }}"
                                                        data-remaining="{{ $remaining }}"
                                                        data-price="{{ $medication->pharmacy_price ?? 0 }}">
                                                    <i class="ti ti-check"></i> صرف
                                                </button>
                                            @else
                                                <span class="text-muted">غير متاح</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- تاريخ الصرف السابق -->
            @if($dispensingHistory->count() > 0)
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="ti ti-history"></i> تاريخ الصرف السابق
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>رقم الصرف</th>
                                        <th>الدواء</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الصيدلية</th>
                                        <th>الصيدلي</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($dispensingHistory as $dispensing)
                                        <tr>
                                            <td>{{ $dispensing->dispensing_number }}</td>
                                            <td>{{ $dispensing->prescriptionMedication->medication_name }}</td>
                                            <td>{{ $dispensing->quantity_dispensed }}</td>
                                            <td>{{ number_format($dispensing->final_price, 2) }} ج.م</td>
                                            <td>{{ $dispensing->pharmacy->name }}</td>
                                            <td>{{ $dispensing->pharmacist->name }}</td>
                                            <td>{{ $dispensing->dispensing_date }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Modal صرف الدواء -->
    <div class="modal fade" id="dispenseModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">صرف الدواء</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="dispenseForm">
                    <div class="modal-body">
                        <input type="hidden" id="medication_id" name="medication_id">
                        
                        <div class="form-group">
                            <label>اسم الدواء:</label>
                            <p id="medication_name_display" class="font-weight-bold"></p>
                        </div>
                        
                        <div class="form-group">
                            <label for="quantity_dispensed">الكمية المطلوب صرفها:</label>
                            <input type="number" class="form-control" id="quantity_dispensed" 
                                   name="quantity_dispensed" min="1" required>
                            <small class="form-text text-muted">
                                الكمية المتاحة: <span id="available_quantity"></span>
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label for="payment_method">طريقة الدفع:</label>
                            <select class="form-control" id="payment_method" name="payment_method" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash">نقداً</option>
                                <option value="wallet">محفظة إلكترونية</option>
                                <option value="insurance">تأمين</option>
                                <option value="credit_card">بطاقة ائتمان</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="discount_amount">مبلغ الخصم (اختياري):</label>
                            <input type="number" class="form-control" id="discount_amount" 
                                   name="discount_amount" min="0" step="0.01">
                        </div>
                        
                        <div class="form-group">
                            <label for="pharmacist_notes">ملاحظات الصيدلي:</label>
                            <textarea class="form-control" id="pharmacist_notes" 
                                      name="pharmacist_notes" rows="3"></textarea>
                        </div>
                        
                        <div class="alert alert-info">
                            <strong>السعر للوحدة:</strong> <span id="unit_price_display"></span> ج.م<br>
                            <strong>إجمالي السعر:</strong> <span id="total_price_display"></span> ج.م
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">
                            <i class="ti ti-check"></i> تأكيد الصرف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // فتح modal الصرف
    $('.dispense-btn').click(function() {
        const medicationId = $(this).data('medication-id');
        const medicationName = $(this).data('medication-name');
        const remaining = $(this).data('remaining');
        const price = $(this).data('price');
        
        $('#medication_id').val(medicationId);
        $('#medication_name_display').text(medicationName);
        $('#available_quantity').text(remaining);
        $('#unit_price_display').text(price);
        $('#quantity_dispensed').attr('max', remaining).val(1);
        
        updateTotalPrice();
        $('#dispenseModal').modal('show');
    });
    
    // تحديث السعر الإجمالي
    $('#quantity_dispensed, #discount_amount').on('input', updateTotalPrice);
    
    function updateTotalPrice() {
        const quantity = parseInt($('#quantity_dispensed').val()) || 0;
        const unitPrice = parseFloat($('#unit_price_display').text()) || 0;
        const discount = parseFloat($('#discount_amount').val()) || 0;
        
        const total = (quantity * unitPrice) - discount;
        $('#total_price_display').text(total.toFixed(2));
    }
    
    // إرسال نموذج الصرف
    $('#dispenseForm').submit(function(e) {
        e.preventDefault();
        
        const medicationId = $('#medication_id').val();
        const formData = $(this).serialize();
        
        $.ajax({
            url: '{{ route("admin.pharmacy.prescriptions.dispense", ":id") }}'.replace(':id', medicationId),
            method: 'POST',
            data: formData + '&_token={{ csrf_token() }}',
            success: function(response) {
                if (response.error) {
                    alert('خطأ: ' + response.message);
                } else {
                    alert('تم صرف الدواء بنجاح');
                    location.reload();
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'حدث خطأ أثناء الصرف';
                alert('خطأ: ' + message);
            }
        });
    });
});
</script>
@endpush

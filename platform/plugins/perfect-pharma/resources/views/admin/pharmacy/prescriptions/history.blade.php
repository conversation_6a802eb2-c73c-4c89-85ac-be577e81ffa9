@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">تاريخ صرف الأدوية - {{ $pharmacy->name }}</h4>
                </div>
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">البحث:</label>
                                    <input type="text" class="form-control" id="search" 
                                           name="search" value="{{ request('search') }}"
                                           placeholder="اسم المريض أو الدواء أو رقم الصرف">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="start_date">من تاريخ:</label>
                                    <input type="date" class="form-control" id="start_date" 
                                           name="start_date" value="{{ request('start_date') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="end_date">إلى تاريخ:</label>
                                    <input type="date" class="form-control" id="end_date" 
                                           name="end_date" value="{{ request('end_date') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="payment_method">طريقة الدفع:</label>
                                    <select class="form-control" id="payment_method" name="payment_method">
                                        <option value="">جميع الطرق</option>
                                        <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>نقداً</option>
                                        <option value="wallet" {{ request('payment_method') == 'wallet' ? 'selected' : '' }}>محفظة إلكترونية</option>
                                        <option value="insurance" {{ request('payment_method') == 'insurance' ? 'selected' : '' }}>تأمين</option>
                                        <option value="credit_card" {{ request('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ti ti-search"></i> بحث
                                        </button>
                                        <a href="{{ route('admin.pharmacy.prescriptions.history') }}" 
                                           class="btn btn-secondary">
                                            <i class="ti ti-refresh"></i> إعادة تعيين
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- جدول تاريخ الصرف -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الصرف</th>
                                    <th>المريض</th>
                                    <th>الدواء</th>
                                    <th>الكمية</th>
                                    <th>السعر الإجمالي</th>
                                    <th>الخصم</th>
                                    <th>السعر النهائي</th>
                                    <th>طريقة الدفع</th>
                                    <th>الصيدلي</th>
                                    <th>تاريخ الصرف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($dispensings as $dispensing)
                                    <tr>
                                        <td>
                                            <strong>{{ $dispensing->dispensing_number }}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $dispensing->prescriptionMedication->prescription->patient->customer->name }}</strong>
                                                <br>
                                                <small class="text-muted">
                                                    {{ $dispensing->prescriptionMedication->prescription->patient->customer->phone }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $dispensing->prescriptionMedication->medication_name }}</strong>
                                                @if($dispensing->prescriptionMedication->product)
                                                    <br>
                                                    <small class="text-muted">
                                                        كود المنتج: {{ $dispensing->prescriptionMedication->product->sku }}
                                                    </small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">
                                                {{ $dispensing->quantity_dispensed }}
                                            </span>
                                        </td>
                                        <td>{{ number_format($dispensing->total_price, 2) }} ج.م</td>
                                        <td>
                                            @if($dispensing->discount_amount > 0)
                                                <span class="text-success">
                                                    {{ number_format($dispensing->discount_amount, 2) }} ج.م
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <strong class="text-success">
                                                {{ number_format($dispensing->final_price, 2) }} ج.م
                                            </strong>
                                        </td>
                                        <td>
                                            @php
                                                $paymentMethods = [
                                                    'cash' => 'نقداً',
                                                    'wallet' => 'محفظة إلكترونية',
                                                    'insurance' => 'تأمين',
                                                    'credit_card' => 'بطاقة ائتمان'
                                                ];
                                            @endphp
                                            <span class="badge badge-info">
                                                {{ $paymentMethods[$dispensing->payment_method] ?? $dispensing->payment_method }}
                                            </span>
                                        </td>
                                        <td>{{ $dispensing->pharmacist->name }}</td>
                                        <td>
                                            {{ \Carbon\Carbon::parse($dispensing->dispensing_date)->format('Y-m-d') }}
                                            <br>
                                            <small class="text-muted">
                                                {{ $dispensing->created_at->format('H:i') }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.pharmacy.prescriptions.show', $dispensing->prescriptionMedication->prescription_id) }}" 
                                                   class="btn btn-sm btn-info" title="عرض الوصفة">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.pharmacy.prescriptions.print', $dispensing->id) }}" 
                                                   class="btn btn-sm btn-success" title="طباعة الإيصال" target="_blank">
                                                    <i class="ti ti-printer"></i>
                                                </a>
                                                @if($dispensing->pharmacist_notes)
                                                    <button class="btn btn-sm btn-warning view-notes-btn" 
                                                            title="عرض الملاحظات"
                                                            data-notes="{{ $dispensing->pharmacist_notes }}">
                                                        <i class="ti ti-note"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="11" class="text-center">
                                            <div class="alert alert-info mb-0">
                                                <i class="ti ti-info-circle"></i> لا توجد عمليات صرف مسجلة
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- الترقيم -->
                    @if($dispensings->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            {{ $dispensings->appends(request()->query())->links() }}
                        </div>
                    @endif

                    <!-- ملخص الصفحة الحالية -->
                    @if($dispensings->count() > 0)
                        <div class="card mt-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-primary">{{ $dispensings->count() }}</h5>
                                            <p class="mb-0">عمليات صرف في هذه الصفحة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-success">
                                                {{ number_format($dispensings->sum('final_price'), 2) }} ج.م
                                            </h5>
                                            <p class="mb-0">إجمالي المبيعات</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-info">
                                                {{ $dispensings->sum('quantity_dispensed') }}
                                            </h5>
                                            <p class="mb-0">إجمالي الكمية المصروفة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-warning">
                                                {{ number_format($dispensings->sum('discount_amount'), 2) }} ج.م
                                            </h5>
                                            <p class="mb-0">إجمالي الخصومات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal عرض الملاحظات -->
    <div class="modal fade" id="notesModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ملاحظات الصيدلي</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p id="notes_content"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // عرض ملاحظات الصيدلي
    $('.view-notes-btn').click(function() {
        const notes = $(this).data('notes');
        $('#notes_content').text(notes);
        $('#notesModal').modal('show');
    });
    
    // تحسين تجربة البحث
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            // يمكن إضافة بحث فوري هنا إذا لزم الأمر
        }, 500);
    });
    
    // تلوين الصفوف حسب طريقة الدفع
    $('tbody tr').each(function() {
        const paymentMethod = $(this).find('td:nth-child(8) .badge').text().trim();
        
        switch(paymentMethod) {
            case 'تأمين':
                $(this).addClass('table-success');
                break;
            case 'محفظة إلكترونية':
                $(this).addClass('table-info');
                break;
        }
    });
});
</script>

<style>
.table-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.table-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
}

.btn-group .btn {
    margin-right: 2px;
}

.badge {
    font-size: 0.75em;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
    }
}
</style>
@endpush

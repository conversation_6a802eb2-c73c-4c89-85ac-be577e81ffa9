@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">إحصائيات صرف الأدوية - {{ $pharmacy->name }}</h4>
                </div>
                <div class="card-body">
                    <!-- فلاتر التاريخ -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="start_date">من تاريخ:</label>
                                    <input type="date" class="form-control" id="start_date" 
                                           name="start_date" value="{{ $startDate }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="end_date">إلى تاريخ:</label>
                                    <input type="date" class="form-control" id="end_date" 
                                           name="end_date" value="{{ $endDate }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ti ti-filter"></i> تطبيق الفلتر
                                        </button>
                                        <a href="{{ route('admin.pharmacy.prescriptions.statistics') }}" 
                                           class="btn btn-secondary">
                                            <i class="ti ti-refresh"></i> إعادة تعيين
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- الإحصائيات الرئيسية -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($stats['total_dispensings']) }}</h4>
                                            <p class="mb-0">إجمالي عمليات الصرف</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="ti ti-pill fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($stats['total_revenue'], 2) }} ج.م</h4>
                                            <p class="mb-0">إجمالي الإيرادات</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="ti ti-currency-dollar fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($stats['total_patients']) }}</h4>
                                            <p class="mb-0">عدد المرضى</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="ti ti-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">
                                                {{ $stats['total_dispensings'] > 0 ? number_format($stats['total_revenue'] / $stats['total_dispensings'], 2) : 0 }} ج.م
                                            </h4>
                                            <p class="mb-0">متوسط قيمة الصرف</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="ti ti-chart-line fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أكثر الأدوية صرفاً -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="ti ti-trending-up"></i> أكثر الأدوية صرفاً
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($stats['top_medications']->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>اسم الدواء</th>
                                                <th>إجمالي الكمية المصروفة</th>
                                                <th>النسبة المئوية</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @php
                                                $totalQuantity = $stats['top_medications']->sum('total_quantity');
                                            @endphp
                                            @foreach($stats['top_medications'] as $index => $medication)
                                                @php
                                                    $percentage = $totalQuantity > 0 ? ($medication->total_quantity / $totalQuantity) * 100 : 0;
                                                @endphp
                                                <tr>
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>{{ $medication->medication_name }}</td>
                                                    <td>{{ number_format($medication->total_quantity) }}</td>
                                                    <td>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar" role="progressbar" 
                                                                 style="width: {{ $percentage }}%">
                                                                {{ number_format($percentage, 1) }}%
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle"></i> لا توجد بيانات صرف في الفترة المحددة
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- رسم بياني للإيرادات اليومية -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="ti ti-chart-bar"></i> الإيرادات اليومية
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="100"></canvas>
                        </div>
                    </div>

                    <!-- تقرير مفصل -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="ti ti-file-text"></i> تقرير مفصل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>معلومات الفترة:</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>من:</strong> {{ $startDate }}</li>
                                        <li><strong>إلى:</strong> {{ $endDate }}</li>
                                        <li><strong>عدد الأيام:</strong> {{ \Carbon\Carbon::parse($startDate)->diffInDays(\Carbon\Carbon::parse($endDate)) + 1 }}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>متوسطات يومية:</h6>
                                    @php
                                        $days = \Carbon\Carbon::parse($startDate)->diffInDays(\Carbon\Carbon::parse($endDate)) + 1;
                                        $avgDispensings = $days > 0 ? $stats['total_dispensings'] / $days : 0;
                                        $avgRevenue = $days > 0 ? $stats['total_revenue'] / $days : 0;
                                        $avgPatients = $days > 0 ? $stats['total_patients'] / $days : 0;
                                    @endphp
                                    <ul class="list-unstyled">
                                        <li><strong>متوسط الصرف اليومي:</strong> {{ number_format($avgDispensings, 1) }}</li>
                                        <li><strong>متوسط الإيرادات اليومية:</strong> {{ number_format($avgRevenue, 2) }} ج.م</li>
                                        <li><strong>متوسط المرضى اليومي:</strong> {{ number_format($avgPatients, 1) }}</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <a href="{{ route('admin.pharmacy.prescriptions.history') }}" 
                                   class="btn btn-primary">
                                    <i class="ti ti-history"></i> عرض تاريخ الصرف التفصيلي
                                </a>
                                <button class="btn btn-success" onclick="window.print()">
                                    <i class="ti ti-printer"></i> طباعة التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // إنشاء الرسم البياني للإيرادات اليومية
    const ctx = document.getElementById('revenueChart').getContext('2d');
    
    // بيانات وهمية للرسم البياني - يمكن استبدالها ببيانات حقيقية من الخادم
    const chartData = {
        labels: generateDateLabels('{{ $startDate }}', '{{ $endDate }}'),
        datasets: [{
            label: 'الإيرادات اليومية (ج.م)',
            data: generateRandomData(generateDateLabels('{{ $startDate }}', '{{ $endDate }}').length),
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2,
            fill: true
        }]
    };
    
    new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'الإيرادات اليومية'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + ' ج.م';
                        }
                    }
                }
            }
        }
    });
    
    function generateDateLabels(startDate, endDate) {
        const labels = [];
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
            labels.push(d.toLocaleDateString('ar-EG'));
        }
        
        return labels;
    }
    
    function generateRandomData(length) {
        const data = [];
        const baseRevenue = {{ $stats['total_revenue'] }} / length;
        
        for (let i = 0; i < length; i++) {
            // توليد بيانات عشوائية حول المتوسط
            const variation = (Math.random() - 0.5) * baseRevenue * 0.5;
            data.push(Math.max(0, baseRevenue + variation));
        }
        
        return data;
    }
});

// تنسيق الطباعة
@media print {
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
    
    .btn {
        display: none !important;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
    }
}
</script>
@endpush

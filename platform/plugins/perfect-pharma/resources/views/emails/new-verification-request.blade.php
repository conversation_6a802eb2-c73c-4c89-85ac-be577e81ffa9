<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب تحقق جديد</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .content {
            padding: 40px 30px;
        }
        .alert {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .alert h2 {
            color: #856404;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .alert p {
            color: #856404;
            margin: 0;
            font-size: 16px;
        }
        .customer-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .customer-details h3 {
            color: #495057;
            margin: 0 0 15px 0;
            font-size: 18px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-item:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .detail-value {
            color: #6c757d;
        }
        .user-type-badge {
            display: inline-block;
            padding: 5px 15px;
            background-color: #007bff;
            color: white;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
        }
        .priority-notice {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .priority-notice h4 {
            color: #721c24;
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .priority-notice p {
            color: #721c24;
            margin: 0;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin: 5px 0;
            color: #6c757d;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            .header, .content, .footer {
                padding: 20px;
            }
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            .detail-value {
                margin-top: 5px;
            }
            .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="icon">🔔</div>
            <h1>Perfect Pharma</h1>
            <p>إشعار إداري - طلب تحقق جديد</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Alert -->
            <div class="alert">
                <h2>📋 طلب تحقق جديد</h2>
                <p>تم تسجيل مستخدم جديد ويحتاج إلى مراجعة وموافقة</p>
            </div>

            <!-- Greeting -->
            <p style="font-size: 18px; margin-bottom: 20px;">
                مرحباً فريق الإدارة،
            </p>

            <p style="margin-bottom: 25px;">
                تم تسجيل مستخدم جديد في منصة Perfect Pharma ويحتاج إلى مراجعة طلب التحقق والموافقة عليه. 
                يرجى مراجعة التفاصيل أدناه واتخاذ الإجراء المناسب.
            </p>

            <!-- Customer Details -->
            <div class="customer-details">
                <h3>تفاصيل المستخدم</h3>
                <div class="detail-item">
                    <span class="detail-label">الاسم الكامل:</span>
                    <span class="detail-value"><strong>{{ $customerName }}</strong></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">البريد الإلكتروني:</span>
                    <span class="detail-value">{{ $customerEmail }}</span>
                </div>
                @if($customerPhone)
                <div class="detail-item">
                    <span class="detail-label">رقم الهاتف:</span>
                    <span class="detail-value">{{ $customerPhone }}</span>
                </div>
                @endif
                <div class="detail-item">
                    <span class="detail-label">نوع الحساب:</span>
                    <span class="detail-value">
                        <span class="user-type-badge">{{ $userTypeName }}</span>
                    </span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">تاريخ التسجيل:</span>
                    <span class="detail-value">{{ $registeredAt->format('Y-m-d H:i') }}</span>
                </div>
            </div>

            <!-- Priority Notice -->
            <div class="priority-notice">
                <h4>⏰ مطلوب إجراء سريع</h4>
                <p>
                    يُنصح بمراجعة طلبات التحقق خلال 24-48 ساعة لضمان تجربة مستخدم ممتازة 
                    والحفاظ على سمعة المنصة.
                </p>
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <p style="margin-bottom: 20px;">اتخذ الإجراء المناسب الآن</p>
                <a href="{{ $verificationUrl }}" class="btn">
                    👁️ مراجعة الطلب
                </a>
                <a href="{{ $dashboardUrl }}" class="btn btn-secondary">
                    📊 لوحة إدارة التحقق
                </a>
            </div>

            <!-- Instructions -->
            <div style="background-color: #e8f4fd; border-radius: 8px; padding: 20px; margin-top: 30px;">
                <h4 style="color: #0c5460; margin: 0 0 15px 0;">📝 خطوات المراجعة:</h4>
                <ol style="color: #0c5460; margin: 0; padding-right: 20px;">
                    <li>مراجعة معلومات المستخدم الأساسية</li>
                    <li>فحص المستندات المرفوعة (إن وجدت)</li>
                    <li>التحقق من صحة ووضوح المستندات</li>
                    <li>اتخاذ قرار الموافقة أو الرفض مع توضيح السبب</li>
                    <li>إرسال الإشعار للمستخدم</li>
                </ol>
            </div>

            <!-- Statistics -->
            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 20px; text-align: center;">
                <h4 style="color: #495057; margin: 0 0 10px 0;">📈 إحصائيات سريعة</h4>
                <p style="color: #6c757d; margin: 0; font-size: 14px;">
                    يمكنك مراجعة جميع الإحصائيات والتقارير في لوحة إدارة التحقق
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Perfect Pharma - نظام الإدارة</strong></p>
            <p>منصة الخدمات الطبية المتكاملة</p>
            
            <p style="font-size: 12px; color: #adb5bd; margin-top: 15px;">
                هذه رسالة تلقائية من نظام Perfect Pharma.
                <br>
                تم إرسالها في: {{ now()->format('Y-m-d H:i:s') }}
            </p>
        </div>
    </div>
</body>
</html>

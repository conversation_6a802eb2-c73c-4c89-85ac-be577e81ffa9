<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم الموافقة على طلب التحقق</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .content {
            padding: 40px 30px;
        }
        .success-message {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .success-message h2 {
            color: #155724;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .success-message p {
            color: #155724;
            margin: 0;
            font-size: 16px;
        }
        .details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .details h3 {
            color: #495057;
            margin: 0 0 15px 0;
            font-size: 18px;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-item:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .detail-value {
            color: #6c757d;
        }
        .benefits {
            background-color: #e8f5e8;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .benefits h3 {
            color: #155724;
            margin: 0 0 15px 0;
            font-size: 18px;
        }
        .benefits ul {
            margin: 0;
            padding-right: 20px;
            color: #155724;
        }
        .benefits li {
            margin-bottom: 8px;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        .footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin: 5px 0;
            color: #6c757d;
            font-size: 14px;
        }
        .social-links {
            margin: 15px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6c757d;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            .header, .content, .footer {
                padding: 20px;
            }
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            .detail-value {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="icon">✅</div>
            <h1>Perfect Pharma</h1>
            <p>منصة الخدمات الطبية المتكاملة</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Success Message -->
            <div class="success-message">
                <h2>🎉 مبروك! تم الموافقة على طلب التحقق</h2>
                <p>تم تفعيل حسابك بنجاح ويمكنك الآن الاستفادة من جميع الخدمات</p>
            </div>

            <!-- Greeting -->
            <p style="font-size: 18px; margin-bottom: 20px;">
                مرحباً <strong>{{ $customerName }}</strong>،
            </p>

            <p style="margin-bottom: 25px;">
                يسعدنا إبلاغك بأنه تم الموافقة على طلب التحقق الخاص بك في منصة Perfect Pharma. 
                حسابك الآن مفعل بالكامل ويمكنك الاستفادة من جميع الخدمات والمزايا المتاحة.
            </p>

            <!-- Details -->
            <div class="details">
                <h3>تفاصيل التحقق</h3>
                <div class="detail-item">
                    <span class="detail-label">نوع الحساب:</span>
                    <span class="detail-value">{{ $userTypeName }}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">تاريخ الموافقة:</span>
                    <span class="detail-value">{{ $verifiedAt->format('Y-m-d H:i') }}</span>
                </div>
                @if($notes)
                <div class="detail-item">
                    <span class="detail-label">ملاحظات:</span>
                    <span class="detail-value">{{ $notes }}</span>
                </div>
                @endif
            </div>

            <!-- Benefits -->
            <div class="benefits">
                <h3>المزايا المتاحة لك الآن:</h3>
                <ul>
                    <li>الوصول لجميع المنتجات والخدمات الطبية</li>
                    <li>الاستفادة من الخصومات الخاصة بنوع حسابك</li>
                    <li>إدارة متقدمة للطلبات والمعاملات</li>
                    <li>دعم فني متخصص على مدار الساعة</li>
                    <li>تقارير وإحصائيات مفصلة</li>
                </ul>
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <p style="margin-bottom: 20px;">ابدأ الآن في استخدام منصة Perfect Pharma</p>
                <a href="{{ $dashboardUrl }}" class="btn">
                    🚀 الذهاب للوحة التحكم
                </a>
            </div>

            <!-- Additional Info -->
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-top: 30px;">
                <h4 style="color: #856404; margin: 0 0 10px 0;">💡 نصائح للبداية:</h4>
                <ul style="color: #856404; margin: 0; padding-right: 20px;">
                    <li>قم بتحديث معلومات ملفك الشخصي</li>
                    <li>استكشف الخدمات المتاحة في لوحة التحكم</li>
                    <li>تواصل معنا في حالة وجود أي استفسارات</li>
                </ul>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Perfect Pharma</strong></p>
            <p>منصة الخدمات الطبية المتكاملة</p>
            
            <div class="social-links">
                <a href="#">📧 البريد الإلكتروني</a>
                <a href="#">📱 واتساب</a>
                <a href="#">🌐 الموقع الإلكتروني</a>
            </div>
            
            <p style="font-size: 12px; color: #adb5bd;">
                هذه رسالة تلقائية، يرجى عدم الرد عليها مباشرة.
                <br>
                للدعم الفني: <EMAIL>
            </p>
        </div>
    </div>
</body>
</html>

@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-calendar me-2"></i>
                        {{ trans('plugins/perfect-pharma::settings.appointments.title') }}
                    </h4>
                    <p class="card-subtitle mb-0">
                        {{ trans('plugins/perfect-pharma::settings.appointments.description') }}
                    </p>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('perfect-pharma.settings.appointments.update') }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.appointments.advance_booking_days') }}</label>
                                    <input type="number" class="form-control" name="appointment_advance_booking_days" 
                                           value="{{ $data['appointment_advance_booking_days'] ?? 30 }}" 
                                           min="1" max="365">
                                    <small class="text-muted">{{ trans('plugins/perfect-pharma::settings.appointments.advance_booking_days_help') }}</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.appointments.cancellation_hours') }}</label>
                                    <input type="number" class="form-control" name="appointment_cancellation_hours" 
                                           value="{{ $data['appointment_cancellation_hours'] ?? 24 }}" 
                                           min="1" max="168">
                                    <small class="text-muted">{{ trans('plugins/perfect-pharma::settings.appointments.cancellation_hours_help') }}</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.appointments.reminder_hours') }}</label>
                                    <input type="number" class="form-control" name="appointment_reminder_hours" 
                                           value="{{ $data['appointment_reminder_hours'] ?? 2 }}" 
                                           min="1" max="72">
                                    <small class="text-muted">{{ trans('plugins/perfect-pharma::settings.appointments.reminder_hours_help') }}</small>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="appointment_auto_confirm" 
                                               value="1" {{ ($data['appointment_auto_confirm'] ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label">
                                            {{ trans('plugins/perfect-pharma::settings.appointments.auto_confirm') }}
                                        </label>
                                    </div>
                                    <small class="text-muted">{{ trans('plugins/perfect-pharma::settings.appointments.auto_confirm_help') }}</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('header')
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0 !important;
            border: none;
        }
        
        .card-title {
            color: white;
            margin-bottom: 0.5rem;
        }
        
        .card-subtitle {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
    </style>
@endpush

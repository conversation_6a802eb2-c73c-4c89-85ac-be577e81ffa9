@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-settings me-2"></i>
                        {{ trans('plugins/perfect-pharma::settings.general.title') }}
                    </h4>
                    <p class="card-subtitle mb-0">
                        {{ trans('plugins/perfect-pharma::settings.general.description') }}
                    </p>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('perfect-pharma.settings.general.update') }}">
                        @csrf
                        @method('PUT')

                        {{-- إعدادات النظام العامة --}}
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">معلومات النظام</h5>

                                <div class="mb-3">
                                    <label class="form-label">اسم النظام</label>
                                    <input type="text" class="form-control" name="system_name"
                                           value="{{ $data['system_name'] ?? 'Perfect Pharma' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">وصف النظام</label>
                                    <textarea class="form-control" name="system_description" rows="3">{{ $data['system_description'] ?? 'نظام طبي متكامل' }}</textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="system_email"
                                           value="{{ $data['system_email'] ?? '<EMAIL>' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" name="system_phone"
                                           value="{{ $data['system_phone'] ?? '+966501234567' }}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="system_address" rows="2">{{ $data['system_address'] ?? 'الرياض، المملكة العربية السعودية' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <hr>

                        {{-- إعدادات العملة --}}
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">إعدادات العملة والتسعير</h5>
                            </div>

                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle me-2"></i>
                                    <strong>إعدادات العملة:</strong> تم دمج إعدادات العملة مع النظام الموحد.
                                    <br>
                                    يمكنك إدارة العملات من: <a href="{{ route('ecommerce.settings.currencies') }}" class="alert-link">إعدادات الإيكومرس > العملات</a>
                                    <br>
                                    العملة الحالية: <strong>{{ perfect_pharma_currency_code() }} ({{ perfect_pharma_currency_symbol() }})</strong>
                                    <br>
                                    مثال على التنسيق: <strong>{{ perfect_pharma_format_currency(100) }}</strong>
                                </div>
                            </div>
                        </div>



                            <div class="col-md-6">
                                <h5 class="mb-3">إعدادات الأطباء</h5>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="doctor_verification_required"
                                               value="1" {{ ($data['doctor_verification_required'] ?? true) ? 'checked' : '' }}>
                                        <label class="form-check-label">يتطلب تحقق الأطباء</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="doctor_auto_approval"
                                               value="1" {{ ($data['doctor_auto_approval'] ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label">الموافقة التلقائية للأطباء</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">نسبة عمولة الأطباء (%)</label>
                                    <input type="number" class="form-control" name="doctor_commission_rate"
                                           value="{{ $data['doctor_commission_rate'] ?? 10 }}"
                                           min="0" max="100" step="0.1">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى لرسوم الاستشارة (ريال)</label>
                                    <input type="number" class="form-control" name="doctor_min_consultation_fee"
                                           value="{{ $data['doctor_min_consultation_fee'] ?? 50 }}"
                                           min="0" step="1">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الحد الأقصى لرسوم الاستشارة (ريال)</label>
                                    <input type="number" class="form-control" name="doctor_max_consultation_fee"
                                           value="{{ $data['doctor_max_consultation_fee'] ?? 1000 }}"
                                           min="0" step="1">
                                </div>
                            </div>
                        </div>

                        <hr>

                        {{-- إعدادات الصيدليات والمعامل --}}
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">إعدادات الصيدليات</h5>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="pharmacy_verification_required"
                                               value="1" {{ ($data['pharmacy_verification_required'] ?? true) ? 'checked' : '' }}>
                                        <label class="form-check-label">يتطلب تحقق الصيدليات</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="pharmacy_auto_approval"
                                               value="1" {{ ($data['pharmacy_auto_approval'] ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label">الموافقة التلقائية للصيدليات</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">نسبة عمولة الصيدليات (%)</label>
                                    <input type="number" class="form-control" name="pharmacy_commission_rate"
                                           value="{{ $data['pharmacy_commission_rate'] ?? 5 }}"
                                           min="0" max="100" step="0.1">
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="pharmacy_delivery_enabled"
                                               value="1" {{ ($data['pharmacy_delivery_enabled'] ?? true) ? 'checked' : '' }}>
                                        <label class="form-check-label">تفعيل خدمة التوصيل</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">رسوم التوصيل (ريال)</label>
                                    <input type="number" class="form-control" name="pharmacy_delivery_fee"
                                           value="{{ $data['pharmacy_delivery_fee'] ?? 15 }}"
                                           min="0" step="1">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5 class="mb-3">إعدادات المعامل</h5>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="lab_verification_required"
                                               value="1" {{ ($data['lab_verification_required'] ?? true) ? 'checked' : '' }}>
                                        <label class="form-check-label">يتطلب تحقق المعامل</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="lab_auto_approval"
                                               value="1" {{ ($data['lab_auto_approval'] ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label">الموافقة التلقائية للمعامل</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">نسبة عمولة المعامل (%)</label>
                                    <input type="number" class="form-control" name="lab_commission_rate"
                                           value="{{ $data['lab_commission_rate'] ?? 8 }}"
                                           min="0" max="100" step="0.1">
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="lab_home_service_enabled"
                                               value="1" {{ ($data['lab_home_service_enabled'] ?? true) ? 'checked' : '' }}>
                                        <label class="form-check-label">تفعيل الخدمة المنزلية</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">رسوم الخدمة المنزلية (ريال)</label>
                                    <input type="number" class="form-control" name="lab_home_service_fee"
                                           value="{{ $data['lab_home_service_fee'] ?? 25 }}"
                                           min="0" step="1">
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('header')
    <style>
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0;
        }
        
        .card-title {
            color: white;
            margin-bottom: 0.5rem;
        }
        
        .card-subtitle {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .collapsible-header {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .collapsible-header:hover {
            background-color: #e9ecef;
        }
        
        .collapsible-header h5 {
            margin: 0;
            color: #495057;
        }
        
        .collapsible-content {
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            padding: 1.5rem;
            background-color: #fff;
        }
        
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #667eea;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .number-input {
            max-width: 200px;
        }
        
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #ced4da;
        }
    </style>
@endpush

@push('footer')
    <script>
        $(document).ready(function() {
            // تفعيل الـ Collapsible
            $('.collapsible-header').click(function() {
                $(this).next('.collapsible-content').slideToggle();
                $(this).find('i').toggleClass('ti-chevron-down ti-chevron-up');
            });
            
            // تفعيل الـ Tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();
            
            // تحسين تجربة المستخدم للنماذج
            $('.form-control').on('focus', function() {
                $(this).closest('.form-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.form-group').removeClass('focused');
            });
            
            // التحقق من صحة البيانات
            $('form').on('submit', function(e) {
                let isValid = true;
                
                // التحقق من الحقول المطلوبة
                $(this).find('[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                        $(this).focus();
                        return false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                // التحقق من صحة النسب المئوية
                $(this).find('input[type="number"][max="100"]').each(function() {
                    const value = parseFloat($(this).val());
                    if (value > 100) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                        alert('النسبة المئوية يجب أن تكون أقل من أو تساوي 100%');
                        $(this).focus();
                        return false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                }
            });
            
            // إضافة تأثيرات بصرية للحفظ
            $('button[type="submit"]').on('click', function() {
                const $btn = $(this);
                const originalText = $btn.text();
                
                $btn.prop('disabled', true)
                    .html('<i class="ti ti-loader-2 me-2"></i>جاري الحفظ...');
                
                setTimeout(function() {
                    $btn.prop('disabled', false).text(originalText);
                }, 2000);
            });
        });
    </script>
@endpush

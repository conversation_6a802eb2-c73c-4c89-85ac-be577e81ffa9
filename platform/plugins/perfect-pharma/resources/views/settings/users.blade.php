@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-users me-2"></i>
                        {{ trans('plugins/perfect-pharma::settings.users.title') }}
                    </h4>
                    <p class="card-subtitle mb-0">
                        {{ trans('plugins/perfect-pharma::settings.users.description') }}
                    </p>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('perfect-pharma.settings.users.update') }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">
                                    <i class="ti ti-percentage me-2"></i>
                                    {{ trans('plugins/perfect-pharma::settings.users.discount_rates') }}
                                </h5>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.users.patient_discount') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount_patient" 
                                               value="{{ $data['discount_patient'] ?? 0 }}" 
                                               min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.users.doctor_discount') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount_doctor" 
                                               value="{{ $data['discount_doctor'] ?? 15 }}" 
                                               min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.users.pharmacy_discount') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount_pharmacy" 
                                               value="{{ $data['discount_pharmacy'] ?? 10 }}" 
                                               min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.users.lab_discount') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount_lab" 
                                               value="{{ $data['discount_lab'] ?? 12 }}" 
                                               min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="mb-3">&nbsp;</h5>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.users.clinic_discount') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount_clinic" 
                                               value="{{ $data['discount_clinic'] ?? 18 }}" 
                                               min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.users.hospital_discount') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount_hospital" 
                                               value="{{ $data['discount_hospital'] ?? 20 }}" 
                                               min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ trans('plugins/perfect-pharma::settings.users.drug_supplier_discount') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount_drug_supplier" 
                                               value="{{ $data['discount_drug_supplier'] ?? 8 }}" 
                                               min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> هذه النسب تُطبق على جميع المنتجات التي تم تفعيل نظام الخصومات عليها.
                                    المرضى العاديون لا يحصلون على خصومات بشكل افتراضي.
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('header')
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0 !important;
            border: none;
        }
        
        .card-title {
            color: white;
            margin-bottom: 0.5rem;
        }
        
        .card-subtitle {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #ced4da;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .alert-info {
            background-color: #e7f3ff;
            border-color: #b8daff;
            color: #004085;
        }
    </style>
@endpush

@push('footer')
    <script>
        $(document).ready(function() {
            // التحقق من صحة البيانات
            $('form').on('submit', function(e) {
                let isValid = true;
                
                // التحقق من النسب المئوية
                $(this).find('input[type="number"]').each(function() {
                    const value = parseFloat($(this).val());
                    if (value > 100) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                        alert('النسبة المئوية يجب أن تكون أقل من أو تساوي 100%');
                        $(this).focus();
                        return false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                }
            });
            
            // إضافة تأثيرات بصرية للحفظ
            $('button[type="submit"]').on('click', function() {
                const $btn = $(this);
                const originalText = $btn.html();
                
                $btn.prop('disabled', true)
                    .html('<i class="ti ti-loader-2 me-2"></i>جاري الحفظ...');
                
                setTimeout(function() {
                    $btn.prop('disabled', false).html(originalText);
                }, 2000);
            });
        });
    </script>
@endpush

@extends('plugins/perfect-pharma::doctor.layout')

@section('title', 'البحث عن التاريخ المرضي')
@section('page-title', 'التاريخ المرضي للمرضى')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    البحث عن التاريخ المرضي للمرضى
                </h5>
                <p class="text-muted mb-0 mt-2">
                    يمكنك الاطلاع على التاريخ المرضي الكامل للمريض من جميع الأطباء المشتركين في النظام
                </p>
            </div>
            <div class="card-body">
                <!-- نموذج البحث -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   id="searchInput" 
                                   placeholder="أدخل رقم الهاتف أو رقم البطاقة الشخصية للمريض">
                            <button class="btn btn-primary" type="button" id="searchBtn">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            يمكنك البحث برقم الهاتف أو رقم البطاقة الشخصية للمريض
                        </small>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" id="clearBtn">
                                <i class="fas fa-times me-1"></i>
                                مسح النتائج
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- تنبيه الخصوصية -->
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-shield-alt me-2"></i>
                        تنبيه الخصوصية الطبية
                    </h6>
                    <p class="mb-0">
                        يرجى التأكد من موافقة المريض قبل الاطلاع على تاريخه المرضي. 
                        جميع المعلومات الطبية محمية بموجب قوانين الخصوصية الطبية.
                    </p>
                </div>
                
                <!-- منطقة النتائج -->
                <div id="searchResults" style="display: none;">
                    <!-- معلومات المريض -->
                    <div id="patientInfo" class="alert alert-info">
                        <h6 class="mb-3">
                            <i class="fas fa-user me-2"></i>
                            معلومات المريض
                        </h6>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>الاسم:</strong> <span id="patientName"></span>
                            </div>
                            <div class="col-md-3">
                                <strong>الهاتف:</strong> <span id="patientPhone"></span>
                            </div>
                            <div class="col-md-3">
                                <strong>رقم البطاقة:</strong> <span id="patientNationalId"></span>
                            </div>
                            <div class="col-md-3">
                                <strong>الجنس:</strong> <span id="patientGender"></span>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-3">
                                <strong>تاريخ الميلاد:</strong> <span id="patientDob"></span>
                            </div>
                            <div class="col-md-3">
                                <strong>البريد الإلكتروني:</strong> <span id="patientEmail"></span>
                            </div>
                            <div class="col-md-6">
                                <div class="text-end">
                                    <button type="button" class="btn btn-success" id="viewHistoryBtn">
                                        <i class="fas fa-history me-1"></i>
                                        عرض التاريخ المرضي الكامل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- رسالة عدم وجود نتائج -->
                <div id="noResults" class="text-center py-5" style="display: none;">
                    <i class="fas fa-user-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لم يتم العثور على مريض</h5>
                    <p class="text-muted">تأكد من صحة رقم الهاتف أو رقم البطاقة</p>
                </div>
                
                <!-- رسالة عدم وجود تاريخ طبي -->
                <div id="noHistory" class="text-center py-5" style="display: none;">
                    <i class="fas fa-file-medical fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد تاريخ طبي</h5>
                    <p class="text-muted">هذا المريض لا يملك تاريخ طبي في النظام بعد</p>
                </div>
                
                <!-- رسالة التحميل -->
                <div id="loadingMessage" class="text-center py-5" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري البحث...</span>
                    </div>
                    <p class="mt-3 text-muted">جاري البحث عن المريض...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    ما يمكنك رؤيته
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i> جميع المواعيد السابقة</li>
                    <li><i class="fas fa-check text-success me-2"></i> الوصفات الطبية من جميع الأطباء</li>
                    <li><i class="fas fa-check text-success me-2"></i> نتائج التحاليل المخبرية</li>
                    <li><i class="fas fa-check text-success me-2"></i> التشخيصات السابقة</li>
                    <li><i class="fas fa-check text-success me-2"></i> الحالات المزمنة والحساسيات</li>
                    <li><i class="fas fa-check text-success me-2"></i> العمليات الجراحية السابقة</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    إرشادات مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-shield-alt text-warning me-2"></i> احترم خصوصية المريض</li>
                    <li><i class="fas fa-user-check text-warning me-2"></i> تأكد من موافقة المريض</li>
                    <li><i class="fas fa-eye-slash text-warning me-2"></i> لا تشارك المعلومات مع الغير</li>
                    <li><i class="fas fa-lock text-warning me-2"></i> استخدم المعلومات للعلاج فقط</li>
                    <li><i class="fas fa-file-medical text-warning me-2"></i> وثق استخدامك للمعلومات</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let currentPatientId = null;
    
    // البحث عند الضغط على زر البحث
    $('#searchBtn').click(function() {
        performSearch();
    });
    
    // البحث عند الضغط على Enter
    $('#searchInput').keypress(function(e) {
        if (e.which == 13) {
            performSearch();
        }
    });
    
    // مسح النتائج
    $('#clearBtn').click(function() {
        clearResults();
    });
    
    // عرض التاريخ المرضي الكامل
    $('#viewHistoryBtn').click(function() {
        if (currentPatientId) {
            window.location.href = `{{ route('doctor.medical-history.patient', '') }}/${currentPatientId}`;
        }
    });
    
    function performSearch() {
        const searchTerm = $('#searchInput').val().trim();
        
        if (!searchTerm) {
            alert('يرجى إدخال رقم الهاتف أو رقم البطاقة');
            return;
        }
        
        // إظهار رسالة التحميل
        $('#loadingMessage').show();
        $('#searchResults').hide();
        $('#noResults').hide();
        $('#noHistory').hide();
        
        // إجراء البحث
        $.get('{{ route("doctor.medical-history.search-patient") }}', {
            search: searchTerm
        })
        .done(function(response) {
            if (response.success) {
                displayPatientInfo(response.patient);
            }
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON?.error || 'حدث خطأ أثناء البحث';
            
            if (xhr.status === 404) {
                if (error.includes('تاريخ طبي')) {
                    showNoHistory();
                } else {
                    showNoResults();
                }
            } else {
                alert(error);
                showNoResults();
            }
        })
        .always(function() {
            $('#loadingMessage').hide();
        });
    }
    
    function displayPatientInfo(patient) {
        currentPatientId = patient.id;
        
        // عرض معلومات المريض
        $('#patientName').text(patient.name);
        $('#patientPhone').text(patient.phone);
        $('#patientNationalId').text(patient.national_id || 'غير محدد');
        $('#patientGender').text(patient.gender === 'male' ? 'ذكر' : (patient.gender === 'female' ? 'أنثى' : 'غير محدد'));
        $('#patientDob').text(patient.date_of_birth || 'غير محدد');
        $('#patientEmail').text(patient.email || 'غير محدد');
        
        $('#searchResults').show();
    }
    
    function showNoResults() {
        $('#noResults').show();
        $('#searchResults').hide();
        $('#noHistory').hide();
    }
    
    function showNoHistory() {
        $('#noHistory').show();
        $('#searchResults').hide();
        $('#noResults').hide();
    }
    
    function clearResults() {
        $('#searchInput').val('');
        $('#searchResults').hide();
        $('#noResults').hide();
        $('#noHistory').hide();
        $('#loadingMessage').hide();
        currentPatientId = null;
    }
});
</script>
@endpush

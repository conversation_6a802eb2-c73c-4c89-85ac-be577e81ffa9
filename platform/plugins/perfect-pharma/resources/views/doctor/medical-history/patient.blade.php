@extends('plugins/perfect-pharma::doctor.layout')

@section('title', 'التاريخ المرضي - ' . $patient->name)
@section('page-title', 'التاريخ المرضي الشامل')

@section('content')
<div class="row">
    <!-- معلومات المريض -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">
                            <i class="fas fa-user me-2"></i>
                            {{ $patient->name }}
                        </h3>
                        <div class="row">
                            <div class="col-md-3">
                                <p class="mb-1">
                                    <strong>الهاتف:</strong> {{ $patient->phone }}
                                </p>
                            </div>
                            <div class="col-md-3">
                                <p class="mb-1">
                                    <strong>رقم البطاقة:</strong> {{ $patient->national_id ?? $patient->id_number ?? 'غير محدد' }}
                                </p>
                            </div>
                            <div class="col-md-3">
                                <p class="mb-1">
                                    <strong>الجنس:</strong> {{ $patient->gender === 'male' ? 'ذكر' : ($patient->gender === 'female' ? 'أنثى' : 'غير محدد') }}
                                </p>
                            </div>
                            <div class="col-md-3">
                                <p class="mb-1">
                                    <strong>تاريخ الميلاد:</strong> {{ $patient->dob ?? 'غير محدد' }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ route('doctor.medical-history.search') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للبحث
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $medicalHistory['stats']['total_appointments'] }}</h3>
                <p class="mb-0">إجمالي المواعيد</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                <i class="fas fa-prescription-bottle-alt fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $medicalHistory['stats']['total_prescriptions'] }}</h3>
                <p class="mb-0">الوصفات الطبية</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-flask fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $medicalHistory['stats']['total_lab_tests'] }}</h3>
                <p class="mb-0">التحاليل المخبرية</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <i class="fas fa-user-md fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $medicalHistory['stats']['doctors_count'] }}</h3>
                <p class="mb-0">الأطباء المعالجين</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الحالات المزمنة والحساسيات -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <!-- الحالات المزمنة -->
                <div class="mb-3">
                    <h6 class="text-danger">
                        <i class="fas fa-heartbeat me-1"></i>
                        الحالات المزمنة
                    </h6>
                    @if($medicalHistory['chronic_conditions']->count() > 0)
                        <ul class="list-unstyled">
                            @foreach($medicalHistory['chronic_conditions'] as $condition)
                                <li><i class="fas fa-dot-circle text-danger me-1"></i> {{ $condition }}</li>
                            @endforeach
                        </ul>
                    @else
                        <p class="text-muted small">لا توجد حالات مزمنة مسجلة</p>
                    @endif
                </div>

                <!-- الحساسيات -->
                <div class="mb-3">
                    <h6 class="text-warning">
                        <i class="fas fa-allergies me-1"></i>
                        الحساسيات
                    </h6>
                    @if($medicalHistory['allergies']->count() > 0)
                        <ul class="list-unstyled">
                            @foreach($medicalHistory['allergies'] as $allergy)
                                <li><i class="fas fa-dot-circle text-warning me-1"></i> {{ $allergy }}</li>
                            @endforeach
                        </ul>
                    @else
                        <p class="text-muted small">لا توجد حساسيات مسجلة</p>
                    @endif
                </div>

                <!-- العمليات الجراحية -->
                <div>
                    <h6 class="text-info">
                        <i class="fas fa-cut me-1"></i>
                        العمليات الجراحية
                    </h6>
                    @if($medicalHistory['surgeries']->count() > 0)
                        <ul class="list-unstyled">
                            @foreach($medicalHistory['surgeries'] as $surgery)
                                <li>
                                    <i class="fas fa-dot-circle text-info me-1"></i>
                                    {{ $surgery->diagnosis }}
                                    <small class="text-muted d-block">{{ $surgery->created_at->format('Y-m-d') }}</small>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <p class="text-muted small">لا توجد عمليات جراحية مسجلة</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- الأطباء المعالجين -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user-md me-2"></i>
                    الأطباء المعالجين
                </h6>
            </div>
            <div class="card-body">
                @if($medicalHistory['doctors_visited']->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($medicalHistory['doctors_visited'] as $doctor)
                            <div class="list-group-item px-0">
                                <h6 class="mb-1">د. {{ $doctor->user->name }}</h6>
                                <p class="mb-0 text-muted small">
                                    {{ $doctor->specialization->name ?? 'تخصص عام' }}
                                </p>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted">لا توجد بيانات أطباء</p>
                @endif
            </div>
        </div>
    </div>

    <!-- التاريخ الزمني للمواعيد والوصفات -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="historyTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="appointments-tab" data-bs-toggle="tab"
                                data-bs-target="#appointments" type="button" role="tab">
                            <i class="fas fa-calendar-check me-1"></i>
                            المواعيد ({{ $medicalHistory['appointments']->count() }})
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="prescriptions-tab" data-bs-toggle="tab"
                                data-bs-target="#prescriptions" type="button" role="tab">
                            <i class="fas fa-prescription-bottle-alt me-1"></i>
                            الوصفات ({{ $medicalHistory['prescriptions']->count() }})
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="lab-tests-tab" data-bs-toggle="tab"
                                data-bs-target="#lab-tests" type="button" role="tab">
                            <i class="fas fa-flask me-1"></i>
                            التحاليل ({{ $medicalHistory['lab_tests']->count() }})
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="historyTabsContent">
                    <!-- تبويب المواعيد -->
                    <div class="tab-pane fade show active" id="appointments" role="tabpanel">
                        @if($medicalHistory['appointments']->count() > 0)
                            <div class="timeline">
                                @foreach($medicalHistory['appointments'] as $appointment)
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-primary"></div>
                                        <div class="timeline-content">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        موعد مع د. {{ $appointment->doctor->user->name }}
                                                    </h6>
                                                    <p class="text-muted mb-1">
                                                        {{ $appointment->appointment_date->format('Y-m-d H:i') }}
                                                    </p>
                                                    @if($appointment->diagnosis)
                                                        <p class="mb-1">
                                                            <strong>التشخيص:</strong> {{ $appointment->diagnosis }}
                                                        </p>
                                                    @endif
                                                    @if($appointment->notes)
                                                        <p class="mb-0 text-muted">
                                                            <small>{{ $appointment->notes }}</small>
                                                        </p>
                                                    @endif
                                                </div>
                                                <span class="badge bg-{{ $appointment->status === 'completed' ? 'success' : 'warning' }}">
                                                    {{ $appointment->status === 'completed' ? 'مكتمل' : 'معلق' }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد مواعيد مسجلة</p>
                            </div>
                        @endif
                    </div>

                    <!-- تبويب الوصفات -->
                    <div class="tab-pane fade" id="prescriptions" role="tabpanel">
                        @if($medicalHistory['prescriptions']->count() > 0)
                            <div class="timeline">
                                @foreach($medicalHistory['prescriptions'] as $prescription)
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-success"></div>
                                        <div class="timeline-content">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        وصفة من د. {{ $prescription->doctor->user->name }}
                                                    </h6>
                                                    <p class="text-muted mb-1">
                                                        {{ $prescription->created_at->format('Y-m-d H:i') }}
                                                    </p>
                                                    <p class="mb-1">
                                                        <strong>عدد الأدوية:</strong> {{ $prescription->medications->count() }}
                                                    </p>
                                                    @if($prescription->diagnosis)
                                                        <p class="mb-1">
                                                            <strong>التشخيص:</strong> {{ $prescription->diagnosis }}
                                                        </p>
                                                    @endif
                                                    @if($prescription->notes)
                                                        <p class="mb-0 text-muted">
                                                            <small>{{ $prescription->notes }}</small>
                                                        </p>
                                                    @endif
                                                </div>
                                                <div class="text-end">
                                                    <span class="badge bg-{{ $prescription->status === 'dispensed' ? 'success' : 'warning' }}">
                                                        {{ $prescription->status === 'dispensed' ? 'مصروفة' : 'معلقة' }}
                                                    </span>
                                                    <br>
                                                    <button type="button" class="btn btn-sm btn-outline-primary mt-1 view-prescription"
                                                            data-id="{{ $prescription->id }}">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-prescription-bottle fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد وصفات مسجلة</p>
                            </div>
                        @endif
                    </div>

                    <!-- تبويب التحاليل -->
                    <div class="tab-pane fade" id="lab-tests" role="tabpanel">
                        @if($medicalHistory['lab_tests']->count() > 0)
                            <div class="timeline">
                                @foreach($medicalHistory['lab_tests'] as $labTest)
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-info"></div>
                                        <div class="timeline-content">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        {{ $labTest->labTest->name }}
                                                    </h6>
                                                    <p class="text-muted mb-1">
                                                        طلب من د. {{ $labTest->doctor->user->name }}
                                                    </p>
                                                    <p class="text-muted mb-1">
                                                        {{ $labTest->created_at->format('Y-m-d H:i') }}
                                                    </p>
                                                    @if($labTest->result && $labTest->result->notes)
                                                        <p class="mb-0 text-muted">
                                                            <small>{{ $labTest->result->notes }}</small>
                                                        </p>
                                                    @endif
                                                </div>
                                                <span class="badge bg-{{ $labTest->status === 'completed' ? 'success' : ($labTest->status === 'in_progress' ? 'warning' : 'secondary') }}">
                                                    {{ $labTest->status === 'completed' ? 'مكتمل' : ($labTest->status === 'in_progress' ? 'قيد التنفيذ' : 'معلق') }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-flask fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد تحاليل مسجلة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.stat-card {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.stat-card.info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.stat-card.success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // عرض تفاصيل الوصفة
    $('.view-prescription').click(function() {
        const prescriptionId = $(this).data('id');

        $.get(`{{ route('doctor.medical-history.prescription-details', '') }}/${prescriptionId}`)
        .done(function(response) {
            if (response.success) {
                const prescription = response.prescription;
                let medicationsHtml = '';

                prescription.medications.forEach(function(med) {
                    medicationsHtml += `
                        <tr>
                            <td>${med.name}</td>
                            <td>${med.dosage}</td>
                            <td>${med.frequency}</td>
                            <td>${med.duration}</td>
                        </tr>
                    `;
                });

                const modalHtml = `
                    <div class="modal fade" id="prescriptionModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">تفاصيل الوصفة ${prescription.code}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <strong>الطبيب:</strong> ${prescription.doctor}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>التاريخ:</strong> ${prescription.date}
                                        </div>
                                    </div>
                                    ${prescription.diagnosis ? `<div class="mb-3"><strong>التشخيص:</strong> ${prescription.diagnosis}</div>` : ''}
                                    <div class="mb-3">
                                        <strong>الأدوية:</strong>
                                        <table class="table table-sm mt-2">
                                            <thead>
                                                <tr>
                                                    <th>الدواء</th>
                                                    <th>الجرعة</th>
                                                    <th>التكرار</th>
                                                    <th>المدة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${medicationsHtml}
                                            </tbody>
                                        </table>
                                    </div>
                                    ${prescription.notes ? `<div class="mb-0"><strong>ملاحظات:</strong> ${prescription.notes}</div>` : ''}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // إزالة أي modal موجود وإضافة الجديد
                $('#prescriptionModal').remove();
                $('body').append(modalHtml);
                $('#prescriptionModal').modal('show');
            }
        })
        .fail(function() {
            alert('حدث خطأ أثناء تحميل تفاصيل الوصفة');
        });
    });
});
</script>
@endpush
@extends('plugins/perfect-pharma::doctor.layout')

@section('title', 'المرضى')
@section('page-title', 'إدارة المرضى')

@section('content')
<div class="row mb-4">
    <!-- Statistics Cards -->
    <div class="col-md-4 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_patients'] }}</h3>
                <p class="mb-0">إجمالي المرضى</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-heartbeat fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['chronic_patients'] }}</h3>
                <p class="mb-0">مرضى مزمنون</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <i class="fas fa-user-plus fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['new_patients_this_month'] }}</h3>
                <p class="mb-0">مرضى جدد هذا الشهر</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('doctor.patients.index') }}">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="اسم المريض، الهاتف، أو رقم المريض"
                           value="{{ request('search') }}">
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">الجنس</label>
                    <select name="gender" class="form-select">
                        <option value="">الكل</option>
                        <option value="male" {{ request('gender') == 'male' ? 'selected' : '' }}>ذكر</option>
                        <option value="female" {{ request('gender') == 'female' ? 'selected' : '' }}>أنثى</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">فصيلة الدم</label>
                    <select name="blood_type" class="form-select">
                        <option value="">الكل</option>
                        <option value="A+" {{ request('blood_type') == 'A+' ? 'selected' : '' }}>A+</option>
                        <option value="A-" {{ request('blood_type') == 'A-' ? 'selected' : '' }}>A-</option>
                        <option value="B+" {{ request('blood_type') == 'B+' ? 'selected' : '' }}>B+</option>
                        <option value="B-" {{ request('blood_type') == 'B-' ? 'selected' : '' }}>B-</option>
                        <option value="AB+" {{ request('blood_type') == 'AB+' ? 'selected' : '' }}>AB+</option>
                        <option value="AB-" {{ request('blood_type') == 'AB-' ? 'selected' : '' }}>AB-</option>
                        <option value="O+" {{ request('blood_type') == 'O+' ? 'selected' : '' }}>O+</option>
                        <option value="O-" {{ request('blood_type') == 'O-' ? 'selected' : '' }}>O-</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">مرضى مزمنون</label>
                    <select name="chronic" class="form-select">
                        <option value="">الكل</option>
                        <option value="yes" {{ request('chronic') == 'yes' ? 'selected' : '' }}>نعم</option>
                        <option value="no" {{ request('chronic') == 'no' ? 'selected' : '' }}>لا</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">ترتيب حسب</label>
                    <select name="sort" class="form-select">
                        <option value="recent" {{ request('sort') == 'recent' ? 'selected' : '' }}>الأحدث</option>
                        <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>الاسم</option>
                        <option value="age" {{ request('sort') == 'age' ? 'selected' : '' }}>العمر</option>
                        <option value="chronic" {{ request('sort') == 'chronic' ? 'selected' : '' }}>المزمنون أولاً</option>
                    </select>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ route('doctor.patients.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء الفلترة
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Patients List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>
            قائمة المرضى ({{ $patients->total() }})
        </h5>
    </div>
    
    <div class="card-body">
        @if($patients->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المريض</th>
                            <th>رقم المريض</th>
                            <th>العمر</th>
                            <th>الجنس</th>
                            <th>فصيلة الدم</th>
                            <th>الهاتف</th>
                            <th>آخر موعد</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($patients as $patient)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $patient->user->name }}</h6>
                                            <small class="text-muted">{{ $patient->user->email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ $patient->patient_code }}</span>
                                </td>
                                <td>
                                    {{ $patient->birth_date ? $patient->birth_date->age . ' سنة' : 'غير محدد' }}
                                </td>
                                <td>
                                    <i class="fas fa-{{ $patient->gender == 'male' ? 'mars text-primary' : 'venus text-danger' }} me-1"></i>
                                    {{ $patient->gender == 'male' ? 'ذكر' : 'أنثى' }}
                                </td>
                                <td>
                                    <span class="badge bg-danger">{{ $patient->blood_type ?? 'غير محدد' }}</span>
                                </td>
                                <td>{{ $patient->user->phone }}</td>
                                <td>
                                    @if($patient->appointments->first())
                                        {{ $patient->appointments->first()->appointment_date->format('Y-m-d') }}
                                    @else
                                        لا يوجد
                                    @endif
                                </td>
                                <td>
                                    @if($patient->is_chronic_patient)
                                        <span class="badge bg-warning">
                                            <i class="fas fa-heartbeat me-1"></i>
                                            مزمن
                                        </span>
                                    @else
                                        <span class="badge bg-success">عادي</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('doctor.patients.show', $patient->id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('doctor.prescriptions.create', ['patient_id' => $patient->id]) }}" 
                                           class="btn btn-sm btn-outline-success" title="وصفة جديدة">
                                            <i class="fas fa-prescription-bottle-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $patients->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-user-slash fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نتائج</h5>
                <p class="text-muted">لم يتم العثور على مرضى بالمعايير المحددة</p>
                <a href="{{ route('doctor.patients.index') }}" class="btn btn-primary">
                    عرض جميع المرضى
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}
</style>
@endpush

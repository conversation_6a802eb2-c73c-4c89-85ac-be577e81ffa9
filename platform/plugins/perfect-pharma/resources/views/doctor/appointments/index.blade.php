@extends('plugins/perfect-pharma::doctor.layout')

@section('title', 'المواعيد')
@section('page-title', 'إدارة المواعيد')

@section('content')
<div class="row mb-4">
    <!-- Statistics Cards -->
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['today_appointments'] }}</h3>
                <p class="mb-0">مواعيد اليوم</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['pending_appointments'] }}</h3>
                <p class="mb-0">معلقة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['confirmed_appointments'] }}</h3>
                <p class="mb-0">مؤكدة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['completed_today'] }}</h3>
                <p class="mb-0">مكتملة اليوم</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('doctor.appointments.index') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="اسم المريض أو رقم الهاتف"
                           value="{{ request('search') }}">
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" 
                           value="{{ request('date_from') }}">
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" 
                           value="{{ request('date_to') }}">
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">الكل</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>معلق</option>
                        <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">ترتيب حسب</label>
                    <select name="sort" class="form-select">
                        <option value="date_desc" {{ request('sort') == 'date_desc' ? 'selected' : '' }}>الأحدث</option>
                        <option value="date_asc" {{ request('sort') == 'date_asc' ? 'selected' : '' }}>الأقدم</option>
                        <option value="patient" {{ request('sort') == 'patient' ? 'selected' : '' }}>اسم المريض</option>
                        <option value="status" {{ request('sort') == 'status' ? 'selected' : '' }}>الحالة</option>
                    </select>
                </div>
                
                <div class="col-md-1 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Appointments List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-calendar-alt me-2"></i>
            قائمة المواعيد ({{ $appointments->total() }})
        </h5>
        <div>
            <button type="button" class="btn btn-outline-primary" id="calendarViewBtn">
                <i class="fas fa-calendar me-1"></i>
                عرض التقويم
            </button>
        </div>
    </div>
    
    <div class="card-body">
        @if($appointments->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المريض</th>
                            <th>التاريخ</th>
                            <th>الوقت</th>
                            <th>نوع الموعد</th>
                            <th>الحالة</th>
                            <th>رسوم الكشف</th>
                            <th>الملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($appointments as $appointment)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $appointment->patient->user->name }}</h6>
                                            <small class="text-muted">{{ $appointment->patient->user->phone }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ $appointment->appointment_date->format('Y-m-d') }}</span>
                                    <br>
                                    <small class="text-muted">{{ $appointment->appointment_date->format('l') }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $appointment->appointment_time }}</span>
                                </td>
                                <td>{{ $appointment->appointment_type ?? 'كشف عام' }}</td>
                                <td>
                                    @php
                                        $statusColors = [
                                            'pending' => 'warning',
                                            'confirmed' => 'success',
                                            'completed' => 'secondary',
                                            'cancelled' => 'danger'
                                        ];
                                        $statusTexts = [
                                            'pending' => 'معلق',
                                            'confirmed' => 'مؤكد',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي'
                                        ];
                                    @endphp
                                    <span class="badge bg-{{ $statusColors[$appointment->status] ?? 'secondary' }}">
                                        {{ $statusTexts[$appointment->status] ?? $appointment->status }}
                                    </span>
                                </td>
                                <td>
                                    @if($appointment->consultation_fee)
                                        <span class="fw-bold text-success">{{ number_format($appointment->consultation_fee) }} ر.س</span>
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                                <td>
                                    @if($appointment->notes)
                                        <span class="text-truncate" style="max-width: 100px;" title="{{ $appointment->notes }}">
                                            {{ Str::limit($appointment->notes, 30) }}
                                        </span>
                                    @else
                                        <span class="text-muted">لا توجد</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('doctor.appointments.show', $appointment->id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        @if($appointment->status == 'pending')
                                            <button type="button" class="btn btn-sm btn-outline-success confirm-appointment" 
                                                    data-id="{{ $appointment->id }}" title="تأكيد">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        @endif
                                        
                                        @if(in_array($appointment->status, ['pending', 'confirmed']))
                                            <button type="button" class="btn btn-sm btn-outline-danger cancel-appointment" 
                                                    data-id="{{ $appointment->id }}" title="إلغاء">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                        
                                        @if($appointment->status == 'confirmed')
                                            <button type="button" class="btn btn-sm btn-outline-info complete-appointment" 
                                                    data-id="{{ $appointment->id }}" title="إكمال">
                                                <i class="fas fa-check-circle"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $appointments->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مواعيد</h5>
                <p class="text-muted">لم يتم العثور على مواعيد بالمعايير المحددة</p>
                <a href="{{ route('doctor.appointments.index') }}" class="btn btn-primary">
                    عرض جميع المواعيد
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Calendar Modal -->
<div class="modal fade" id="calendarModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقويم المواعيد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="calendar"></div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Appointment Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إلغاء الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="cancelForm">
                    <div class="mb-3">
                        <label class="form-label">سبب الإلغاء</label>
                        <textarea name="reason" class="form-control" rows="3" 
                                  placeholder="اختياري - اكتب سبب إلغاء الموعد"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmCancel">تأكيد الإلغاء</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    let currentAppointmentId = null;
    
    // Confirm appointment
    $('.confirm-appointment').click(function() {
        const appointmentId = $(this).data('id');
        
        if (confirm('هل تريد تأكيد هذا الموعد؟')) {
            $.post(`/doctor/appointments/${appointmentId}/confirm`, {
                _token: $('meta[name="csrf-token"]').attr('content')
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                }
            })
            .fail(function() {
                alert('حدث خطأ أثناء تأكيد الموعد');
            });
        }
    });
    
    // Cancel appointment
    $('.cancel-appointment').click(function() {
        currentAppointmentId = $(this).data('id');
        $('#cancelModal').modal('show');
    });
    
    $('#confirmCancel').click(function() {
        const reason = $('#cancelForm textarea[name="reason"]').val();
        
        $.post(`/doctor/appointments/${currentAppointmentId}/cancel`, {
            _token: $('meta[name="csrf-token"]').attr('content'),
            reason: reason
        })
        .done(function(response) {
            if (response.success) {
                $('#cancelModal').modal('hide');
                location.reload();
            }
        })
        .fail(function() {
            alert('حدث خطأ أثناء إلغاء الموعد');
        });
    });
    
    // Complete appointment
    $('.complete-appointment').click(function() {
        const appointmentId = $(this).data('id');
        const notes = prompt('ملاحظات الموعد (اختياري):');
        
        if (notes !== null) {
            $.post(`/doctor/appointments/${appointmentId}/complete`, {
                _token: $('meta[name="csrf-token"]').attr('content'),
                notes: notes
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                }
            })
            .fail(function() {
                alert('حدث خطأ أثناء إكمال الموعد');
            });
        }
    });
});
</script>
@endpush

@extends('plugins/perfect-pharma::doctor.layout')

@section('title', 'الوصفات الطبية')
@section('page-title', 'إدارة الوصفات الطبية')

@section('content')
<div class="row mb-4">
    <!-- Statistics Cards -->
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-prescription-bottle-alt fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_prescriptions'] }}</h3>
                <p class="mb-0">إجمالي الوصفات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['pending_prescriptions'] }}</h3>
                <p class="mb-0">معلقة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['dispensed_prescriptions'] }}</h3>
                <p class="mb-0">مصروفة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['today_prescriptions'] }}</h3>
                <p class="mb-0">وصفات اليوم</p>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">إجراءات سريعة</h5>
                    <div>
                        <a href="{{ route('doctor.prescriptions.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            وصفة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('doctor.prescriptions.index') }}">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="رقم الوصفة أو اسم المريض"
                           value="{{ request('search') }}">
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" 
                           value="{{ request('date_from') }}">
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" 
                           value="{{ request('date_to') }}">
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">الكل</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>معلقة</option>
                        <option value="dispensed" {{ request('status') == 'dispensed' ? 'selected' : '' }}>مصروفة</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغية</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Prescriptions List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-prescription me-2"></i>
            قائمة الوصفات ({{ $prescriptions->total() }})
        </h5>
    </div>
    
    <div class="card-body">
        @if($prescriptions->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الوصفة</th>
                            <th>المريض</th>
                            <th>التشخيص</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الحالة</th>
                            <th>عدد الأدوية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($prescriptions as $prescription)
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ $prescription->prescription_code }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $prescription->patient->user->name }}</h6>
                                            <small class="text-muted">{{ $prescription->patient->user->phone }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-truncate" style="max-width: 200px;" title="{{ $prescription->diagnosis }}">
                                        {{ Str::limit($prescription->diagnosis, 50) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ $prescription->created_at->format('Y-m-d') }}</span>
                                    <br>
                                    <small class="text-muted">{{ $prescription->created_at->format('H:i') }}</small>
                                </td>
                                <td>
                                    @php
                                        $statusColors = [
                                            'pending' => 'warning',
                                            'dispensed' => 'success',
                                            'cancelled' => 'danger'
                                        ];
                                        $statusTexts = [
                                            'pending' => 'معلقة',
                                            'dispensed' => 'مصروفة',
                                            'cancelled' => 'ملغية'
                                        ];
                                    @endphp
                                    <span class="badge bg-{{ $statusColors[$prescription->status] ?? 'secondary' }}">
                                        {{ $statusTexts[$prescription->status] ?? $prescription->status }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $prescription->items->count() }} دواء</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('doctor.prescriptions.show', $prescription->id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('doctor.prescriptions.print', $prescription->id) }}" 
                                           class="btn btn-sm btn-outline-info" title="طباعة" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        @if($prescription->status == 'pending')
                                            <button type="button" class="btn btn-sm btn-outline-danger cancel-prescription" 
                                                    data-id="{{ $prescription->id }}" title="إلغاء">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $prescriptions->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-prescription-bottle fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد وصفات</h5>
                <p class="text-muted">لم يتم العثور على وصفات بالمعايير المحددة</p>
                <a href="{{ route('doctor.prescriptions.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إنشاء وصفة جديدة
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Cancel prescription
    $('.cancel-prescription').click(function() {
        const prescriptionId = $(this).data('id');
        
        if (confirm('هل تريد إلغاء هذه الوصفة؟')) {
            $.post(`/doctor/prescriptions/${prescriptionId}/cancel`, {
                _token: $('meta[name="csrf-token"]').attr('content')
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || 'حدث خطأ أثناء إلغاء الوصفة');
                }
            })
            .fail(function() {
                alert('حدث خطأ أثناء إلغاء الوصفة');
            });
        }
    });
});
</script>
@endpush

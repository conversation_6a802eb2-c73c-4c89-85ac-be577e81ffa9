@extends('plugins/perfect-pharma::doctor.layout')

@section('title', 'لوحة تحكم الطبيب')
@section('page-title', 'لوحة التحكم')

@section('content')
<div class="row">
    <!-- Welcome Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">مرحباً، د. {{ $doctor->user->name }}</h3>
                        <p class="text-muted mb-0">{{ $doctor->specialization }}</p>
                        <small class="text-muted">آخر تسجيل دخول: {{ auth('customer')->user()->last_login_at?->diffForHumans() ?? 'الآن' }}</small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('doctor.prescriptions.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                وصفة جديدة
                            </a>
                            <a href="{{ route('doctor.appointments.index') }}" class="btn btn-outline-primary">
                                <i class="fas fa-calendar me-1"></i>
                                المواعيد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_patients'] }}</h3>
                <p class="mb-0">إجمالي المرضى</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['today_appointments'] }}</h3>
                <p class="mb-0">مواعيد اليوم</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['pending_appointments'] }}</h3>
                <p class="mb-0">مواعيد معلقة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                <i class="fas fa-prescription-bottle-alt fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_prescriptions'] }}</h3>
                <p class="mb-0">إجمالي الوصفات</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Today's Appointments -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    مواعيد اليوم
                </h5>
                <a href="{{ route('doctor.appointments.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($todayAppointments->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($todayAppointments->take(5) as $appointment)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $appointment->patient->user->name }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $appointment->appointment_time }}
                                    </small>
                                </div>
                                <span class="badge bg-{{ $appointment->status == 'confirmed' ? 'success' : ($appointment->status == 'pending' ? 'warning' : 'secondary') }}">
                                    {{ $appointment->status == 'confirmed' ? 'مؤكد' : ($appointment->status == 'pending' ? 'معلق' : 'مكتمل') }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مواعيد اليوم</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Recent Patients -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user-injured me-2"></i>
                    آخر المرضى
                </h5>
                <a href="{{ route('doctor.patients.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($recentPatients->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentPatients as $patient)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $patient->user->name }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-phone me-1"></i>
                                        {{ $patient->user->phone }}
                                    </small>
                                </div>
                                <a href="{{ route('doctor.patients.show', $patient->id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    عرض
                                </a>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد بيانات مرضى</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Prescriptions -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-prescription me-2"></i>
                    آخر الوصفات
                </h5>
                <a href="{{ route('doctor.prescriptions.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                @if($recentPrescriptions->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الوصفة</th>
                                    <th>المريض</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentPrescriptions as $prescription)
                                    <tr>
                                        <td>{{ $prescription->prescription_code }}</td>
                                        <td>{{ $prescription->patient->user->name }}</td>
                                        <td>{{ $prescription->created_at->format('Y-m-d') }}</td>
                                        <td>
                                            <span class="badge bg-{{ $prescription->status == 'dispensed' ? 'success' : 'warning' }}">
                                                {{ $prescription->status == 'dispensed' ? 'مصروفة' : 'معلقة' }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('doctor.prescriptions.show', $prescription->id) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                عرض
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-prescription-bottle fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد وصفات حديثة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>المواعيد المكتملة اليوم</span>
                        <strong>{{ $stats['completed_appointments_today'] }}</strong>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الإيرادات الشهرية</span>
                        <strong>{{ number_format($stats['monthly_revenue']) }} ر.س</strong>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>التقييم</span>
                        <div>
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= $stats['average_rating'] ? 'text-warning' : 'text-muted' }}"></i>
                            @endfor
                            <small class="text-muted">({{ $stats['total_reviews'] }})</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid">
                    <a href="{{ route('doctor.profile.statistics') }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar me-1"></i>
                        عرض التقرير الشامل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
@endpush

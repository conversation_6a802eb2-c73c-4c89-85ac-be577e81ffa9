@extends(Theme::getThemeNamespace('layouts.base'))

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-warning text-dark text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-file-upload"></i>
                        رفع المستندات المطلوبة
                    </h2>
                    <p class="mb-0 mt-2">يرجى رفع المستندات المطلوبة لتفعيل حسابك</p>
                </div>
                
                <div class="card-body p-5">
                    <!-- حالة التحقق -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                @switch($verificationStatus)
                                    @case('verified')
                                        <i class="fas fa-check-circle fa-2x text-success"></i>
                                        @break
                                    @case('pending')
                                        <i class="fas fa-clock fa-2x text-warning"></i>
                                        @break
                                    @default
                                        <i class="fas fa-exclamation-circle fa-2x text-info"></i>
                                @endswitch
                            </div>
                            <div>
                                <h6 class="mb-1">
                                    حالة التحقق: 
                                    @switch($verificationStatus)
                                        @case('verified')
                                            <span class="badge bg-success">محقق</span>
                                            @break
                                        @case('pending')
                                            <span class="badge bg-warning">قيد المراجعة</span>
                                            @break
                                        @default
                                            <span class="badge bg-info">مطلوب رفع المستندات</span>
                                    @endswitch
                                </h6>
                                <p class="mb-0 text-muted">
                                    نوع الحساب: <strong>{{ $primaryUserType->display_name }}</strong>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if (session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check"></i>
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            {{ session('error') }}
                        </div>
                    @endif

                    @if($verificationStatus === 'verified')
                        <div class="alert alert-success text-center">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <h4>تم تفعيل حسابك بنجاح!</h4>
                            <p>يمكنك الآن الاستفادة من جميع الخدمات والخصومات المتاحة لنوع حسابك.</p>
                            <a href="{{ route('customer.overview') }}" class="btn btn-success">
                                <i class="fas fa-tachometer-alt"></i>
                                الذهاب للوحة التحكم
                            </a>
                        </div>
                    @else
                        <!-- نموذج رفع المستندات -->
                        <form method="POST" action="{{ route('customer.documents.upload.post') }}" 
                              enctype="multipart/form-data" id="documentUploadForm">
                            @csrf
                            
                            <div class="mb-4">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-paperclip"></i>
                                    المستندات المطلوبة
                                </h5>
                                
                                @foreach($requiredDocuments as $document)
                                <div class="mb-4">
                                    <label for="{{ $document }}" class="form-label fw-bold">
                                        @switch($document)
                                            @case('medical_license')
                                                <i class="fas fa-certificate text-success"></i>
                                                رخصة مزاولة المهنة الطبية
                                                @break
                                            @case('pharmacy_license')
                                                <i class="fas fa-certificate text-info"></i>
                                                ترخيص الصيدلية
                                                @break
                                            @case('lab_license')
                                                <i class="fas fa-certificate text-warning"></i>
                                                ترخيص المعمل
                                                @break
                                            @case('hospital_license')
                                                <i class="fas fa-certificate text-danger"></i>
                                                ترخيص المستشفى
                                                @break
                                            @case('clinic_license')
                                                <i class="fas fa-certificate text-secondary"></i>
                                                ترخيص العيادة
                                                @break
                                            @case('government_id')
                                            @case('owner_id')
                                            @case('manager_id')
                                            @case('doctor_id')
                                                <i class="fas fa-id-card text-primary"></i>
                                                الهوية الشخصية
                                                @break
                                            @default
                                                <i class="fas fa-file text-dark"></i>
                                                {{ $document }}
                                        @endswitch
                                        <span class="text-danger">*</span>
                                    </label>
                                    
                                    <div class="upload-area" data-document="{{ $document }}">
                                        <input type="file" class="form-control d-none" id="{{ $document }}" 
                                               name="{{ $document }}" accept=".pdf,.jpg,.jpeg,.png" required>
                                        <div class="upload-placeholder text-center p-4 border border-dashed rounded">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                            <p class="mb-2">اضغط هنا لاختيار الملف أو اسحبه إلى هنا</p>
                                            <small class="text-muted">
                                                الأنواع المدعومة: PDF, JPG, PNG (حد أقصى 5 ميجابايت)
                                            </small>
                                        </div>
                                        <div class="file-preview d-none mt-2">
                                            <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-alt text-primary me-2"></i>
                                                    <span class="file-name"></span>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <!-- ملاحظات إضافية -->
                            <div class="mb-4">
                                <label for="notes" class="form-label fw-bold">
                                    <i class="fas fa-sticky-note"></i>
                                    ملاحظات إضافية (اختياري)
                                </label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="أي ملاحظات أو معلومات إضافية تود إضافتها..."></textarea>
                            </div>

                            <!-- تنبيه مهم -->
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> تنبيه مهم:</h6>
                                <ul class="mb-0">
                                    <li>تأكد من وضوح جميع المستندات وقابليتها للقراءة</li>
                                    <li>يجب أن تكون المستندات سارية المفعول</li>
                                    <li>سيتم مراجعة المستندات خلال 24-48 ساعة</li>
                                    <li>ستصلك رسالة تأكيد عبر البريد الإلكتروني عند الموافقة</li>
                                </ul>
                            </div>

                            <!-- أزرار التحكم -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="fas fa-upload"></i>
                                    رفع المستندات
                                </button>
                                <a href="{{ route('customer.overview') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة للوحة التحكم
                                </a>
                            </div>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.upload-area {
    position: relative;
}

.upload-placeholder {
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-placeholder:hover {
    border-color: #007bff !important;
    background-color: #f8f9fa;
}

.upload-placeholder.dragover {
    border-color: #007bff !important;
    background-color: #e3f2fd;
}

.file-preview {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .card-body {
        padding: 2rem !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadAreas = document.querySelectorAll('.upload-area');
    
    uploadAreas.forEach(area => {
        const input = area.querySelector('input[type="file"]');
        const placeholder = area.querySelector('.upload-placeholder');
        const preview = area.querySelector('.file-preview');
        const fileName = area.querySelector('.file-name');
        const removeBtn = area.querySelector('.remove-file');
        
        // النقر على المنطقة لاختيار الملف
        placeholder.addEventListener('click', () => {
            input.click();
        });
        
        // تغيير الملف
        input.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                
                // التحقق من حجم الملف (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت.');
                    this.value = '';
                    return;
                }
                
                // عرض معاينة الملف
                fileName.textContent = file.name;
                placeholder.style.display = 'none';
                preview.classList.remove('d-none');
            }
        });
        
        // إزالة الملف
        removeBtn.addEventListener('click', function() {
            input.value = '';
            placeholder.style.display = 'block';
            preview.classList.add('d-none');
        });
        
        // السحب والإفلات
        placeholder.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        placeholder.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        placeholder.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                input.files = files;
                input.dispatchEvent(new Event('change'));
            }
        });
    });
    
    // التحقق من النموذج قبل الإرسال
    const form = document.getElementById('documentUploadForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الرفع...';
        });
    }
});
</script>
@endsection

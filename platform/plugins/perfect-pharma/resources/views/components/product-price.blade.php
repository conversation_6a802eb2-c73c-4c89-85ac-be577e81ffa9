@php
    $sizeClasses = $getSizeClasses();
    $hasDiscount = $hasDiscount();
@endphp

<div class="product-price-component">
    <!-- السعر النهائي -->
    <div class="final-price {{ $sizeClasses['price'] }} mb-1">
        <span class="price-amount text-primary fw-bold">
            {{ format_price($pricing['final_price']) }}
        </span>
        
        @if($hasDiscount && $showDiscountBadge)
            <span class="discount-badge badge bg-success ms-2 {{ $sizeClasses['badge'] }}">
                <i class="fas fa-tag"></i>
                {{ $pricing['discount_percentage'] }}% خصم
            </span>
        @endif
    </div>

    <!-- السعر الأصلي (إذا كان هناك خصم) -->
    @if($hasDiscount && $showOriginalPrice && $pricing['sale_price'] != $pricing['final_price'])
        <div class="original-price {{ $sizeClasses['original'] }}">
            <span class="text-decoration-line-through text-muted">
                {{ format_price($pricing['sale_price']) }}
            </span>
            <small class="text-success ms-1">
                وفر {{ format_price($pricing['discount_amount']) }}
            </small>
        </div>
    @endif

    <!-- معلومات نوع المستخدم -->
    @if($hasDiscount)
        <div class="user-type-info">
            <small class="text-info">
                <i class="fas fa-user-tag"></i>
                {{ $getDiscountText() }}
            </small>
        </div>
    @elseif(isset($pricing['verification_required']) && $pricing['verification_required'])
        <div class="verification-notice">
            <small class="text-warning">
                <i class="fas fa-exclamation-triangle"></i>
                يجب التحقق من الحساب للحصول على خصم {{ $pricing['user_type_display'] }}
            </small>
        </div>
    @elseif($pricing['user_type'] === 'guest')
        <div class="login-notice">
            <small class="text-muted">
                <i class="fas fa-sign-in-alt"></i>
                <a href="{{ route('customer.login') }}" class="text-decoration-none">
                    سجل دخولك للحصول على خصومات خاصة
                </a>
            </small>
        </div>
    @endif
</div>

<style>
.product-price-component {
    line-height: 1.4;
}

.discount-badge {
    font-size: 0.75em;
    vertical-align: middle;
}

.badge-sm {
    font-size: 0.65em;
    padding: 0.25em 0.5em;
}

.badge-lg {
    font-size: 0.85em;
    padding: 0.5em 0.75em;
}

.user-type-info,
.verification-notice,
.login-notice {
    margin-top: 0.25rem;
}

.verification-notice {
    padding: 0.25rem 0.5rem;
    background-color: #fff3cd;
    border-radius: 0.25rem;
    border: 1px solid #ffeaa7;
}

.login-notice a:hover {
    text-decoration: underline !important;
}

@media (max-width: 576px) {
    .discount-badge {
        font-size: 0.65em;
    }
    
    .user-type-info,
    .verification-notice,
    .login-notice {
        font-size: 0.8rem;
    }
}
</style>

@if($canSeePrices())
<div class="perfect-pharma-price-display size-{{ $size }}">
    @if($showAllUserTypes && $hasDiscounts())
        {{-- عرض جميع أنواع المستخدمين (للإدارة) --}}
        <div class="all-user-types-pricing">
            <h6 class="mb-3">
                <i class="ti ti-users me-2"></i>
                أسعار جميع أنواع المستخدمين
            </h6>
            
            <div class="row g-2">
                @foreach($getAllUserTypePrices() as $userType => $pricing)
                <div class="col-md-6 col-lg-4">
                    <div class="user-type-price-card {{ $userType === $getCurrentUserType() ? 'current-user' : '' }}">
                        <div class="user-type-name">
                            {{ $pricing['name'] }}
                            @if($userType === $getCurrentUserType())
                                <span class="badge bg-primary ms-1">أنت</span>
                            @endif
                        </div>
                        
                        @if($pricing['discount_rate'] > 0)
                            <div class="price-info">
                                <div class="original-price">{{ $pricing['formatted_original'] }}</div>
                                <div class="discounted-price">{{ $pricing['formatted_discounted'] }}</div>
                                <div class="discount-info">
                                    <span class="discount-rate">{{ $pricing['discount_rate'] }}% خصم</span>
                                    <span class="savings">توفير {{ $pricing['formatted_savings'] }}</span>
                                </div>
                            </div>
                        @else
                            <div class="price-info">
                                <div class="regular-price">{{ $pricing['formatted_original'] }}</div>
                                <div class="no-discount">بدون خصم</div>
                            </div>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    @else
        {{-- عرض سعر المستخدم الحالي فقط --}}
        <div class="current-user-pricing">
            @if($priceInfo['discount_rate'] > 0 && $showDiscount)
                <div class="price-with-discount">
                    <div class="original-price">
                        <span class="price-label">السعر الأصلي:</span>
                        <span class="price-value crossed">{{ $priceInfo['formatted_original'] }}</span>
                    </div>
                    
                    <div class="discounted-price">
                        <span class="price-label">سعرك:</span>
                        <span class="price-value highlighted">{{ $priceInfo['formatted_discounted'] }}</span>
                        <span class="discount-badge">{{ $priceInfo['discount_rate'] }}% خصم</span>
                    </div>
                    
                    <div class="savings-info">
                        <i class="ti ti-discount me-1"></i>
                        توفر {{ $priceInfo['formatted_discount_amount'] }}
                    </div>
                </div>
            @else
                <div class="regular-price">
                    <span class="price-value">{{ $priceInfo['formatted_discounted'] }}</span>
                </div>
            @endif
            
            @if($priceInfo['user_type'] !== 'patient')
                <div class="user-type-info">
                    <small class="text-muted">
                        <i class="ti ti-user me-1"></i>
                        {{ perfect_pharma_user_types()[$priceInfo['user_type']]['name'] ?? $priceInfo['user_type'] }}
                    </small>
                </div>
            @endif
        </div>
    @endif
</div>

<style>
.perfect-pharma-price-display {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.perfect-pharma-price-display.size-small {
    font-size: 0.9rem;
}

.perfect-pharma-price-display.size-large {
    font-size: 1.1rem;
}

.user-type-price-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    background: #fff;
}

.user-type-price-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.user-type-price-card.current-user {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
}

.user-type-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.price-info .original-price {
    color: #6c757d;
    text-decoration: line-through;
    font-size: 0.9rem;
}

.price-info .discounted-price,
.price-info .regular-price {
    color: #28a745;
    font-weight: 700;
    font-size: 1.1rem;
}

.discount-info {
    margin-top: 4px;
}

.discount-rate {
    background: #667eea;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

.savings {
    color: #28a745;
    font-size: 0.8rem;
    margin-right: 8px;
}

.no-discount {
    color: #6c757d;
    font-size: 0.8rem;
}

.current-user-pricing .price-with-discount {
    text-align: center;
}

.current-user-pricing .original-price {
    margin-bottom: 5px;
}

.current-user-pricing .original-price .price-value.crossed {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.9rem;
}

.current-user-pricing .discounted-price {
    margin-bottom: 8px;
}

.current-user-pricing .discounted-price .price-value.highlighted {
    color: #28a745;
    font-weight: 700;
    font-size: 1.3rem;
}

.discount-badge {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 8px;
}

.savings-info {
    color: #28a745;
    font-size: 0.9rem;
    font-weight: 600;
}

.regular-price .price-value {
    color: #495057;
    font-weight: 700;
    font-size: 1.2rem;
}

.user-type-info {
    margin-top: 8px;
    text-align: center;
}

.price-label {
    color: #6c757d;
    font-size: 0.85rem;
    margin-left: 5px;
}

/* Responsive */
@media (max-width: 768px) {
    .user-type-price-card {
        margin-bottom: 8px;
        padding: 10px;
    }
    
    .current-user-pricing .discounted-price .price-value.highlighted {
        font-size: 1.1rem;
    }
}

/* RTL Support */
[dir="rtl"] .discount-badge {
    margin-right: 0;
    margin-left: 8px;
}

[dir="rtl"] .savings {
    margin-right: 0;
    margin-left: 8px;
}

[dir="rtl"] .price-label {
    margin-left: 0;
    margin-right: 5px;
}
</style>
@endif

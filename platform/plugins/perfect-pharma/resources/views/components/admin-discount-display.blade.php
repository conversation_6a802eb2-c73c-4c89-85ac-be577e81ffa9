@php
    $discountRates = $getDiscountRates();
    $hasDiscounts = $hasDiscountsEnabled();
    $highestDiscount = $getHighestDiscount();
@endphp

@if($hasDiscounts && !empty($discountRates))
    <div class="perfect-pharma-admin-discounts" data-display-mode="{{ $displayMode }}">
        
        @if($displayMode === 'table')
            <!-- عرض جدول مفصل -->
            <div class="discount-table-view">
                <h6 class="mb-2">
                    <i class="ti ti-percentage text-success"></i>
                    خصومات Perfect Pharma
                    <small class="text-muted">(للمشرفين فقط)</small>
                </h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>نوع المستخدم</th>
                                <th>نسبة الخصم</th>
                                <th>السعر بعد الخصم</th>
                                <th>الوفورات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- المرضى (بدون خصم) -->
                            <tr>
                                <td><strong>مريض</strong></td>
                                <td><span class="badge bg-secondary">0%</span></td>
                                <td>{{ format_price($product->sale_price ?: $product->price) }}</td>
                                <td>-</td>
                            </tr>
                            
                            <!-- أنواع المستخدمين الأخرى -->
                            @foreach($discountRates as $rate)
                            <tr>
                                <td><strong>{{ $rate['display_name'] }}</strong></td>
                                <td>
                                    @if($rate['percentage'] > 0)
                                        <span class="badge bg-success">{{ $rate['percentage'] }}%</span>
                                    @else
                                        <span class="badge bg-secondary">0%</span>
                                    @endif
                                </td>
                                <td>
                                    @if($rate['percentage'] > 0)
                                        <strong class="text-success">{{ format_price($rate['calculated_price']) }}</strong>
                                    @else
                                        {{ format_price($product->sale_price ?: $product->price) }}
                                    @endif
                                </td>
                                <td>
                                    @if($rate['percentage'] > 0)
                                        <span class="text-success">
                                            {{ format_price(($product->sale_price ?: $product->price) - $rate['calculated_price']) }}
                                        </span>
                                    @else
                                        -
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

        @elseif($displayMode === 'badges')
            <!-- عرض شارات مختصرة -->
            <div class="discount-badges-view">
                <div class="d-flex flex-wrap align-items-center gap-1 mb-2">
                    <small class="text-muted me-2">
                        <i class="ti ti-percentage"></i>
                        خصومات:
                    </small>
                    @foreach($discountRates as $rate)
                        @if($rate['percentage'] > 0)
                            <span class="badge bg-success" title="{{ $rate['display_name'] }}: {{ format_price($rate['calculated_price']) }}">
                                {{ $rate['display_name'] }} {{ $rate['percentage'] }}%
                            </span>
                        @endif
                    @endforeach
                    
                    @if(empty(array_filter($discountRates, fn($r) => $r['percentage'] > 0)))
                        <span class="badge bg-secondary">لا توجد خصومات</span>
                    @endif
                </div>
            </div>

        @elseif($displayMode === 'inline')
            <!-- عرض مضغوط في سطر واحد -->
            <div class="discount-inline-view">
                @if($highestDiscount['percentage'] > 0)
                    <small class="text-success">
                        <i class="ti ti-percentage"></i>
                        أعلى خصم: <strong>{{ $highestDiscount['percentage'] }}%</strong> 
                        ({{ $highestDiscount['display_name'] }})
                        - {{ $getActiveDiscountsCount() }} نوع مستخدم
                    </small>
                @else
                    <small class="text-muted">
                        <i class="ti ti-percentage"></i>
                        لا توجد خصومات مفعلة
                    </small>
                @endif
            </div>

        @elseif($displayMode === 'summary')
            <!-- عرض ملخص سريع -->
            <div class="discount-summary-view">
                <div class="alert alert-info alert-sm mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-info-circle"></i>
                            <strong>خصومات Perfect Pharma:</strong>
                            {{ $getActiveDiscountsCount() }} من {{ $userTypes->count() }} أنواع مستخدمين
                        </div>
                        <div>
                            @if($highestDiscount['percentage'] > 0)
                                <span class="badge bg-success">
                                    أعلى خصم: {{ $highestDiscount['percentage'] }}%
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- رابط سريع لتعديل الخصومات -->
        <div class="discount-actions mt-2">
            <a href="{{ route('admin.perfect-pharma.product-discounts.edit', $product->id) }}" 
               class="btn btn-sm btn-outline-primary" title="تعديل خصومات هذا المنتج">
                <i class="ti ti-edit"></i>
                تعديل الخصومات
            </a>
        </div>
    </div>

@elseif($hasDiscounts && empty($discountRates))
    <!-- المنتج له خصومات مفعلة لكن لا توجد نسب محددة -->
    <div class="perfect-pharma-admin-discounts">
        <div class="alert alert-warning alert-sm">
            <i class="ti ti-alert-triangle"></i>
            <strong>تنبيه:</strong> الخصومات المتدرجة مفعلة لكن لم يتم تحديد نسب الخصم.
            <a href="{{ route('admin.perfect-pharma.product-discounts.edit', $product->id) }}" class="alert-link">
                تحديد النسب الآن
            </a>
        </div>
    </div>

@else
    <!-- المنتج ليس له خصومات مفعلة -->
    <div class="perfect-pharma-admin-discounts">
        <div class="alert alert-secondary alert-sm">
            <i class="ti ti-info-circle"></i>
            الخصومات المتدرجة غير مفعلة لهذا المنتج.
            <a href="{{ route('admin.perfect-pharma.product-discounts.edit', $product->id) }}" class="alert-link">
                تفعيل الآن
            </a>
        </div>
    </div>
@endif

<style>
.perfect-pharma-admin-discounts {
    border: 1px solid #e3f2fd;
    border-radius: 8px;
    padding: 12px;
    background-color: #f8f9fa;
    margin: 10px 0;
}

.perfect-pharma-admin-discounts .table {
    margin-bottom: 0;
    font-size: 0.875rem;
}

.perfect-pharma-admin-discounts .badge {
    font-size: 0.75rem;
}

.perfect-pharma-admin-discounts .alert-sm {
    padding: 8px 12px;
    margin-bottom: 8px;
    font-size: 0.875rem;
}

.discount-badges-view .badge {
    margin-right: 4px;
    margin-bottom: 4px;
}

.discount-inline-view {
    padding: 6px 0;
}

.discount-actions {
    border-top: 1px solid #dee2e6;
    padding-top: 8px;
}

/* إخفاء للمستخدمين العاديين */
body:not(.admin-panel) .perfect-pharma-admin-discounts {
    display: none;
}

@media (max-width: 768px) {
    .perfect-pharma-admin-discounts .table-responsive {
        font-size: 0.8rem;
    }
    
    .discount-badges-view .badge {
        font-size: 0.7rem;
        padding: 0.25em 0.4em;
    }
}
</style>

/**
 * Perfect Pharma - عرض الخصومات للمشرفين
 */

class PerfectPharmaAdminDiscounts {
    constructor() {
        this.init();
    }

    init() {
        // إضافة الخصومات لصفحات المنتجات
        this.addDiscountsToProductPages();
        
        // إضافة الخصومات لقائمة المنتجات
        this.addDiscountsToProductList();
        
        // إضافة CSS
        this.addStyles();
    }

    /**
     * إضافة الخصومات لصفحات المنتجات
     */
    addDiscountsToProductPages() {
        // صفحة تعديل المنتج
        if (window.location.pathname.includes('/admin/ecommerce/products/edit/')) {
            this.addDiscountsToProductEdit();
        }
        
        // صفحة إضافة المنتج
        if (window.location.pathname.includes('/admin/ecommerce/products/create')) {
            this.addDiscountsToProductCreate();
        }
    }

    /**
     * إضافة الخصومات لصفحة تعديل المنتج
     */
    addDiscountsToProductEdit() {
        const productId = this.getProductIdFromUrl();
        if (!productId) return;

        // البحث عن مكان مناسب لإدراج الخصومات
        const targetElement = document.querySelector('.main-form-body') || 
                            document.querySelector('.card-body') ||
                            document.querySelector('.form-body');

        if (targetElement) {
            this.fetchAndDisplayDiscounts(productId, targetElement);
        }
    }

    /**
     * إضافة الخصومات لقائمة المنتجات
     */
    addDiscountsToProductList() {
        if (!window.location.pathname.includes('/admin/ecommerce/products')) return;

        // إضافة عمود الخصومات للجدول
        const table = document.querySelector('.table');
        if (table) {
            this.addDiscountColumnToTable(table);
        }
    }

    /**
     * جلب وعرض الخصومات
     */
    async fetchAndDisplayDiscounts(productId, targetElement) {
        try {
            const response = await fetch(`/admin/perfect-pharma/api/product-discounts/${productId}`);
            if (response.ok) {
                const data = await response.json();
                this.displayDiscounts(data, targetElement);
            }
        } catch (error) {
            console.log('Perfect Pharma: Could not fetch discounts', error);
        }
    }

    /**
     * عرض الخصومات
     */
    displayDiscounts(data, targetElement) {
        const discountHtml = this.generateDiscountHtml(data);
        
        // إنشاء عنصر الخصومات
        const discountElement = document.createElement('div');
        discountElement.className = 'perfect-pharma-admin-discounts-widget';
        discountElement.innerHTML = discountHtml;
        
        // إدراج العنصر
        targetElement.insertBefore(discountElement, targetElement.firstChild);
    }

    /**
     * إنشاء HTML للخصومات
     */
    generateDiscountHtml(data) {
        if (!data.enabled) {
            return `
                <div class="alert alert-info">
                    <i class="ti ti-info-circle"></i>
                    <strong>Perfect Pharma:</strong> الخصومات المتدرجة غير مفعلة لهذا المنتج.
                    <a href="/admin/perfect-pharma/product-discounts/${data.product_id}/edit" class="alert-link">تفعيل الآن</a>
                </div>
            `;
        }

        if (!data.discounts || data.discounts.length === 0) {
            return `
                <div class="alert alert-warning">
                    <i class="ti ti-alert-triangle"></i>
                    <strong>تنبيه:</strong> الخصومات مفعلة لكن لم يتم تحديد نسب الخصم.
                    <a href="/admin/perfect-pharma/product-discounts/${data.product_id}/edit" class="alert-link">تحديد النسب</a>
                </div>
            `;
        }

        let html = `
            <div class="card border-success mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="ti ti-percentage text-success"></i>
                        خصومات Perfect Pharma
                        <small class="text-muted">(للمشرفين فقط)</small>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
        `;

        // عرض الخصومات
        data.discounts.forEach(discount => {
            if (discount.percentage > 0) {
                html += `
                    <div class="col-md-4 mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-bold">${discount.display_name}:</span>
                            <span class="badge bg-success">${discount.percentage}%</span>
                        </div>
                        <small class="text-muted">${discount.final_price}</small>
                    </div>
                `;
            }
        });

        html += `
                    </div>
                    <div class="mt-2">
                        <a href="/admin/perfect-pharma/product-discounts/${data.product_id}/edit" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="ti ti-edit"></i>
                            تعديل الخصومات
                        </a>
                    </div>
                </div>
            </div>
        `;

        return html;
    }

    /**
     * إضافة عمود الخصومات للجدول
     */
    addDiscountColumnToTable(table) {
        const headerRow = table.querySelector('thead tr');
        if (headerRow) {
            const discountHeader = document.createElement('th');
            discountHeader.textContent = 'خصومات Perfect Pharma';
            discountHeader.className = 'text-center';
            headerRow.appendChild(discountHeader);
        }

        // إضافة خلايا الخصومات لكل صف
        const bodyRows = table.querySelectorAll('tbody tr');
        bodyRows.forEach(row => {
            const productId = this.getProductIdFromRow(row);
            if (productId) {
                const discountCell = document.createElement('td');
                discountCell.className = 'text-center';
                discountCell.innerHTML = '<small class="text-muted">جاري التحميل...</small>';
                row.appendChild(discountCell);
                
                // جلب بيانات الخصومات
                this.fetchDiscountForRow(productId, discountCell);
            }
        });
    }

    /**
     * جلب الخصومات لصف في الجدول
     */
    async fetchDiscountForRow(productId, cell) {
        try {
            const response = await fetch(`/admin/perfect-pharma/api/product-discounts/${productId}`);
            if (response.ok) {
                const data = await response.json();
                cell.innerHTML = this.generateRowDiscountHtml(data);
            }
        } catch (error) {
            cell.innerHTML = '<small class="text-muted">-</small>';
        }
    }

    /**
     * إنشاء HTML مختصر للخصومات في الجدول
     */
    generateRowDiscountHtml(data) {
        if (!data.enabled) {
            return '<small class="text-muted">غير مفعل</small>';
        }

        if (!data.discounts || data.discounts.length === 0) {
            return '<small class="text-warning">لا توجد نسب</small>';
        }

        const activeDiscounts = data.discounts.filter(d => d.percentage > 0);
        if (activeDiscounts.length === 0) {
            return '<small class="text-muted">0%</small>';
        }

        const highest = activeDiscounts.reduce((max, current) => 
            current.percentage > max.percentage ? current : max
        );

        return `
            <div class="text-center">
                <span class="badge bg-success">${highest.percentage}%</span>
                <br>
                <small class="text-muted">${activeDiscounts.length} نوع</small>
            </div>
        `;
    }

    /**
     * استخراج معرف المنتج من الرابط
     */
    getProductIdFromUrl() {
        const matches = window.location.pathname.match(/\/admin\/ecommerce\/products\/edit\/(\d+)/);
        return matches ? matches[1] : null;
    }

    /**
     * استخراج معرف المنتج من صف الجدول
     */
    getProductIdFromRow(row) {
        const editLink = row.querySelector('a[href*="/edit/"]');
        if (editLink) {
            const matches = editLink.href.match(/\/edit\/(\d+)/);
            return matches ? matches[1] : null;
        }
        return null;
    }

    /**
     * إضافة CSS
     */
    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .perfect-pharma-admin-discounts-widget {
                margin-bottom: 20px;
            }
            
            .perfect-pharma-admin-discounts-widget .badge {
                font-size: 0.75rem;
            }
            
            .perfect-pharma-admin-discounts-widget .card {
                border-left: 4px solid #198754;
            }
            
            .perfect-pharma-admin-discounts-widget .alert {
                border-left: 4px solid #0dcaf0;
            }
        `;
        document.head.appendChild(style);
    }
}

// تشغيل النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من أننا في لوحة الإدارة
    if (document.body.classList.contains('admin-panel') || 
        window.location.pathname.includes('/admin/')) {
        new PerfectPharmaAdminDiscounts();
    }
});

/**
 * Perfect Pharma - Currency Helper
 * 
 * مساعد العملة الموحد لـ Perfect Pharma
 * يستخدم إعدادات العملة من النظام الأساسي
 */

(function(window) {
    'use strict';

    var CurrencyHelper = {
        
        /**
         * تهيئة مساعد العملة
         */
        init: function() {
            this.loadSystemCurrency();
        },

        /**
         * تحميل إعدادات العملة من النظام
         */
        loadSystemCurrency: function() {
            // إذا كانت العملة محملة مسبقاً، لا نحتاج لإعادة تحميلها
            if (window.systemCurrency) {
                return;
            }

            // محاولة الحصول على العملة من meta tag
            var currencyMeta = document.querySelector('meta[name="system-currency"]');
            if (currencyMeta) {
                try {
                    window.systemCurrency = JSON.parse(currencyMeta.getAttribute('content'));
                    return;
                } catch (e) {
                    console.warn('Failed to parse system currency from meta tag:', e);
                }
            }

            // محاولة الحصول على العملة من AJAX
            this.fetchSystemCurrency();
        },

        /**
         * جلب إعدادات العملة من الخادم
         */
        fetchSystemCurrency: function() {
            fetch('/admin/perfect-pharma/api/system-currency')
                .then(response => response.json())
                .then(data => {
                    if (data.error === false && data.data) {
                        window.systemCurrency = data.data;
                        this.triggerCurrencyLoaded();
                    } else {
                        this.setFallbackCurrency();
                    }
                })
                .catch(error => {
                    console.warn('Failed to fetch system currency:', error);
                    this.setFallbackCurrency();
                });
        },

        /**
         * تعيين العملة الافتراضية (الريال السعودي)
         */
        setFallbackCurrency: function() {
            window.systemCurrency = {
                title: 'SAR',
                symbol: 'ر.س',
                is_prefix_symbol: false,
                decimals: 2,
                locale: 'ar-SA',
                is_default: true,
                exchange_rate: 1
            };
            this.triggerCurrencyLoaded();
        },

        /**
         * إطلاق حدث تحميل العملة
         */
        triggerCurrencyLoaded: function() {
            var event = new CustomEvent('systemCurrencyLoaded', {
                detail: window.systemCurrency
            });
            document.dispatchEvent(event);
        },

        /**
         * تنسيق السعر باستخدام عملة النظام
         * 
         * @param {number} amount المبلغ
         * @param {boolean} showSymbol عرض رمز العملة (افتراضي: true)
         * @param {boolean} useIntl استخدام Intl.NumberFormat (افتراضي: true)
         * @returns {string} السعر المنسق
         */
        formatPrice: function(amount, showSymbol = true, useIntl = true) {
            if (!window.systemCurrency) {
                this.setFallbackCurrency();
            }

            var currency = window.systemCurrency;
            var numericAmount = parseFloat(amount) || 0;

            if (useIntl && window.Intl && window.Intl.NumberFormat) {
                var formatted = new Intl.NumberFormat(currency.locale || 'ar-SA', {
                    minimumFractionDigits: currency.decimals || 2,
                    maximumFractionDigits: currency.decimals || 2
                }).format(numericAmount);

                if (showSymbol) {
                    if (currency.is_prefix_symbol) {
                        return currency.symbol + ' ' + formatted;
                    } else {
                        return formatted + ' ' + currency.symbol;
                    }
                }

                return formatted;
            } else {
                // fallback للتنسيق البسيط
                var formatted = numericAmount.toFixed(currency.decimals || 2);
                
                if (showSymbol) {
                    if (currency.is_prefix_symbol) {
                        return currency.symbol + ' ' + formatted;
                    } else {
                        return formatted + ' ' + currency.symbol;
                    }
                }

                return formatted;
            }
        },

        /**
         * استخراج القيمة الرقمية من النص المنسق
         * 
         * @param {string} formattedText النص المنسق
         * @returns {number} القيمة الرقمية
         */
        extractNumericValue: function(formattedText) {
            if (!formattedText || typeof formattedText !== 'string') {
                return 0;
            }

            // إزالة جميع الرموز والحروف والاحتفاظ بالأرقام والفواصل العشرية فقط
            var numericString = formattedText.replace(/[^\d.,]/g, '');

            if (!numericString) {
                return 0;
            }

            // التعامل مع الفواصل
            if (numericString.includes(',') && numericString.includes('.')) {
                // إذا كان هناك فاصلة ونقطة، الفاصلة للآلاف والنقطة للعشرية
                numericString = numericString.replace(/,/g, '');
            } else if (numericString.includes(',')) {
                // إذا كان هناك فاصلة فقط، تحديد ما إذا كانت للآلاف أم للعشرية
                var parts = numericString.split(',');
                if (parts.length === 2 && parts[1].length <= 2) {
                    // فاصلة عشرية
                    numericString = numericString.replace(',', '.');
                } else {
                    // فاصلة للآلاف
                    numericString = numericString.replace(/,/g, '');
                }
            }

            return parseFloat(numericString) || 0;
        },

        /**
         * الحصول على رمز العملة
         * 
         * @returns {string} رمز العملة
         */
        getCurrencySymbol: function() {
            if (!window.systemCurrency) {
                this.setFallbackCurrency();
            }
            return window.systemCurrency.symbol || 'ر.س';
        },

        /**
         * الحصول على كود العملة
         * 
         * @returns {string} كود العملة
         */
        getCurrencyCode: function() {
            if (!window.systemCurrency) {
                this.setFallbackCurrency();
            }
            return window.systemCurrency.title || 'SAR';
        },

        /**
         * الحصول على عدد الخانات العشرية
         * 
         * @returns {number} عدد الخانات العشرية
         */
        getDecimals: function() {
            if (!window.systemCurrency) {
                this.setFallbackCurrency();
            }
            return window.systemCurrency.decimals || 2;
        },

        /**
         * التحقق من أن رمز العملة يأتي قبل الرقم
         * 
         * @returns {boolean} true إذا كان الرمز يأتي قبل الرقم
         */
        isPrefixSymbol: function() {
            if (!window.systemCurrency) {
                this.setFallbackCurrency();
            }
            return window.systemCurrency.is_prefix_symbol || false;
        },

        /**
         * تحويل العملة (مبسط للنظام الموحد)
         * 
         * @param {number} amount المبلغ
         * @param {number} exchangeRate سعر الصرف (افتراضي: 1)
         * @returns {number} المبلغ المحول
         */
        convertCurrency: function(amount, exchangeRate = 1) {
            return parseFloat(amount) * parseFloat(exchangeRate);
        }
    };

    // تصدير المساعد للنطاق العام
    window.CurrencyHelper = CurrencyHelper;

    // تهيئة تلقائية عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            CurrencyHelper.init();
        });
    } else {
        CurrencyHelper.init();
    }

    // دوال مساعدة عامة
    window.formatPrice = function(amount, showSymbol = true) {
        return CurrencyHelper.formatPrice(amount, showSymbol);
    };

    window.extractNumericValue = function(formattedText) {
        return CurrencyHelper.extractNumericValue(formattedText);
    };

})(window);

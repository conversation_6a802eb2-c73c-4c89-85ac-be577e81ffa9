/**
 * Perfect Pharma JavaScript
 * Main JavaScript file for Perfect Pharma plugin
 */

(function($) {
    'use strict';

    // Initialize Perfect Pharma
    var PerfectPharma = {
        init: function() {
            this.initDatePickers();
            this.initSelect2();
            this.initTooltips();
            this.initConfirmDialogs();
            this.initAjaxForms();
        },

        // Initialize date pickers
        initDatePickers: function() {
            if ($.fn.datepicker) {
                $('.datepicker').datepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true,
                    todayHighlight: true
                });
            }
        },

        // Initialize Select2
        initSelect2: function() {
            if ($.fn.select2) {
                $('.select2').select2({
                    width: '100%'
                });
            }
        },

        // Initialize tooltips
        initTooltips: function() {
            if ($.fn.tooltip) {
                $('[data-toggle="tooltip"]').tooltip();
            }
        },

        // Initialize confirm dialogs
        initConfirmDialogs: function() {
            $(document).on('click', '.btn-confirm', function(e) {
                e.preventDefault();
                var message = $(this).data('confirm') || 'هل أنت متأكد؟';
                if (confirm(message)) {
                    if ($(this).is('a')) {
                        window.location.href = $(this).attr('href');
                    } else if ($(this).is('button') && $(this).closest('form').length) {
                        $(this).closest('form').submit();
                    }
                }
            });
        },

        // Initialize AJAX forms
        initAjaxForms: function() {
            $(document).on('submit', '.ajax-form', function(e) {
                e.preventDefault();
                var form = $(this);
                var submitBtn = form.find('[type="submit"]');
                var originalText = submitBtn.text();

                // Disable submit button
                submitBtn.prop('disabled', true).text('جاري المعالجة...');

                $.ajax({
                    url: form.attr('action'),
                    method: form.attr('method') || 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        if (response.success) {
                            PerfectPharma.showAlert('success', response.message || 'تم بنجاح');
                            if (response.redirect) {
                                setTimeout(function() {
                                    window.location.href = response.redirect;
                                }, 1500);
                            }
                        } else {
                            PerfectPharma.showAlert('error', response.message || 'حدث خطأ');
                        }
                    },
                    error: function(xhr) {
                        var message = 'حدث خطأ في الخادم';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        PerfectPharma.showAlert('error', message);
                    },
                    complete: function() {
                        // Re-enable submit button
                        submitBtn.prop('disabled', false).text(originalText);
                    }
                });
            });
        },

        // Show alert message
        showAlert: function(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                           message +
                           '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                           '</div>';
            
            // Remove existing alerts
            $('.alert').remove();
            
            // Add new alert
            $('body').prepend(alertHtml);
            
            // Auto hide after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        },

        // Utility functions
        formatCurrency: function(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        },

        formatDate: function(date) {
            return new Intl.DateTimeFormat('ar-SA').format(new Date(date));
        }
    };

    // POS specific functions
    var POS = {
        cart: [],
        
        init: function() {
            this.initBarcodeScanner();
            this.initProductSearch();
            this.initPaymentMethods();
        },

        initBarcodeScanner: function() {
            $('#barcode-input').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    var barcode = $(this).val();
                    if (barcode) {
                        POS.searchProductByBarcode(barcode);
                        $(this).val('');
                    }
                }
            });
        },

        initProductSearch: function() {
            $('#product-search').on('input', function() {
                var query = $(this).val();
                if (query.length >= 2) {
                    POS.searchProducts(query);
                }
            });
        },

        initPaymentMethods: function() {
            $('.payment-method').on('click', function() {
                $('.payment-method').removeClass('active');
                $(this).addClass('active');
                var method = $(this).data('method');
                POS.selectPaymentMethod(method);
            });
        },

        searchProductByBarcode: function(barcode) {
            // Implementation for barcode search
            console.log('Searching for barcode:', barcode);
        },

        searchProducts: function(query) {
            // Implementation for product search
            console.log('Searching for products:', query);
        },

        addToCart: function(product) {
            // Implementation for adding to cart
            console.log('Adding to cart:', product);
        },

        removeFromCart: function(index) {
            // Implementation for removing from cart
            console.log('Removing from cart:', index);
        },

        selectPaymentMethod: function(method) {
            // Implementation for payment method selection
            console.log('Payment method selected:', method);
        },

        processPayment: function() {
            // Implementation for payment processing
            console.log('Processing payment...');
        }
    };

    // Appointments specific functions
    var Appointments = {
        init: function() {
            this.initCalendar();
            this.initTimeSlots();
        },

        initCalendar: function() {
            // Calendar initialization
            console.log('Initializing calendar...');
        },

        initTimeSlots: function() {
            // Time slots initialization
            console.log('Initializing time slots...');
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        PerfectPharma.init();
        
        // Initialize specific modules based on page
        if ($('body').hasClass('pos-page')) {
            POS.init();
        }
        
        if ($('body').hasClass('appointments-page')) {
            Appointments.init();
        }
    });

    // Export to global scope
    window.PerfectPharma = PerfectPharma;
    window.POS = POS;
    window.Appointments = Appointments;

})(jQuery);

/**
 * Perfect Pharma - Inventory Management JavaScript
 * 
 * This file contains JavaScript functions for inventory management
 */

(function($) {
    'use strict';

    // Initialize inventory management
    var InventoryManager = {
        init: function() {
            this.bindEvents();
            this.initializeComponents();
        },

        bindEvents: function() {
            // Stock adjustment modals
            $(document).on('click', '[data-bs-target="#addStockModal"]', this.showAddStockModal);
            $(document).on('click', '[data-bs-target="#removeStockModal"]', this.showRemoveStockModal);
            $(document).on('click', '[data-bs-target="#adjustStockModal"]', this.showAdjustStockModal);

            // Form validations
            $(document).on('submit', '.inventory-form', this.validateForm);

            // Real-time calculations
            $(document).on('input', 'input[name="cost_price"], input[name="selling_price"]', this.calculateProfitMargin);
            $(document).on('input', 'input[name="minimum_stock_level"], input[name="maximum_stock_level"], input[name="reorder_level"]', this.validateStockLevels);
        },

        initializeComponents: function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Auto-refresh alerts every 5 minutes
            if (window.location.pathname.includes('inventory')) {
                setInterval(this.refreshAlerts, 300000);
            }
        },

        showAddStockModal: function() {
            $('#addStockModal').modal('show');
        },

        showRemoveStockModal: function() {
            $('#removeStockModal').modal('show');
        },

        showAdjustStockModal: function() {
            $('#adjustStockModal').modal('show');
        },

        calculateProfitMargin: function() {
            var costPrice = parseFloat($('input[name="cost_price"]').val()) || 0;
            var sellingPrice = parseFloat($('input[name="selling_price"]').val()) || 0;
            var profitMarginField = $('#profit_margin');

            if (costPrice > 0) {
                var margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
                profitMarginField.val(margin + '%');

                // Change color based on margin
                if (margin < 10) {
                    profitMarginField.removeClass('text-success text-warning').addClass('text-danger');
                } else if (margin < 20) {
                    profitMarginField.removeClass('text-success text-danger').addClass('text-warning');
                } else {
                    profitMarginField.removeClass('text-danger text-warning').addClass('text-success');
                }
            } else {
                profitMarginField.val('0%').removeClass('text-success text-warning text-danger');
            }
        },

        validateStockLevels: function() {
            var minLevel = parseInt($('input[name="minimum_stock_level"]').val()) || 0;
            var maxLevel = parseInt($('input[name="maximum_stock_level"]').val()) || 0;
            var reorderLevel = parseInt($('input[name="reorder_level"]').val()) || 0;

            var maxLevelField = $('input[name="maximum_stock_level"]')[0];
            var reorderLevelField = $('input[name="reorder_level"]')[0];

            if (minLevel >= maxLevel) {
                maxLevelField.setCustomValidity('الحد الأقصى يجب أن يكون أكبر من الحد الأدنى');
            } else {
                maxLevelField.setCustomValidity('');
            }

            if (reorderLevel < minLevel) {
                reorderLevelField.setCustomValidity('مستوى إعادة الطلب يجب أن يكون أكبر من أو يساوي الحد الأدنى');
            } else {
                reorderLevelField.setCustomValidity('');
            }
        },

        validateForm: function(e) {
            var form = $(this);
            var isValid = true;

            // Check required fields
            form.find('[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // Check stock levels
            var minLevel = parseInt(form.find('input[name="minimum_stock_level"]').val()) || 0;
            var maxLevel = parseInt(form.find('input[name="maximum_stock_level"]').val()) || 0;
            var reorderLevel = parseInt(form.find('input[name="reorder_level"]').val()) || 0;

            if (minLevel >= maxLevel) {
                isValid = false;
                form.find('input[name="maximum_stock_level"]').addClass('is-invalid');
            }

            if (reorderLevel < minLevel) {
                isValid = false;
                form.find('input[name="reorder_level"]').addClass('is-invalid');
            }

            if (!isValid) {
                e.preventDefault();
                this.showValidationError('يرجى تصحيح الأخطاء في النموذج');
            }
        },

        refreshAlerts: function() {
            // Refresh alerts count in navigation
            $.get('/admin/perfect-pharma/inventory/alerts', function(data) {
                // Update alerts badge if exists
                var alertsBadge = $('.alerts-badge');
                if (alertsBadge.length) {
                    // Extract alerts count from response
                    var alertsCount = $(data).find('.alert-item').length;
                    if (alertsCount > 0) {
                        alertsBadge.text(alertsCount).show();
                    } else {
                        alertsBadge.hide();
                    }
                }
            }).fail(function() {
                console.log('Failed to refresh alerts');
            });
        },

        showValidationError: function(message) {
            // Show validation error message
            if (typeof Botble !== 'undefined' && Botble.showError) {
                Botble.showError(message);
            } else {
                alert(message);
            }
        },

        showSuccess: function(message) {
            // Show success message
            if (typeof Botble !== 'undefined' && Botble.showSuccess) {
                Botble.showSuccess(message);
            } else {
                alert(message);
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        InventoryManager.init();
    });

    // Global functions for inventory management
    window.showAddStockModal = function() {
        InventoryManager.showAddStockModal();
    };

    window.showRemoveStockModal = function() {
        InventoryManager.showRemoveStockModal();
    };

    window.showAdjustStockModal = function() {
        InventoryManager.showAdjustStockModal();
    };

})(jQuery);

// POS Cashier System JavaScript
(function($) {
    'use strict';

    var POSCashier = {
        cart: [],
        selectedPaymentMethod: 'cash',

        init: function() {
            this.bindEvents();
            this.updateCartDisplay();
        },

        bindEvents: function() {
            // البحث عن المنتجات
            $('#product-search').on('input', this.searchProducts.bind(this));

            // إضافة منتج للسلة
            $(document).on('click', '.product-card', this.addToCart.bind(this));

            // تحديث الكمية
            $(document).on('click', '.quantity-increase', this.increaseQuantity.bind(this));
            $(document).on('click', '.quantity-decrease', this.decreaseQuantity.bind(this));
            $(document).on('change', '.quantity-input', this.updateQuantity.bind(this));

            // حذف من السلة
            $(document).on('click', '.remove-item', this.removeFromCart.bind(this));

            // مسح السلة
            $('#clear-cart').on('click', this.clearCart.bind(this));

            // طرق الدفع
            $('.payment-method').on('click', this.selectPaymentMethod.bind(this));

            // مبلغ الدفع
            $('#payment-amount').on('input', this.calculateChange.bind(this));

            // إتمام البيع
            $('#complete-sale').on('click', this.showSaleConfirmation.bind(this));
            $('#confirm-sale').on('click', this.completeSale.bind(this));
        },

        searchProducts: function() {
            var query = $('#product-search').val();

            if (query.length < 2) {
                $('#search-results').hide();
                return;
            }

            $.ajax({
                url: '/admin/perfect-pharma/pos/search-products',
                method: 'GET',
                data: { q: query },
                success: function(products) {
                    this.displaySearchResults(products);
                }.bind(this)
            });
        },

        displaySearchResults: function(products) {
            var html = '';

            products.forEach(function(product) {
                html += `
                    <div class="search-result-item p-2 border-bottom"
                         data-product-id="${product.id}"
                         data-product-name="${product.name}"
                         data-product-price="${product.price}"
                         data-product-stock="${product.stock}"
                         data-inventory-id="${product.inventory_id}"
                         style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${product.name}</strong>
                                <br><small class="text-muted">الرمز: ${product.sku}</small>
                            </div>
                            <div class="text-end">
                                <div class="text-success fw-bold">${product.price} ريال</div>
                                <small class="text-muted">المتاح: ${product.stock}</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            $('#search-results-list').html(html);
            $('#search-results').show();

            // إضافة حدث النقر لنتائج البحث
            $('.search-result-item').on('click', function() {
                var productData = $(this).data();
                POSCashier.addProductToCart(productData);
                $('#search-results').hide();
                $('#product-search').val('');
            });
        },

        addToCart: function(e) {
            var productData = $(e.currentTarget).data();
            this.addProductToCart(productData);
        },

        addProductToCart: function(productData) {
            var existingItem = this.cart.find(item => item.productId == productData.productId);

            if (existingItem) {
                if (existingItem.quantity < productData.productStock) {
                    existingItem.quantity++;
                } else {
                    this.showMessage('الكمية المطلوبة غير متوفرة', 'warning');
                    return;
                }
            } else {
                this.cart.push({
                    productId: productData.productId,
                    productName: productData.productName,
                    price: parseFloat(productData.productPrice),
                    quantity: 1,
                    stock: productData.productStock,
                    inventoryId: productData.inventoryId,
                    batchNumber: productData.batchNumber,
                    expiryDate: productData.expiryDate,
                    prescriptionRequired: productData.prescriptionRequired === 'true'
                });
            }

            this.updateCartDisplay();
        },

        increaseQuantity: function(e) {
            var index = $(e.currentTarget).data('index');
            var item = this.cart[index];

            if (item.quantity < item.stock) {
                item.quantity++;
                this.updateCartDisplay();
            } else {
                this.showMessage('الكمية المطلوبة غير متوفرة', 'warning');
            }
        },

        decreaseQuantity: function(e) {
            var index = $(e.currentTarget).data('index');
            var item = this.cart[index];

            if (item.quantity > 1) {
                item.quantity--;
                this.updateCartDisplay();
            }
        },

        updateQuantity: function(e) {
            var index = $(e.currentTarget).data('index');
            var newQuantity = parseInt($(e.currentTarget).val());
            var item = this.cart[index];

            if (newQuantity > 0 && newQuantity <= item.stock) {
                item.quantity = newQuantity;
                this.updateCartDisplay();
            } else {
                $(e.currentTarget).val(item.quantity);
                this.showMessage('كمية غير صحيحة', 'warning');
            }
        },

        removeFromCart: function(e) {
            var index = $(e.currentTarget).data('index');
            this.cart.splice(index, 1);
            this.updateCartDisplay();
        },

        clearCart: function() {
            if (this.cart.length > 0) {
                if (confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
                    this.cart = [];
                    this.updateCartDisplay();
                }
            }
        },

        updateCartDisplay: function() {
            var container = $('#cart-items-container');
            var emptyMessage = $('#empty-cart-message');

            if (this.cart.length === 0) {
                container.html('');
                emptyMessage.show();
                $('#complete-sale, #hold-sale').prop('disabled', true);
                this.updateTotals();
                return;
            }

            emptyMessage.hide();

            var html = '';
            var subtotal = 0;

            this.cart.forEach(function(item, index) {
                var itemTotal = item.price * item.quantity;
                subtotal += itemTotal;

                html += `
                    <div class="cart-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${item.productName}</h6>
                                <small class="text-muted">${item.price} ريال × ${item.quantity}</small>
                                ${item.prescriptionRequired ? '<br><span class="badge bg-warning badge-sm">يتطلب وصفة</span>' : ''}
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger remove-item" data-index="${index}">
                                <i class="ti ti-x"></i>
                            </button>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="quantity-controls">
                                <button type="button" class="quantity-decrease" data-index="${index}">-</button>
                                <input type="number" class="quantity-input" data-index="${index}" value="${item.quantity}" min="1" max="${item.stock}">
                                <button type="button" class="quantity-increase" data-index="${index}">+</button>
                            </div>
                            <div class="fw-bold text-success">${itemTotal.toFixed(2)} ريال</div>
                        </div>
                    </div>
                `;
            });

            container.html(html);
            $('#complete-sale, #hold-sale').prop('disabled', false);
            this.updateTotals();
        },

        updateTotals: function() {
            var subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            var discount = 0; // يمكن إضافة منطق الخصم هنا
            var tax = 0; // يمكن إضافة منطق الضريبة هنا
            var total = subtotal - discount + tax;

            $('#subtotal-display').text(subtotal.toFixed(2) + ' ريال');
            $('#discount-display').text(discount.toFixed(2) + ' ريال');
            $('#tax-display').text(tax.toFixed(2) + ' ريال');
            $('#total-display').text(total.toFixed(2) + ' ريال');

            // تحديث مبلغ الدفع الافتراضي
            if (!$('#payment-amount').val()) {
                $('#payment-amount').val(total.toFixed(2));
            }

            this.calculateChange();
        },

        selectPaymentMethod: function(e) {
            $('.payment-method').removeClass('active');
            $(e.currentTarget).addClass('active');
            this.selectedPaymentMethod = $(e.currentTarget).data('method');
        },

        calculateChange: function() {
            var total = parseFloat($('#total-display').text().replace(' ريال', ''));
            var paid = parseFloat($('#payment-amount').val()) || 0;
            var change = Math.max(0, paid - total);

            $('#change-display').text(change.toFixed(2) + ' ريال');
        },

        showSaleConfirmation: function() {
            if (this.cart.length === 0) {
                this.showMessage('السلة فارغة', 'warning');
                return;
            }

            var total = parseFloat($('#total-display').text().replace(' ريال', ''));
            var paid = parseFloat($('#payment-amount').val()) || 0;

            if (paid < total) {
                this.showMessage('المبلغ المدفوع أقل من الإجمالي', 'warning');
                return;
            }

            // عرض ملخص البيع
            var summaryHtml = this.generateSaleSummary();
            $('#sale-summary').html(summaryHtml);
            $('#sale-confirmation-modal').modal('show');
        },

        generateSaleSummary: function() {
            var html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr></thead><tbody>';

            this.cart.forEach(function(item) {
                html += `<tr>
                    <td>${item.productName}</td>
                    <td>${item.quantity}</td>
                    <td>${item.price.toFixed(2)}</td>
                    <td>${(item.price * item.quantity).toFixed(2)}</td>
                </tr>`;
            });

            html += '</tbody></table></div>';

            var total = parseFloat($('#total-display').text().replace(' ريال', ''));
            var paid = parseFloat($('#payment-amount').val());
            var change = paid - total;

            html += `<div class="mt-3">
                <div class="d-flex justify-content-between"><span>الإجمالي:</span><span>${total.toFixed(2)} ريال</span></div>
                <div class="d-flex justify-content-between"><span>المدفوع:</span><span>${paid.toFixed(2)} ريال</span></div>
                <div class="d-flex justify-content-between"><span>الباقي:</span><span>${change.toFixed(2)} ريال</span></div>
                <div class="d-flex justify-content-between"><span>طريقة الدفع:</span><span>${this.getPaymentMethodLabel()}</span></div>
            </div>`;

            return html;
        },

        getPaymentMethodLabel: function() {
            var labels = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'wallet': 'محفظة رقمية',
                'insurance': 'تأمين'
            };
            return labels[this.selectedPaymentMethod] || 'غير محدد';
        },

        completeSale: function() {
            var saleData = {
                items: this.cart.map(item => ({
                    product_id: item.productId,
                    quantity: item.quantity,
                    price: item.price
                })),
                customer_id: $('#customer-select').val() || null,
                payment_method: this.selectedPaymentMethod,
                payment_amount: parseFloat($('#payment-amount').val()),
                _token: $('meta[name="csrf-token"]').attr('content')
            };

            $.ajax({
                url: '/admin/perfect-pharma/pos/create-sale',
                method: 'POST',
                data: saleData,
                success: function(response) {
                    if (response.error) {
                        this.showMessage(response.message, 'error');
                    } else {
                        this.showMessage('تم إنجاز البيع بنجاح', 'success');
                        this.cart = [];
                        this.updateCartDisplay();
                        $('#sale-confirmation-modal').modal('hide');

                        // فتح صفحة الطباعة
                        if (response.data && response.data.sale_id) {
                            window.open(`/admin/perfect-pharma/pos/sales/${response.data.sale_id}/receipt`, '_blank');
                        }
                    }
                }.bind(this),
                error: function() {
                    this.showMessage('حدث خطأ أثناء معالجة البيع', 'error');
                }.bind(this)
            });
        },

        showMessage: function(message, type) {
            // يمكن استخدام نظام التنبيهات الموجود في Botble
            if (typeof Botble !== 'undefined' && Botble.showNotification) {
                Botble.showNotification(message, type);
            } else {
                alert(message);
            }
        }
    };

    // تهيئة النظام عند تحميل الصفحة
    $(document).ready(function() {
        if ($('#cart-items-container').length) {
            POSCashier.init();
        }
    });

    // تصدير للاستخدام العام
    window.POSCashier = POSCashier;

})(jQuery);

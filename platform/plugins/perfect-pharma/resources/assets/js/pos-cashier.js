/**
 * Perfect Pharma - POS Cashier System JavaScript
 * 
 * This file contains JavaScript functions for the POS cashier interface
 */

(function($) {
    'use strict';

    var POSCashier = {
        cart: [],
        selectedPaymentMethod: 'cash',
        
        init: function() {
            this.bindEvents();
            this.updateCartDisplay();
            this.loadShortcuts();
        },

        bindEvents: function() {
            // البحث عن المنتجات
            $('#product-search').on('input', this.debounce(this.searchProducts.bind(this), 300));
            
            // إضافة منتج للسلة
            $(document).on('click', '.product-card', this.addToCart.bind(this));
            $(document).on('click', '.search-result-item', this.addToCartFromSearch.bind(this));
            
            // تحديث الكمية
            $(document).on('click', '.quantity-increase', this.increaseQuantity.bind(this));
            $(document).on('click', '.quantity-decrease', this.decreaseQuantity.bind(this));
            $(document).on('change', '.quantity-input', this.updateQuantity.bind(this));
            
            // حذف من السلة
            $(document).on('click', '.remove-item', this.removeFromCart.bind(this));
            
            // مسح السلة
            $('#clear-cart').on('click', this.clearCart.bind(this));
            
            // طرق الدفع
            $('.payment-method').on('click', this.selectPaymentMethod.bind(this));
            
            // مبلغ الدفع
            $('#payment-amount').on('input', this.calculateChange.bind(this));
            
            // إتمام البيع
            $('#complete-sale').on('click', this.showSaleConfirmation.bind(this));
            $('#confirm-sale').on('click', this.completeSale.bind(this));
            
            // تعليق البيع
            $('#hold-sale').on('click', this.holdSale.bind(this));
            
            // اختصارات لوحة المفاتيح
            $(document).on('keydown', this.handleKeyboardShortcuts.bind(this));
            
            // إخفاء نتائج البحث عند النقر خارجها
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#product-search, #search-results').length) {
                    $('#search-results').hide();
                }
            });
        },

        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        searchProducts: function() {
            var query = $('#product-search').val().trim();
            
            if (query.length < 2) {
                $('#search-results').hide();
                return;
            }

            $.ajax({
                url: '/admin/perfect-pharma/pos/search-products',
                method: 'GET',
                data: { 
                    q: query,
                    pharmacy_id: this.getCurrentPharmacyId()
                },
                success: function(products) {
                    this.displaySearchResults(products);
                }.bind(this),
                error: function() {
                    this.showMessage('خطأ في البحث عن المنتجات', 'error');
                }.bind(this)
            });
        },

        displaySearchResults: function(products) {
            var html = '';
            
            if (products.length === 0) {
                html = '<div class="text-center py-3 text-muted">لا توجد نتائج</div>';
            } else {
                products.forEach(function(product) {
                    html += `
                        <div class="search-result-item p-2 border-bottom" 
                             data-product='${JSON.stringify(product)}'
                             style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${product.name}</strong>
                                    <br><small class="text-muted">الرمز: ${product.sku || 'غير محدد'}</small>
                                    ${product.is_prescription_required ? '<br><span class="badge bg-warning badge-sm">يتطلب وصفة</span>' : ''}
                                </div>
                                <div class="text-end">
                                    <div class="text-success fw-bold">${parseFloat(product.price).toFixed(2)} ريال</div>
                                    <small class="text-muted">المتاح: ${product.stock}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }
            
            $('#search-results-list').html(html);
            $('#search-results').show();
        },

        addToCart: function(e) {
            var $card = $(e.currentTarget);
            var productData = {
                productId: $card.data('product-id'),
                productName: $card.data('product-name'),
                price: parseFloat($card.data('product-price')),
                stock: parseInt($card.data('product-stock')),
                inventoryId: $card.data('inventory-id'),
                batchNumber: $card.data('batch-number'),
                expiryDate: $card.data('expiry-date'),
                prescriptionRequired: $card.data('prescription-required') === true
            };
            
            this.addProductToCart(productData);
        },

        addToCartFromSearch: function(e) {
            var productData = JSON.parse($(e.currentTarget).data('product'));
            var formattedData = {
                productId: productData.id,
                productName: productData.name,
                price: parseFloat(productData.price),
                stock: parseInt(productData.stock),
                inventoryId: productData.inventory_id,
                batchNumber: productData.batch_number,
                expiryDate: productData.expiry_date,
                prescriptionRequired: productData.is_prescription_required
            };
            
            this.addProductToCart(formattedData);
            $('#search-results').hide();
            $('#product-search').val('');
        },

        addProductToCart: function(productData) {
            // التحقق من صحة البيانات
            if (!productData.productId || !productData.productName || productData.price < 0) {
                this.showMessage('بيانات المنتج غير صحيحة', 'error');
                return;
            }

            // التحقق من توفر المخزون
            if (productData.stock <= 0) {
                this.showMessage('المنتج غير متوفر في المخزون', 'warning');
                return;
            }

            var existingItem = this.cart.find(item => item.productId == productData.productId);
            
            if (existingItem) {
                if (existingItem.quantity < productData.stock) {
                    existingItem.quantity++;
                    this.showMessage(`تم زيادة كمية ${productData.productName}`, 'success');
                } else {
                    this.showMessage('الكمية المطلوبة غير متوفرة', 'warning');
                    return;
                }
            } else {
                this.cart.push({
                    productId: productData.productId,
                    productName: productData.productName,
                    price: productData.price,
                    quantity: 1,
                    stock: productData.stock,
                    inventoryId: productData.inventoryId,
                    batchNumber: productData.batchNumber,
                    expiryDate: productData.expiryDate,
                    prescriptionRequired: productData.prescriptionRequired
                });
                
                this.showMessage(`تم إضافة ${productData.productName} للسلة`, 'success');
            }
            
            this.updateCartDisplay();
            this.saveCartToStorage();
        },

        increaseQuantity: function(e) {
            var index = $(e.currentTarget).data('index');
            var item = this.cart[index];
            
            if (item.quantity < item.stock) {
                item.quantity++;
                this.updateCartDisplay();
                this.saveCartToStorage();
            } else {
                this.showMessage('الكمية المطلوبة غير متوفرة', 'warning');
            }
        },

        decreaseQuantity: function(e) {
            var index = $(e.currentTarget).data('index');
            var item = this.cart[index];
            
            if (item.quantity > 1) {
                item.quantity--;
                this.updateCartDisplay();
                this.saveCartToStorage();
            }
        },

        updateQuantity: function(e) {
            var index = $(e.currentTarget).data('index');
            var newQuantity = parseInt($(e.currentTarget).val());
            var item = this.cart[index];
            
            if (isNaN(newQuantity) || newQuantity <= 0) {
                $(e.currentTarget).val(item.quantity);
                this.showMessage('كمية غير صحيحة', 'warning');
                return;
            }
            
            if (newQuantity <= item.stock) {
                item.quantity = newQuantity;
                this.updateCartDisplay();
                this.saveCartToStorage();
            } else {
                $(e.currentTarget).val(item.quantity);
                this.showMessage('الكمية المطلوبة غير متوفرة', 'warning');
            }
        },

        removeFromCart: function(e) {
            var index = $(e.currentTarget).data('index');
            var item = this.cart[index];
            
            if (confirm(`هل أنت متأكد من حذف ${item.productName}؟`)) {
                this.cart.splice(index, 1);
                this.updateCartDisplay();
                this.saveCartToStorage();
                this.showMessage('تم حذف المنتج من السلة', 'info');
            }
        },

        clearCart: function() {
            if (this.cart.length > 0) {
                if (confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
                    this.cart = [];
                    this.updateCartDisplay();
                    this.saveCartToStorage();
                    this.showMessage('تم مسح السلة', 'info');
                }
            }
        },

        updateCartDisplay: function() {
            var container = $('#cart-items-container');
            var emptyMessage = $('#empty-cart-message');
            
            if (this.cart.length === 0) {
                container.html('');
                emptyMessage.show();
                $('#complete-sale, #hold-sale').prop('disabled', true);
                this.updateTotals();
                return;
            }
            
            emptyMessage.hide();
            
            var html = '';
            
            this.cart.forEach(function(item, index) {
                var itemTotal = item.price * item.quantity;
                
                html += `
                    <div class="cart-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${item.productName}</h6>
                                <small class="text-muted">${item.price.toFixed(2)} ريال × ${item.quantity}</small>
                                ${item.prescriptionRequired ? '<br><span class="badge bg-warning badge-sm">يتطلب وصفة</span>' : ''}
                                ${item.expiryDate ? `<br><small class="text-muted">انتهاء: ${item.expiryDate}</small>` : ''}
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger remove-item" data-index="${index}" title="حذف">
                                <i class="ti ti-x"></i>
                            </button>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="quantity-controls">
                                <button type="button" class="quantity-decrease" data-index="${index}" title="تقليل">-</button>
                                <input type="number" class="quantity-input" data-index="${index}" value="${item.quantity}" min="1" max="${item.stock}">
                                <button type="button" class="quantity-increase" data-index="${index}" title="زيادة">+</button>
                            </div>
                            <div class="fw-bold text-success">${itemTotal.toFixed(2)} ريال</div>
                        </div>
                    </div>
                `;
            });
            
            container.html(html);
            $('#complete-sale, #hold-sale').prop('disabled', false);
            this.updateTotals();
        },

        updateTotals: function() {
            var subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            var discount = 0; // يمكن إضافة منطق الخصم هنا
            var tax = subtotal * 0.15; // ضريبة القيمة المضافة 15%
            var total = subtotal - discount + tax;

            $('#subtotal-display').text(this.formatPrice(subtotal));
            $('#discount-display').text(this.formatPrice(discount));
            $('#tax-display').text(this.formatPrice(tax));
            $('#total-display').text(this.formatPrice(total));

            // تحديث مبلغ الدفع الافتراضي
            var currentPayment = parseFloat($('#payment-amount').val()) || 0;
            if (currentPayment === 0 || currentPayment < total) {
                $('#payment-amount').val(total.toFixed(2));
            }

            this.calculateChange();
        },

        selectPaymentMethod: function(e) {
            $('.payment-method').removeClass('active');
            $(e.currentTarget).addClass('active');
            this.selectedPaymentMethod = $(e.currentTarget).data('method');
            
            // تحديث واجهة الدفع حسب الطريقة
            this.updatePaymentInterface();
        },

        updatePaymentInterface: function() {
            var total = parseFloat($('#total-display').text().replace(' ريال', ''));
            
            // للدفع النقدي، اقتراح مبالغ سريعة
            if (this.selectedPaymentMethod === 'cash') {
                $('#payment-amount').val(total.toFixed(2));
            }
        },

        calculateChange: function() {
            // استخراج القيمة الرقمية من النص المنسق
            var totalText = $('#total-display').text();
            var total = this.extractNumericValue(totalText);
            var paid = parseFloat($('#payment-amount').val()) || 0;
            var change = Math.max(0, paid - total);

            $('#change-display').text(this.formatPrice(change));

            // تغيير لون الباقي
            if (change > 0) {
                $('#change-display').removeClass('text-danger').addClass('text-success');
            } else if (paid < total) {
                $('#change-display').removeClass('text-success').addClass('text-danger');
            } else {
                $('#change-display').removeClass('text-success text-danger');
            }
        },

        showSaleConfirmation: function() {
            if (this.cart.length === 0) {
                this.showMessage('السلة فارغة', 'warning');
                return;
            }
            
            var total = parseFloat($('#total-display').text().replace(' ريال', '')) || 0;
            var paid = parseFloat($('#payment-amount').val()) || 0;
            
            if (paid < total) {
                this.showMessage('المبلغ المدفوع أقل من الإجمالي', 'warning');
                $('#payment-amount').focus();
                return;
            }
            
            // التحقق من المنتجات التي تتطلب وصفة
            var prescriptionItems = this.cart.filter(item => item.prescriptionRequired);
            if (prescriptionItems.length > 0) {
                var itemNames = prescriptionItems.map(item => item.productName).join(', ');
                if (!confirm(`تحتوي السلة على منتجات تتطلب وصفة طبية: ${itemNames}\nهل تريد المتابعة؟`)) {
                    return;
                }
            }
            
            // عرض ملخص البيع
            var summaryHtml = this.generateSaleSummary();
            $('#sale-summary').html(summaryHtml);
            $('#sale-confirmation-modal').modal('show');
        },

        generateSaleSummary: function() {
            var html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr></thead><tbody>';
            
            this.cart.forEach(function(item) {
                html += `<tr>
                    <td>${item.productName}</td>
                    <td>${item.quantity}</td>
                    <td>${item.price.toFixed(2)}</td>
                    <td>${(item.price * item.quantity).toFixed(2)}</td>
                </tr>`;
            });
            
            html += '</tbody></table></div>';
            
            var subtotal = parseFloat($('#subtotal-display').text().replace(' ريال', ''));
            var tax = parseFloat($('#tax-display').text().replace(' ريال', ''));
            var total = parseFloat($('#total-display').text().replace(' ريال', ''));
            var paid = parseFloat($('#payment-amount').val());
            var change = paid - total;
            
            html += `<div class="mt-3">
                <div class="d-flex justify-content-between"><span>المجموع الفرعي:</span><span>${subtotal.toFixed(2)} ريال</span></div>
                <div class="d-flex justify-content-between"><span>الضريبة (15%):</span><span>${tax.toFixed(2)} ريال</span></div>
                <div class="d-flex justify-content-between fw-bold"><span>الإجمالي:</span><span>${total.toFixed(2)} ريال</span></div>
                <hr>
                <div class="d-flex justify-content-between"><span>المدفوع:</span><span>${paid.toFixed(2)} ريال</span></div>
                <div class="d-flex justify-content-between"><span>الباقي:</span><span>${change.toFixed(2)} ريال</span></div>
                <div class="d-flex justify-content-between"><span>طريقة الدفع:</span><span>${this.getPaymentMethodLabel()}</span></div>
            </div>`;
            
            return html;
        },

        getPaymentMethodLabel: function() {
            var labels = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'wallet': 'محفظة رقمية',
                'bank_transfer': 'تحويل بنكي',
                'insurance': 'تأمين',
                'credit': 'آجل'
            };
            return labels[this.selectedPaymentMethod] || 'غير محدد';
        },

        completeSale: function() {
            var saleData = {
                items: this.cart.map(item => ({
                    product_id: item.productId,
                    quantity: item.quantity,
                    price: item.price,
                    inventory_id: item.inventoryId
                })),
                customer_id: $('#customer-select').val() || null,
                payment_method: this.selectedPaymentMethod,
                payment_amount: parseFloat($('#payment-amount').val()),
                discount_amount: this.extractNumericValue($('#discount-display').text()) || 0,
                notes: '',
                _token: $('meta[name="csrf-token"]').attr('content')
            };
            
            // تعطيل الزر لمنع الإرسال المتكرر
            $('#confirm-sale').prop('disabled', true).text('جاري المعالجة...');
            
            $.ajax({
                url: '/admin/perfect-pharma/pos/create-sale',
                method: 'POST',
                data: saleData,
                success: function(response) {
                    if (response.error) {
                        this.showMessage(response.message, 'error');
                    } else {
                        this.showMessage('تم إنجاز البيع بنجاح', 'success');
                        
                        // مسح السلة
                        this.cart = [];
                        this.updateCartDisplay();
                        this.clearCartFromStorage();
                        
                        // إغلاق النافذة
                        $('#sale-confirmation-modal').modal('hide');
                        
                        // فتح صفحة الطباعة
                        if (response.data && response.data.sale_id) {
                            window.open(`/admin/perfect-pharma/pos/sales/${response.data.sale_id}/receipt`, '_blank');
                        }
                        
                        // إعادة تعيين النموذج
                        $('#customer-select').val('');
                        $('#payment-amount').val('');
                    }
                }.bind(this),
                error: function(xhr) {
                    var message = 'حدث خطأ أثناء معالجة البيع';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    this.showMessage(message, 'error');
                }.bind(this),
                complete: function() {
                    $('#confirm-sale').prop('disabled', false).text('تأكيد البيع');
                }
            });
        },

        holdSale: function() {
            if (this.cart.length === 0) {
                this.showMessage('السلة فارغة', 'warning');
                return;
            }
            
            // حفظ البيع المعلق في التخزين المحلي
            var heldSales = JSON.parse(localStorage.getItem('pos_held_sales') || '[]');
            var heldSale = {
                id: Date.now(),
                cart: this.cart,
                customer_id: $('#customer-select').val(),
                payment_method: this.selectedPaymentMethod,
                created_at: new Date().toISOString()
            };
            
            heldSales.push(heldSale);
            localStorage.setItem('pos_held_sales', JSON.stringify(heldSales));
            
            // مسح السلة الحالية
            this.cart = [];
            this.updateCartDisplay();
            this.clearCartFromStorage();
            
            this.showMessage('تم تعليق البيع', 'info');
        },

        loadShortcuts: function() {
            // تحميل الاختصارات المحفوظة
            // يمكن إضافة منطق تحميل الاختصارات هنا
        },

        handleKeyboardShortcuts: function(e) {
            // F1 - فتح البحث
            if (e.key === 'F1') {
                e.preventDefault();
                $('#product-search').focus();
            }
            
            // F2 - مسح السلة
            if (e.key === 'F2') {
                e.preventDefault();
                this.clearCart();
            }
            
            // F3 - تعليق البيع
            if (e.key === 'F3') {
                e.preventDefault();
                this.holdSale();
            }
            
            // F4 - إتمام البيع
            if (e.key === 'F4') {
                e.preventDefault();
                this.showSaleConfirmation();
            }
            
            // Enter في حقل البحث
            if (e.key === 'Enter' && $(e.target).is('#product-search')) {
                e.preventDefault();
                var firstResult = $('.search-result-item').first();
                if (firstResult.length) {
                    firstResult.click();
                }
            }
        },

        getCurrentPharmacyId: function() {
            // استخراج معرف الصيدلية من الجلسة الحالية
            return window.currentPharmacyId || null;
        },

        // تنسيق السعر باستخدام مساعد العملة
        formatPrice: function(amount) {
            return window.CurrencyHelper ? window.CurrencyHelper.formatPrice(amount) : amount.toFixed(2);
        },

        // استخراج القيمة الرقمية من النص المنسق
        extractNumericValue: function(formattedText) {
            return window.CurrencyHelper ? window.CurrencyHelper.extractNumericValue(formattedText) : parseFloat(formattedText) || 0;
        },

        saveCartToStorage: function() {
            localStorage.setItem('pos_current_cart', JSON.stringify(this.cart));
        },

        loadCartFromStorage: function() {
            var savedCart = localStorage.getItem('pos_current_cart');
            if (savedCart) {
                this.cart = JSON.parse(savedCart);
                this.updateCartDisplay();
            }
        },

        clearCartFromStorage: function() {
            localStorage.removeItem('pos_current_cart');
        },

        showMessage: function(message, type) {
            // استخدام نظام التنبيهات الموجود في Botble
            if (typeof Botble !== 'undefined' && Botble.showNotification) {
                Botble.showNotification(message, type);
            } else if (typeof toastr !== 'undefined') {
                toastr[type](message);
            } else {
                // fallback للتنبيه العادي
                var alertClass = type === 'error' ? 'danger' : type;
                var alertHtml = `<div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>`;
                
                // إضافة التنبيه في أعلى الصفحة
                if ($('.alert-container').length === 0) {
                    $('body').prepend('<div class="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>');
                }
                
                $('.alert-container').append(alertHtml);
                
                // إزالة التنبيه تلقائياً بعد 5 ثوان
                setTimeout(function() {
                    $('.alert-container .alert').first().remove();
                }, 5000);
            }
        }
    };

    // تهيئة النظام عند تحميل الصفحة
    $(document).ready(function() {
        if ($('#cart-items-container').length) {
            POSCashier.init();
            POSCashier.loadCartFromStorage();
        }
    });

    // حفظ السلة عند إغلاق الصفحة
    $(window).on('beforeunload', function() {
        if (POSCashier.cart.length > 0) {
            POSCashier.saveCartToStorage();
        }
    });

    // تصدير للاستخدام العام
    window.POSCashier = POSCashier;

})(jQuery);

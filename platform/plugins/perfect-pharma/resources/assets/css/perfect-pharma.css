/**
 * Perfect Pharma CSS
 * Main stylesheet for Perfect Pharma plugin
 */

/* General Styles */
.perfect-pharma-container {
    direction: rtl;
    text-align: right;
}

.perfect-pharma-card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.perfect-pharma-btn {
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.perfect-pharma-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-active { background-color: #d4edda; color: #155724; }
.status-inactive { background-color: #f8d7da; color: #721c24; }
.status-pending { background-color: #fff3cd; color: #856404; }
.status-completed { background-color: #d1ecf1; color: #0c5460; }

/* POS Styles */
.pos-container {
    height: 100vh;
    overflow: hidden;
}

.pos-products-panel {
    height: 100%;
    overflow-y: auto;
    background: #f8f9fa;
    padding: 15px;
}

.pos-cart-panel {
    height: 100%;
    background: white;
    border-left: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
}

.pos-product-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.pos-product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.pos-cart-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
}

.pos-cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.pos-cart-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
}

.pos-cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f1f1;
}

.pos-total-section {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.pos-payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.payment-method {
    padding: 15px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.payment-method:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.payment-method.active {
    border-color: #007bff;
    background: #e7f3ff;
    color: #007bff;
}

/* Appointments Styles */
.appointment-calendar {
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.appointment-time-slots {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.time-slot {
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.time-slot:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.time-slot.available {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.time-slot.booked {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    cursor: not-allowed;
}

.time-slot.selected {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

/* Inventory Styles */
.inventory-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #28a745;
}

.inventory-item.low-stock {
    border-left-color: #ffc107;
}

.inventory-item.out-of-stock {
    border-left-color: #dc3545;
}

.inventory-item.expired {
    border-left-color: #6c757d;
    opacity: 0.7;
}

.stock-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
}

.stock-indicator.high { background-color: #28a745; }
.stock-indicator.medium { background-color: #ffc107; }
.stock-indicator.low { background-color: #fd7e14; }
.stock-indicator.out { background-color: #dc3545; }

/* Reports Styles */
.report-widget {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.report-widget-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    opacity: 0.8;
}

.report-widget-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.report-widget-label {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Notifications Styles */
.notification-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notification-item.unread {
    background: #f8f9fa;
    border-left-color: #28a745;
}

.notification-item.urgent {
    border-left-color: #dc3545;
    background: #fff5f5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pos-container {
        flex-direction: column;
    }
    
    .pos-products-panel,
    .pos-cart-panel {
        height: 50vh;
    }
    
    .pos-payment-methods {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .appointment-time-slots {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 576px) {
    .pos-payment-methods {
        grid-template-columns: 1fr;
    }
    
    .appointment-time-slots {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-right { text-align: right; }
.text-left { text-align: left; }
.text-center { text-align: center; }

.mb-10 { margin-bottom: 10px; }
.mb-15 { margin-bottom: 15px; }
.mb-20 { margin-bottom: 20px; }

.p-10 { padding: 10px; }
.p-15 { padding: 15px; }
.p-20 { padding: 20px; }

.border-radius-8 { border-radius: 8px; }
.border-radius-12 { border-radius: 12px; }

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .pos-receipt {
        width: 80mm;
        margin: 0 auto;
    }
}

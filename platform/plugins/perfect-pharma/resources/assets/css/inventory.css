/**
 * Perfect Pharma - Inventory Management Styles
 * 
 * This file contains CSS styles for inventory management
 */

/* Inventory Dashboard Styles */
.inventory-stats-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.inventory-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.inventory-stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stats-total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-low { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-expired { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-value { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

/* Stock Status Indicators */
.stock-status {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.stock-status.in-stock {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.stock-status.low-stock {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.stock-status.out-of-stock {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.stock-status.expired {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* Progress Bars */
.stock-progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #e9ecef;
}

.stock-progress .progress-bar {
    transition: width 0.3s ease;
}

.stock-progress.critical .progress-bar {
    background-color: #dc3545;
}

.stock-progress.warning .progress-bar {
    background-color: #ffc107;
}

.stock-progress.good .progress-bar {
    background-color: #28a745;
}

/* Alert Styles */
.inventory-alert {
    border-left: 4px solid;
    border-radius: 0 8px 8px 0;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.inventory-alert:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.inventory-alert.alert-critical {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.inventory-alert.alert-high {
    border-left-color: #fd7e14;
    background-color: #fff3cd;
}

.inventory-alert.alert-medium {
    border-left-color: #ffc107;
    background-color: #fff3cd;
}

.inventory-alert.alert-low {
    border-left-color: #17a2b8;
    background-color: #d1ecf1;
}

/* Movement History */
.movement-item {
    border-left: 3px solid;
    padding-left: 15px;
    margin-bottom: 15px;
    position: relative;
}

.movement-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: inherit;
}

.movement-item.movement-in {
    border-left-color: #28a745;
}

.movement-item.movement-out {
    border-left-color: #dc3545;
}

.movement-item.movement-adjustment {
    border-left-color: #17a2b8;
}

/* Form Enhancements */
.inventory-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.inventory-form .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.profit-margin-display {
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.profit-margin-display.positive {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.profit-margin-display.negative {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

/* Responsive Tables */
.inventory-table {
    font-size: 14px;
}

.inventory-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.inventory-table td {
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

.inventory-table .btn-group .btn {
    padding: 4px 8px;
    font-size: 12px;
}

/* Loading States */
.inventory-loading {
    position: relative;
    overflow: hidden;
}

.inventory-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .inventory-stats-card {
        margin-bottom: 15px;
    }
    
    .inventory-table {
        font-size: 12px;
    }
    
    .inventory-table .btn-group {
        flex-direction: column;
    }
    
    .inventory-table .btn-group .btn {
        margin-bottom: 2px;
        border-radius: 4px !important;
    }
    
    .card-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .card-actions .btn {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .inventory-stats-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn, .card-actions {
        display: none !important;
    }
    
    .inventory-table {
        font-size: 12px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .inventory-stats-card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .inventory-table th {
        background-color: #4a5568;
        color: #e2e8f0;
    }
    
    .inventory-table td {
        border-bottom-color: #4a5568;
    }
}

<?php

namespace Bo<PERSON><PERSON>\PerfectPharma\Forms\Settings;

use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextareaFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\EmailFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NumberFieldOption;
use Botble\Base\Forms\FieldOptions\OnOffFieldOption;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\EmailField;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\OnOffField;
use Bo<PERSON>ble\Setting\Forms\SettingForm;

class GeneralSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->setSectionTitle(trans('plugins/perfect-pharma::settings.general.title'))
            ->setSectionDescription(trans('plugins/perfect-pharma::settings.general.description'))
            ->setFormOption('url', route('perfect-pharma.settings.general.update'))
            ->add('system_name', TextField::class, TextFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.system_name'))
                ->value(get_perfect_pharma_setting('system_name', 'Perfect Pharma'))
                ->placeholder('Perfect Pharma')
                ->required()
            )
            ->add('system_description', TextareaField::class, TextareaFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.system_description'))
                ->value(get_perfect_pharma_setting('system_description', 'نظام طبي متكامل'))
                ->placeholder('نظام طبي متكامل')
                ->rows(3)
            )
            ->add('system_email', EmailField::class, EmailFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.system_email'))
                ->value(get_perfect_pharma_setting('system_email', '<EMAIL>'))
                ->placeholder('<EMAIL>')
                ->required()
            )
            ->add('system_phone', TextField::class, TextFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.system_phone'))
                ->value(get_perfect_pharma_setting('system_phone', '+966501234567'))
                ->placeholder('+966501234567')
            )
            ->add('system_address', TextareaField::class, TextareaFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.system_address'))
                ->value(get_perfect_pharma_setting('system_address', 'الرياض، المملكة العربية السعودية'))
                ->placeholder('الرياض، المملكة العربية السعودية')
                ->rows(2)
            )
            
            // إعدادات الأطباء
            ->addOpenCollapsible('doctors', trans('plugins/perfect-pharma::settings.general.doctors_settings'))
            ->add('doctor_verification_required', OnOffField::class, OnOffFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.doctor_verification_required'))
                ->value(get_perfect_pharma_setting('doctor_verification_required', true))
                ->helperText(trans('plugins/perfect-pharma::settings.general.doctor_verification_required_help'))
            )
            ->add('doctor_auto_approval', OnOffField::class, OnOffFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.doctor_auto_approval'))
                ->value(get_perfect_pharma_setting('doctor_auto_approval', false))
                ->helperText(trans('plugins/perfect-pharma::settings.general.doctor_auto_approval_help'))
            )
            ->add('doctor_commission_rate', NumberField::class, NumberFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.doctor_commission_rate'))
                ->value(get_perfect_pharma_setting('doctor_commission_rate', 10))
                ->placeholder('10')
                ->helperText(trans('plugins/perfect-pharma::settings.general.commission_rate_help'))
                ->addAttribute('min', 0)
                ->addAttribute('max', 100)
                ->addAttribute('step', 0.1)
            )
            ->add('doctor_min_consultation_fee', NumberField::class, NumberFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.doctor_min_consultation_fee'))
                ->value(get_perfect_pharma_setting('doctor_min_consultation_fee', 50))
                ->placeholder('50')
                ->addAttribute('min', 0)
                ->addAttribute('step', 1)
            )
            ->add('doctor_max_consultation_fee', NumberField::class, NumberFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.doctor_max_consultation_fee'))
                ->value(get_perfect_pharma_setting('doctor_max_consultation_fee', 1000))
                ->placeholder('1000')
                ->addAttribute('min', 0)
                ->addAttribute('step', 1)
            )
            ->addCloseCollapsible('doctors')
            
            // إعدادات الصيدليات
            ->addOpenCollapsible('pharmacies', trans('plugins/perfect-pharma::settings.general.pharmacies_settings'))
            ->add('pharmacy_verification_required', OnOffField::class, OnOffFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.pharmacy_verification_required'))
                ->value(get_perfect_pharma_setting('pharmacy_verification_required', true))
            )
            ->add('pharmacy_auto_approval', OnOffField::class, OnOffFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.pharmacy_auto_approval'))
                ->value(get_perfect_pharma_setting('pharmacy_auto_approval', false))
            )
            ->add('pharmacy_commission_rate', NumberField::class, NumberFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.pharmacy_commission_rate'))
                ->value(get_perfect_pharma_setting('pharmacy_commission_rate', 5))
                ->placeholder('5')
                ->addAttribute('min', 0)
                ->addAttribute('max', 100)
                ->addAttribute('step', 0.1)
            )
            ->add('pharmacy_delivery_enabled', OnOffField::class, OnOffFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.pharmacy_delivery_enabled'))
                ->value(get_perfect_pharma_setting('pharmacy_delivery_enabled', true))
            )
            ->add('pharmacy_delivery_fee', NumberField::class, NumberFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.pharmacy_delivery_fee'))
                ->value(get_perfect_pharma_setting('pharmacy_delivery_fee', 15))
                ->placeholder('15')
                ->addAttribute('min', 0)
                ->addAttribute('step', 1)
            )
            ->addCloseCollapsible('pharmacies')
            
            // إعدادات المعامل
            ->addOpenCollapsible('labs', trans('plugins/perfect-pharma::settings.general.labs_settings'))
            ->add('lab_verification_required', OnOffField::class, OnOffFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.lab_verification_required'))
                ->value(get_perfect_pharma_setting('lab_verification_required', true))
            )
            ->add('lab_auto_approval', OnOffField::class, OnOffFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.lab_auto_approval'))
                ->value(get_perfect_pharma_setting('lab_auto_approval', false))
            )
            ->add('lab_commission_rate', NumberField::class, NumberFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.lab_commission_rate'))
                ->value(get_perfect_pharma_setting('lab_commission_rate', 8))
                ->placeholder('8')
                ->addAttribute('min', 0)
                ->addAttribute('max', 100)
                ->addAttribute('step', 0.1)
            )
            ->add('lab_home_service_enabled', OnOffField::class, OnOffFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.lab_home_service_enabled'))
                ->value(get_perfect_pharma_setting('lab_home_service_enabled', true))
            )
            ->add('lab_home_service_fee', NumberField::class, NumberFieldOption::make()
                ->label(trans('plugins/perfect-pharma::settings.general.lab_home_service_fee'))
                ->value(get_perfect_pharma_setting('lab_home_service_fee', 25))
                ->placeholder('25')
                ->addAttribute('min', 0)
                ->addAttribute('step', 1)
            )
            ->addCloseCollapsible('labs');
    }
}

<?php

namespace Bo<PERSON><PERSON>\PerfectPharma\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\RadioFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\FieldOptions\EmailFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\PasswordFieldOption;
use Botble\Base\Forms\FieldOptions\CheckboxFieldOption;
use Botble\Base\Forms\Fields\RadioField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\EmailField;
use Botble\Base\Forms\Fields\PasswordField;
use Botble\Base\Forms\Fields\CheckboxField;
use Botble\Base\Forms\FormAbstract;
use Botble\PerfectPharma\Models\UserType;

class CustomerRegistrationForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->setUrl(route('customer.register.post'))
            ->setValidatorClass(\Botble\Ecommerce\Http\Requests\RegisterRequest::class)
            ->add('user_type', RadioField::class, RadioFieldOption::make()
                ->label('نوع الحساب')
                ->required()
                ->choices($this->getUserTypeChoices())
                ->defaultValue(UserType::TYPE_PATIENT)
                ->helperText('اختر نوع الحساب المناسب لك')
                ->wrapperAttributes(['class' => 'mb-4'])
            )
            ->add('name', TextField::class, TextFieldOption::make()
                ->label('الاسم الكامل')
                ->placeholder('أدخل اسمك الكامل')
                ->required()
                ->maxLength(120)
            )
            ->add('email', EmailField::class, EmailFieldOption::make()
                ->label('البريد الإلكتروني')
                ->placeholder('أدخل بريدك الإلكتروني')
                ->required()
            )
            ->add('phone', TextField::class, TextFieldOption::make()
                ->label('رقم الهاتف')
                ->placeholder('أدخل رقم هاتفك')
                ->helperText('سيتم استخدامه للتواصل معك')
            )
            ->add('password', PasswordField::class, PasswordFieldOption::make()
                ->label('كلمة المرور')
                ->placeholder('أدخل كلمة مرور قوية')
                ->required()
                ->minLength(6)
            )
            ->add('password_confirmation', PasswordField::class, PasswordFieldOption::make()
                ->label('تأكيد كلمة المرور')
                ->placeholder('أعد إدخال كلمة المرور')
                ->required()
            )
            ->add('agree_terms_and_policy', CheckboxField::class, CheckboxFieldOption::make()
                ->label('أوافق على <a href="#" target="_blank">الشروط والأحكام</a> و <a href="#" target="_blank">سياسة الخصوصية</a>')
                ->required()
                ->when(get_ecommerce_setting('enable_guest_checkout', 1), function () {
                    $this->helperText('يجب الموافقة على الشروط والأحكام للمتابعة');
                })
            )
            ->addBefore('agree_terms_and_policy', 'user_type_info', 'html', [
                'html' => $this->getUserTypeInfoHtml()
            ]);
    }

    /**
     * الحصول على خيارات أنواع المستخدمين
     */
    protected function getUserTypeChoices(): array
    {
        $userTypes = UserType::where('is_active', true)->get();
        $choices = [];

        foreach ($userTypes as $userType) {
            $choices[$userType->name] = $userType->display_name;
        }

        return $choices;
    }

    /**
     * HTML معلومات أنواع المستخدمين
     */
    protected function getUserTypeInfoHtml(): string
    {
        return '
        <div class="user-type-info mb-4" style="display: none;">
            <div class="alert alert-info" id="patient-info">
                <h6><i class="fas fa-user-injured"></i> مريض</h6>
                <p>حساب للمرضى للحصول على الخدمات الطبية والأدوية. لا يحتاج إلى وثائق تحقق.</p>
                <ul>
                    <li>شراء الأدوية والمنتجات الطبية</li>
                    <li>حجز المواعيد مع الأطباء</li>
                    <li>طلب التحاليل الطبية</li>
                    <li>الوصول للأكاديمية التعليمية</li>
                </ul>
            </div>
            
            <div class="alert alert-warning" id="doctor-info" style="display: none;">
                <h6><i class="fas fa-user-md"></i> طبيب</h6>
                <p>حساب للأطباء لتقديم الخدمات الطبية. <strong>يحتاج إلى تحقق من الوثائق.</strong></p>
                <ul>
                    <li>إدارة المواعيد والمرضى</li>
                    <li>كتابة الوصفات الطبية</li>
                    <li>خصومات خاصة على المنتجات الطبية</li>
                    <li>الوصول لأدوات الأطباء المتقدمة</li>
                </ul>
                <p><small><strong>المستندات المطلوبة:</strong> رخصة مزاولة المهنة + الهوية الشخصية</small></p>
            </div>
            
            <div class="alert alert-warning" id="pharmacy-info" style="display: none;">
                <h6><i class="fas fa-pills"></i> صيدلية</h6>
                <p>حساب للصيدليات لبيع الأدوية والمنتجات الطبية. <strong>يحتاج إلى تحقق من الوثائق.</strong></p>
                <ul>
                    <li>بيع الأدوية والمنتجات الطبية</li>
                    <li>صرف الوصفات الطبية</li>
                    <li>خصومات تجارية خاصة</li>
                    <li>إدارة المخزون والمبيعات</li>
                </ul>
                <p><small><strong>المستندات المطلوبة:</strong> ترخيص الصيدلية + هوية المالك</small></p>
            </div>
            
            <div class="alert alert-warning" id="lab-info" style="display: none;">
                <h6><i class="fas fa-flask"></i> معمل تحاليل</h6>
                <p>حساب للمعامل لتقديم خدمات التحاليل الطبية. <strong>يحتاج إلى تحقق من الوثائق.</strong></p>
                <ul>
                    <li>تقديم خدمات التحاليل الطبية</li>
                    <li>إدارة طلبات التحاليل</li>
                    <li>خصومات تجارية خاصة</li>
                    <li>تقارير التحاليل الإلكترونية</li>
                </ul>
                <p><small><strong>المستندات المطلوبة:</strong> ترخيص المعمل + هوية المالك</small></p>
            </div>
            
            <div class="alert alert-warning" id="hospital-info" style="display: none;">
                <h6><i class="fas fa-hospital"></i> مستشفى</h6>
                <p>حساب للمستشفيات لتقديم الخدمات الطبية الشاملة. <strong>يحتاج إلى تحقق من الوثائق.</strong></p>
                <ul>
                    <li>إدارة الأقسام والخدمات الطبية</li>
                    <li>حجز الأسرة والعمليات</li>
                    <li>خصومات مؤسسية خاصة</li>
                    <li>نظام إدارة المرضى المتقدم</li>
                </ul>
                <p><small><strong>المستندات المطلوبة:</strong> ترخيص المستشفى + هوية المدير</small></p>
            </div>
            
            <div class="alert alert-warning" id="clinic-info" style="display: none;">
                <h6><i class="fas fa-clinic-medical"></i> عيادة</h6>
                <p>حساب للعيادات الطبية المتخصصة. <strong>يحتاج إلى تحقق من الوثائق.</strong></p>
                <ul>
                    <li>إدارة المواعيد والمرضى</li>
                    <li>تقديم الخدمات الطبية المتخصصة</li>
                    <li>خصومات تجارية خاصة</li>
                    <li>نظام إدارة العيادة</li>
                </ul>
                <p><small><strong>المستندات المطلوبة:</strong> ترخيص العيادة + هوية الطبيب</small></p>
            </div>
        </div>
        
        <script>
        document.addEventListener("DOMContentLoaded", function() {
            const userTypeRadios = document.querySelectorAll("input[name=\'user_type\']");
            const infoContainer = document.querySelector(".user-type-info");
            const infoBoxes = document.querySelectorAll(".user-type-info .alert");
            
            function showUserTypeInfo(userType) {
                infoContainer.style.display = "block";
                infoBoxes.forEach(box => box.style.display = "none");
                
                const targetBox = document.getElementById(userType + "-info");
                if (targetBox) {
                    targetBox.style.display = "block";
                }
            }
            
            userTypeRadios.forEach(radio => {
                radio.addEventListener("change", function() {
                    if (this.checked) {
                        showUserTypeInfo(this.value);
                    }
                });
                
                // إظهار المعلومات للخيار المحدد مسبقاً
                if (radio.checked) {
                    showUserTypeInfo(radio.value);
                }
            });
        });
        </script>';
    }
}

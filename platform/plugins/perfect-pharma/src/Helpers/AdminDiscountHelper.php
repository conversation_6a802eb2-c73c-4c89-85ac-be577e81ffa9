<?php

namespace Botble\PerfectPharma\Helpers;

use Botble\Ecommerce\Models\Product;
use Botble\PerfectPharma\Models\UserType;
use Illuminate\Support\Facades\Auth;

class AdminDiscountHelper
{
    /**
     * عرض خصومات المنتج للمشرفين
     */
    public static function displayProductDiscounts(Product $product, string $mode = 'badges'): string
    {
        // التحقق من أن المستخدم مشرف
        if (!self::isAdminUser()) {
            return '';
        }

        // التحقق من تفعيل الخصومات
        if (!($product->enable_user_type_discounts ?? false)) {
            return '<small class="text-muted"><i class="ti ti-info-circle"></i> الخصومات غير مفعلة</small>';
        }

        $userTypes = UserType::where('name', '!=', UserType::TYPE_PATIENT)
                           ->where('is_active', true)
                           ->get();

        $discounts = [];
        foreach ($userTypes as $userType) {
            $field = $userType->name . '_discount_percentage';
            $percentage = $product->$field ?? 0;
            
            if ($percentage > 0) {
                $discounts[] = [
                    'name' => $userType->display_name,
                    'percentage' => $percentage,
                    'price' => self::calculateDiscountedPrice($product, $percentage)
                ];
            }
        }

        if (empty($discounts)) {
            return '<small class="text-warning"><i class="ti ti-alert-triangle"></i> لا توجد نسب خصم محددة</small>';
        }

        return self::renderDiscounts($discounts, $mode, $product);
    }

    /**
     * عرض الخصومات بأشكال مختلفة
     */
    protected static function renderDiscounts(array $discounts, string $mode, Product $product): string
    {
        switch ($mode) {
            case 'badges':
                return self::renderBadges($discounts);
            
            case 'table':
                return self::renderTable($discounts, $product);
            
            case 'inline':
                return self::renderInline($discounts);
            
            case 'summary':
                return self::renderSummary($discounts);
            
            default:
                return self::renderBadges($discounts);
        }
    }

    /**
     * عرض شارات الخصومات
     */
    protected static function renderBadges(array $discounts): string
    {
        $html = '<div class="perfect-pharma-discount-badges">';
        $html .= '<small class="text-muted me-2"><i class="ti ti-percentage"></i> خصومات:</small>';
        
        foreach ($discounts as $discount) {
            $html .= sprintf(
                '<span class="badge bg-success me-1" title="%s: %s">%s %s%%</span>',
                $discount['name'],
                format_price($discount['price']),
                $discount['name'],
                $discount['percentage']
            );
        }
        
        $html .= '</div>';
        return $html;
    }

    /**
     * عرض جدول الخصومات
     */
    protected static function renderTable(array $discounts, Product $product): string
    {
        $basePrice = $product->sale_price ?: $product->price;
        
        $html = '<div class="perfect-pharma-discount-table">';
        $html .= '<h6><i class="ti ti-percentage text-success"></i> خصومات Perfect Pharma</h6>';
        $html .= '<div class="table-responsive">';
        $html .= '<table class="table table-sm table-bordered">';
        $html .= '<thead class="table-light">';
        $html .= '<tr><th>نوع المستخدم</th><th>الخصم</th><th>السعر النهائي</th><th>الوفورات</th></tr>';
        $html .= '</thead><tbody>';
        
        // المرضى (بدون خصم)
        $html .= sprintf(
            '<tr><td><strong>مريض</strong></td><td><span class="badge bg-secondary">0%%</span></td><td>%s</td><td>-</td></tr>',
            format_price($basePrice)
        );
        
        // أنواع المستخدمين الأخرى
        foreach ($discounts as $discount) {
            $savings = $basePrice - $discount['price'];
            $html .= sprintf(
                '<tr><td><strong>%s</strong></td><td><span class="badge bg-success">%s%%</span></td><td><strong class="text-success">%s</strong></td><td><span class="text-success">%s</span></td></tr>',
                $discount['name'],
                $discount['percentage'],
                format_price($discount['price']),
                format_price($savings)
            );
        }
        
        $html .= '</tbody></table></div></div>';
        return $html;
    }

    /**
     * عرض مضغوط في سطر واحد
     */
    protected static function renderInline(array $discounts): string
    {
        if (empty($discounts)) {
            return '<small class="text-muted"><i class="ti ti-percentage"></i> لا توجد خصومات</small>';
        }

        $highest = collect($discounts)->sortByDesc('percentage')->first();
        
        return sprintf(
            '<small class="text-success"><i class="ti ti-percentage"></i> أعلى خصم: <strong>%s%%</strong> (%s) - %d نوع مستخدم</small>',
            $highest['percentage'],
            $highest['name'],
            count($discounts)
        );
    }

    /**
     * عرض ملخص
     */
    protected static function renderSummary(array $discounts): string
    {
        $count = count($discounts);
        $highest = collect($discounts)->sortByDesc('percentage')->first();
        
        $html = '<div class="alert alert-info alert-sm">';
        $html .= '<div class="d-flex justify-content-between align-items-center">';
        $html .= sprintf(
            '<div><i class="ti ti-info-circle"></i> <strong>خصومات Perfect Pharma:</strong> %d نوع مستخدم</div>',
            $count
        );
        
        if ($highest) {
            $html .= sprintf(
                '<div><span class="badge bg-success">أعلى خصم: %s%%</span></div>',
                $highest['percentage']
            );
        }
        
        $html .= '</div></div>';
        return $html;
    }

    /**
     * حساب السعر بعد الخصم
     */
    protected static function calculateDiscountedPrice(Product $product, float $percentage): float
    {
        $basePrice = $product->sale_price ?: $product->price;
        return $basePrice - ($basePrice * $percentage / 100);
    }

    /**
     * التحقق من أن المستخدم مشرف
     */
    protected static function isAdminUser(): bool
    {
        return Auth::check() && is_in_admin(true);
    }

    /**
     * إضافة CSS للتنسيق
     */
    public static function addAdminStyles(): string
    {
        return '
        <style>
        .perfect-pharma-discount-badges .badge {
            font-size: 0.75rem;
            margin-right: 2px;
        }
        .perfect-pharma-discount-table .table {
            font-size: 0.875rem;
            margin-bottom: 0;
        }
        .perfect-pharma-discount-table h6 {
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        .alert-sm {
            padding: 8px 12px;
            font-size: 0.875rem;
        }
        </style>';
    }
}

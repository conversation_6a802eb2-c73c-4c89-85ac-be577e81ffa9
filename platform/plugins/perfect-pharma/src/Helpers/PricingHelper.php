<?php

namespace Botble\PerfectPharma\Helpers;

use Botble\Ecommerce\Models\Product;
use Botble\PerfectPharma\Services\PricingService;
use Illuminate\Support\Facades\Auth;

class PricingHelper
{
    protected static $pricingService;

    /**
     * الحصول على PricingService
     */
    protected static function getPricingService(): PricingService
    {
        if (!static::$pricingService) {
            static::$pricingService = app(PricingService::class);
        }
        
        return static::$pricingService;
    }

    /**
     * حساب السعر للمنتج
     */
    public static function calculatePrice(Product $product, $customer = null): array
    {
        return static::getPricingService()->calculatePriceForUser($product, $customer);
    }

    /**
     * عرض السعر مع التنسيق
     */
    public static function formatPrice(Product $product, array $options = []): string
    {
        $pricing = static::calculatePrice($product);
        $options = array_merge([
            'show_original' => true,
            'show_discount_badge' => true,
            'show_user_type' => true,
            'size' => 'normal'
        ], $options);

        $html = '<div class="perfect-pharma-price">';
        
        // السعر النهائي
        $html .= '<span class="final-price text-primary fw-bold">';
        $html .= format_price($pricing['final_price']);
        $html .= '</span>';

        // شارة الخصم
        if ($pricing['discount_percentage'] > 0 && $options['show_discount_badge']) {
            $html .= ' <span class="badge bg-success ms-1">';
            $html .= '<i class="fas fa-tag"></i> ' . $pricing['discount_percentage'] . '% خصم';
            $html .= '</span>';
        }

        // السعر الأصلي
        if ($pricing['discount_percentage'] > 0 && $options['show_original']) {
            $html .= '<br><small class="text-muted text-decoration-line-through">';
            $html .= format_price($pricing['sale_price']);
            $html .= '</small>';
            $html .= ' <small class="text-success">وفر ' . format_price($pricing['discount_amount']) . '</small>';
        }

        // معلومات نوع المستخدم
        if ($options['show_user_type']) {
            if ($pricing['discount_percentage'] > 0) {
                $html .= '<br><small class="text-info">';
                $html .= '<i class="fas fa-user-tag"></i> خصم ' . $pricing['user_type_display'];
                $html .= '</small>';
            } elseif (isset($pricing['verification_required']) && $pricing['verification_required']) {
                $html .= '<br><small class="text-warning">';
                $html .= '<i class="fas fa-exclamation-triangle"></i> يجب التحقق من الحساب';
                $html .= '</small>';
            } elseif ($pricing['user_type'] === 'guest') {
                $html .= '<br><small class="text-muted">';
                $html .= '<a href="' . route('customer.login') . '">سجل دخولك للحصول على خصومات</a>';
                $html .= '</small>';
            }
        }

        $html .= '</div>';
        
        return $html;
    }

    /**
     * الحصول على معلومات خصم المستخدم
     */
    public static function getUserDiscountInfo($customer = null): array
    {
        if (!$customer) {
            $customer = Auth::guard('customer')->user();
        }

        if (!$customer) {
            return [
                'eligible' => false,
                'user_type' => 'guest',
                'user_type_display' => 'زائر',
                'message' => 'سجل دخولك للحصول على خصومات خاصة'
            ];
        }

        return static::getPricingService()->getUserDiscountInfo($customer);
    }

    /**
     * التحقق من أهلية المستخدم للخصم
     */
    public static function isEligibleForDiscount($customer = null): bool
    {
        if (!$customer) {
            $customer = Auth::guard('customer')->user();
        }

        if (!$customer) {
            return false;
        }

        return static::getPricingService()->isUserEligibleForDiscount($customer);
    }

    /**
     * حساب إجمالي السلة
     */
    public static function calculateCartTotal(array $cartItems, $customer = null): array
    {
        return static::getPricingService()->calculateCartTotal($cartItems, $customer);
    }

    /**
     * عرض رسالة حالة الخصم
     */
    public static function getDiscountStatusMessage($customer = null): string
    {
        $discountInfo = static::getUserDiscountInfo($customer);
        
        if ($discountInfo['eligible']) {
            return '<div class="alert alert-success"><i class="fas fa-check-circle"></i> ' . $discountInfo['message'] . '</div>';
        } elseif (isset($discountInfo['verification_required']) && $discountInfo['verification_required']) {
            return '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> ' . $discountInfo['message'] . '</div>';
        } else {
            return '<div class="alert alert-info"><i class="fas fa-info-circle"></i> ' . $discountInfo['message'] . '</div>';
        }
    }

    /**
     * الحصول على نسب الخصم لجميع أنواع المستخدمين
     */
    public static function getProductDiscountRates(Product $product): array
    {
        return static::getPricingService()->getProductDiscountRates($product);
    }

    /**
     * عرض جدول مقارنة الأسعار
     */
    public static function getPriceComparisonTable(Product $product): string
    {
        if (!$product->enable_user_type_discounts) {
            return '';
        }

        $userTypes = \Botble\PerfectPharma\Models\UserType::where('is_active', true)->get();
        $basePrice = $product->sale_price ?: $product->price;
        
        $html = '<div class="price-comparison-table">';
        $html .= '<h6>أسعار خاصة حسب نوع المستخدم:</h6>';
        $html .= '<div class="table-responsive">';
        $html .= '<table class="table table-sm table-striped">';
        $html .= '<thead><tr><th>نوع المستخدم</th><th>نسبة الخصم</th><th>السعر</th></tr></thead>';
        $html .= '<tbody>';

        foreach ($userTypes as $userType) {
            if ($userType->name === 'patient') {
                $html .= '<tr>';
                $html .= '<td>' . $userType->display_name . '</td>';
                $html .= '<td>-</td>';
                $html .= '<td>' . format_price($basePrice) . '</td>';
                $html .= '</tr>';
            } else {
                $discountField = $userType->name . '_discount_percentage';
                $discountPercentage = $product->$discountField ?? 0;
                
                if ($discountPercentage > 0) {
                    $discountAmount = ($basePrice * $discountPercentage) / 100;
                    $finalPrice = $basePrice - $discountAmount;
                    
                    $html .= '<tr>';
                    $html .= '<td><strong>' . $userType->display_name . '</strong></td>';
                    $html .= '<td><span class="badge bg-success">' . $discountPercentage . '%</span></td>';
                    $html .= '<td><strong class="text-success">' . format_price($finalPrice) . '</strong></td>';
                    $html .= '</tr>';
                }
            }
        }

        $html .= '</tbody></table>';
        $html .= '</div>';
        $html .= '<small class="text-muted">* الأسعار الخاصة متاحة للحسابات المحققة فقط</small>';
        $html .= '</div>';

        return $html;
    }

    /**
     * مسح cache التسعير للمستخدم
     */
    public static function clearUserCache(int $customerId): void
    {
        static::getPricingService()->clearUserCache($customerId);
    }
}

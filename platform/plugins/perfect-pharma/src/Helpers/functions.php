<?php

use Botble\PerfectPharma\Helpers\AdminDiscountHelper;
use Botble\Ecommerce\Models\Product;

if (!function_exists('perfect_pharma_admin_discounts')) {
    /**
     * عرض خصومات Perfect Pharma للمشرفين
     *
     * @param Product $product
     * @param string $mode (badges, table, inline, summary)
     * @return string
     */
    function perfect_pharma_admin_discounts(Product $product, string $mode = 'badges'): string
    {
        return AdminDiscountHelper::displayProductDiscounts($product, $mode);
    }
}

if (!function_exists('perfect_pharma_admin_styles')) {
    /**
     * إضافة CSS للخصومات
     *
     * @return string
     */
    function perfect_pharma_admin_styles(): string
    {
        return AdminDiscountHelper::addAdminStyles();
    }
}

if (!function_exists('perfect_pharma_has_discounts')) {
    /**
     * التحقق من وجود خصومات للمنتج
     *
     * @param Product $product
     * @return bool
     */
    function perfect_pharma_has_discounts(Product $product): bool
    {
        if (!($product->enable_user_type_discounts ?? false)) {
            return false;
        }

        $userTypes = ['hospital', 'pharmacy', 'clinic', 'lab', 'doctor', 'drug_supplier'];
        
        foreach ($userTypes as $type) {
            $field = $type . '_discount_percentage';
            if (($product->$field ?? 0) > 0) {
                return true;
            }
        }
        
        return false;
    }
}

if (!function_exists('perfect_pharma_highest_discount')) {
    /**
     * الحصول على أعلى نسبة خصم للمنتج
     *
     * @param Product $product
     * @return array
     */
    function perfect_pharma_highest_discount(Product $product): array
    {
        if (!perfect_pharma_has_discounts($product)) {
            return ['percentage' => 0, 'user_type' => '', 'display_name' => ''];
        }

        $userTypes = [
            'hospital' => 'مستشفى',
            'pharmacy' => 'صيدلية', 
            'clinic' => 'عيادة',
            'lab' => 'معمل',
            'doctor' => 'طبيب',
            'drug_supplier' => 'مورد أدوية'
        ];

        $highest = ['percentage' => 0, 'user_type' => '', 'display_name' => ''];

        foreach ($userTypes as $type => $displayName) {
            $field = $type . '_discount_percentage';
            $percentage = $product->$field ?? 0;
            
            if ($percentage > $highest['percentage']) {
                $highest = [
                    'percentage' => $percentage,
                    'user_type' => $type,
                    'display_name' => $displayName
                ];
            }
        }

        return $highest;
    }
}

if (!function_exists('perfect_pharma_discount_count')) {
    /**
     * عدد أنواع المستخدمين التي لها خصم
     *
     * @param Product $product
     * @return int
     */
    function perfect_pharma_discount_count(Product $product): int
    {
        if (!($product->enable_user_type_discounts ?? false)) {
            return 0;
        }

        $userTypes = ['hospital', 'pharmacy', 'clinic', 'lab', 'doctor', 'drug_supplier'];
        $count = 0;
        
        foreach ($userTypes as $type) {
            $field = $type . '_discount_percentage';
            if (($product->$field ?? 0) > 0) {
                $count++;
            }
        }
        
        return $count;
    }
}

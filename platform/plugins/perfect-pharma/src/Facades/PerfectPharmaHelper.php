<?php

namespace Botble\PerfectPharma\Facades;

use Botble\PerfectPharma\Supports\PerfectPharmaHelper as PerfectPharmaHelperSupport;
use Illuminate\Support\Facades\Facade;

/**
 * @method static string getSettingPrefix()
 * @method static array getDefaultSettings()
 * @method static void initializeDefaultSettings()
 * @method static array getUserTypes()
 * @method static string getUserTypeName(string $type)
 * @method static bool userTypeRequiresVerification(string $type)
 * @method static float getUserTypeDiscountRate(string $type)
 * @method static array getMedicalSpecializations()
 * @method static array getAppointmentTypes()
 * @method static array getLabTestCategories()
 * @method static bool isFeatureEnabled(string $feature)
 * @method static array getSystemConfig()
 * @method static array getCommissionRates()
 * @method static array getDiscountRates()
 * @method static array getWalletConfig()
 * @method static array getAppointmentConfig()
 * @method static array getDonationsConfig()
 * @method static array getAcademyConfig()
 * @method static array getNotificationsConfig()
 * @method static array getSecurityConfig()
 * @method static float calculateDiscountedPrice(float $originalPrice, string $userType)
 * @method static string formatCurrency(float $amount, string $currency = 'ريال')
 * @method static string getVersion()
 *
 * @see \Botble\PerfectPharma\Supports\PerfectPharmaHelper
 */
class PerfectPharmaHelper extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return PerfectPharmaHelperSupport::class;
    }
}

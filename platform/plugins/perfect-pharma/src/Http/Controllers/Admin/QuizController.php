<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use <PERSON><PERSON>ble\PerfectPharma\Models\CourseQuiz;
use <PERSON><PERSON>ble\PerfectPharma\Models\AcademyCourse;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class QuizController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle('جميع الاختبارات');

        $quizzes = CourseQuiz::with(['course'])
            ->when($request->course_id, function($query, $courseId) {
                $query->where('course_id', $courseId);
            })
            ->when($request->search, function($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhereHas('course', function($q) use ($search) {
                          $q->where('title', 'like', "%{$search}%");
                      });
            })
            ->latest()
            ->paginate(20);

        $courses = AcademyCourse::select('id', 'title')->get();

        $stats = [
            'total_quizzes' => CourseQuiz::count(),
            'recent_quizzes' => CourseQuiz::where('created_at', '>=', now()->subDays(30))->count(),
            'quizzes_with_questions' => CourseQuiz::whereHas('questions')->count(),
            'total_questions' => DB::table('quiz_questions')->count(),
        ];

        return view('plugins/perfect-pharma::admin.academy.quizzes.index', compact('quizzes', 'courses', 'stats'));
    }

    public function courseIndex(int $courseId, Request $request)
    {
        $course = AcademyCourse::findOrFail($courseId);

        $this->pageTitle('إدارة اختبارات الدورة: ' . $course->title);

        $quizzes = CourseQuiz::where('course_id', $courseId)
            ->withCount('questions')
            ->latest()
            ->paginate(20);

        return view('plugins/perfect-pharma::admin.academy.courses.quizzes.index', compact('course', 'quizzes'));
    }

    public function create(int $courseId)
    {
        $course = AcademyCourse::findOrFail($courseId);
        
        $this->pageTitle('إضافة اختبار جديد للدورة: ' . $course->title);

        return view('plugins/perfect-pharma::admin.academy.quizzes.create', compact('course'));
    }

    public function store(int $courseId, Request $request, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);

            $validatedData = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'passing_score' => 'required|integer|min:1|max:100',
                'time_limit_minutes' => 'nullable|integer|min:1',
                'max_attempts' => 'required|integer|min:1|max:10',
                'randomize_questions' => 'boolean',
                'show_results_immediately' => 'boolean',
            ]);

            $validatedData['course_id'] = $courseId;
            $validatedData['randomize_questions'] = $request->has('randomize_questions');
            $validatedData['show_results_immediately'] = $request->has('show_results_immediately');

            $quiz = CourseQuiz::create($validatedData);

            return $response
                ->setMessage('تم إنشاء الاختبار بنجاح')
                ->setData(['redirect' => route('admin.academy.courses.quizzes.show', [$courseId, $quiz->id])]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $response
                ->setError()
                ->setMessage('يرجى تصحيح الأخطاء في النموذج')
                ->setData(['errors' => $e->errors()]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء إنشاء الاختبار: ' . $e->getMessage());
        }
    }

    public function show(int $courseId, int $quizId)
    {
        $course = AcademyCourse::findOrFail($courseId);
        $quiz = CourseQuiz::with(['questions' => function($query) {
            $query->orderBy('order');
        }])->where('course_id', $courseId)->findOrFail($quizId);

        $this->pageTitle('تفاصيل الاختبار: ' . $quiz->title);

        return view('plugins/perfect-pharma::admin.academy.quizzes.show', compact('course', 'quiz'));
    }

    public function edit(int $courseId, int $quizId)
    {
        $course = AcademyCourse::findOrFail($courseId);
        $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);

        $this->pageTitle('تعديل الاختبار: ' . $quiz->title);

        return view('plugins/perfect-pharma::admin.academy.quizzes.edit', compact('course', 'quiz'));
    }

    public function update(int $courseId, int $quizId, Request $request, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);
            $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);

            $validatedData = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'passing_score' => 'required|integer|min:1|max:100',
                'time_limit_minutes' => 'nullable|integer|min:1',
                'max_attempts' => 'required|integer|min:1|max:10',
                'randomize_questions' => 'boolean',
                'show_results_immediately' => 'boolean',
            ]);

            $validatedData['randomize_questions'] = $request->has('randomize_questions');
            $validatedData['show_results_immediately'] = $request->has('show_results_immediately');

            $quiz->update($validatedData);

            return $response
                ->setMessage('تم تحديث الاختبار بنجاح')
                ->setData(['redirect' => route('admin.academy.courses.quizzes.show', [$courseId, $quiz->id])]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $response
                ->setError()
                ->setMessage('يرجى تصحيح الأخطاء في النموذج')
                ->setData(['errors' => $e->errors()]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تحديث الاختبار: ' . $e->getMessage());
        }
    }

    public function destroy(int $courseId, int $quizId, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);
            $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);

            // التحقق من وجود محاولات للاختبار
            if ($quiz->attempts()->count() > 0) {
                return $response
                    ->setError()
                    ->setMessage('لا يمكن حذف الاختبار لأنه يحتوي على محاولات من الطلاب');
            }

            $quiz->delete();

            return $response
                ->setMessage('تم حذف الاختبار بنجاح')
                ->setData(['redirect' => route('admin.academy.courses.quizzes.index', $courseId)]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حذف الاختبار: ' . $e->getMessage());
        }
    }

    public function duplicate(int $courseId, int $quizId, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);
            $originalQuiz = CourseQuiz::with('questions')->where('course_id', $courseId)->findOrFail($quizId);

            // إنشاء نسخة من الاختبار
            $newQuiz = $originalQuiz->replicate();
            $newQuiz->title = $originalQuiz->title . ' - نسخة';
            $newQuiz->save();

            // نسخ الأسئلة
            foreach ($originalQuiz->questions as $question) {
                $newQuestion = $question->replicate();
                $newQuestion->quiz_id = $newQuiz->id;
                $newQuestion->save();
            }

            return $response
                ->setMessage('تم نسخ الاختبار بنجاح')
                ->setData(['redirect' => route('admin.academy.courses.quizzes.show', [$courseId, $newQuiz->id])]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء نسخ الاختبار: ' . $e->getMessage());
        }
    }

    public function statistics(int $courseId, int $quizId)
    {
        $course = AcademyCourse::findOrFail($courseId);
        $quiz = CourseQuiz::with(['attempts.user', 'questions'])
            ->where('course_id', $courseId)
            ->findOrFail($quizId);

        $this->pageTitle('إحصائيات الاختبار: ' . $quiz->title);

        $stats = [
            'total_attempts' => $quiz->attempts()->count(),
            'unique_students' => $quiz->attempts()->distinct('user_id')->count(),
            'passed_attempts' => $quiz->attempts()->where('passed', true)->count(),
            'average_score' => $quiz->attempts()->avg('score'),
            'highest_score' => $quiz->attempts()->max('score'),
            'lowest_score' => $quiz->attempts()->min('score'),
        ];

        return view('plugins/perfect-pharma::admin.academy.quizzes.statistics', compact('course', 'quiz', 'stats'));
    }
}

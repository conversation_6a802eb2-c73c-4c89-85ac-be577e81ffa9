<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Bo<PERSON>ble\PerfectPharma\Models\Patient;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\PerfectPharma\Tables\PatientTable;
use Illuminate\Http\Request;

class PatientController extends BaseController
{
    public function index(PatientTable $table)
    {
        $this->pageTitle(trans('إدارة المرضى'));

        return $table->renderTable();
    }

    public function show(int $id, Request $request, BaseHttpResponse $response)
    {
        $patient = Patient::with(['customer', 'prescriptions.medications.product', 'labTestRequests.testItems.results'])
            ->findOrFail($id);

        if ($request->ajax()) {
            return $response->setData([
                'patient' => $patient,
                'statistics' => $patient->getStatistics(),
                'active_prescriptions' => $patient->getActivePrescriptions(),
                'recent_lab_tests' => $patient->getRecentLabTests(),
                'medical_timeline' => $patient->medical_timeline,
            ]);
        }

        $this->pageTitle('ملف المريض: ' . $patient->customer->name);

        return view('plugins/perfect-pharma::admin.patients.show', compact('patient'));
    }

    public function create()
    {
        $this->pageTitle('إضافة مريض جديد');

        return view('plugins/perfect-pharma::admin.patients.create');
    }

    public function store(Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:ec_customers,email',
            'phone' => 'required|string|max:20',
            'birth_date' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'blood_type' => 'nullable|string|max:5',
            'medical_history' => 'nullable|string',
            'allergies' => 'nullable|string',
            'chronic_diseases' => 'nullable|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'national_id' => 'nullable|string|max:20|unique:patients',
        ]);

        try {
            // إنشاء حساب عميل
            $customer = \Botble\Ecommerce\Models\Customer::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => \Hash::make('123456'), // كلمة مرور مؤقتة
            ]);

            // إنشاء ملف المريض
            $patient = Patient::create([
                'user_id' => $customer->id,
                'birth_date' => $request->birth_date,
                'gender' => $request->gender,
                'blood_type' => $request->blood_type,
                'medical_history' => $request->medical_history,
                'allergies' => $request->allergies,
                'chronic_diseases' => $request->chronic_diseases,
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'national_id' => $request->national_id,
            ]);

            return $response
                ->setMessage('تم إضافة المريض بنجاح. رقم المريض: ' . $patient->patient_code)
                ->setNextUrl(route('admin.patients.show', $patient->id));

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء إضافة المريض: ' . $e->getMessage());
        }
    }

    public function edit(int $id)
    {
        $patient = Patient::with('customer')->findOrFail($id);

        $this->pageTitle('تعديل بيانات المريض: ' . $patient->customer->name);

        return view('plugins/perfect-pharma::admin.patients.edit', compact('patient'));
    }

    public function update(int $id, Request $request, BaseHttpResponse $response)
    {
        $patient = Patient::with('customer')->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:ec_customers,email,' . $patient->user_id,
            'phone' => 'required|string|max:20',
            'birth_date' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'blood_type' => 'nullable|string|max:5',
            'medical_history' => 'nullable|string',
            'allergies' => 'nullable|string',
            'chronic_diseases' => 'nullable|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'national_id' => 'nullable|string|max:20|unique:patients,national_id,' . $patient->id,
        ]);

        try {
            // تحديث بيانات العميل
            $patient->customer->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            // تحديث بيانات المريض
            $patient->update([
                'birth_date' => $request->birth_date,
                'gender' => $request->gender,
                'blood_type' => $request->blood_type,
                'medical_history' => $request->medical_history,
                'allergies' => $request->allergies,
                'chronic_diseases' => $request->chronic_diseases,
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'national_id' => $request->national_id,
            ]);

            return $response
                ->setMessage('تم تحديث بيانات المريض بنجاح')
                ->setNextUrl(route('admin.patients.show', $patient->id));

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تحديث بيانات المريض: ' . $e->getMessage());
        }
    }

    public function destroy(int $id, BaseHttpResponse $response)
    {
        try {
            $patient = Patient::findOrFail($id);
            
            // التحقق من عدم وجود وصفات أو تحاليل نشطة
            if ($patient->prescriptions()->whereIn('status', ['active', 'partially_filled'])->exists()) {
                return $response
                    ->setError()
                    ->setMessage('لا يمكن حذف المريض لوجود وصفات نشطة');
            }

            if ($patient->labTestRequests()->whereIn('status', ['pending', 'in_progress'])->exists()) {
                return $response
                    ->setError()
                    ->setMessage('لا يمكن حذف المريض لوجود تحاليل قيد التنفيذ');
            }

            $patient->delete();

            return $response->setMessage('تم حذف المريض بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حذف المريض: ' . $e->getMessage());
        }
    }

    public function search(Request $request, BaseHttpResponse $response)
    {
        $query = $request->get('q');
        
        if (!$query) {
            return $response->setError()->setMessage('يرجى إدخال كلمة البحث');
        }

        $patients = Patient::search($query);

        return $response->setData($patients);
    }

    public function statistics(BaseHttpResponse $response)
    {
        $totalPatients = Patient::count();
        $chronicPatients = Patient::where('is_chronic_patient', true)->count();
        $newPatientsThisMonth = Patient::whereMonth('created_at', now()->month)->count();
        $activePrescriptions = \App\Models\Prescription::whereIn('status', ['active', 'partially_filled'])->count();

        $statistics = [
            'total_patients' => $totalPatients,
            'chronic_patients' => $chronicPatients,
            'new_patients_this_month' => $newPatientsThisMonth,
            'active_prescriptions' => $activePrescriptions,
            'chronic_percentage' => $totalPatients > 0 ? ($chronicPatients / $totalPatients) * 100 : 0,
        ];

        return $response->setData($statistics);
    }
}

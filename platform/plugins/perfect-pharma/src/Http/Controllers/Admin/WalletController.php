<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use App\Models\PharmaWallet;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;

class WalletController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle('إدارة المحافظ المالية');

        $wallets = PharmaWallet::with(['user'])
            ->latest()
            ->paginate(20);

        if ($request->ajax()) {
            return view('plugins/perfect-pharma::admin.wallets.table', compact('wallets'))->render();
        }

        return view('plugins/perfect-pharma::admin.wallets.index', compact('wallets'));
    }

    public function show(int $id)
    {
        $wallet = PharmaWallet::with(['user', 'transactions'])->findOrFail($id);

        $this->pageTitle('تفاصيل المحفظة: ' . $wallet->wallet_id);

        return view('plugins/perfect-pharma::admin.wallets.show', compact('wallet'));
    }

    public function toggle(int $id, BaseHttpResponse $response)
    {
        try {
            $wallet = PharmaWallet::findOrFail($id);
            $wallet->is_active = !$wallet->is_active;
            $wallet->save();

            $status = $wallet->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

            return $response
                ->setMessage($status . ' المحفظة بنجاح')
                ->setData(['is_active' => $wallet->is_active]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تغيير حالة المحفظة: ' . $e->getMessage());
        }
    }
}

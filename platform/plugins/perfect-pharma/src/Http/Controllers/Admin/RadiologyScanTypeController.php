<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\PerfectPharma\Models\RadiologyScanType;
use Illuminate\Http\Request;

class RadiologyScanTypeController extends BaseController
{
    /**
     * عرض قائمة أنواع الأشعة
     */
    public function index(Request $request)
    {
        $this->pageTitle('إدارة أنواع الأشعة');

        $query = RadiologyScanType::query();

        // البحث
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // فلترة بالفئة
        if ($request->filled('category')) {
            $query->where('category', $request->get('category'));
        }

        // فلترة بالحالة
        if ($request->filled('status')) {
            $isActive = $request->get('status') === 'active';
            $query->where('is_active', $isActive);
        }

        $scanTypes = $query->orderBy('category')->orderBy('name')->paginate(20);

        // الفئات المتاحة
        $categories = RadiologyScanType::getCategories();

        // إحصائيات
        $stats = [
            'total' => RadiologyScanType::count(),
            'active' => RadiologyScanType::where('is_active', true)->count(),
            'inactive' => RadiologyScanType::where('is_active', false)->count(),
        ];

        return view('plugins/perfect-pharma::admin.radiology.scan-types.index', compact(
            'scanTypes',
            'categories',
            'stats'
        ));
    }
    
    /**
     * عرض تفاصيل نوع أشعة
     */
    public function show(int $id)
    {
        $scanType = RadiologyScanType::with(['radiologyRequests'])->findOrFail($id);

        $this->pageTitle('تفاصيل نوع الأشعة: ' . $scanType->name);

        return view('plugins/perfect-pharma::admin.radiology.scan-types.show', compact('scanType'));
    }

    /**
     * إنشاء نوع أشعة جديد
     */
    public function create()
    {
        $this->pageTitle('إضافة نوع أشعة جديد');

        $categories = RadiologyScanType::getCategories();

        return view('plugins/perfect-pharma::admin.radiology.scan-types.create', compact('categories'));
    }

    /**
     * حفظ نوع أشعة جديد
     */
    public function store(Request $request, BaseHttpResponse $response)
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'required|string|max:50|unique:radiology_scan_types,code',
                'category' => 'required|string|max:100',
                'description' => 'nullable|string',
                'preparation_instructions' => 'nullable|string',
                'duration_minutes' => 'nullable|integer|min:1|max:480',
                'base_price' => 'nullable|numeric|min:0',
                'requires_contrast' => 'boolean',
                'requires_fasting' => 'boolean',
                'is_active' => 'boolean',
            ]);

            $validatedData['is_active'] = $request->has('is_active');
            $validatedData['requires_contrast'] = $request->has('requires_contrast');
            $validatedData['requires_fasting'] = $request->has('requires_fasting');

            RadiologyScanType::create($validatedData);

            return $response
                ->setMessage('تم إنشاء نوع الأشعة بنجاح')
                ->setData(['redirect' => route('admin.radiology.scan-types.index')]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $response
                ->setError()
                ->setMessage('يرجى تصحيح الأخطاء في النموذج')
                ->setData(['errors' => $e->errors()]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء إنشاء نوع الأشعة: ' . $e->getMessage());
        }
    }
    
    /**
     * تعديل نوع أشعة
     */
    public function edit(int $id)
    {
        $scanType = RadiologyScanType::findOrFail($id);
        $categories = RadiologyScanType::getCategories();

        $this->pageTitle('تعديل نوع الأشعة: ' . $scanType->name);

        return view('plugins/perfect-pharma::admin.radiology.scan-types.edit', compact(
            'scanType',
            'categories'
        ));
    }

    /**
     * تحديث نوع أشعة
     */
    public function update(Request $request, int $id, BaseHttpResponse $response)
    {
        try {
            $scanType = RadiologyScanType::findOrFail($id);

            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'required|string|max:50|unique:radiology_scan_types,code,' . $id,
                'category' => 'required|string|max:100',
                'description' => 'nullable|string',
                'preparation_instructions' => 'nullable|string',
                'duration_minutes' => 'nullable|integer|min:1|max:480',
                'base_price' => 'nullable|numeric|min:0',
                'requires_contrast' => 'boolean',
                'requires_fasting' => 'boolean',
                'is_active' => 'boolean',
            ]);

            $validatedData['is_active'] = $request->has('is_active');
            $validatedData['requires_contrast'] = $request->has('requires_contrast');
            $validatedData['requires_fasting'] = $request->has('requires_fasting');

            $scanType->update($validatedData);

            return $response
                ->setMessage('تم تحديث نوع الأشعة بنجاح')
                ->setData(['redirect' => route('admin.radiology.scan-types.index')]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $response
                ->setError()
                ->setMessage('يرجى تصحيح الأخطاء في النموذج')
                ->setData(['errors' => $e->errors()]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تحديث نوع الأشعة: ' . $e->getMessage());
        }
    }
    
    /**
     * تفعيل/إلغاء تفعيل نوع أشعة
     */
    public function toggle(int $id, BaseHttpResponse $response)
    {
        try {
            $scanType = RadiologyScanType::findOrFail($id);
            $scanType->is_active = !$scanType->is_active;
            $scanType->save();

            $status = $scanType->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

            return $response
                ->setMessage($status . ' نوع الأشعة بنجاح')
                ->setData(['is_active' => $scanType->is_active]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تغيير حالة نوع الأشعة: ' . $e->getMessage());
        }
    }

    /**
     * حذف نوع أشعة
     */
    public function destroy(int $id, BaseHttpResponse $response)
    {
        try {
            $scanType = RadiologyScanType::findOrFail($id);

            // التحقق من وجود طلبات مرتبطة
            if ($scanType->radiologyRequests()->count() > 0) {
                return $response
                    ->setError()
                    ->setMessage('لا يمكن حذف نوع الأشعة لوجود طلبات مرتبطة به');
            }

            $scanType->delete();

            return $response
                ->setMessage('تم حذف نوع الأشعة بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حذف نوع الأشعة: ' . $e->getMessage());
        }
    }
    
    /**
     * إجراءات مجمعة
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'ids' => 'required|array',
            'ids.*' => 'exists:radiology_scan_types,id',
        ]);
        
        $action = $request->get('action');
        $ids = $request->get('ids');
        
        switch ($action) {
            case 'activate':
                RadiologyScanType::whereIn('id', $ids)->update(['is_active' => true]);
                $message = 'تم تفعيل أنواع الأشعة المحددة';
                break;
                
            case 'deactivate':
                RadiologyScanType::whereIn('id', $ids)->update(['is_active' => false]);
                $message = 'تم إلغاء تفعيل أنواع الأشعة المحددة';
                break;
                
            case 'delete':
                // التحقق من عدم وجود طلبات مرتبطة
                $hasRequests = RadiologyScanType::whereIn('id', $ids)
                    ->whereHas('radiologyRequests')
                    ->exists();
                
                if ($hasRequests) {
                    return response()->json([
                        'success' => false,
                        'message' => 'لا يمكن حذف بعض أنواع الأشعة لوجود طلبات مرتبطة بها'
                    ], 400);
                }
                
                RadiologyScanType::whereIn('id', $ids)->delete();
                $message = 'تم حذف أنواع الأشعة المحددة';
                break;
        }
        
        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }
}

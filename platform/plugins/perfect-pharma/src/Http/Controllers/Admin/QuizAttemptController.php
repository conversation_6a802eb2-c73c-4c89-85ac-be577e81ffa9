<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Bo<PERSON>ble\PerfectPharma\Models\QuizAttempt;
use Bo<PERSON>ble\PerfectPharma\Models\CourseQuiz;
use Botble\PerfectPharma\Models\AcademyCourse;
use Botble\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;

class QuizAttemptController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle('محاولات الاختبارات');

        $attempts = QuizAttempt::with(['user', 'quiz.course'])
            ->when($request->quiz_id, function($query, $quizId) {
                $query->where('quiz_id', $quizId);
            })
            ->when($request->course_id, function($query, $courseId) {
                $query->whereHas('quiz', function($q) use ($courseId) {
                    $q->where('course_id', $courseId);
                });
            })
            ->when($request->status, function($query, $status) {
                if ($status === 'completed') {
                    $query->whereNotNull('completed_at');
                } elseif ($status === 'in_progress') {
                    $query->whereNull('completed_at')->whereNotNull('started_at');
                } elseif ($status === 'abandoned') {
                    $query->whereNull('completed_at')->whereNull('started_at');
                }
            })
            ->when($request->search, function($query, $search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                })->orWhereHas('quiz', function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%");
                });
            })
            ->latest()
            ->paginate(20);

        $courses = AcademyCourse::select('id', 'title')->get();
        $quizzes = CourseQuiz::select('id', 'title', 'course_id')->get();

        $stats = [
            'total_attempts' => QuizAttempt::count(),
            'completed_attempts' => QuizAttempt::whereNotNull('completed_at')->count(),
            'in_progress_attempts' => QuizAttempt::whereNull('completed_at')->whereNotNull('started_at')->count(),
            'average_score' => QuizAttempt::whereNotNull('completed_at')->avg('score') ?? 0,
        ];

        return view('plugins/perfect-pharma::admin.academy.quiz-attempts.index', compact('attempts', 'courses', 'quizzes', 'stats'));
    }

    public function show(int $attemptId)
    {
        $attempt = QuizAttempt::with(['user', 'quiz.course', 'answers.question'])
            ->findOrFail($attemptId);

        $this->pageTitle('تفاصيل محاولة الاختبار');

        return view('plugins/perfect-pharma::admin.academy.quiz-attempts.show', compact('attempt'));
    }

    public function analytics(Request $request)
    {
        $this->pageTitle('تحليلات الاختبارات');

        $courseId = $request->input('course_id');
        
        // إحصائيات عامة
        $generalStats = [
            'total_attempts' => QuizAttempt::count(),
            'completed_attempts' => QuizAttempt::whereNotNull('completed_at')->count(),
            'average_score' => QuizAttempt::whereNotNull('completed_at')->avg('score') ?? 0,
            'pass_rate' => $this->calculatePassRate(),
        ];

        // أفضل الاختبارات أداءً
        $topPerformingQuizzes = CourseQuiz::withCount(['attempts as completed_attempts' => function($query) {
                $query->whereNotNull('completed_at');
            }])
            ->withAvg(['attempts as average_score' => function($query) {
                $query->whereNotNull('completed_at');
            }], 'score')
            ->having('completed_attempts', '>=', 5)
            ->orderBy('average_score', 'desc')
            ->limit(10)
            ->get();

        // توزيع الدرجات
        $scoreDistribution = [];
        for ($i = 0; $i <= 100; $i += 10) {
            $count = QuizAttempt::where('status', 'completed')
                ->where('score', '>=', $i)
                ->where('score', '<', $i + 10)
                ->count();
            $scoreDistribution[] = [
                'range' => $i . '-' . ($i + 9),
                'count' => $count,
            ];
        }

        // المحاولات الشهرية
        $monthlyAttempts = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $count = QuizAttempt::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();
                
            $monthlyAttempts[] = [
                'month' => $date->format('Y-m'),
                'month_name' => $date->format('M Y'),
                'count' => $count,
            ];
        }

        // تحليلات خاصة بدورة معينة
        $courseAnalytics = null;
        if ($courseId) {
            $course = AcademyCourse::find($courseId);
            if ($course) {
                $courseAnalytics = [
                    'course' => $course,
                    'total_attempts' => QuizAttempt::whereHas('quiz', function($q) use ($courseId) {
                        $q->where('course_id', $courseId);
                    })->count(),
                    'average_score' => QuizAttempt::whereHas('quiz', function($q) use ($courseId) {
                        $q->where('course_id', $courseId);
                    })->where('status', 'completed')->avg('score') ?? 0,
                    'pass_rate' => $this->calculateCoursePassRate($courseId),
                ];
            }
        }

        $courses = AcademyCourse::select('id', 'title')->get();

        return view('plugins/perfect-pharma::admin.academy.quiz-attempts.analytics', compact(
            'generalStats', 'topPerformingQuizzes', 'scoreDistribution', 'monthlyAttempts',
            'courseAnalytics', 'courses'
        ));
    }

    private function calculatePassRate(): float
    {
        $totalCompleted = QuizAttempt::where('status', 'completed')->count();
        if ($totalCompleted === 0) {
            return 0;
        }

        $passed = QuizAttempt::where('status', 'completed')
            ->where('score', '>=', 60) // افتراض أن درجة النجاح 60%
            ->count();

        return round(($passed / $totalCompleted) * 100, 1);
    }

    private function calculateCoursePassRate(int $courseId): float
    {
        $totalCompleted = QuizAttempt::whereHas('quiz', function($q) use ($courseId) {
            $q->where('course_id', $courseId);
        })->where('status', 'completed')->count();

        if ($totalCompleted === 0) {
            return 0;
        }

        $passed = QuizAttempt::whereHas('quiz', function($q) use ($courseId) {
            $q->where('course_id', $courseId);
        })->where('status', 'completed')
          ->where('score', '>=', 60)
          ->count();

        return round(($passed / $totalCompleted) * 100, 1);
    }
}

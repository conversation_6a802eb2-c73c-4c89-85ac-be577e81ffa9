<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\PerfectPharma\Models\NotificationTemplate;
use Botble\PerfectPharma\Services\NotificationTemplateService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationTemplateController extends BaseController
{
    protected NotificationTemplateService $templateService;

    public function __construct(NotificationTemplateService $templateService)
    {
        $this->templateService = $templateService;
    }

    /**
     * عرض قائمة القوالب
     */
    public function index(Request $request)
    {
        $query = NotificationTemplate::query();

        // تطبيق الفلاتر
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('channel')) {
            $query->where('channel', $request->channel);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $templates = $query->orderBy('type')->orderBy('channel')->paginate(20);

        $types = $this->getNotificationTypes();
        $channels = $this->getChannels();

        return view('plugins/perfect-pharma::admin.notification-templates.index', compact('templates', 'types', 'channels'));
    }

    /**
     * عرض نموذج إنشاء قالب جديد
     */
    public function create()
    {
        $types = $this->getNotificationTypes();
        $channels = $this->getChannels();

        return view('plugins/perfect-pharma::admin.notification-templates.create', compact('types', 'channels'));
    }

    /**
     * حفظ قالب جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string',
            'channel' => 'required|string',
            'subject' => 'nullable|string|max:255',
            'content' => 'required|string',
            'variables' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        try {
            $template = $this->templateService->createTemplate($request->all());

            return redirect()
                ->route('admin.notification-templates.show', $template->id)
                ->with('success', 'تم إنشاء القالب بنجاح');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'فشل إنشاء القالب: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل القالب
     */
    public function show(int $id)
    {
        $template = NotificationTemplate::findOrFail($id);

        return view('plugins/perfect-pharma::admin.notification-templates.show', compact('template'));
    }

    /**
     * عرض نموذج تعديل القالب
     */
    public function edit(int $id)
    {
        $template = NotificationTemplate::findOrFail($id);
        $types = $this->getNotificationTypes();
        $channels = $this->getChannels();

        return view('plugins/perfect-pharma::admin.notification-templates.edit', compact('template', 'types', 'channels'));
    }

    /**
     * تحديث القالب
     */
    public function update(Request $request, int $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string',
            'channel' => 'required|string',
            'subject' => 'nullable|string|max:255',
            'content' => 'required|string',
            'variables' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        try {
            $template = NotificationTemplate::findOrFail($id);
            $template = $this->templateService->updateTemplate($template, $request->all());

            return redirect()
                ->route('admin.notification-templates.show', $template->id)
                ->with('success', 'تم تحديث القالب بنجاح');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'فشل تحديث القالب: ' . $e->getMessage());
        }
    }

    /**
     * حذف القالب
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $template = NotificationTemplate::findOrFail($id);
            $this->templateService->deleteTemplate($template);

            return response()->json([
                'success' => true,
                'message' => 'تم حذف القالب بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل حذف القالب: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * تكرار القالب
     */
    public function duplicate(int $id): JsonResponse
    {
        try {
            $template = NotificationTemplate::findOrFail($id);
            $newTemplate = $this->templateService->duplicateTemplate($template);

            return response()->json([
                'success' => true,
                'message' => 'تم تكرار القالب بنجاح',
                'redirect' => route('admin.notification-templates.edit', $newTemplate->id)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل تكرار القالب: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * تفعيل/إلغاء تفعيل القالب
     */
    public function toggle(int $id): JsonResponse
    {
        try {
            $template = NotificationTemplate::findOrFail($id);
            $template = $this->templateService->toggleTemplate($template);

            return response()->json([
                'success' => true,
                'message' => $template->is_active ? 'تم تفعيل القالب' : 'تم إلغاء تفعيل القالب',
                'is_active' => $template->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل تغيير حالة القالب: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * تعيين القالب كافتراضي
     */
    public function setDefault(int $id): JsonResponse
    {
        try {
            $template = NotificationTemplate::findOrFail($id);
            $template = $this->templateService->setAsDefault($template);

            return response()->json([
                'success' => true,
                'message' => 'تم تعيين القالب كافتراضي'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل تعيين القالب كافتراضي: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * معاينة القالب
     */
    public function preview(Request $request, int $id): JsonResponse
    {
        try {
            $template = NotificationTemplate::findOrFail($id);
            $sampleData = $request->get('sample_data', []);

            $preview = $this->templateService->previewTemplate($template, $sampleData);

            return response()->json([
                'success' => true,
                'preview' => $preview
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل معاينة القالب: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * تصدير القوالب
     */
    public function export(Request $request)
    {
        try {
            $templateIds = $request->get('template_ids', []);
            $templates = $this->templateService->exportTemplates($templateIds);

            $filename = 'notification_templates_' . now()->format('Y_m_d_H_i_s') . '.json';

            return response()->json($templates)
                          ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                          ->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل تصدير القوالب: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * استيراد القوالب
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            'templates_file' => 'required|file|mimes:json',
            'overwrite' => 'boolean',
        ]);

        try {
            $file = $request->file('templates_file');
            $content = file_get_contents($file->getPathname());
            $templatesData = json_decode($content, true);

            if (!is_array($templatesData)) {
                throw new \Exception('ملف القوالب غير صحيح');
            }

            $results = $this->templateService->importTemplates($templatesData, $request->boolean('overwrite'));

            return response()->json([
                'success' => true,
                'message' => "تم استيراد {$results['imported']} قالب بنجاح",
                'results' => $results
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل استيراد القوالب: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * إنشاء القوالب الافتراضية
     */
    public function createDefaults(): JsonResponse
    {
        try {
            $this->templateService->createDefaultTemplates();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء القوالب الافتراضية بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل إنشاء القوالب الافتراضية: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * الحصول على أنواع الإشعارات
     */
    private function getNotificationTypes(): array
    {
        return [
            'appointment_reminder' => 'تذكير موعد',
            'appointment_confirmation' => 'تأكيد موعد',
            'appointment_cancellation' => 'إلغاء موعد',
            'medication_reminder' => 'تذكير دواء',
            'prescription_ready' => 'وصفة جاهزة',
            'test_result_ready' => 'نتيجة تحليل جاهزة',
            'inventory_alert' => 'تنبيه مخزون',
            'payment_reminder' => 'تذكير دفع',
            'system_maintenance' => 'صيانة النظام',
            'promotional' => 'عرض ترويجي',
            'security_alert' => 'تنبيه أمني',
        ];
    }

    /**
     * الحصول على القنوات
     */
    private function getChannels(): array
    {
        return [
            'email' => 'بريد إلكتروني',
            'sms' => 'رسالة نصية',
            'push' => 'إشعار فوري',
            'in_app' => 'داخل التطبيق',
            'whatsapp' => 'واتساب',
        ];
    }
}

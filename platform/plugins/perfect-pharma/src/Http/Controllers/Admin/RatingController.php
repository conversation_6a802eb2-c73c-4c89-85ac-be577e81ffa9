<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Botble\PerfectPharma\Models\CourseRating;
use <PERSON><PERSON>ble\PerfectPharma\Models\ContentRating;
use Bo<PERSON>ble\PerfectPharma\Models\AcademyCourse;
use Botble\PerfectPharma\Models\CourseContent;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;

class RatingController extends BaseController
{
    public function courseRatings(Request $request)
    {
        $this->pageTitle('تقييمات الدورات');

        $ratings = CourseRating::with(['user', 'course'])
            ->when($request->course_id, function($query, $courseId) {
                $query->where('course_id', $courseId);
            })
            ->when($request->rating, function($query, $rating) {
                $query->where('rating', $rating);
            })
            ->when($request->status, function($query, $status) {
                if ($status === 'approved') {
                    $query->approved();
                } elseif ($status === 'pending') {
                    $query->pending();
                }
            })
            ->when($request->search, function($query, $search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                })->orWhereHas('course', function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%");
                })->orWhere('review', 'like', "%{$search}%");
            })
            ->latest()
            ->paginate(20);

        $courses = AcademyCourse::select('id', 'title')->get();

        $stats = [
            'total_ratings' => CourseRating::count(),
            'approved_ratings' => CourseRating::approved()->count(),
            'pending_ratings' => CourseRating::pending()->count(),
            'average_rating' => CourseRating::approved()->avg('rating') ?? 0,
        ];

        return view('plugins/perfect-pharma::admin.academy.ratings.courses', compact('ratings', 'courses', 'stats'));
    }

    public function contentRatings(Request $request)
    {
        $this->pageTitle('تقييمات المحتوى');

        $ratings = ContentRating::with(['user', 'content.course'])
            ->when($request->content_id, function($query, $contentId) {
                $query->where('content_id', $contentId);
            })
            ->when($request->course_id, function($query, $courseId) {
                $query->whereHas('content', function($q) use ($courseId) {
                    $q->where('course_id', $courseId);
                });
            })
            ->when($request->rating, function($query, $rating) {
                $query->where('rating', $rating);
            })
            ->when($request->helpful, function($query, $helpful) {
                $query->where('is_helpful', $helpful === 'yes');
            })
            ->when($request->search, function($query, $search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                })->orWhereHas('content', function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%");
                })->orWhere('comment', 'like', "%{$search}%");
            })
            ->latest()
            ->paginate(20);

        $courses = AcademyCourse::select('id', 'title')->get();

        $stats = [
            'total_ratings' => ContentRating::count(),
            'helpful_ratings' => ContentRating::helpful()->count(),
            'average_rating' => ContentRating::avg('rating') ?? 0,
        ];

        return view('plugins/perfect-pharma::admin.academy.ratings.content', compact('ratings', 'courses', 'stats'));
    }

    public function approveCourseRating(int $id, BaseHttpResponse $response)
    {
        try {
            $rating = CourseRating::findOrFail($id);
            $rating->approve();

            return $response->setMessage('تم الموافقة على التقييم بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء الموافقة على التقييم: ' . $e->getMessage());
        }
    }

    public function rejectCourseRating(int $id, BaseHttpResponse $response)
    {
        try {
            $rating = CourseRating::findOrFail($id);
            $rating->reject();

            return $response->setMessage('تم رفض التقييم بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء رفض التقييم: ' . $e->getMessage());
        }
    }

    public function deleteCourseRating(int $id, BaseHttpResponse $response)
    {
        try {
            $rating = CourseRating::findOrFail($id);
            $rating->delete();

            return $response->setMessage('تم حذف التقييم بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حذف التقييم: ' . $e->getMessage());
        }
    }

    public function deleteContentRating(int $id, BaseHttpResponse $response)
    {
        try {
            $rating = ContentRating::findOrFail($id);
            $rating->delete();

            return $response->setMessage('تم حذف التقييم بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حذف التقييم: ' . $e->getMessage());
        }
    }

    public function ratingsAnalytics(Request $request)
    {
        $this->pageTitle('تحليلات التقييمات');

        $courseId = $request->input('course_id');
        
        // إحصائيات عامة
        $generalStats = [
            'total_course_ratings' => CourseRating::count(),
            'total_content_ratings' => ContentRating::count(),
            'average_course_rating' => CourseRating::approved()->avg('rating') ?? 0,
            'average_content_rating' => ContentRating::avg('rating') ?? 0,
        ];

        // أفضل الدورات تقييماً
        $topRatedCourses = CourseRating::getTopRatedCourses(10);

        // توزيع التقييمات
        $ratingDistribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $courseCount = CourseRating::approved()->where('rating', $i)->count();
            $contentCount = ContentRating::where('rating', $i)->count();
            
            $ratingDistribution[$i] = [
                'course_count' => $courseCount,
                'content_count' => $contentCount,
                'total' => $courseCount + $contentCount,
            ];
        }

        // التقييمات الشهرية
        $monthlyRatings = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $courseRatings = CourseRating::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();
            $contentRatings = ContentRating::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();
                
            $monthlyRatings[] = [
                'month' => $date->format('Y-m'),
                'month_name' => $date->format('M Y'),
                'course_ratings' => $courseRatings,
                'content_ratings' => $contentRatings,
                'total' => $courseRatings + $contentRatings,
            ];
        }

        // تحليلات خاصة بدورة معينة
        $courseAnalytics = null;
        if ($courseId) {
            $course = AcademyCourse::find($courseId);
            if ($course) {
                $courseAnalytics = [
                    'course' => $course,
                    'ratings_summary' => CourseRating::getCourseRatingsSummary($courseId),
                    'top_content' => ContentRating::getTopRatedContent($courseId),
                    'least_content' => ContentRating::getLeastRatedContent($courseId),
                ];
            }
        }

        $courses = AcademyCourse::select('id', 'title')->get();

        return view('plugins/perfect-pharma::admin.academy.ratings.analytics', compact(
            'generalStats', 'topRatedCourses', 'ratingDistribution', 'monthlyRatings',
            'courseAnalytics', 'courses'
        ));
    }

    public function bulkApproveCourseRatings(Request $request, BaseHttpResponse $response)
    {
        try {
            $request->validate([
                'rating_ids' => 'required|array',
                'rating_ids.*' => 'exists:course_ratings,id',
            ]);

            $count = CourseRating::whereIn('id', $request->rating_ids)
                ->update(['is_approved' => true]);

            return $response->setMessage("تم الموافقة على {$count} تقييم بنجاح");

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء الموافقة على التقييمات: ' . $e->getMessage());
        }
    }

    public function bulkDeleteCourseRatings(Request $request, BaseHttpResponse $response)
    {
        try {
            $request->validate([
                'rating_ids' => 'required|array',
                'rating_ids.*' => 'exists:course_ratings,id',
            ]);

            $count = CourseRating::whereIn('id', $request->rating_ids)->count();
            CourseRating::whereIn('id', $request->rating_ids)->delete();

            return $response->setMessage("تم حذف {$count} تقييم بنجاح");

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حذف التقييمات: ' . $e->getMessage());
        }
    }

    public function exportRatings(Request $request)
    {
        $type = $request->input('type', 'course'); // course or content
        $courseId = $request->input('course_id');

        if ($type === 'course') {
            $ratings = CourseRating::with(['user', 'course'])
                ->when($courseId, function($query, $courseId) {
                    $query->where('course_id', $courseId);
                })
                ->get();

            $data = $ratings->map(function($rating) {
                return [
                    'الدورة' => $rating->course->title,
                    'الطالب' => $rating->user->name,
                    'البريد الإلكتروني' => $rating->user->email,
                    'التقييم' => $rating->rating,
                    'المراجعة' => $rating->review,
                    'الحالة' => $rating->is_approved ? 'معتمد' : 'في الانتظار',
                    'تاريخ التقييم' => $rating->created_at->format('Y-m-d H:i'),
                ];
            });
        } else {
            $ratings = ContentRating::with(['user', 'content.course'])
                ->when($courseId, function($query, $courseId) {
                    $query->whereHas('content', function($q) use ($courseId) {
                        $q->where('course_id', $courseId);
                    });
                })
                ->get();

            $data = $ratings->map(function($rating) {
                return [
                    'الدورة' => $rating->content->course->title,
                    'المحتوى' => $rating->content->title,
                    'الطالب' => $rating->user->name,
                    'البريد الإلكتروني' => $rating->user->email,
                    'التقييم' => $rating->rating,
                    'التعليق' => $rating->comment,
                    'مفيد' => $rating->is_helpful ? 'نعم' : 'لا',
                    'تاريخ التقييم' => $rating->created_at->format('Y-m-d H:i'),
                ];
            });
        }

        $filename = 'تقييمات_' . ($type === 'course' ? 'الدورات' : 'المحتوى') . '_' . now()->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // إضافة العناوين
            if (!empty($data)) {
                fputcsv($file, array_keys($data[0]));
                
                // إضافة البيانات
                foreach ($data as $row) {
                    fputcsv($file, $row);
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}

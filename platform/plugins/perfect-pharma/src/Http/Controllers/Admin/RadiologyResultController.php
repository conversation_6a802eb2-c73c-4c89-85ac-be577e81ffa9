<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\PerfectPharma\Models\RadiologyResult;
use Illuminate\Http\Request;

class RadiologyResultController extends BaseController
{
    /**
     * عرض قائمة نتائج الأشعة
     */
    public function index(Request $request)
    {
        $query = RadiologyResult::with([
            'request.patient',
            'request.doctor.user',
            'request.radiologyCenter',
            'request.scanType'
        ]);
        
        // البحث
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('request', function($requestQuery) use ($search) {
                $requestQuery->where('request_code', 'like', "%{$search}%")
                    ->orWhereHas('patient', function($patientQuery) use ($search) {
                        $patientQuery->where('name', 'like', "%{$search}%")
                                    ->orWhere('phone', 'like', "%{$search}%");
                    });
            });
        }
        
        // فلترة بالحالة
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // فلترة بالتاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('reported_at', '>=', $request->get('date_from'));
        }
        
        if ($request->filled('date_to')) {
            $query->whereDate('reported_at', '<=', $request->get('date_to'));
        }
        
        $results = $query->orderBy('reported_at', 'desc')->paginate(20);
        
        // إحصائيات
        $stats = [
            'total' => RadiologyResult::count(),
            'draft' => RadiologyResult::where('status', 'draft')->count(),
            'pending_review' => RadiologyResult::where('status', 'pending_review')->count(),
            'approved' => RadiologyResult::where('status', 'approved')->count(),
            'delivered' => RadiologyResult::where('status', 'delivered')->count(),
        ];
        
        return view('plugins/perfect-pharma::admin.radiology.results.index', compact(
            'results',
            'stats'
        ));
    }
    
    /**
     * عرض تفاصيل نتيجة أشعة
     */
    public function show($id)
    {
        $result = RadiologyResult::with([
            'request.patient',
            'request.doctor.user',
            'request.radiologyCenter',
            'request.scanType'
        ])->findOrFail($id);
        
        return view('plugins/perfect-pharma::admin.radiology.results.show', compact('result'));
    }
    
    /**
     * الموافقة على نتيجة أشعة
     */
    public function approve($id)
    {
        $result = RadiologyResult::findOrFail($id);
        
        $result->update([
            'status' => 'approved',
            'reviewed_by' => auth()->id(),
            'reviewed_at' => now(),
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'تم اعتماد نتيجة الأشعة'
        ]);
    }
    
    /**
     * رفض نتيجة أشعة
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);
        
        $result = RadiologyResult::findOrFail($id);
        
        $result->update([
            'status' => 'draft',
            'rejection_reason' => $request->get('reason'),
            'reviewed_by' => auth()->id(),
            'reviewed_at' => now(),
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'تم رفض نتيجة الأشعة وإرجاعها للمراجعة'
        ]);
    }
    
    /**
     * تحميل نتيجة أشعة
     */
    public function download($id)
    {
        $result = RadiologyResult::findOrFail($id);
        
        // سيتم تطوير هذه الوظيفة لاحقاً لتحميل ملف PDF
        return response()->json([
            'message' => 'سيتم تطوير ميزة التحميل قريباً'
        ]);
    }
    
    /**
     * مراقبة الجودة
     */
    public function qualityControl(Request $request)
    {
        // إحصائيات مراقبة الجودة
        $stats = [
            'total_results' => RadiologyResult::count(),
            'approved_rate' => RadiologyResult::where('status', 'approved')->count() / max(RadiologyResult::count(), 1) * 100,
            'average_review_time' => RadiologyResult::whereNotNull('reviewed_at')
                ->whereNotNull('reported_at')
                ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, reported_at, reviewed_at)) as avg_hours')
                ->value('avg_hours') ?? 0,
            'pending_review' => RadiologyResult::where('status', 'pending_review')->count(),
        ];
        
        // النتائج التي تحتاج مراجعة
        $pendingResults = RadiologyResult::where('status', 'pending_review')
            ->with([
                'request.patient',
                'request.radiologyCenter',
                'request.scanType'
            ])
            ->orderBy('reported_at')
            ->limit(10)
            ->get();
        
        return view('plugins/perfect-pharma::admin.radiology.results.quality-control', compact(
            'stats',
            'pendingResults'
        ));
    }
}

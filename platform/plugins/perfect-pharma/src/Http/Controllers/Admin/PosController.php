<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\PerfectPharma\Models\PosSale;
use <PERSON><PERSON>ble\PerfectPharma\Models\PosSaleItem;
use Bo<PERSON>ble\PerfectPharma\Models\PosPayment;
use Botble\PerfectPharma\Models\PharmacyInventory;
use Botble\PerfectPharma\Models\Pharmacy;
use Botble\PerfectPharma\Models\PosCashierSession;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PosController extends BaseController
{
    public function __construct()
    {
        $this->breadcrumb()
            ->add(trans('core/base::layouts.dashboard'), route('dashboard.index'))
            ->add('نظام نقاط البيع', route('admin.pos.index'));
    }

    public function index()
    {
        $this->pageTitle('نظام نقاط البيع');

        // التحقق من وجود جلسة كاشير مفتوحة
        $currentSession = PosCashierSession::getCurrentSession(Auth::id());
        
        if (!$currentSession) {
            return redirect()->route('admin.pos.session.open');
        }

        // إحصائيات اليوم
        $todayStats = PosSale::getTodayStats();
        $paymentStats = PosPayment::getTodayStats();

        // المنتجات الأكثر مبيعاً
        $topProducts = PosSale::getTopProducts(null, 5);

        // الصيدليات
        $pharmacies = Pharmacy::where('is_active', true)->get();

        return view('plugins/perfect-pharma::admin.pos.index', compact(
            'currentSession',
            'todayStats',
            'paymentStats',
            'topProducts',
            'pharmacies'
        ));
    }

    public function cashier()
    {
        $this->pageTitle('واجهة الكاشير');

        // التحقق من وجود جلسة كاشير مفتوحة
        $currentSession = PosCashierSession::getCurrentSession(Auth::id());
        
        if (!$currentSession) {
            return redirect()->route('admin.pos.session.open')
                           ->with('warning', 'يجب فتح جلسة كاشير أولاً');
        }

        // المنتجات المتاحة
        $products = Product::with(['categories', 'pharmacyInventory' => function($query) use ($currentSession) {
            $query->where('pharmacy_id', $currentSession->pharmacy_id)
                  ->where('is_active', true)
                  ->where('quantity_in_stock', '>', 0);
        }])
        ->where('status', 'published')
        ->limit(50)
        ->get();

        // العملاء
        $customers = Customer::where('is_vendor', false)->limit(100)->get();

        return view('plugins/perfect-pharma::admin.pos.cashier', compact(
            'currentSession',
            'products',
            'customers'
        ));
    }

    public function searchProducts(Request $request)
    {
        $query = $request->get('q');
        $pharmacyId = $request->get('pharmacy_id');

        if (!$query || strlen($query) < 2) {
            return response()->json([]);
        }

        $products = Product::with(['pharmacyInventory' => function($q) use ($pharmacyId) {
            if ($pharmacyId) {
                $q->where('pharmacy_id', $pharmacyId);
            }
            $q->where('is_active', true)
              ->where('quantity_in_stock', '>', 0);
        }])
        ->where('status', 'published')
        ->where(function($q) use ($query) {
            $q->where('name', 'LIKE', "%{$query}%")
              ->orWhere('sku', 'LIKE', "%{$query}%");
        })
        ->limit(20)
        ->get()
        ->map(function($product) {
            $inventory = $product->pharmacyInventory->first();
            
            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'price' => $inventory ? $inventory->selling_price : $product->price,
                'stock' => $inventory ? $inventory->available_quantity : 0,
                'inventory_id' => $inventory ? $inventory->id : null,
                'batch_number' => $inventory ? $inventory->batch_number : null,
                'expiry_date' => $inventory ? $inventory->expiry_date?->format('Y-m-d') : null,
                'is_prescription_required' => $inventory ? $inventory->is_prescription_required : false,
            ];
        });

        return response()->json($products);
    }

    public function createSale(Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:ec_products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'customer_id' => 'nullable|exists:ec_customers,id',
            'payment_method' => 'required|in:cash,card,wallet,bank_transfer,insurance,credit',
            'payment_amount' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $currentSession = PosCashierSession::getCurrentSession(Auth::id());
            
            if (!$currentSession) {
                return $response->setError()
                               ->setMessage('لا توجد جلسة كاشير مفتوحة');
            }

            // حساب المجاميع
            $subtotal = 0;
            $taxAmount = 0;
            $discountAmount = $request->get('discount_amount', 0);

            foreach ($request->items as $item) {
                $itemTotal = $item['quantity'] * $item['price'];
                $subtotal += $itemTotal;
                // يمكن إضافة حساب الضريبة هنا
            }

            $totalAmount = $subtotal + $taxAmount - $discountAmount;

            // إنشاء البيع
            $sale = PosSale::create([
                'sale_number' => PosSale::generateSaleNumber(),
                'pharmacy_id' => $currentSession->pharmacy_id,
                'cashier_id' => Auth::id(),
                'customer_id' => $request->customer_id,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'payment_status' => 'pending',
                'sale_date' => now(),
                'notes' => $request->notes,
            ]);

            // إضافة عناصر البيع
            foreach ($request->items as $item) {
                $product = Product::find($item['product_id']);
                $inventory = PharmacyInventory::where('product_id', $item['product_id'])
                                            ->where('pharmacy_id', $currentSession->pharmacy_id)
                                            ->first();

                if (!$inventory || $inventory->available_quantity < $item['quantity']) {
                    throw new \Exception("الكمية المطلوبة غير متوفرة للمنتج: {$product->name}");
                }

                $saleItem = PosSaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $item['product_id'],
                    'pharmacy_inventory_id' => $inventory->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['price'],
                    'total_price' => $item['quantity'] * $item['price'],
                    'batch_number' => $inventory->batch_number,
                    'expiry_date' => $inventory->expiry_date,
                    'is_prescription_required' => $inventory->is_prescription_required,
                ]);

                // حجز الكمية من المخزون
                $inventory->reserveStock($item['quantity']);
            }

            // إضافة الدفعة
            $payment = PosPayment::create([
                'payment_number' => PosPayment::generatePaymentNumber(),
                'sale_id' => $sale->id,
                'cashier_id' => Auth::id(),
                'payment_method' => $request->payment_method,
                'amount' => $request->payment_amount,
                'status' => 'approved',
                'payment_date' => now(),
                'processed_at' => now(),
                'reference_number' => $request->reference_number,
                'notes' => $request->payment_notes,
            ]);

            // تحديث حالة البيع
            $sale->update([
                'paid_amount' => $request->payment_amount,
                'change_amount' => max(0, $request->payment_amount - $totalAmount),
                'payment_status' => $request->payment_amount >= $totalAmount ? 'paid' : 'partial',
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            // تحديث المخزون
            foreach ($sale->items as $item) {
                $item->updateInventory();
            }

            DB::commit();

            return $response->setData([
                'sale_id' => $sale->id,
                'sale_number' => $sale->sale_number,
                'total_amount' => $sale->total_amount,
                'change_amount' => $sale->change_amount,
            ])->setMessage('تم إنجاز البيع بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return $response->setError()
                           ->setMessage('حدث خطأ أثناء معالجة البيع: ' . $e->getMessage());
        }
    }

    public function getSale($id)
    {
        $sale = PosSale::with([
            'items.product',
            'payments',
            'customer',
            'pharmacy',
            'cashier'
        ])->findOrFail($id);

        return response()->json([
            'sale' => $sale,
            'items' => $sale->items,
            'payments' => $sale->payments,
        ]);
    }

    public function printReceipt($id)
    {
        $sale = PosSale::with([
            'items.product',
            'payments',
            'customer',
            'pharmacy',
            'cashier'
        ])->findOrFail($id);

        return view('plugins/perfect-pharma::admin.pos.receipt', compact('sale'));
    }

    public function cancelSale($id, Request $request, BaseHttpResponse $response)
    {
        try {
            DB::beginTransaction();

            $sale = PosSale::findOrFail($id);

            if (!$sale->canBeCancelled()) {
                return $response->setError()
                               ->setMessage('لا يمكن إلغاء هذا البيع');
            }

            // إرجاع المخزون المحجوز
            foreach ($sale->items as $item) {
                if ($item->pharmacyInventory) {
                    $item->pharmacyInventory->unreserveStock($item->quantity);
                }
            }

            // إلغاء البيع
            $sale->cancel($request->cancellation_reason);

            DB::commit();

            return $response->setMessage('تم إلغاء البيع بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return $response->setError()
                           ->setMessage('حدث خطأ أثناء إلغاء البيع: ' . $e->getMessage());
        }
    }

    public function getDashboardStats()
    {
        $todayStats = PosSale::getTodayStats();
        $paymentStats = PosPayment::getTodayStats();
        $currentSession = PosCashierSession::getCurrentSession(Auth::id());

        return response()->json([
            'today_stats' => $todayStats,
            'payment_stats' => $paymentStats,
            'current_session' => $currentSession,
        ]);
    }

    // إدارة جلسات الكاشير
    public function openSessionForm()
    {
        $this->pageTitle('فتح جلسة كاشير');

        $pharmacies = Pharmacy::where('is_active', true)->get();
        $currentSession = PosCashierSession::getCurrentSession(Auth::id());

        return view('plugins/perfect-pharma::admin.pos.session.open', compact(
            'pharmacies',
            'currentSession'
        ));
    }

    public function openSession(Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'pharmacy_id' => 'required|exists:pharmacies,id',
            'opening_balance' => 'required|numeric|min:0',
            'opening_notes' => 'nullable|string|max:500',
        ]);

        try {
            $session = PosCashierSession::openSession(
                Auth::id(),
                $request->pharmacy_id,
                $request->opening_balance,
                $request->opening_notes
            );

            return $response->setData(['session_id' => $session->id])
                           ->setMessage('تم فتح جلسة الكاشير بنجاح')
                           ->setNextUrl(route('admin.pos.cashier'));

        } catch (\Exception $e) {
            return $response->setError()
                           ->setMessage('حدث خطأ أثناء فتح الجلسة: ' . $e->getMessage());
        }
    }

    public function closeSessionForm()
    {
        $this->pageTitle('إغلاق جلسة كاشير');

        $currentSession = PosCashierSession::getCurrentSession(Auth::id());

        if (!$currentSession) {
            return redirect()->route('admin.pos.session.open')
                           ->with('warning', 'لا توجد جلسة مفتوحة');
        }

        // تحديث إحصائيات الجلسة
        $currentSession->updateSessionStats();
        $currentSession->refresh();

        return view('plugins/perfect-pharma::admin.pos.session.close', compact('currentSession'));
    }

    public function closeSession(Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'closing_balance' => 'required|numeric|min:0',
            'closing_notes' => 'nullable|string|max:500',
        ]);

        try {
            $currentSession = PosCashierSession::getCurrentSession(Auth::id());

            if (!$currentSession) {
                return $response->setError()
                               ->setMessage('لا توجد جلسة مفتوحة');
            }

            $currentSession->close(
                $request->closing_balance,
                $request->closing_notes
            );

            return $response->setMessage('تم إغلاق جلسة الكاشير بنجاح')
                           ->setNextUrl(route('admin.pos.index'));

        } catch (\Exception $e) {
            return $response->setError()
                           ->setMessage('حدث خطأ أثناء إغلاق الجلسة: ' . $e->getMessage());
        }
    }
}

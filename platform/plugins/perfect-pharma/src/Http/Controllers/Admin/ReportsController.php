<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\PerfectPharma\Models\CustomerUserType;
use Botble\PerfectPharma\Models\UserType;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportsController extends BaseController
{
    /**
     * عرض لوحة التقارير الرئيسية
     */
    public function index()
    {
        $stats = $this->getOverallStats();
        $chartData = $this->getChartData();
        $recentActivity = $this->getRecentActivity();
        
        return view('plugins/perfect-pharma::admin.reports.index', compact(
            'stats',
            'chartData', 
            'recentActivity'
        ));
    }

    /**
     * تقرير أنواع المستخدمين
     */
    public function userTypes(Request $request)
    {
        $period = $request->get('period', '30'); // آخر 30 يوم افتراضياً
        $startDate = Carbon::now()->subDays($period);
        
        $userTypeStats = UserType::withCount([
            'customerUserTypes as total_users',
            'customerUserTypes as verified_users' => function($query) {
                $query->where('is_verified', true);
            },
            'customerUserTypes as pending_users' => function($query) {
                $query->where('is_verified', false);
            },
            'customerUserTypes as recent_users' => function($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }
        ])->where('is_active', true)->get();

        $growthData = $this->getUserTypeGrowthData($period);
        
        return view('plugins/perfect-pharma::admin.reports.user-types', compact(
            'userTypeStats',
            'growthData',
            'period'
        ));
    }

    /**
     * تقرير التحقق
     */
    public function verification(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);
        
        $verificationStats = [
            'total_requests' => CustomerUserType::whereHas('userType', function($q) {
                $q->where('name', '!=', UserType::TYPE_PATIENT);
            })->count(),
            
            'pending_requests' => CustomerUserType::whereHas('userType', function($q) {
                $q->where('name', '!=', UserType::TYPE_PATIENT);
            })->where('is_verified', false)->count(),
            
            'approved_requests' => CustomerUserType::whereHas('userType', function($q) {
                $q->where('name', '!=', UserType::TYPE_PATIENT);
            })->where('is_verified', true)->count(),
            
            'recent_requests' => CustomerUserType::whereHas('userType', function($q) {
                $q->where('name', '!=', UserType::TYPE_PATIENT);
            })->where('created_at', '>=', $startDate)->count(),
            
            'avg_processing_time' => $this->getAverageProcessingTime($period)
        ];

        $dailyVerifications = $this->getDailyVerificationData($period);
        $verificationByType = $this->getVerificationByTypeData();
        
        return view('plugins/perfect-pharma::admin.reports.verification', compact(
            'verificationStats',
            'dailyVerifications',
            'verificationByType',
            'period'
        ));
    }

    /**
     * تقرير النشاط
     */
    public function activity(Request $request)
    {
        $period = $request->get('period', '7');
        $startDate = Carbon::now()->subDays($period);
        
        $activityData = [
            'registrations' => $this->getRegistrationActivity($startDate),
            'verifications' => $this->getVerificationActivity($startDate),
            'peak_hours' => $this->getPeakHoursData($startDate),
            'user_engagement' => $this->getUserEngagementData($startDate)
        ];
        
        return view('plugins/perfect-pharma::admin.reports.activity', compact(
            'activityData',
            'period'
        ));
    }

    /**
     * تصدير التقارير
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'verification');
        $format = $request->get('format', 'csv');
        $period = $request->get('period', '30');
        
        switch ($type) {
            case 'user-types':
                return $this->exportUserTypesReport($format, $period);
            case 'verification':
                return $this->exportVerificationReport($format, $period);
            case 'activity':
                return $this->exportActivityReport($format, $period);
            default:
                return $this->exportVerificationReport($format, $period);
        }
    }

    /**
     * الحصول على الإحصائيات العامة
     */
    protected function getOverallStats()
    {
        return [
            'total_users' => Customer::count(),
            'verified_users' => CustomerUserType::where('is_verified', true)->count(),
            'pending_verification' => CustomerUserType::where('is_verified', false)
                                                   ->whereHas('userType', function($q) {
                                                       $q->where('name', '!=', UserType::TYPE_PATIENT);
                                                   })->count(),
            'active_user_types' => UserType::where('is_active', true)->count(),
            'today_registrations' => Customer::whereDate('created_at', today())->count(),
            'this_week_verifications' => CustomerUserType::where('is_verified', true)
                                                        ->whereBetween('verified_at', [
                                                            Carbon::now()->startOfWeek(),
                                                            Carbon::now()->endOfWeek()
                                                        ])->count()
        ];
    }

    /**
     * الحصول على بيانات الرسوم البيانية
     */
    protected function getChartData()
    {
        $last30Days = collect(range(29, 0))->map(function ($daysAgo) {
            $date = Carbon::now()->subDays($daysAgo);
            return [
                'date' => $date->format('Y-m-d'),
                'registrations' => Customer::whereDate('created_at', $date)->count(),
                'verifications' => CustomerUserType::where('is_verified', true)
                                                 ->whereDate('verified_at', $date)->count()
            ];
        });

        return $last30Days;
    }

    /**
     * الحصول على النشاط الأخير
     */
    protected function getRecentActivity()
    {
        return CustomerUserType::with(['customer', 'userType'])
                              ->whereHas('userType', function($q) {
                                  $q->where('name', '!=', UserType::TYPE_PATIENT);
                              })
                              ->orderBy('created_at', 'desc')
                              ->limit(10)
                              ->get();
    }

    /**
     * الحصول على بيانات نمو أنواع المستخدمين
     */
    protected function getUserTypeGrowthData($period)
    {
        $userTypes = UserType::where('name', '!=', UserType::TYPE_PATIENT)
                            ->where('is_active', true)
                            ->get();

        // إنشاء التواريخ للفترة المحددة
        $dates = collect();
        for ($i = $period; $i >= 0; $i--) {
            $dates->push(Carbon::now()->subDays($i)->format('Y-m-d'));
        }

        // إنشاء datasets لكل نوع مستخدم
        $datasets = $userTypes->map(function($userType, $index) use ($dates) {
            $colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
            $color = $colors[$index % count($colors)];

            $data = $dates->map(function($date) use ($userType) {
                return CustomerUserType::where('user_type_id', $userType->id)
                                     ->whereDate('created_at', $date)
                                     ->count();
            });

            return [
                'label' => $userType->display_name,
                'data' => $data->values()->toArray(),
                'borderColor' => $color,
                'backgroundColor' => $color . '20',
                'tension' => 0.1
            ];
        });

        return [
            'labels' => $dates->toArray(),
            'datasets' => $datasets->toArray()
        ];
    }

    /**
     * الحصول على متوسط وقت المعالجة
     */
    protected function getAverageProcessingTime($period)
    {
        $startDate = Carbon::now()->subDays($period);
        
        $avgTime = CustomerUserType::whereHas('userType', function($q) {
                                     $q->where('name', '!=', UserType::TYPE_PATIENT);
                                 })
                                 ->where('is_verified', true)
                                 ->where('created_at', '>=', $startDate)
                                 ->whereNotNull('verified_at')
                                 ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, created_at, verified_at)) as avg_hours')
                                 ->value('avg_hours');

        return round($avgTime ?? 0, 1);
    }

    /**
     * الحصول على بيانات التحقق اليومية
     */
    protected function getDailyVerificationData($period)
    {
        $startDate = Carbon::now()->subDays($period);
        
        return collect(range(intval($startDate->diffInDays(Carbon::now())), 0))
               ->map(function($daysAgo) {
                   $date = Carbon::now()->subDays($daysAgo);
                   return [
                       'date' => $date->format('Y-m-d'),
                       'approved' => CustomerUserType::where('is_verified', true)
                                                   ->whereDate('verified_at', $date)
                                                   ->count(),
                       'pending' => CustomerUserType::where('is_verified', false)
                                                  ->whereDate('created_at', $date)
                                                  ->count()
                   ];
               });
    }

    /**
     * الحصول على بيانات التحقق حسب النوع
     */
    protected function getVerificationByTypeData()
    {
        return UserType::where('name', '!=', UserType::TYPE_PATIENT)
                      ->where('is_active', true)
                      ->withCount([
                          'customerUserTypes as total',
                          'customerUserTypes as verified' => function($query) {
                              $query->where('is_verified', true);
                          },
                          'customerUserTypes as pending' => function($query) {
                              $query->where('is_verified', false);
                          }
                      ])
                      ->get();
    }

    /**
     * الحصول على نشاط التسجيل
     */
    protected function getRegistrationActivity($startDate)
    {
        return Customer::where('created_at', '>=', $startDate)
                      ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                      ->groupBy('date')
                      ->orderBy('date')
                      ->get();
    }

    /**
     * الحصول على نشاط التحقق
     */
    protected function getVerificationActivity($startDate)
    {
        return CustomerUserType::where('verified_at', '>=', $startDate)
                              ->where('is_verified', true)
                              ->selectRaw('DATE(verified_at) as date, COUNT(*) as count')
                              ->groupBy('date')
                              ->orderBy('date')
                              ->get();
    }

    /**
     * الحصول على بيانات ساعات الذروة
     */
    protected function getPeakHoursData($startDate)
    {
        return Customer::where('created_at', '>=', $startDate)
                      ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
                      ->groupBy('hour')
                      ->orderBy('hour')
                      ->get();
    }

    /**
     * الحصول على بيانات تفاعل المستخدمين
     */
    protected function getUserEngagementData($startDate)
    {
        // يمكن توسيع هذه الدالة لتشمل المزيد من مقاييس التفاعل
        return [
            'active_users' => Customer::where('updated_at', '>=', $startDate)->count(),
            'new_users' => Customer::where('created_at', '>=', $startDate)->count(),
            'returning_users' => Customer::where('created_at', '<', $startDate)
                                       ->where('updated_at', '>=', $startDate)->count()
        ];
    }

    /**
     * تصدير تقرير أنواع المستخدمين
     */
    protected function exportUserTypesReport($format, $period)
    {
        // سيتم تطوير منطق التصدير
        return response()->json(['message' => 'تصدير تقرير أنواع المستخدمين قيد التطوير']);
    }

    /**
     * تصدير تقرير التحقق
     */
    protected function exportVerificationReport($format, $period)
    {
        // سيتم تطوير منطق التصدير
        return response()->json(['message' => 'تصدير تقرير التحقق قيد التطوير']);
    }

    /**
     * تصدير تقرير النشاط
     */
    protected function exportActivityReport($format, $period)
    {
        // سيتم تطوير منطق التصدير
        return response()->json(['message' => 'تصدير تقرير النشاط قيد التطوير']);
    }
}

<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\PerfectPharma\Models\PosSale;
use Bo<PERSON>ble\PerfectPharma\Models\PosReturn;
use Botble\PerfectPharma\Models\Pharmacy;
use Botble\ACL\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PosSalesController extends BaseController
{
    public function __construct()
    {
        $this->breadcrumb()
            ->add(trans('core/base::layouts.dashboard'), route('dashboard.index'))
            ->add('نظام نقاط البيع', route('admin.pos.index'))
            ->add('إدارة المبيعات', route('admin.pos.sales.index'));
    }

    public function index(Request $request)
    {
        $this->pageTitle('إدارة المبيعات');

        // فلاتر البحث
        $pharmacyId = $request->get('pharmacy_id');
        $cashierId = $request->get('cashier_id');
        $status = $request->get('status');
        $paymentStatus = $request->get('payment_status');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        // بناء الاستعلام
        $query = PosSale::with(['pharmacy', 'cashier', 'customer'])
                        ->orderBy('sale_date', 'desc');

        if ($pharmacyId) {
            $query->where('pharmacy_id', $pharmacyId);
        }

        if ($cashierId) {
            $query->where('cashier_id', $cashierId);
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($paymentStatus) {
            $query->where('payment_status', $paymentStatus);
        }

        if ($dateFrom) {
            $query->whereDate('sale_date', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('sale_date', '<=', $dateTo);
        }

        $sales = $query->paginate(20);

        // بيانات الفلاتر
        $pharmacies = Pharmacy::where('is_active', true)->get();
        $cashiers = User::whereHas('roles', function($q) {
            $q->where('slug', 'cashier');
        })->get();

        $statuses = [
            'pending' => 'معلق',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            'refunded' => 'مسترد',
        ];

        $paymentStatuses = [
            'pending' => 'في الانتظار',
            'partial' => 'دفع جزئي',
            'paid' => 'مدفوع',
            'refunded' => 'مسترد',
        ];

        // إحصائيات
        $stats = [
            'total_sales' => $query->count(),
            'total_amount' => $query->sum('total_amount'),
            'completed_sales' => $query->where('status', 'completed')->count(),
            'pending_sales' => $query->where('status', 'pending')->count(),
        ];

        return view('plugins/perfect-pharma::admin.pos.sales.index', compact(
            'sales',
            'pharmacies',
            'cashiers',
            'statuses',
            'paymentStatuses',
            'stats'
        ));
    }

    public function show($id)
    {
        $sale = PosSale::with([
            'items.product',
            'payments',
            'customer',
            'pharmacy',
            'cashier',
            'returns.items'
        ])->findOrFail($id);

        $this->pageTitle('تفاصيل البيع: ' . $sale->sale_number);

        return view('plugins/perfect-pharma::admin.pos.sales.show', compact('sale'));
    }

    public function cancel($id, Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        try {
            $sale = PosSale::findOrFail($id);

            if (!$sale->canBeCancelled()) {
                return $response->setError()
                               ->setMessage('لا يمكن إلغاء هذا البيع');
            }

            $sale->cancel($request->cancellation_reason);

            return $response->setMessage('تم إلغاء البيع بنجاح');

        } catch (\Exception $e) {
            return $response->setError()
                           ->setMessage('حدث خطأ أثناء إلغاء البيع: ' . $e->getMessage());
        }
    }

    public function refund($id)
    {
        $sale = PosSale::with(['items.product', 'returns'])->findOrFail($id);

        if (!$sale->canBeRefunded()) {
            return redirect()->back()->with('error', 'لا يمكن إرجاع هذا البيع');
        }

        $this->pageTitle('إرجاع البيع: ' . $sale->sale_number);

        return view('plugins/perfect-pharma::admin.pos.sales.refund', compact('sale'));
    }

    public function processRefund($id, Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'return_type' => 'required|in:full,partial',
            'reason' => 'required|in:defective,expired,wrong_item,customer_request,other',
            'reason_details' => 'nullable|string|max:500',
            'items' => 'required_if:return_type,partial|array',
            'items.*.sale_item_id' => 'required_with:items|exists:pos_sale_items,id',
            'items.*.quantity' => 'required_with:items|integer|min:1',
            'items.*.condition' => 'required_with:items|in:good,damaged,expired',
        ]);

        try {
            $sale = PosSale::findOrFail($id);

            if (!$sale->canBeRefunded()) {
                return $response->setError()
                               ->setMessage('لا يمكن إرجاع هذا البيع');
            }

            // إنشاء المرتجع
            $return = PosReturn::create([
                'return_number' => PosReturn::generateReturnNumber(),
                'sale_id' => $sale->id,
                'cashier_id' => Auth::id(),
                'customer_id' => $sale->customer_id,
                'return_type' => $request->return_type,
                'reason' => $request->reason,
                'reason_details' => $request->reason_details,
                'return_date' => now(),
                'status' => 'approved', // موافقة تلقائية للكاشير
            ]);

            $totalAmount = 0;

            if ($request->return_type === 'full') {
                // إرجاع كامل
                foreach ($sale->items as $item) {
                    $return->items()->create([
                        'sale_item_id' => $item->id,
                        'product_id' => $item->product_id,
                        'quantity_returned' => $item->quantity,
                        'unit_price' => $item->unit_price,
                        'total_amount' => $item->total_price,
                        'condition' => 'good',
                    ]);
                    
                    $totalAmount += $item->total_price;
                }
            } else {
                // إرجاع جزئي
                foreach ($request->items as $itemData) {
                    $saleItem = $sale->items()->findOrFail($itemData['sale_item_id']);
                    $itemTotal = $itemData['quantity'] * $saleItem->unit_price;
                    
                    $return->items()->create([
                        'sale_item_id' => $saleItem->id,
                        'product_id' => $saleItem->product_id,
                        'quantity_returned' => $itemData['quantity'],
                        'unit_price' => $saleItem->unit_price,
                        'total_amount' => $itemTotal,
                        'condition' => $itemData['condition'],
                    ]);
                    
                    $totalAmount += $itemTotal;
                }
            }

            // تحديث المرتجع
            $return->update(['total_amount' => $totalAmount]);

            // معالجة المرتجع
            $return->process('cash', $totalAmount);

            // تحديث حالة البيع
            if ($request->return_type === 'full') {
                $sale->update(['status' => 'refunded']);
            }

            return $response->setMessage('تم معالجة المرتجع بنجاح')
                           ->setNextUrl(route('admin.pos.sales.show', $sale->id));

        } catch (\Exception $e) {
            return $response->setError()
                           ->setMessage('حدث خطأ أثناء معالجة المرتجع: ' . $e->getMessage());
        }
    }

    public function receipt($id)
    {
        $sale = PosSale::with([
            'items.product',
            'payments',
            'customer',
            'pharmacy',
            'cashier'
        ])->findOrFail($id);

        return view('plugins/perfect-pharma::admin.pos.sales.receipt', compact('sale'));
    }

    public function reports(Request $request)
    {
        $this->pageTitle('تقارير المبيعات');

        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $pharmacyId = $request->get('pharmacy_id');

        // إحصائيات المبيعات
        $salesStats = PosSale::getStats($pharmacyId, $dateFrom, $dateTo);
        
        // أفضل المنتجات مبيعاً
        $topProducts = PosSale::getTopProducts($pharmacyId, 10);

        // إحصائيات الكاشيرين
        $cashierStats = $this->getCashierStats($pharmacyId, $dateFrom, $dateTo);

        // المبيعات اليومية
        $dailySales = $this->getDailySales($pharmacyId, $dateFrom, $dateTo);

        $pharmacies = Pharmacy::where('is_active', true)->get();

        return view('plugins/perfect-pharma::admin.pos.sales.reports', compact(
            'salesStats',
            'topProducts',
            'cashierStats',
            'dailySales',
            'pharmacies',
            'dateFrom',
            'dateTo'
        ));
    }

    private function getCashierStats($pharmacyId, $dateFrom, $dateTo): array
    {
        $query = PosSale::with('cashier')
                        ->whereBetween('sale_date', [$dateFrom, $dateTo])
                        ->where('status', 'completed');

        if ($pharmacyId) {
            $query->where('pharmacy_id', $pharmacyId);
        }

        return $query->get()
                    ->groupBy('cashier_id')
                    ->map(function($sales, $cashierId) {
                        $cashier = $sales->first()->cashier;
                        return [
                            'cashier_name' => $cashier->name,
                            'total_sales' => $sales->count(),
                            'total_amount' => $sales->sum('total_amount'),
                            'average_sale' => $sales->avg('total_amount'),
                        ];
                    })
                    ->values()
                    ->toArray();
    }

    private function getDailySales($pharmacyId, $dateFrom, $dateTo): array
    {
        $query = PosSale::whereBetween('sale_date', [$dateFrom, $dateTo])
                        ->where('status', 'completed');

        if ($pharmacyId) {
            $query->where('pharmacy_id', $pharmacyId);
        }

        return $query->selectRaw('DATE(sale_date) as date, COUNT(*) as count, SUM(total_amount) as amount')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get()
                    ->toArray();
    }
}

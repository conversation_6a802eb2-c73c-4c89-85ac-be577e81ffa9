<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use App\Models\WalletTransaction;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;

class WalletTransactionController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle('المعاملات المالية');

        $transactions = WalletTransaction::with(['wallet.user'])
            ->latest()
            ->paginate(20);

        if ($request->ajax()) {
            return view('plugins/perfect-pharma::admin.wallet-transactions.table', compact('transactions'))->render();
        }

        return view('plugins/perfect-pharma::admin.wallet-transactions.index', compact('transactions'));
    }

    public function show(int $id, BaseHttpResponse $response)
    {
        $transaction = WalletTransaction::with(['wallet.user'])->findOrFail($id);

        $this->pageTitle('تفاصيل المعاملة: ' . $transaction->transaction_id);

        return view('plugins/perfect-pharma::admin.wallet-transactions.show', compact('transaction'));
    }
}

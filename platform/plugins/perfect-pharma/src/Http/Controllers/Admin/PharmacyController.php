<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\PerfectPharma\Models\Pharmacy;
use Bo<PERSON>ble\PerfectPharma\Tables\PharmacyTable;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;

class PharmacyController extends BaseController
{
    public function index(PharmacyTable $dataTable)
    {
        $this->pageTitle('إدارة الصيدليات');

        return $dataTable->renderTable();
    }

    public function create()
    {
        $this->pageTitle('إضافة صيدلية جديدة');

        return view('plugins/perfect-pharma::admin.pharmacies.create');
    }

    public function store(Request $request)
    {
        // TODO: Implement store method
        return redirect()->route('admin.pharmacies.index')->with('success', 'تم إضافة الصيدلية بنجاح');
    }

    public function show(int $id)
    {
        $pharmacy = Pharmacy::with(['user'])->findOrFail($id);

        $this->pageTitle('ملف الصيدلية: ' . $pharmacy->name);

        return view('plugins/perfect-pharma::admin.pharmacies.show', compact('pharmacy'));
    }

    public function edit(int $id)
    {
        $pharmacy = Pharmacy::findOrFail($id);

        $this->pageTitle('تعديل الصيدلية: ' . $pharmacy->name);

        return view('plugins/perfect-pharma::admin.pharmacies.edit', compact('pharmacy'));
    }

    public function update(Request $request, int $id)
    {
        // TODO: Implement update method
        return redirect()->route('admin.pharmacies.index')->with('success', 'تم تحديث الصيدلية بنجاح');
    }

    public function destroy(int $id)
    {
        $pharmacy = Pharmacy::findOrFail($id);
        $pharmacy->delete();

        return redirect()->route('admin.pharmacies.index')->with('success', 'تم حذف الصيدلية بنجاح');
    }

    public function verification(Request $request)
    {
        $this->pageTitle('طلبات التحقق من الصيدليات');

        $pharmacies = Pharmacy::with(['user'])
            ->where('is_verified', false)
            ->latest()
            ->paginate(20);

        return view('plugins/perfect-pharma::admin.pharmacies.verification', compact('pharmacies'));
    }

    public function verify(int $id, BaseHttpResponse $response)
    {
        try {
            $pharmacy = Pharmacy::findOrFail($id);
            $pharmacy->is_verified = true;
            $pharmacy->verified_at = now();
            $pharmacy->save();

            return $response
                ->setMessage('تم التحقق من الصيدلية بنجاح')
                ->setNextUrl(route('admin.pharmacies.verification'));

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء التحقق من الصيدلية: ' . $e->getMessage());
        }
    }

    public function reject(int $id, BaseHttpResponse $response)
    {
        try {
            $pharmacy = Pharmacy::findOrFail($id);
            $pharmacy->is_verified = false;
            $pharmacy->verified_at = null;
            $pharmacy->save();

            return $response
                ->setMessage('تم رفض طلب التحقق')
                ->setNextUrl(route('admin.pharmacies.verification'));

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء رفض الطلب: ' . $e->getMessage());
        }
    }
}

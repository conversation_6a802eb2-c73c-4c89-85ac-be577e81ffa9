<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Ecommerce\Models\Product;
use Botble\PerfectPharma\Models\UserType;
use Botble\PerfectPharma\Services\PricingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductDiscountController extends BaseController
{
    protected $pricingService;

    public function __construct(PricingService $pricingService)
    {
        $this->pricingService = $pricingService;
    }

    /**
     * عرض صفحة إدارة خصومات المنتجات
     */
    public function index(Request $request)
    {
        $query = Product::query();

        // البحث بالاسم
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // فلترة حسب حالة الخصومات
        if ($request->filled('discount_status')) {
            if ($request->discount_status === 'enabled') {
                $query->where('enable_user_type_discounts', true);
            } elseif ($request->discount_status === 'disabled') {
                $query->where('enable_user_type_discounts', false);
            }
        }

        $products = $query->orderBy('name')
                         ->paginate(20);

        $userTypes = UserType::where('name', '!=', UserType::TYPE_PATIENT)
                           ->where('is_active', true)
                           ->get();

        return view('plugins/perfect-pharma::admin.product-discounts.index', compact(
            'products',
            'userTypes'
        ));
    }

    /**
     * عرض صفحة تعديل خصومات منتج معين
     */
    public function edit($id)
    {
        $product = Product::findOrFail($id);
        
        $userTypes = UserType::where('name', '!=', UserType::TYPE_PATIENT)
                           ->where('is_active', true)
                           ->get();

        $currentDiscounts = $this->pricingService->getProductDiscountRates($product);

        return view('plugins/perfect-pharma::admin.product-discounts.edit', compact(
            'product',
            'userTypes',
            'currentDiscounts'
        ));
    }

    /**
     * تحديث خصومات منتج معين
     */
    public function update(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'enable_user_type_discounts' => 'boolean',
            'hospital_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'pharmacy_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'clinic_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'lab_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'doctor_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'drug_supplier_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'pricing_notes' => 'nullable|string|max:1000'
        ], [
            'hospital_discount_percentage.max' => 'نسبة خصم المستشفيات لا يجب أن تتجاوز 100%',
            'pharmacy_discount_percentage.max' => 'نسبة خصم الصيدليات لا يجب أن تتجاوز 100%',
            'clinic_discount_percentage.max' => 'نسبة خصم العيادات لا يجب أن تتجاوز 100%',
            'lab_discount_percentage.max' => 'نسبة خصم المعامل لا يجب أن تتجاوز 100%',
            'doctor_discount_percentage.max' => 'نسبة خصم الأطباء لا يجب أن تتجاوز 100%',
            'drug_supplier_discount_percentage.max' => 'نسبة خصم موردين الأدوية لا يجب أن تتجاوز 100%',
        ]);

        $product->update([
            'enable_user_type_discounts' => $request->boolean('enable_user_type_discounts'),
            'hospital_discount_percentage' => $request->input('hospital_discount_percentage', 0),
            'pharmacy_discount_percentage' => $request->input('pharmacy_discount_percentage', 0),
            'clinic_discount_percentage' => $request->input('clinic_discount_percentage', 0),
            'lab_discount_percentage' => $request->input('lab_discount_percentage', 0),
            'doctor_discount_percentage' => $request->input('doctor_discount_percentage', 0),
            'drug_supplier_discount_percentage' => $request->input('drug_supplier_discount_percentage', 0),
            'pricing_notes' => $request->input('pricing_notes')
        ]);

        return redirect()
            ->route('admin.perfect-pharma.product-discounts.edit', $id)
            ->with('success', 'تم تحديث خصومات المنتج بنجاح');
    }

    /**
     * تطبيق خصومات مجمعة على منتجات متعددة
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:ec_products,id',
            'action' => 'required|in:enable_discounts,disable_discounts,set_discounts',
            'hospital_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'pharmacy_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'clinic_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'lab_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'doctor_discount_percentage' => 'nullable|numeric|min:0|max:100',
            'drug_supplier_discount_percentage' => 'nullable|numeric|min:0|max:100',
        ]);

        $productIds = $request->input('product_ids');
        $action = $request->input('action');

        DB::beginTransaction();
        try {
            switch ($action) {
                case 'enable_discounts':
                    Product::whereIn('id', $productIds)
                          ->update(['enable_user_type_discounts' => true]);
                    $message = 'تم تفعيل الخصومات المتدرجة للمنتجات المحددة';
                    break;

                case 'disable_discounts':
                    Product::whereIn('id', $productIds)
                          ->update(['enable_user_type_discounts' => false]);
                    $message = 'تم إلغاء الخصومات المتدرجة للمنتجات المحددة';
                    break;

                case 'set_discounts':
                    $updateData = [
                        'enable_user_type_discounts' => true,
                        'hospital_discount_percentage' => $request->input('hospital_discount_percentage', 0),
                        'pharmacy_discount_percentage' => $request->input('pharmacy_discount_percentage', 0),
                        'clinic_discount_percentage' => $request->input('clinic_discount_percentage', 0),
                        'lab_discount_percentage' => $request->input('lab_discount_percentage', 0),
                        'doctor_discount_percentage' => $request->input('doctor_discount_percentage', 0),
                        'drug_supplier_discount_percentage' => $request->input('drug_supplier_discount_percentage', 0),
                    ];
                    
                    Product::whereIn('id', $productIds)->update($updateData);
                    $message = 'تم تطبيق الخصومات على المنتجات المحددة';
                    break;
            }

            DB::commit();
            return response()->json([
                'error' => false,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'error' => true,
                'message' => 'حدث خطأ أثناء تحديث المنتجات: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * تطبيق خصومات افتراضية حسب نوع المستخدم
     */
    public function applyDefaultDiscounts(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:ec_products,id',
        ]);

        $productIds = $request->input('product_ids');
        
        // الحصول على الخصومات الافتراضية من أنواع المستخدمين
        $userTypes = UserType::where('name', '!=', UserType::TYPE_PATIENT)
                           ->where('is_active', true)
                           ->get();

        $defaultDiscounts = [
            'enable_user_type_discounts' => true,
        ];

        foreach ($userTypes as $userType) {
            $field = $userType->name . '_discount_percentage';
            $defaultDiscounts[$field] = $userType->default_discount_percentage ?? 0;
        }

        DB::beginTransaction();
        try {
            Product::whereIn('id', $productIds)->update($defaultDiscounts);
            
            DB::commit();
            return response()->json([
                'error' => false,
                'message' => 'تم تطبيق الخصومات الافتراضية بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'error' => true,
                'message' => 'حدث خطأ أثناء تطبيق الخصومات: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * معاينة الأسعار لمنتج معين
     */
    public function previewPrices($id)
    {
        $product = Product::findOrFail($id);
        
        $userTypes = UserType::where('is_active', true)->get();
        $pricePreview = [];

        foreach ($userTypes as $userType) {
            if ($userType->name === UserType::TYPE_PATIENT) {
                // المرضى - السعر العادي
                $pricePreview[] = [
                    'user_type' => $userType->display_name,
                    'original_price' => $product->price,
                    'discount_percentage' => 0,
                    'final_price' => $product->sale_price ?: $product->price,
                    'savings' => 0
                ];
            } else {
                // الأنواع الأخرى - مع الخصم
                $discountField = $userType->name . '_discount_percentage';
                $discountPercentage = $product->$discountField ?? 0;
                $basePrice = $product->sale_price ?: $product->price;
                $discountAmount = ($basePrice * $discountPercentage) / 100;
                $finalPrice = $basePrice - $discountAmount;

                $pricePreview[] = [
                    'user_type' => $userType->display_name,
                    'original_price' => $product->price,
                    'sale_price' => $basePrice,
                    'discount_percentage' => $discountPercentage,
                    'discount_amount' => $discountAmount,
                    'final_price' => $finalPrice,
                    'savings' => $discountAmount
                ];
            }
        }

        return response()->json([
            'product' => [
                'name' => $product->name,
                'price' => $product->price,
                'sale_price' => $product->sale_price,
                'enable_discounts' => $product->enable_user_type_discounts
            ],
            'price_preview' => $pricePreview
        ]);
    }

    /**
     * إحصائيات الخصومات
     */
    public function statistics()
    {
        $stats = [
            'total_products' => Product::count(),
            'products_with_discounts' => Product::where('enable_user_type_discounts', true)->count(),
            'products_without_discounts' => Product::where('enable_user_type_discounts', false)->count(),
        ];

        // إحصائيات الخصومات لكل نوع مستخدم
        $userTypes = UserType::where('name', '!=', UserType::TYPE_PATIENT)
                           ->where('is_active', true)
                           ->get();

        $discountStats = [];
        foreach ($userTypes as $userType) {
            $field = $userType->name . '_discount_percentage';
            
            $discountStats[$userType->name] = [
                'display_name' => $userType->display_name,
                'products_with_discount' => Product::where($field, '>', 0)->count(),
                'average_discount' => Product::where($field, '>', 0)->avg($field) ?? 0,
                'max_discount' => Product::max($field) ?? 0,
            ];
        }

        return response()->json([
            'general_stats' => $stats,
            'discount_stats' => $discountStats
        ]);
    }
}

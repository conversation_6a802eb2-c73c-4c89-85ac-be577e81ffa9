<?php

namespace Bo<PERSON><PERSON>\PerfectPharma\Http\Controllers\Admin;

use <PERSON><PERSON>ble\PerfectPharma\Models\QuizQuestion;
use <PERSON><PERSON>ble\PerfectPharma\Models\CourseQuiz;
use <PERSON><PERSON>ble\PerfectPharma\Models\AcademyCourse;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;

class QuizQuestionController extends BaseController
{
    public function index(int $courseId, int $quizId, Request $request)
    {
        $course = AcademyCourse::findOrFail($courseId);
        $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);
        
        $this->pageTitle('إدارة أسئلة الاختبار: ' . $quiz->title);

        $questions = QuizQuestion::where('quiz_id', $quizId)
            ->orderBy('order')
            ->paginate(20);

        return view('plugins/perfect-pharma::admin.academy.questions.index', compact('course', 'quiz', 'questions'));
    }

    public function create(int $courseId, int $quizId)
    {
        $course = AcademyCourse::findOrFail($courseId);
        $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);
        
        $this->pageTitle('إضافة سؤال جديد للاختبار: ' . $quiz->title);

        $questionTypes = QuizQuestion::getQuestionTypes();

        return view('plugins/perfect-pharma::admin.academy.questions.create', compact('course', 'quiz', 'questionTypes'));
    }

    public function store(int $courseId, int $quizId, Request $request, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);
            $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);

            $validatedData = $request->validate([
                'question' => 'required|string',
                'type' => 'required|in:multiple_choice,true_false,text',
                'options' => 'nullable|array',
                'options.*' => 'nullable|string',
                'correct_answer' => 'required|string',
                'explanation' => 'nullable|string',
                'points' => 'required|integer|min:1|max:100',
                'order' => 'nullable|integer|min:0',
            ]);

            $validatedData['quiz_id'] = $quizId;

            // تحديد الترتيب التلقائي إذا لم يتم تحديده
            if (!isset($validatedData['order'])) {
                $maxOrder = QuizQuestion::where('quiz_id', $quizId)->max('order') ?? 0;
                $validatedData['order'] = $maxOrder + 1;
            }

            // معالجة الخيارات للأسئلة متعددة الخيارات
            if ($validatedData['type'] === 'multiple_choice') {
                $validatedData['options'] = array_filter($validatedData['options'] ?? []);
                
                if (count($validatedData['options']) < 2) {
                    return $response
                        ->setError()
                        ->setMessage('يجب إضافة خيارين على الأقل للسؤال متعدد الخيارات');
                }
            } else {
                $validatedData['options'] = null;
            }

            $question = QuizQuestion::create($validatedData);

            return $response
                ->setMessage('تم إضافة السؤال بنجاح')
                ->setData(['redirect' => route('admin.academy.courses.quizzes.questions.index', [$courseId, $quizId])]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $response
                ->setError()
                ->setMessage('يرجى تصحيح الأخطاء في النموذج')
                ->setData(['errors' => $e->errors()]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء إضافة السؤال: ' . $e->getMessage());
        }
    }

    public function show(int $courseId, int $quizId, int $questionId)
    {
        $course = AcademyCourse::findOrFail($courseId);
        $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);
        $question = QuizQuestion::where('quiz_id', $quizId)->findOrFail($questionId);

        $this->pageTitle('تفاصيل السؤال');

        return view('plugins/perfect-pharma::admin.academy.questions.show', compact('course', 'quiz', 'question'));
    }

    public function edit(int $courseId, int $quizId, int $questionId)
    {
        $course = AcademyCourse::findOrFail($courseId);
        $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);
        $question = QuizQuestion::where('quiz_id', $quizId)->findOrFail($questionId);

        $this->pageTitle('تعديل السؤال');

        $questionTypes = QuizQuestion::getQuestionTypes();

        return view('plugins/perfect-pharma::admin.academy.questions.edit', compact('course', 'quiz', 'question', 'questionTypes'));
    }

    public function update(int $courseId, int $quizId, int $questionId, Request $request, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);
            $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);
            $question = QuizQuestion::where('quiz_id', $quizId)->findOrFail($questionId);

            $validatedData = $request->validate([
                'question' => 'required|string',
                'type' => 'required|in:multiple_choice,true_false,text',
                'options' => 'nullable|array',
                'options.*' => 'nullable|string',
                'correct_answer' => 'required|string',
                'explanation' => 'nullable|string',
                'points' => 'required|integer|min:1|max:100',
                'order' => 'nullable|integer|min:0',
            ]);

            // معالجة الخيارات للأسئلة متعددة الخيارات
            if ($validatedData['type'] === 'multiple_choice') {
                $validatedData['options'] = array_filter($validatedData['options'] ?? []);
                
                if (count($validatedData['options']) < 2) {
                    return $response
                        ->setError()
                        ->setMessage('يجب إضافة خيارين على الأقل للسؤال متعدد الخيارات');
                }
            } else {
                $validatedData['options'] = null;
            }

            $question->update($validatedData);

            return $response
                ->setMessage('تم تحديث السؤال بنجاح')
                ->setData(['redirect' => route('admin.academy.courses.quizzes.questions.index', [$courseId, $quizId])]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $response
                ->setError()
                ->setMessage('يرجى تصحيح الأخطاء في النموذج')
                ->setData(['errors' => $e->errors()]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تحديث السؤال: ' . $e->getMessage());
        }
    }

    public function destroy(int $courseId, int $quizId, int $questionId, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);
            $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);
            $question = QuizQuestion::where('quiz_id', $quizId)->findOrFail($questionId);

            $question->delete();

            return $response
                ->setMessage('تم حذف السؤال بنجاح')
                ->setData(['redirect' => route('admin.academy.courses.quizzes.questions.index', [$courseId, $quizId])]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حذف السؤال: ' . $e->getMessage());
        }
    }

    public function reorder(int $courseId, int $quizId, Request $request, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);
            $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);

            $request->validate([
                'questions' => 'required|array',
                'questions.*.id' => 'required|exists:quiz_questions,id',
                'questions.*.order' => 'required|integer|min:0',
            ]);

            foreach ($request->questions as $questionData) {
                QuizQuestion::where('id', $questionData['id'])
                    ->where('quiz_id', $quizId)
                    ->update(['order' => $questionData['order']]);
            }

            return $response->setMessage('تم إعادة ترتيب الأسئلة بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء إعادة ترتيب الأسئلة: ' . $e->getMessage());
        }
    }

    public function bulkImport(int $courseId, int $quizId, Request $request, BaseHttpResponse $response)
    {
        try {
            $course = AcademyCourse::findOrFail($courseId);
            $quiz = CourseQuiz::where('course_id', $courseId)->findOrFail($quizId);

            $validatedData = $request->validate([
                'questions_text' => 'required|string',
            ]);

            $questionsText = $validatedData['questions_text'];
            $questions = $this->parseQuestionsFromText($questionsText, $quizId);

            $importedCount = 0;
            foreach ($questions as $questionData) {
                QuizQuestion::create($questionData);
                $importedCount++;
            }

            return $response
                ->setMessage("تم استيراد {$importedCount} سؤال بنجاح")
                ->setData(['redirect' => route('admin.academy.courses.quizzes.questions.index', [$courseId, $quizId])]);

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء استيراد الأسئلة: ' . $e->getMessage());
        }
    }

    private function parseQuestionsFromText(string $text, int $quizId): array
    {
        $questions = [];
        $lines = explode("\n", $text);
        $currentQuestion = null;
        $order = QuizQuestion::where('quiz_id', $quizId)->max('order') ?? 0;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // بداية سؤال جديد
            if (preg_match('/^(\d+)\.\s*(.+)/', $line, $matches)) {
                if ($currentQuestion) {
                    $questions[] = $currentQuestion;
                }
                
                $order++;
                $currentQuestion = [
                    'quiz_id' => $quizId,
                    'question' => $matches[2],
                    'type' => 'multiple_choice',
                    'options' => [],
                    'correct_answer' => '',
                    'points' => 1,
                    'order' => $order,
                ];
            }
            // خيار إجابة
            elseif (preg_match('/^([أ-ي]|[a-d])\)\s*(.+)/', $line, $matches)) {
                if ($currentQuestion) {
                    $currentQuestion['options'][] = $matches[2];
                }
            }
            // الإجابة الصحيحة
            elseif (preg_match('/^الإجابة:\s*(.+)/', $line, $matches)) {
                if ($currentQuestion) {
                    $currentQuestion['correct_answer'] = $matches[1];
                }
            }
        }

        if ($currentQuestion) {
            $questions[] = $currentQuestion;
        }

        return $questions;
    }
}

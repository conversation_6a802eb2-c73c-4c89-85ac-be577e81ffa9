<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Setting\Supports\SettingStore;
use Illuminate\Http\Request;

class SettingController extends BaseController
{
    public function index()
    {
        $this->pageTitle('إعدادات Perfect Pharma');

        return view('plugins/perfect-pharma::admin.settings.index');
    }

    public function store(Request $request, BaseHttpResponse $response, SettingStore $settingStore)
    {
        $request->validate([
            // إعدادات عامة
            'pharma_system_name' => 'required|string|max:255',
            'pharma_system_description' => 'nullable|string',
            'pharma_contact_email' => 'required|email',
            'pharma_contact_phone' => 'required|string',
            'pharma_address' => 'nullable|string',
            
            // إعدادات المحفظة
            'wallet_min_charge_amount' => 'required|numeric|min:1',
            'wallet_max_charge_amount' => 'required|numeric|min:1',
            'wallet_transfer_fee_percentage' => 'required|numeric|min:0|max:100',
            'wallet_currency' => 'required|string|max:3',
            
            // إعدادات الوصفات
            'prescription_default_expiry_days' => 'required|integer|min:1',
            'prescription_max_refills' => 'required|integer|min:1',
            'prescription_require_doctor_verification' => 'boolean',
            
            // إعدادات التحاليل
            'lab_test_auto_assign' => 'boolean',
            'lab_test_require_payment' => 'boolean',
            'lab_test_critical_alert' => 'boolean',
            
            // إعدادات الصيدليات
            'pharmacy_verification_required' => 'boolean',
            'pharmacy_delivery_default_fee' => 'required|numeric|min:0',
            'pharmacy_delivery_default_radius' => 'required|integer|min:1',
            
            // إعدادات الأكاديمية
            'academy_course_approval_required' => 'boolean',
            'academy_certificate_validity_months' => 'required|integer|min:1',
            'academy_min_passing_score' => 'required|integer|min:1|max:100',
            
            // إعدادات التبرعات
            'donation_approval_required' => 'boolean',
            'donation_min_amount' => 'required|numeric|min:1',
            'donation_receipt_auto_generate' => 'boolean',
            
            // إعدادات الإشعارات
            'notifications_email_enabled' => 'boolean',
            'notifications_sms_enabled' => 'boolean',
            'notifications_push_enabled' => 'boolean',
            
            // إعدادات الأمان
            'security_two_factor_required' => 'boolean',
            'security_session_timeout_minutes' => 'required|integer|min:5',
            'security_max_login_attempts' => 'required|integer|min:1',
        ]);

        try {
            $settings = $request->except(['_token', '_method']);
            
            foreach ($settings as $key => $value) {
                $settingStore->set($key, $value);
            }

            $settingStore->save();

            return $response
                ->setMessage('تم حفظ الإعدادات بنجاح')
                ->setNextUrl(route('admin.pharma.settings'));

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage());
        }
    }
}

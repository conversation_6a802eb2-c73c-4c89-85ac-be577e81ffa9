<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\PerfectPharma\Models\RadiologyRequest;
use Illuminate\Http\Request;

class RadiologyRequestController extends BaseController
{
    /**
     * عرض قائمة طلبات الأشعة
     */
    public function index(Request $request)
    {
        $query = RadiologyRequest::with([
            'patient',
            'doctor.user',
            'radiologyCenter',
            'scanType'
        ]);
        
        // البحث
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('request_code', 'like', "%{$search}%")
                  ->orWhereHas('patient', function($patientQuery) use ($search) {
                      $patientQuery->where('name', 'like', "%{$search}%")
                                  ->orWhere('phone', 'like', "%{$search}%");
                  })
                  ->orWhereHas('doctor.user', function($doctorQuery) use ($search) {
                      $doctorQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }
        
        // فلترة بالحالة
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // فلترة بالأولوية
        if ($request->filled('urgency')) {
            $query->where('urgency', $request->get('urgency'));
        }
        
        // فلترة بالتاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }
        
        $requests = $query->orderBy('urgency', 'desc')
                         ->orderBy('created_at', 'desc')
                         ->paginate(20);
        
        // إحصائيات
        $stats = [
            'total' => RadiologyRequest::count(),
            'pending' => RadiologyRequest::where('status', 'pending')->count(),
            'in_progress' => RadiologyRequest::where('status', 'in_progress')->count(),
            'completed' => RadiologyRequest::where('status', 'completed')->count(),
            'emergency' => RadiologyRequest::where('urgency', 'emergency')->count(),
        ];
        
        return view('plugins/perfect-pharma::admin.radiology.requests.index', compact(
            'requests',
            'stats'
        ));
    }
    
    /**
     * عرض تفاصيل طلب أشعة
     */
    public function show($id)
    {
        $request = RadiologyRequest::with([
            'patient',
            'doctor.user',
            'radiologyCenter',
            'scanType',
            'result',
            'appointments'
        ])->findOrFail($id);
        
        return view('plugins/perfect-pharma::admin.radiology.requests.show', compact('request'));
    }
    
    /**
     * الموافقة على طلب أشعة
     */
    public function approve($id)
    {
        $request = RadiologyRequest::findOrFail($id);
        
        $request->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'تم الموافقة على طلب الأشعة'
        ]);
    }
    
    /**
     * إلغاء طلب أشعة
     */
    public function cancel(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);
        
        $radiologyRequest = RadiologyRequest::findOrFail($id);
        
        $radiologyRequest->update([
            'status' => 'cancelled',
            'cancellation_reason' => $request->get('reason'),
            'cancelled_at' => now(),
            'cancelled_by' => auth()->id(),
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء طلب الأشعة'
        ]);
    }
    
    /**
     * تصدير طلبات الأشعة
     */
    public function export(Request $request)
    {
        // سيتم تطوير هذه الوظيفة لاحقاً
        return response()->json([
            'message' => 'سيتم تطوير ميزة التصدير قريباً'
        ]);
    }
    
    /**
     * إحصائيات طلبات الأشعة
     */
    public function statistics(Request $request)
    {
        $stats = [
            'daily' => RadiologyRequest::whereDate('created_at', today())->count(),
            'weekly' => RadiologyRequest::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'monthly' => RadiologyRequest::whereMonth('created_at', now()->month)->count(),
            'by_status' => RadiologyRequest::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),
            'by_urgency' => RadiologyRequest::selectRaw('urgency, count(*) as count')
                ->groupBy('urgency')
                ->pluck('count', 'urgency')
                ->toArray(),
        ];
        
        return view('plugins/perfect-pharma::admin.radiology.requests.statistics', compact('stats'));
    }
}

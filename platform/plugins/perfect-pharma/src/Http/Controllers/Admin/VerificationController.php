<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\PerfectPharma\Models\CustomerUserType;
use Botble\PerfectPharma\Models\UserType;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\PerfectPharma\Mail\VerificationApprovedMail;
use Botble\PerfectPharma\Mail\VerificationRejectedMail;

class VerificationController extends BaseController
{
    /**
     * عرض قائمة طلبات التحقق
     */
    public function index(Request $request)
    {
        $query = CustomerUserType::with(['customer', 'userType'])
                                ->whereHas('userType', function($q) {
                                    $q->where('name', '!=', UserType::TYPE_PATIENT);
                                });

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            if ($request->status === 'pending') {
                $query->where('is_verified', false);
            } elseif ($request->status === 'verified') {
                $query->where('is_verified', true);
            }
        }

        // فلترة حسب نوع المستخدم
        if ($request->filled('user_type')) {
            $query->whereHas('userType', function($q) use ($request) {
                $q->where('name', $request->user_type);
            });
        }

        // البحث بالاسم أو البريد الإلكتروني
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('customer', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $verificationRequests = $query->orderBy('created_at', 'desc')
                                    ->paginate(20);

        $userTypes = UserType::where('name', '!=', UserType::TYPE_PATIENT)
                           ->where('is_active', true)
                           ->get();

        $stats = $this->getVerificationStats();

        return view('plugins/perfect-pharma::admin.verification.index', compact(
            'verificationRequests', 
            'userTypes', 
            'stats'
        ));
    }

    /**
     * عرض تفاصيل طلب التحقق
     */
    public function show($id)
    {
        $verificationRequest = CustomerUserType::with(['customer', 'userType'])
                                              ->findOrFail($id);

        $documents = $verificationRequest->verification_documents ?? [];
        
        return view('plugins/perfect-pharma::admin.verification.show', compact(
            'verificationRequest',
            'documents'
        ));
    }

    /**
     * الموافقة على طلب التحقق
     */
    public function approve(Request $request, $id)
    {
        $verificationRequest = CustomerUserType::findOrFail($id);
        
        $verificationRequest->update([
            'is_verified' => true,
            'verified_at' => now(),
            'verified_by' => auth()->id(),
            'verification_notes' => $request->input('notes', 'تم الموافقة على الطلب')
        ]);

        // إرسال إشعار بالموافقة
        $this->sendApprovalNotification($verificationRequest);

        return response()->json([
            'error' => false,
            'message' => 'تم الموافقة على طلب التحقق بنجاح'
        ]);
    }

    /**
     * رفض طلب التحقق
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ], [
            'reason.required' => 'سبب الرفض مطلوب',
            'reason.max' => 'سبب الرفض لا يجب أن يتجاوز 500 حرف'
        ]);

        $verificationRequest = CustomerUserType::findOrFail($id);
        
        $verificationRequest->update([
            'is_verified' => false,
            'verified_at' => null,
            'verified_by' => auth()->id(),
            'verification_notes' => 'مرفوض: ' . $request->reason,
            'verification_documents' => [] // حذف المستندات المرفوضة
        ]);

        // إرسال إشعار بالرفض
        $this->sendRejectionNotification($verificationRequest, $request->reason);

        return response()->json([
            'error' => false,
            'message' => 'تم رفض طلب التحقق'
        ]);
    }

    /**
     * عرض المستند
     */
    public function viewDocument($id, $document)
    {
        $verificationRequest = CustomerUserType::findOrFail($id);
        $documents = $verificationRequest->verification_documents ?? [];
        
        if (!isset($documents[$document])) {
            abort(404, 'المستند غير موجود');
        }

        $filePath = $documents[$document];
        
        if (!Storage::disk('local')->exists($filePath)) {
            abort(404, 'الملف غير موجود');
        }

        $file = Storage::disk('local')->get($filePath);
        $mimeType = Storage::disk('local')->mimeType($filePath);
        
        return response($file, 200)
               ->header('Content-Type', $mimeType)
               ->header('Content-Disposition', 'inline; filename="' . basename($filePath) . '"');
    }

    /**
     * تحميل المستند
     */
    public function downloadDocument($id, $document)
    {
        $verificationRequest = CustomerUserType::findOrFail($id);
        $documents = $verificationRequest->verification_documents ?? [];
        
        if (!isset($documents[$document])) {
            abort(404, 'المستند غير موجود');
        }

        $filePath = $documents[$document];
        
        if (!Storage::disk('local')->exists($filePath)) {
            abort(404, 'الملف غير موجود');
        }

        return Storage::disk('local')->download($filePath, $document . '_' . $verificationRequest->customer->name);
    }

    /**
     * الحصول على إحصائيات التحقق
     */
    protected function getVerificationStats()
    {
        $total = CustomerUserType::whereHas('userType', function($q) {
            $q->where('name', '!=', UserType::TYPE_PATIENT);
        })->count();

        $pending = CustomerUserType::whereHas('userType', function($q) {
            $q->where('name', '!=', UserType::TYPE_PATIENT);
        })->where('is_verified', false)->count();

        $verified = CustomerUserType::whereHas('userType', function($q) {
            $q->where('name', '!=', UserType::TYPE_PATIENT);
        })->where('is_verified', true)->count();

        $today = CustomerUserType::whereHas('userType', function($q) {
            $q->where('name', '!=', UserType::TYPE_PATIENT);
        })->whereDate('created_at', today())->count();

        return compact('total', 'pending', 'verified', 'today');
    }

    /**
     * إرسال إشعار الموافقة
     */
    protected function sendApprovalNotification(CustomerUserType $verificationRequest)
    {
        try {
            // إرسال بريد إلكتروني للعميل
            Mail::to($verificationRequest->customer->email)
                ->send(new VerificationApprovedMail($verificationRequest));

            logger()->info('تم إرسال إشعار الموافقة بنجاح', [
                'customer_id' => $verificationRequest->customer_id,
                'customer_email' => $verificationRequest->customer->email,
                'user_type' => $verificationRequest->userType->name
            ]);
        } catch (\Exception $e) {
            logger()->error('خطأ في إرسال إشعار الموافقة: ' . $e->getMessage(), [
                'customer_id' => $verificationRequest->customer_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * إرسال إشعار الرفض
     */
    protected function sendRejectionNotification(CustomerUserType $verificationRequest, string $reason)
    {
        try {
            // إرسال بريد إلكتروني للعميل
            Mail::to($verificationRequest->customer->email)
                ->send(new VerificationRejectedMail($verificationRequest, $reason));

            logger()->info('تم إرسال إشعار الرفض بنجاح', [
                'customer_id' => $verificationRequest->customer_id,
                'customer_email' => $verificationRequest->customer->email,
                'user_type' => $verificationRequest->userType->name,
                'reason' => $reason
            ]);
        } catch (\Exception $e) {
            logger()->error('خطأ في إرسال إشعار الرفض: ' . $e->getMessage(), [
                'customer_id' => $verificationRequest->customer_id,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * تصدير تقرير التحقق
     */
    public function exportReport(Request $request)
    {
        $query = CustomerUserType::with(['customer', 'userType'])
                                ->whereHas('userType', function($q) {
                                    $q->where('name', '!=', UserType::TYPE_PATIENT);
                                });

        // تطبيق نفس الفلاتر
        if ($request->filled('status')) {
            if ($request->status === 'pending') {
                $query->where('is_verified', false);
            } elseif ($request->status === 'verified') {
                $query->where('is_verified', true);
            }
        }

        if ($request->filled('user_type')) {
            $query->whereHas('userType', function($q) use ($request) {
                $q->where('name', $request->user_type);
            });
        }

        $data = $query->get();

        $csvData = [];
        $csvData[] = ['الاسم', 'البريد الإلكتروني', 'نوع المستخدم', 'الحالة', 'تاريخ التسجيل', 'تاريخ التحقق'];

        foreach ($data as $item) {
            $csvData[] = [
                $item->customer->name,
                $item->customer->email,
                $item->userType->display_name,
                $item->is_verified ? 'محقق' : 'قيد المراجعة',
                $item->created_at->format('Y-m-d H:i'),
                $item->verified_at ? $item->verified_at->format('Y-m-d H:i') : '-'
            ];
        }

        $filename = 'verification_report_' . date('Y-m-d_H-i-s') . '.csv';
        
        $handle = fopen('php://temp', 'w+');
        foreach ($csvData as $row) {
            fputcsv($handle, $row);
        }
        rewind($handle);
        $csv = stream_get_contents($handle);
        fclose($handle);

        return response($csv, 200)
               ->header('Content-Type', 'text/csv; charset=UTF-8')
               ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }
}

<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON><PERSON>\PerfectPharma\Models\Prescription;
use Bo<PERSON><PERSON>\PerfectPharma\Models\PrescriptionMedication;
use Bo<PERSON>ble\PerfectPharma\Models\MedicationDispensing;
use Botble\PerfectPharma\Models\Pharmacy;
use Botble\PerfectPharma\Services\PrescriptionService;
use Botble\PerfectPharma\Services\TieredPricingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PharmacyPrescriptionController extends BaseController
{
    protected PrescriptionService $prescriptionService;
    protected TieredPricingService $pricingService;

    public function __construct(
        PrescriptionService $prescriptionService,
        TieredPricingService $pricingService
    ) {
        $this->prescriptionService = $prescriptionService;
        $this->pricingService = $pricingService;
    }

    public function index(Request $request)
    {
        $this->pageTitle('البحث عن الوصفات الطبية');

        // الحصول على الصيدلية الحالية
        $pharmacy = $this->getCurrentPharmacy();
        
        if (!$pharmacy) {
            return redirect()->route('admin.dashboard')
                           ->with('error', 'يجب أن تكون مسجلاً كصيدلية للوصول لهذه الصفحة');
        }

        return view('plugins/perfect-pharma::admin.pharmacy.prescriptions.index', compact('pharmacy'));
    }

    public function searchByPhone(Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'phone' => 'required|string|min:9|max:15'
        ]);

        $result = $this->prescriptionService->findPrescriptionByPhone($request->phone);

        if (!$result['success']) {
            return $response->setError()->setMessage($result['message']);
        }

        // إضافة معلومات التسعير للأدوية
        $prescriptions = $result['data']['prescriptions'];
        $pharmacy = $this->getCurrentPharmacy();
        
        foreach ($prescriptions as $prescription) {
            foreach ($prescription->medications as $medication) {
                if ($medication->product && $pharmacy) {
                    $customer = $pharmacy->user; // العميل المرتبط بالصيدلية
                    $priceData = $this->pricingService->getPriceForCustomer(
                        $medication->product, 
                        $customer, 
                        1
                    );
                    
                    $medication->pharmacy_price = $priceData['final_price'];
                    $medication->original_price = $priceData['original_price'];
                    $medication->discount_percentage = $priceData['discount_percentage'];
                }
            }
        }

        return $response->setData($result['data'])->setMessage($result['message']);
    }

    public function showPrescription(Request $request, int $prescriptionId)
    {
        $prescription = Prescription::with([
            'patient.customer',
            'doctor.user',
            'medications.product'
        ])->findOrFail($prescriptionId);

        $this->pageTitle('وصفة طبية رقم: ' . $prescription->prescription_number);

        // التحقق من صحة الوصفة
        $validation = $this->prescriptionService->validatePrescription($prescription);
        
        // الحصول على الصيدلية الحالية
        $pharmacy = $this->getCurrentPharmacy();
        
        // حساب الأسعار للصيدلية
        if ($pharmacy) {
            $customer = $pharmacy->user;
            foreach ($prescription->medications as $medication) {
                if ($medication->product) {
                    $priceData = $this->pricingService->getPriceForCustomer(
                        $medication->product, 
                        $customer, 
                        1
                    );
                    
                    $medication->pharmacy_price = $priceData['final_price'];
                    $medication->original_price = $priceData['original_price'];
                    $medication->discount_percentage = $priceData['discount_percentage'];
                }
            }
        }

        // الحصول على تاريخ الصرف السابق
        $dispensingHistory = MedicationDispensing::whereHas('prescriptionMedication', function ($query) use ($prescriptionId) {
                                                    $query->where('prescription_id', $prescriptionId);
                                                })
                                                ->with(['pharmacy', 'pharmacist'])
                                                ->orderBy('dispensing_date', 'desc')
                                                ->get();

        return view('plugins/perfect-pharma::admin.pharmacy.prescriptions.show', compact(
            'prescription', 'validation', 'pharmacy', 'dispensingHistory'
        ));
    }

    public function dispenseMedication(Request $request, int $medicationId, BaseHttpResponse $response)
    {
        $request->validate([
            'quantity_dispensed' => 'required|integer|min:1',
            'payment_method' => 'required|in:cash,wallet,insurance,credit_card',
            'pharmacist_notes' => 'nullable|string|max:500',
            'discount_amount' => 'nullable|numeric|min:0',
        ]);

        $pharmacy = $this->getCurrentPharmacy();
        if (!$pharmacy) {
            return $response->setError()->setMessage('غير مصرح لك بصرف الأدوية');
        }

        $prescriptionMedication = PrescriptionMedication::findOrFail($medicationId);
        
        // التحقق من الكمية المتاحة
        $remainingQuantity = $prescriptionMedication->quantity - $prescriptionMedication->dispensed_quantity;
        if ($request->quantity_dispensed > $remainingQuantity) {
            return $response->setError()->setMessage('الكمية المطلوبة أكبر من المتاح للصرف');
        }

        $dispensingData = [
            'prescription_medication_id' => $medicationId,
            'pharmacy_id' => $pharmacy->id,
            'pharmacist_id' => Auth::id(),
            'quantity_dispensed' => $request->quantity_dispensed,
            'payment_method' => $request->payment_method,
            'pharmacist_notes' => $request->pharmacist_notes,
            'discount_amount' => $request->discount_amount ?? 0,
        ];

        $result = $this->prescriptionService->dispenseMedication($dispensingData);

        if (!$result['success']) {
            return $response->setError()->setMessage($result['message']);
        }

        return $response
            ->setMessage($result['message'])
            ->setData($result['data'])
            ->setNextUrl(route('admin.pharmacy.prescriptions.show', $prescriptionMedication->prescription_id));
    }

    public function dispensingHistory(Request $request)
    {
        $this->pageTitle('تاريخ صرف الأدوية');

        $pharmacy = $this->getCurrentPharmacy();
        if (!$pharmacy) {
            return redirect()->route('admin.dashboard')
                           ->with('error', 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        $dispensings = MedicationDispensing::where('pharmacy_id', $pharmacy->id)
                                          ->with([
                                              'prescriptionMedication.prescription.patient.customer',
                                              'prescriptionMedication.product',
                                              'pharmacist'
                                          ])
                                          ->orderBy('dispensing_date', 'desc')
                                          ->paginate(20);

        return view('plugins/perfect-pharma::admin.pharmacy.prescriptions.history', compact(
            'dispensings', 'pharmacy'
        ));
    }

    public function printDispensing(Request $request, int $dispensingId)
    {
        $dispensing = MedicationDispensing::with([
            'prescriptionMedication.prescription.patient.customer',
            'prescriptionMedication.prescription.doctor.user',
            'prescriptionMedication.product',
            'pharmacy',
            'pharmacist'
        ])->findOrFail($dispensingId);

        // التحقق من أن الصرف تابع للصيدلية الحالية
        $pharmacy = $this->getCurrentPharmacy();
        if (!$pharmacy || $dispensing->pharmacy_id !== $pharmacy->id) {
            return redirect()->route('admin.pharmacy.prescriptions.history')
                           ->with('error', 'غير مصرح لك بطباعة هذا الإيصال');
        }

        return view('plugins/perfect-pharma::admin.pharmacy.prescriptions.print', compact('dispensing'));
    }

    public function statistics(Request $request)
    {
        $this->pageTitle('إحصائيات الصرف');

        $pharmacy = $this->getCurrentPharmacy();
        if (!$pharmacy) {
            return redirect()->route('admin.dashboard')
                           ->with('error', 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        $startDate = $request->get('start_date', now()->startOfMonth()->toDateString());
        $endDate = $request->get('end_date', now()->toDateString());

        $stats = [
            'total_dispensings' => MedicationDispensing::where('pharmacy_id', $pharmacy->id)
                                                      ->whereBetween('dispensing_date', [$startDate, $endDate])
                                                      ->count(),
            
            'total_revenue' => MedicationDispensing::where('pharmacy_id', $pharmacy->id)
                                                  ->whereBetween('dispensing_date', [$startDate, $endDate])
                                                  ->sum('final_price'),
            
            'total_patients' => MedicationDispensing::where('pharmacy_id', $pharmacy->id)
                                                   ->whereBetween('dispensing_date', [$startDate, $endDate])
                                                   ->join('prescription_medications', 'medication_dispensings.prescription_medication_id', '=', 'prescription_medications.id')
                                                   ->join('prescriptions', 'prescription_medications.prescription_id', '=', 'prescriptions.id')
                                                   ->distinct('prescriptions.patient_id')
                                                   ->count(),
            
            'top_medications' => MedicationDispensing::where('pharmacy_id', $pharmacy->id)
                                                    ->whereBetween('dispensing_date', [$startDate, $endDate])
                                                    ->join('prescription_medications', 'medication_dispensings.prescription_medication_id', '=', 'prescription_medications.id')
                                                    ->groupBy('prescription_medications.medication_name')
                                                    ->selectRaw('prescription_medications.medication_name, sum(medication_dispensings.quantity_dispensed) as total_quantity')
                                                    ->orderBy('total_quantity', 'desc')
                                                    ->limit(10)
                                                    ->get(),
        ];

        return view('plugins/perfect-pharma::admin.pharmacy.prescriptions.statistics', compact(
            'stats', 'pharmacy', 'startDate', 'endDate'
        ));
    }

    private function getCurrentPharmacy(): ?Pharmacy
    {
        $user = Auth::user();
        return Pharmacy::where('user_id', $user->id)->first();
    }

    public function validatePrescription(Request $request, int $prescriptionId, BaseHttpResponse $response)
    {
        $prescription = Prescription::findOrFail($prescriptionId);
        $validation = $this->prescriptionService->validatePrescription($prescription);

        return $response->setData($validation);
    }
}

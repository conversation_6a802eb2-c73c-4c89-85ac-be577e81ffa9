<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Ecommerce\Models\Product;
use Bo<PERSON>ble\Ecommerce\Models\ProductCategory;
use Bo<PERSON>ble\PerfectPharma\Models\UserType;
use Botble\PerfectPharma\Models\ProductTieredPricing;
use Botble\PerfectPharma\Models\CategoryTieredPricing;
use Botble\PerfectPharma\Services\TieredPricingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TieredPricingController extends BaseController
{
    protected TieredPricingService $pricingService;

    public function __construct(TieredPricingService $pricingService)
    {
        $this->pricingService = $pricingService;
    }

    public function index(Request $request)
    {
        $this->pageTitle('إدارة التسعير المتدرج');

        $userTypes = UserType::active()->get();
        $products = Product::with(['categories'])->paginate(20);
        $statistics = $this->pricingService->getPricingStatistics();

        return view('plugins/perfect-pharma::admin.tiered-pricing.index', compact(
            'userTypes', 'products', 'statistics'
        ));
    }

    public function productPricing(Request $request, int $productId)
    {
        $product = Product::findOrFail($productId);
        $this->pageTitle('تسعير المنتج: ' . $product->name);

        $userTypes = UserType::active()->get();
        $existingPricing = ProductTieredPricing::where('product_id', $productId)
                                              ->with('userType')
                                              ->get()
                                              ->keyBy('user_type_id');

        return view('plugins/perfect-pharma::admin.tiered-pricing.product', compact(
            'product', 'userTypes', 'existingPricing'
        ));
    }

    public function storeProductPricing(Request $request, int $productId, BaseHttpResponse $response)
    {
        $product = Product::findOrFail($productId);

        $request->validate([
            'pricing' => 'required|array',
            'pricing.*.user_type_id' => 'required|exists:user_types,id',
            'pricing.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'pricing.*.fixed_price' => 'nullable|numeric|min:0',
            'pricing.*.min_quantity' => 'required|integer|min:1',
            'pricing.*.max_quantity' => 'nullable|integer|min:1',
            'pricing.*.start_date' => 'nullable|date',
            'pricing.*.end_date' => 'nullable|date|after_or_equal:pricing.*.start_date',
        ]);

        DB::beginTransaction();
        try {
            foreach ($request->pricing as $pricingData) {
                // التحقق من صحة البيانات
                $errors = $this->pricingService->validatePricingData($pricingData);
                if (!empty($errors)) {
                    return $response->setError()->setMessage(implode(', ', $errors));
                }

                $this->pricingService->createProductPricing(
                    $product,
                    UserType::find($pricingData['user_type_id']),
                    $pricingData
                );
            }

            // تحديث المنتج لتفعيل التسعير المتدرج
            $product->update(['has_tiered_pricing' => true]);

            DB::commit();

            return $response
                ->setMessage('تم حفظ التسعير المتدرج بنجاح')
                ->setNextUrl(route('admin.tiered-pricing.product', $productId));

        } catch (\Exception $e) {
            DB::rollBack();
            return $response->setError()->setMessage('حدث خطأ أثناء حفظ التسعير: ' . $e->getMessage());
        }
    }

    public function bulkPricing(Request $request)
    {
        $this->pageTitle('التسعير المجمع');

        $userTypes = UserType::active()->get();
        $categories = ProductCategory::with(['products'])->get();

        return view('plugins/perfect-pharma::admin.tiered-pricing.bulk', compact(
            'userTypes', 'categories'
        ));
    }

    public function storeBulkPricing(Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'user_type_id' => 'required|exists:user_types,id',
            'product_ids' => 'required|array|min:1',
            'product_ids.*' => 'exists:ec_products,id',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'fixed_price' => 'nullable|numeric|min:0',
            'min_quantity' => 'required|integer|min:1',
            'max_quantity' => 'nullable|integer|min:1',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $userType = UserType::find($request->user_type_id);
        $pricingData = $request->only([
            'discount_percentage', 'fixed_price', 'min_quantity', 
            'max_quantity', 'start_date', 'end_date'
        ]);
        $pricingData['is_active'] = true;

        // التحقق من صحة البيانات
        $errors = $this->pricingService->validatePricingData($pricingData);
        if (!empty($errors)) {
            return $response->setError()->setMessage(implode(', ', $errors));
        }

        DB::beginTransaction();
        try {
            $created = $this->pricingService->createBulkPricing(
                $request->product_ids,
                $userType,
                $pricingData
            );

            // تحديث المنتجات لتفعيل التسعير المتدرج
            Product::whereIn('id', $request->product_ids)
                   ->update(['has_tiered_pricing' => true]);

            DB::commit();

            return $response
                ->setMessage('تم إنشاء التسعير المجمع لـ ' . count($created) . ' منتج بنجاح')
                ->setNextUrl(route('admin.tiered-pricing.bulk'));

        } catch (\Exception $e) {
            DB::rollBack();
            return $response->setError()->setMessage('حدث خطأ أثناء إنشاء التسعير: ' . $e->getMessage());
        }
    }

    public function categoryPricing(Request $request)
    {
        $this->pageTitle('تسعير الفئات');

        $userTypes = UserType::active()->get();
        $categories = ProductCategory::withCount('products')->get();
        $existingPricing = CategoryTieredPricing::with(['category', 'userType'])->get();

        return view('plugins/perfect-pharma::admin.tiered-pricing.category', compact(
            'userTypes', 'categories', 'existingPricing'
        ));
    }

    public function storeCategoryPricing(Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'category_id' => 'required|exists:ec_product_categories,id',
            'user_type_id' => 'required|exists:user_types,id',
            'discount_percentage' => 'required|numeric|min:0|max:100',
            'min_quantity' => 'required|integer|min:1',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            CategoryTieredPricing::updateOrCreate(
                [
                    'category_id' => $request->category_id,
                    'user_type_id' => $request->user_type_id,
                ],
                $request->only([
                    'discount_percentage', 'min_quantity', 'start_date', 'end_date'
                ]) + ['is_active' => true]
            );

            return $response
                ->setMessage('تم حفظ تسعير الفئة بنجاح')
                ->setNextUrl(route('admin.tiered-pricing.category'));

        } catch (\Exception $e) {
            return $response->setError()->setMessage('حدث خطأ أثناء حفظ التسعير: ' . $e->getMessage());
        }
    }

    public function deletePricing(Request $request, int $pricingId, BaseHttpResponse $response)
    {
        try {
            $pricing = ProductTieredPricing::findOrFail($pricingId);
            $productId = $pricing->product_id;
            
            $pricing->delete();
            
            // مسح الكاش
            $this->pricingService->clearProductPriceCache($productId);

            return $response->setMessage('تم حذف التسعير بنجاح');

        } catch (\Exception $e) {
            return $response->setError()->setMessage('حدث خطأ أثناء حذف التسعير: ' . $e->getMessage());
        }
    }

    public function statistics(Request $request)
    {
        $this->pageTitle('إحصائيات التسعير المتدرج');

        $statistics = $this->pricingService->getPricingStatistics();
        
        // إحصائيات إضافية
        $userTypeStats = UserType::withCount(['productPricing', 'categoryPricing'])->get();
        $topProducts = ProductTieredPricing::with(['product', 'userType'])
                                          ->orderBy('discount_percentage', 'desc')
                                          ->limit(10)
                                          ->get();

        return view('plugins/perfect-pharma::admin.tiered-pricing.statistics', compact(
            'statistics', 'userTypeStats', 'topProducts'
        ));
    }

    public function previewPrice(Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'product_id' => 'required|exists:ec_products,id',
            'user_type_id' => 'required|exists:user_types,id',
            'quantity' => 'required|integer|min:1',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'fixed_price' => 'nullable|numeric|min:0',
        ]);

        $product = Product::find($request->product_id);
        $originalPrice = $product->price;
        $quantity = $request->quantity;

        if ($request->fixed_price) {
            $finalPrice = $request->fixed_price;
        } elseif ($request->discount_percentage) {
            $discountAmount = $originalPrice * ($request->discount_percentage / 100);
            $finalPrice = max(0, $originalPrice - $discountAmount);
        } else {
            $finalPrice = $originalPrice;
        }

        $totalOriginal = $originalPrice * $quantity;
        $totalFinal = $finalPrice * $quantity;
        $totalSavings = $totalOriginal - $totalFinal;

        return $response->setData([
            'original_price' => $originalPrice,
            'final_price' => $finalPrice,
            'total_original' => $totalOriginal,
            'total_final' => $totalFinal,
            'total_savings' => $totalSavings,
            'savings_percentage' => $totalOriginal > 0 ? round(($totalSavings / $totalOriginal) * 100, 2) : 0,
        ]);
    }
}

<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\PerfectPharma\Models\RadiologyCenter;
use Botble\PerfectPharma\Models\CustomerUserType;
use Botble\PerfectPharma\Models\UserType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RadiologyController extends BaseController
{
    /**
     * عرض قائمة مراكز الأشعة
     */
    public function index(Request $request)
    {
        $query = RadiologyCenter::with(['user']);
        
        // البحث
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('center_code', 'like', "%{$search}%")
                  ->orWhere('license_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%")
                               ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }
        
        // فلترة بالحالة
        if ($request->filled('status')) {
            if ($request->get('status') === 'verified') {
                $query->where('is_verified', true);
            } elseif ($request->get('status') === 'unverified') {
                $query->where('is_verified', false);
            } elseif ($request->get('status') === 'active') {
                $query->where('is_active', true);
            } elseif ($request->get('status') === 'inactive') {
                $query->where('is_active', false);
            }
        }
        
        $radiologyCenters = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // إحصائيات
        $stats = [
            'total' => RadiologyCenter::count(),
            'verified' => RadiologyCenter::where('is_verified', true)->count(),
            'unverified' => RadiologyCenter::where('is_verified', false)->count(),
            'active' => RadiologyCenter::where('is_active', true)->count(),
            'inactive' => RadiologyCenter::where('is_active', false)->count(),
        ];
        
        return view('plugins/perfect-pharma::admin.radiology.index', compact(
            'radiologyCenters',
            'stats'
        ));
    }
    
    /**
     * عرض تفاصيل مركز أشعة
     */
    public function show($id)
    {
        $radiologyCenter = RadiologyCenter::with([
            'user',
            'radiologyRequests.patient',
            'radiologyRequests.doctor.user',
            'radiologyRequests.scanType'
        ])->findOrFail($id);
        
        // إحصائيات المركز
        $centerStats = [
            'total_requests' => $radiologyCenter->radiologyRequests()->count(),
            'pending_requests' => $radiologyCenter->radiologyRequests()->where('status', 'pending')->count(),
            'completed_requests' => $radiologyCenter->radiologyRequests()->where('status', 'completed')->count(),
            'monthly_revenue' => $radiologyCenter->radiologyRequests()
                ->where('status', 'completed')
                ->whereMonth('completed_at', now()->month)
                ->sum('total_cost'),
        ];
        
        return view('plugins/perfect-pharma::admin.radiology.show', compact(
            'radiologyCenter',
            'centerStats'
        ));
    }
    
    /**
     * عرض طلبات التحقق
     */
    public function verification(Request $request)
    {
        $radiologyUserType = UserType::where('name', 'radiology')->first();
        
        if (!$radiologyUserType) {
            return redirect()->back()->with('error', 'نوع مستخدم مراكز الأشعة غير موجود');
        }
        
        $query = CustomerUserType::where('user_type_id', $radiologyUserType->id)
            ->with(['customer', 'userType']);
        
        // فلترة بحالة التحقق
        if ($request->filled('verification_status')) {
            $status = $request->get('verification_status');
            if ($status === 'pending') {
                $query->where('is_verified', false);
            } elseif ($status === 'verified') {
                $query->where('is_verified', true);
            }
        }
        
        $verificationRequests = $query->orderBy('created_at', 'desc')->paginate(20);
        
        return view('plugins/perfect-pharma::admin.radiology.verification', compact(
            'verificationRequests'
        ));
    }
    
    /**
     * الموافقة على طلب تحقق
     */
    public function verify($id)
    {
        try {
            DB::beginTransaction();
            
            $customerUserType = CustomerUserType::findOrFail($id);
            $customerUserType->update([
                'is_verified' => true,
                'verified_at' => now(),
                'verified_by' => auth()->id(),
            ]);
            
            // إنشاء ملف مركز الأشعة إذا لم يكن موجوداً
            $radiologyCenter = RadiologyCenter::where('user_id', $customerUserType->customer_id)->first();
            
            if (!$radiologyCenter) {
                RadiologyCenter::create([
                    'user_id' => $customerUserType->customer_id,
                    'name' => $customerUserType->customer->name,
                    'license_number' => 'RAD-' . str_pad($customerUserType->customer_id, 6, '0', STR_PAD_LEFT),
                    'address' => $customerUserType->customer->address ?? 'غير محدد',
                    'phone' => $customerUserType->customer->phone,
                    'email' => $customerUserType->customer->email,
                    'is_verified' => true,
                    'is_active' => true,
                ]);
            }
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'تم التحقق من مركز الأشعة بنجاح'
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء التحقق: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * رفض طلب تحقق
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);
        
        $customerUserType = CustomerUserType::findOrFail($id);
        $customerUserType->update([
            'is_verified' => false,
            'rejection_reason' => $request->get('reason'),
            'rejected_at' => now(),
            'rejected_by' => auth()->id(),
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'تم رفض طلب التحقق'
        ]);
    }
    
    /**
     * تفعيل/إلغاء تفعيل مركز أشعة
     */
    public function toggleStatus($id)
    {
        $radiologyCenter = RadiologyCenter::findOrFail($id);
        $radiologyCenter->update([
            'is_active' => !$radiologyCenter->is_active
        ]);
        
        $status = $radiologyCenter->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';
        
        return response()->json([
            'success' => true,
            'message' => $status . ' مركز الأشعة بنجاح'
        ]);
    }
    
    /**
     * إنشاء مركز أشعة جديد
     */
    public function create()
    {
        return view('plugins/perfect-pharma::admin.radiology.create');
    }
    
    /**
     * حفظ مركز أشعة جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'license_number' => 'required|string|max:255|unique:radiology_centers,license_number',
            'phone' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'address' => 'required|string',
            'description' => 'nullable|string',
            'opening_time' => 'nullable|date_format:H:i',
            'closing_time' => 'nullable|date_format:H:i',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'home_service' => 'boolean',
            'home_service_fee' => 'nullable|numeric|min:0',
            'home_service_radius_km' => 'nullable|numeric|min:0',
            'emergency_service' => 'boolean',
            'accepts_insurance' => 'boolean',
            'is_verified' => 'boolean',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // إنشاء مركز الأشعة
            $radiologyCenter = RadiologyCenter::create([
                'name' => $request->get('name'),
                'license_number' => $request->get('license_number'),
                'phone' => $request->get('phone'),
                'email' => $request->get('email'),
                'address' => $request->get('address'),
                'description' => $request->get('description'),
                'opening_time' => $request->get('opening_time'),
                'closing_time' => $request->get('closing_time'),
                'latitude' => $request->get('latitude'),
                'longitude' => $request->get('longitude'),
                'home_service' => $request->boolean('home_service'),
                'home_service_fee' => $request->get('home_service_fee'),
                'home_service_radius_km' => $request->get('home_service_radius_km'),
                'emergency_service' => $request->boolean('emergency_service'),
                'accepts_insurance' => $request->boolean('accepts_insurance'),
                'is_verified' => $request->boolean('is_verified'),
                'is_active' => $request->boolean('is_active'),
                'user_id' => 1, // سيتم ربطه بمستخدم حقيقي لاحقاً
            ]);

            DB::commit();

            return redirect()->route('radiology.index')
                ->with('success', 'تم إنشاء مركز الأشعة بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء مركز الأشعة: ' . $e->getMessage());
        }
    }
    
    /**
     * تعديل مركز أشعة
     */
    public function edit($id)
    {
        $radiologyCenter = RadiologyCenter::findOrFail($id);
        return view('plugins/perfect-pharma::admin.radiology.edit', compact('radiologyCenter'));
    }
    
    /**
     * تحديث مركز أشعة
     */
    public function update(Request $request, $id)
    {
        $radiologyCenter = RadiologyCenter::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'license_number' => 'required|string|max:255|unique:radiology_centers,license_number,' . $id,
            'phone' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'address' => 'required|string',
            'description' => 'nullable|string',
            'opening_time' => 'nullable|date_format:H:i',
            'closing_time' => 'nullable|date_format:H:i',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'home_service' => 'boolean',
            'home_service_fee' => 'nullable|numeric|min:0',
            'home_service_radius_km' => 'nullable|numeric|min:0',
            'emergency_service' => 'boolean',
            'accepts_insurance' => 'boolean',
            'is_verified' => 'boolean',
            'is_active' => 'boolean',
        ]);

        try {
            $radiologyCenter->update([
                'name' => $request->get('name'),
                'license_number' => $request->get('license_number'),
                'phone' => $request->get('phone'),
                'email' => $request->get('email'),
                'address' => $request->get('address'),
                'description' => $request->get('description'),
                'opening_time' => $request->get('opening_time'),
                'closing_time' => $request->get('closing_time'),
                'latitude' => $request->get('latitude'),
                'longitude' => $request->get('longitude'),
                'home_service' => $request->boolean('home_service'),
                'home_service_fee' => $request->get('home_service_fee'),
                'home_service_radius_km' => $request->get('home_service_radius_km'),
                'emergency_service' => $request->boolean('emergency_service'),
                'accepts_insurance' => $request->boolean('accepts_insurance'),
                'is_verified' => $request->boolean('is_verified'),
                'is_active' => $request->boolean('is_active'),
            ]);

            return redirect()->route('radiology.index')
                ->with('success', 'تم تحديث مركز الأشعة بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث مركز الأشعة: ' . $e->getMessage());
        }
    }
    
    /**
     * حذف مركز أشعة
     */
    public function destroy($id)
    {
        $radiologyCenter = RadiologyCenter::findOrFail($id);
        $radiologyCenter->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'تم حذف مركز الأشعة بنجاح'
        ]);
    }
}

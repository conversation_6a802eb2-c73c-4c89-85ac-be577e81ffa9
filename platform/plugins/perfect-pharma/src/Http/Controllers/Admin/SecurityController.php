<?php

namespace Botble\PerfectPharma\Http\Controllers\Admin;

use Botble\Base\Http\Controllers\BaseController;
use Botble\PerfectPharma\Services\SecurityService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class SecurityController extends BaseController
{
    protected SecurityService $securityService;

    public function __construct(SecurityService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * عرض لوحة تحكم الأمان
     */
    public function index()
    {
        $stats = $this->getSecurityStats();
        
        return view('plugins/perfect-pharma::admin.security.index', compact('stats'));
    }

    /**
     * عرض سجلات الأمان
     */
    public function logs(Request $request)
    {
        $query = \DB::table('security_logs')->orderBy('created_at', 'desc');

        // تطبيق الفلاتر
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('ip_address')) {
            $query->where('ip_address', 'like', "%{$request->ip_address}%");
        }

        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->paginate(50);

        return view('plugins/perfect-pharma::admin.security.logs', compact('logs'));
    }

    /**
     * تقرير الأمان
     */
    public function report(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $report = $this->securityService->generateSecurityReport(
            Carbon::parse($dateFrom),
            Carbon::parse($dateTo)
        );

        if ($request->ajax()) {
            return response()->json($report);
        }

        return view('plugins/perfect-pharma::admin.security.report', compact('report'));
    }

    /**
     * فحص قوة كلمة المرور
     */
    public function checkPasswordStrength(Request $request): JsonResponse
    {
        $request->validate([
            'password' => 'required|string',
        ]);

        $result = $this->securityService->validatePasswordStrength($request->password);

        return response()->json($result);
    }

    /**
     * حظر IP
     */
    public function blockIp(Request $request): JsonResponse
    {
        $request->validate([
            'ip_address' => 'required|ip',
            'duration' => 'required|integer|min:1|max:1440', // دقائق
            'reason' => 'nullable|string',
        ]);

        try {
            $this->securityService->blockIpTemporarily(
                $request->ip_address,
                $request->duration
            );

            // تسجيل الحظر اليدوي
            \DB::table('security_logs')->insert([
                'type' => 'manual_ip_block',
                'ip_address' => $request->ip_address,
                'data' => json_encode([
                    'duration_minutes' => $request->duration,
                    'reason' => $request->reason,
                    'blocked_by' => auth()->id(),
                ]),
                'severity' => 'warning',
                'created_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حظر عنوان IP بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل حظر عنوان IP: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * إلغاء حظر IP
     */
    public function unblockIp(Request $request): JsonResponse
    {
        $request->validate([
            'ip_address' => 'required|ip',
        ]);

        try {
            \Cache::forget("blocked_ip:{$request->ip_address}");

            // تسجيل إلغاء الحظر
            \DB::table('security_logs')->insert([
                'type' => 'manual_ip_unblock',
                'ip_address' => $request->ip_address,
                'data' => json_encode([
                    'unblocked_by' => auth()->id(),
                ]),
                'severity' => 'info',
                'created_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء حظر عنوان IP بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل إلغاء حظر عنوان IP: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * تنظيف السجلات القديمة
     */
    public function cleanOldLogs(Request $request): JsonResponse
    {
        $request->validate([
            'days' => 'required|integer|min:1|max:365',
        ]);

        try {
            $deleted = $this->securityService->cleanOldSecurityLogs($request->days);

            return response()->json([
                'success' => true,
                'message' => "تم حذف {$deleted} سجل قديم",
                'deleted_count' => $deleted,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل تنظيف السجلات: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * تصدير تقرير الأمان
     */
    public function exportReport(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'format' => 'required|in:pdf,excel,csv',
        ]);

        try {
            $report = $this->securityService->generateSecurityReport(
                Carbon::parse($request->date_from),
                Carbon::parse($request->date_to)
            );

            // هنا يمكن إضافة منطق التصدير الفعلي
            $filename = "security_report_{$request->format}_" . now()->format('Y_m_d_H_i_s');

            return response()->json([
                'success' => true,
                'download_url' => '#', // سيتم تنفيذه لاحقاً
                'filename' => $filename,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل تصدير التقرير: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * الحصول على إحصائيات الأمان
     */
    private function getSecurityStats(): array
    {
        $today = now()->startOfDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();

        return [
            'today' => [
                'total_events' => \DB::table('security_logs')->whereDate('created_at', $today)->count(),
                'login_attempts' => \DB::table('security_logs')->where('type', 'login_attempt')->whereDate('created_at', $today)->count(),
                'suspicious_activities' => \DB::table('security_logs')->where('type', 'suspicious_activity')->whereDate('created_at', $today)->count(),
                'blocked_ips' => \DB::table('security_logs')->where('type', 'manual_ip_block')->whereDate('created_at', $today)->count(),
            ],
            'this_week' => [
                'total_events' => \DB::table('security_logs')->where('created_at', '>=', $thisWeek)->count(),
                'login_attempts' => \DB::table('security_logs')->where('type', 'login_attempt')->where('created_at', '>=', $thisWeek)->count(),
                'suspicious_activities' => \DB::table('security_logs')->where('type', 'suspicious_activity')->where('created_at', '>=', $thisWeek)->count(),
                'blocked_ips' => \DB::table('security_logs')->where('type', 'manual_ip_block')->where('created_at', '>=', $thisWeek)->count(),
            ],
            'this_month' => [
                'total_events' => \DB::table('security_logs')->where('created_at', '>=', $thisMonth)->count(),
                'login_attempts' => \DB::table('security_logs')->where('type', 'login_attempt')->where('created_at', '>=', $thisMonth)->count(),
                'suspicious_activities' => \DB::table('security_logs')->where('type', 'suspicious_activity')->where('created_at', '>=', $thisMonth)->count(),
                'blocked_ips' => \DB::table('security_logs')->where('type', 'manual_ip_block')->where('created_at', '>=', $thisMonth)->count(),
            ],
        ];
    }

    /**
     * فحص حالة الأمان
     */
    public function securityStatus(): JsonResponse
    {
        $status = [
            'ssl_enabled' => request()->isSecure(),
            'session_secure' => config('session.secure'),
            'csrf_protection' => config('app.csrf_protection', true),
            'rate_limiting' => true, // يمكن فحص إعدادات Rate Limiting
            'ip_blocking' => true,
            'security_headers' => $this->checkSecurityHeaders(),
        ];

        $overallScore = collect($status)->filter()->count() / count($status) * 100;

        return response()->json([
            'status' => $status,
            'overall_score' => round($overallScore, 2),
            'recommendations' => $this->getSecurityRecommendations($status),
        ]);
    }

    /**
     * فحص Security Headers
     */
    private function checkSecurityHeaders(): array
    {
        // هذا مثال - يمكن تطويره أكثر
        return [
            'x_frame_options' => true,
            'x_content_type_options' => true,
            'x_xss_protection' => true,
            'strict_transport_security' => request()->isSecure(),
        ];
    }

    /**
     * الحصول على توصيات الأمان
     */
    private function getSecurityRecommendations(array $status): array
    {
        $recommendations = [];

        if (!$status['ssl_enabled']) {
            $recommendations[] = 'تفعيل شهادة SSL للموقع';
        }

        if (!$status['session_secure']) {
            $recommendations[] = 'تفعيل الجلسات الآمنة';
        }

        return $recommendations;
    }
}

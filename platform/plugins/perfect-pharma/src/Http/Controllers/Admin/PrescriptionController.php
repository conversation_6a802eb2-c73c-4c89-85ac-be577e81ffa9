<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use App\Models\Prescription;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Illuminate\Http\Request;

class PrescriptionController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle('إدارة الوصفات الطبية');

        $prescriptions = Prescription::with(['patient.customer', 'doctor.user'])
            ->latest()
            ->paginate(20);

        return view('plugins/perfect-pharma::admin.prescriptions.index', compact('prescriptions'));
    }

    public function show(int $id)
    {
        $prescription = Prescription::with(['patient.customer', 'doctor.user', 'medications.product'])
            ->findOrFail($id);

        $this->pageTitle('تفاصيل الوصفة: ' . $prescription->prescription_number);

        return view('plugins/perfect-pharma::admin.prescriptions.show', compact('prescription'));
    }

    public function active(Request $request)
    {
        $this->pageTitle('الوصفات النشطة');

        $prescriptions = Prescription::with(['patient.customer', 'doctor.user'])
            ->whereIn('status', ['active', 'partially_filled'])
            ->latest()
            ->paginate(20);

        return view('plugins/perfect-pharma::admin.prescriptions.active', compact('prescriptions'));
    }

    public function expired(Request $request)
    {
        $this->pageTitle('الوصفات المنتهية الصلاحية');

        $prescriptions = Prescription::with(['patient.customer', 'doctor.user'])
            ->where('status', 'expired')
            ->latest()
            ->paginate(20);

        return view('plugins/perfect-pharma::admin.prescriptions.expired', compact('prescriptions'));
    }

    public function cancel(int $id, BaseHttpResponse $response)
    {
        try {
            $prescription = Prescription::findOrFail($id);
            
            if (in_array($prescription->status, ['completed', 'cancelled'])) {
                return $response
                    ->setError()
                    ->setMessage('لا يمكن إلغاء هذه الوصفة');
            }

            $prescription->status = 'cancelled';
            $prescription->cancelled_at = now();
            $prescription->save();

            return $response
                ->setMessage('تم إلغاء الوصفة بنجاح')
                ->setNextUrl(route('admin.prescriptions.show', $prescription->id));

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء إلغاء الوصفة: ' . $e->getMessage());
        }
    }
}

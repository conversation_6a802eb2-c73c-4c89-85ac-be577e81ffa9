<?php

namespace Bo<PERSON>ble\PerfectPharma\Http\Controllers\Admin;

use Bo<PERSON>ble\PerfectPharma\Models\StudentProgress;
use <PERSON><PERSON>ble\PerfectPharma\Models\CourseEnrollment;
use Botble\PerfectPharma\Models\AcademyCourse;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\ACL\Models\User;
use Illuminate\Http\Request;

class ProgressController extends BaseController
{
    public function overview()
    {
        $this->pageTitle('نظرة عامة على التقدم');

        $stats = [
            'total_students' => CourseEnrollment::distinct('user_id')->count(),
            'total_courses' => AcademyCourse::count(),
            'completed_courses' => CourseEnrollment::where('status', 'completed')->count(),
            'in_progress_courses' => CourseEnrollment::where('status', 'in_progress')->count(),
            'average_progress' => CourseEnrollment::avg('progress_percentage') ?? 0,
        ];

        $recentEnrollments = CourseEnrollment::with(['user', 'course'])
            ->latest()
            ->limit(10)
            ->get();

        return view('plugins/perfect-pharma::admin.academy.progress.overview', compact('stats', 'recentEnrollments'));
    }
    public function courseProgress(int $courseId, Request $request)
    {
        $course = AcademyCourse::with(['contents', 'quizzes'])->findOrFail($courseId);
        
        $this->pageTitle('تقدم الطلاب في الدورة: ' . $course->title);

        $enrollments = CourseEnrollment::with(['user'])
            ->forCourse($courseId)
            ->when($request->status, function($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->search, function($query, $search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            })
            ->latest()
            ->paginate(20);

        $stats = [
            'total_enrolled' => CourseEnrollment::forCourse($courseId)->count(),
            'completed' => CourseEnrollment::forCourse($courseId)->completed()->count(),
            'in_progress' => CourseEnrollment::forCourse($courseId)->inProgress()->count(),
            'average_progress' => CourseEnrollment::forCourse($courseId)->avg('progress_percentage') ?? 0,
        ];

        return view('plugins/perfect-pharma::admin.academy.progress.course', compact('course', 'enrollments', 'stats'));
    }

    public function studentProgress(int $courseId, int $userId, Request $request)
    {
        $course = AcademyCourse::with(['contents', 'quizzes'])->findOrFail($courseId);
        $user = User::findOrFail($userId);
        $enrollment = CourseEnrollment::forUser($userId)->forCourse($courseId)->firstOrFail();
        
        $this->pageTitle('تقدم الطالب: ' . $user->name . ' في الدورة: ' . $course->title);

        $detailedProgress = $enrollment->getDetailedProgress();
        $quizResults = $enrollment->getQuizResults();

        return view('plugins/perfect-pharma::admin.academy.progress.student', compact('course', 'user', 'enrollment', 'detailedProgress', 'quizResults'));
    }

    public function allStudentsProgress(Request $request)
    {
        $this->pageTitle('تقدم جميع الطلاب');

        $enrollments = CourseEnrollment::with(['user', 'course'])
            ->when($request->course_id, function($query, $courseId) {
                $query->forCourse($courseId);
            })
            ->when($request->status, function($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->search, function($query, $search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            })
            ->latest()
            ->paginate(20);

        $courses = AcademyCourse::select('id', 'title')->get();
        $statuses = CourseEnrollment::getStatuses();

        $stats = [
            'total_enrollments' => CourseEnrollment::count(),
            'completed_enrollments' => CourseEnrollment::completed()->count(),
            'in_progress_enrollments' => CourseEnrollment::inProgress()->count(),
            'average_progress' => CourseEnrollment::avg('progress_percentage') ?? 0,
        ];

        return view('plugins/perfect-pharma::admin.academy.progress.all', compact('enrollments', 'courses', 'statuses', 'stats'));
    }

    public function updateProgress(int $courseId, int $userId, Request $request, BaseHttpResponse $response)
    {
        try {
            $validatedData = $request->validate([
                'content_id' => 'required|exists:course_contents,id',
                'progress_percentage' => 'required|numeric|min:0|max:100',
                'time_spent' => 'nullable|integer|min:0',
            ]);

            $progress = StudentProgress::getOrCreateProgress(
                $userId,
                $courseId,
                $validatedData['content_id']
            );

            $progress->updateProgress(
                $validatedData['progress_percentage'],
                $validatedData['time_spent'] ?? 0
            );

            // تحديث تقدم التسجيل العام
            $enrollment = CourseEnrollment::forUser($userId)->forCourse($courseId)->first();
            if ($enrollment) {
                $enrollment->updateProgress();
            }

            return $response->setMessage('تم تحديث التقدم بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تحديث التقدم: ' . $e->getMessage());
        }
    }

    public function resetProgress(int $courseId, int $userId, BaseHttpResponse $response)
    {
        try {
            // إعادة تعيين تقدم المحتوى
            StudentProgress::forUser($userId)->forCourse($courseId)->delete();

            // إعادة تعيين التسجيل
            $enrollment = CourseEnrollment::forUser($userId)->forCourse($courseId)->first();
            if ($enrollment) {
                $enrollment->update([
                    'status' => CourseEnrollment::STATUS_ENROLLED,
                    'progress_percentage' => 0,
                    'total_time_spent' => 0,
                    'completion_date' => null,
                    'certificate_issued' => false,
                ]);
            }

            return $response->setMessage('تم إعادة تعيين التقدم بنجاح');

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء إعادة تعيين التقدم: ' . $e->getMessage());
        }
    }

    public function bulkUpdateStatus(Request $request, BaseHttpResponse $response)
    {
        try {
            $validatedData = $request->validate([
                'enrollment_ids' => 'required|array',
                'enrollment_ids.*' => 'exists:course_enrollments,id',
                'status' => 'required|in:enrolled,in_progress,completed,dropped,suspended',
            ]);

            CourseEnrollment::whereIn('id', $validatedData['enrollment_ids'])
                ->update(['status' => $validatedData['status']]);

            $count = count($validatedData['enrollment_ids']);
            return $response->setMessage("تم تحديث حالة {$count} تسجيل بنجاح");

        } catch (\Exception $e) {
            return $response
                ->setError()
                ->setMessage('حدث خطأ أثناء تحديث الحالات: ' . $e->getMessage());
        }
    }

    public function exportProgress(int $courseId, Request $request)
    {
        $course = AcademyCourse::findOrFail($courseId);
        $enrollments = CourseEnrollment::with(['user', 'progress'])
            ->forCourse($courseId)
            ->get();

        $data = [];
        foreach ($enrollments as $enrollment) {
            $data[] = [
                'اسم الطالب' => $enrollment->user->name,
                'البريد الإلكتروني' => $enrollment->user->email,
                'تاريخ التسجيل' => $enrollment->enrollment_date->format('Y-m-d'),
                'الحالة' => $enrollment->status_name,
                'نسبة التقدم' => $enrollment->progress_percentage . '%',
                'الوقت المستغرق' => $enrollment->formatted_total_time_spent,
                'تاريخ الإكمال' => $enrollment->completion_date?->format('Y-m-d') ?? 'لم يكتمل',
                'شهادة صادرة' => $enrollment->certificate_issued ? 'نعم' : 'لا',
            ];
        }

        $filename = 'تقدم_الطلاب_' . $course->title . '_' . now()->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // إضافة العناوين
            if (!empty($data)) {
                fputcsv($file, array_keys($data[0]));
                
                // إضافة البيانات
                foreach ($data as $row) {
                    fputcsv($file, $row);
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function progressAnalytics(int $courseId)
    {
        $course = AcademyCourse::with(['contents', 'quizzes'])->findOrFail($courseId);
        
        $this->pageTitle('تحليلات التقدم للدورة: ' . $course->title);

        // إحصائيات عامة
        $totalEnrollments = CourseEnrollment::forCourse($courseId)->count();
        $completedEnrollments = CourseEnrollment::forCourse($courseId)->completed()->count();
        $averageCompletionTime = CourseEnrollment::forCourse($courseId)
            ->completed()
            ->whereNotNull('completion_date')
            ->get()
            ->avg(function($enrollment) {
                return $enrollment->enrollment_date->diffInDays($enrollment->completion_date);
            });

        // تحليل التقدم حسب المحتوى
        $contentAnalytics = [];
        foreach ($course->contents as $content) {
            $contentProgress = StudentProgress::forContent($content->id)->get();
            $contentAnalytics[] = [
                'content' => $content,
                'total_views' => $contentProgress->count(),
                'completed' => $contentProgress->where('status', StudentProgress::STATUS_COMPLETED)->count(),
                'average_time' => $contentProgress->avg('time_spent_minutes') ?? 0,
                'completion_rate' => $contentProgress->count() > 0 ? 
                    ($contentProgress->where('status', StudentProgress::STATUS_COMPLETED)->count() / $contentProgress->count()) * 100 : 0,
            ];
        }

        // تحليل الاختبارات
        $quizAnalytics = [];
        foreach ($course->quizzes as $quiz) {
            $attempts = $quiz->attempts;
            $quizAnalytics[] = [
                'quiz' => $quiz,
                'total_attempts' => $attempts->count(),
                'unique_students' => $attempts->pluck('user_id')->unique()->count(),
                'pass_rate' => $quiz->pass_rate,
                'average_score' => $attempts->avg('score') ?? 0,
            ];
        }

        return view('plugins/perfect-pharma::admin.academy.progress.analytics', compact(
            'course', 'totalEnrollments', 'completedEnrollments', 'averageCompletionTime',
            'contentAnalytics', 'quizAnalytics'
        ));
    }

    public function analytics()
    {
        $this->pageTitle('تحليلات التقدم العامة');

        // إحصائيات عامة
        $generalStats = [
            'total_enrollments' => CourseEnrollment::count(),
            'active_enrollments' => CourseEnrollment::whereNull('completion_date')->count(),
            'completed_enrollments' => CourseEnrollment::whereNotNull('completion_date')->count(),
            'total_courses' => AcademyCourse::count(),
            'active_courses' => AcademyCourse::where('status', 'published')->count(),
            'total_students' => CourseEnrollment::distinct('user_id')->count(),
        ];

        // معدل الإكمال العام
        $generalStats['completion_rate'] = $generalStats['total_enrollments'] > 0 ?
            ($generalStats['completed_enrollments'] / $generalStats['total_enrollments']) * 100 : 0;

        // أكثر الدورات شعبية
        $popularCourses = CourseEnrollment::select('course_id')
            ->selectRaw('COUNT(*) as enrollments_count')
            ->with('course:id,title')
            ->groupBy('course_id')
            ->orderBy('enrollments_count', 'desc')
            ->limit(10)
            ->get();

        // أكثر الدورات إكمالاً
        $topCompletedCourses = CourseEnrollment::select('course_id')
            ->selectRaw('COUNT(*) as total_enrollments')
            ->selectRaw('SUM(CASE WHEN completion_date IS NOT NULL THEN 1 ELSE 0 END) as completed_enrollments')
            ->selectRaw('(SUM(CASE WHEN completion_date IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*)) * 100 as completion_rate')
            ->with('course:id,title')
            ->groupBy('course_id')
            ->having('total_enrollments', '>=', 5)
            ->orderBy('completion_rate', 'desc')
            ->limit(10)
            ->get();

        // التسجيلات الشهرية
        $monthlyEnrollments = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $count = CourseEnrollment::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();

            $monthlyEnrollments[] = [
                'month' => $date->format('Y-m'),
                'month_name' => $date->format('M Y'),
                'count' => $count,
            ];
        }

        // الإكمالات الشهرية
        $monthlyCompletions = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $count = CourseEnrollment::whereNotNull('completion_date')
                ->whereYear('completion_date', $date->year)
                ->whereMonth('completion_date', $date->month)
                ->count();

            $monthlyCompletions[] = [
                'month' => $date->format('Y-m'),
                'month_name' => $date->format('M Y'),
                'count' => $count,
            ];
        }

        // توزيع التقدم
        $progressDistribution = [
            '0-25%' => CourseEnrollment::where('progress_percentage', '>=', 0)->where('progress_percentage', '<', 25)->count(),
            '25-50%' => CourseEnrollment::where('progress_percentage', '>=', 25)->where('progress_percentage', '<', 50)->count(),
            '50-75%' => CourseEnrollment::where('progress_percentage', '>=', 50)->where('progress_percentage', '<', 75)->count(),
            '75-100%' => CourseEnrollment::where('progress_percentage', '>=', 75)->where('progress_percentage', '<=', 100)->count(),
        ];

        return view('plugins/perfect-pharma::admin.academy.progress.general-analytics', compact(
            'generalStats', 'popularCourses', 'topCompletedCourses', 'monthlyEnrollments',
            'monthlyCompletions', 'progressDistribution'
        ));
    }
}

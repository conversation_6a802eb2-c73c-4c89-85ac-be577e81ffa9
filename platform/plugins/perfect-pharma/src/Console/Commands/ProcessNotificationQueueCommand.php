<?php

namespace Bo<PERSON>ble\PerfectPharma\Console\Commands;

use Illuminate\Console\Command;
use Botble\PerfectPharma\Services\NotificationService;
use Botble\PerfectPharma\Services\NotificationScheduleService;

class ProcessNotificationQueueCommand extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'notifications:process-queue 
                            {--queue=default : اسم قائمة الانتظار}
                            {--max-jobs=10 : الحد الأقصى للمهام}
                            {--timeout=60 : مهلة التنفيذ بالثواني}';

    /**
     * وصف الأمر
     */
    protected $description = 'معالجة قائمة انتظار الإشعارات';

    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * تنفيذ الأمر
     */
    public function handle(): int
    {
        $queueName = $this->option('queue');
        $maxJobs = (int) $this->option('max-jobs');
        $timeout = (int) $this->option('timeout');

        $this->info("بدء معالجة قائمة الانتظار: {$queueName}");
        $this->info("الحد الأقصى للمهام: {$maxJobs}");

        $startTime = time();
        $processedJobs = 0;

        try {
            while (time() - $startTime < $timeout && $processedJobs < $maxJobs) {
                $results = $this->notificationService->processQueue($queueName, 1);
                
                if (empty($results)) {
                    $this->info('لا توجد مهام في قائمة الانتظار');
                    break;
                }

                foreach ($results as $result) {
                    $processedJobs++;
                    
                    if ($result['success']) {
                        $this->info("✓ تم إرسال الإشعار {$result['notification_id']} بنجاح");
                    } else {
                        $this->error("✗ فشل إرسال الإشعار {$result['notification_id']}: {$result['error']}");
                    }
                }

                // توقف قصير لتجنب استهلاك الموارد
                usleep(100000); // 0.1 ثانية
            }

            $this->info("تم معالجة {$processedJobs} مهمة في " . (time() - $startTime) . " ثانية");
            
            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("خطأ في معالجة قائمة الانتظار: " . $e->getMessage());
            return self::FAILURE;
        }
    }
}

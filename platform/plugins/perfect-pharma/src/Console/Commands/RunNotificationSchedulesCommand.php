<?php

namespace Bo<PERSON>ble\PerfectPharma\Console\Commands;

use Illuminate\Console\Command;
use Botble\PerfectPharma\Services\NotificationScheduleService;

class RunNotificationSchedulesCommand extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'notifications:run-schedules 
                            {--force : تشغيل جميع الجدولات حتى لو لم تكن مستحقة}
                            {--schedule-id= : تشغيل جدولة محددة}';

    /**
     * وصف الأمر
     */
    protected $description = 'تشغيل جدولات الإشعارات المستحقة';

    protected NotificationScheduleService $scheduleService;

    public function __construct(NotificationScheduleService $scheduleService)
    {
        parent::__construct();
        $this->scheduleService = $scheduleService;
    }

    /**
     * تنفيذ الأمر
     */
    public function handle(): int
    {
        $force = $this->option('force');
        $scheduleId = $this->option('schedule-id');

        try {
            if ($scheduleId) {
                return $this->runSpecificSchedule($scheduleId);
            }

            $this->info('البحث عن الجدولات المستحقة...');
            
            $results = $this->scheduleService->runDueSchedules();

            if (empty($results)) {
                $this->info('لا توجد جدولات مستحقة للتشغيل');
                return self::SUCCESS;
            }

            $this->info("تم العثور على " . count($results) . " جدولة مستحقة");

            $successCount = 0;
            $failureCount = 0;

            foreach ($results as $result) {
                if ($result['success']) {
                    $successCount++;
                    $this->info("✓ تم تشغيل الجدولة '{$result['name']}' بنجاح");
                    
                    if (isset($result['result']['notifications_created'])) {
                        $this->line("  - تم إنشاء {$result['result']['notifications_created']} إشعار");
                    }
                } else {
                    $failureCount++;
                    $this->error("✗ فشل تشغيل الجدولة '{$result['name']}': {$result['error']}");
                }
            }

            $this->info("النتائج: {$successCount} نجح، {$failureCount} فشل");

            return $failureCount > 0 ? self::FAILURE : self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("خطأ في تشغيل الجدولات: " . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * تشغيل جدولة محددة
     */
    private function runSpecificSchedule(int $scheduleId): int
    {
        try {
            $schedule = \Botble\PerfectPharma\Models\NotificationSchedule::findOrFail($scheduleId);
            
            $this->info("تشغيل الجدولة: {$schedule->name}");

            $result = $this->scheduleService->executeSchedule($schedule);

            if (isset($result['skipped']) && $result['skipped']) {
                $this->warn("تم تخطي الجدولة: {$result['reason']}");
                return self::SUCCESS;
            }

            $this->info("✓ تم تشغيل الجدولة بنجاح");
            $this->line("- تم إنشاء {$result['notifications_created']} إشعار");
            $this->line("- عدد المستقبلين: {$result['recipients_count']}");

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("فشل تشغيل الجدولة: " . $e->getMessage());
            return self::FAILURE;
        }
    }
}

<?php

namespace Botble\PerfectPharma\Console\Commands;

use Illuminate\Console\Command;
use Botble\PerfectPharma\Services\BackupService;
use Illuminate\Support\Facades\Storage;

class AutoBackupCommand extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'backup:auto 
                            {--force : تشغيل النسخ الاحتياطي حتى لو لم يكن مجدولاً}';

    /**
     * وصف الأمر
     */
    protected $description = 'تشغيل النسخ الاحتياطي التلقائي حسب الجدولة المحددة';

    protected BackupService $backupService;

    public function __construct(BackupService $backupService)
    {
        parent::__construct();
        $this->backupService = $backupService;
    }

    /**
     * تنفيذ الأمر
     */
    public function handle(): int
    {
        try {
            // الحصول على إعدادات الجدولة
            $schedule = $this->getScheduleConfig();

            if (!$schedule['enabled'] && !$this->option('force')) {
                $this->info('النسخ الاحتياطي التلقائي معطل');
                return self::SUCCESS;
            }

            // التحقق من الوقت المناسب للتشغيل
            if (!$this->option('force') && !$this->shouldRunNow($schedule)) {
                $this->info('ليس الوقت المناسب لتشغيل النسخ الاحتياطي');
                return self::SUCCESS;
            }

            $this->info('بدء النسخ الاحتياطي التلقائي...');

            // إعداد خيارات النسخ الاحتياطي
            $options = [
                'database' => $schedule['database'] ?? true,
                'files' => $schedule['files'] ?? true,
                'compression' => $schedule['compression'] ?? true,
                'encryption' => $schedule['encryption'] ?? false,
            ];

            // إنشاء النسخة الاحتياطية
            $result = $this->backupService->createFullBackup($options);

            $this->info("✓ تم إنشاء النسخة الاحتياطية بنجاح");
            $this->line("- معرف النسخة: {$result['backup_id']}");
            $this->line("- الحجم: " . $this->formatBytes($result['size']));
            $this->line("- المدة: {$result['duration']} ثانية");

            // تنظيف النسخ القديمة
            $retentionDays = $schedule['retention_days'] ?? 30;
            $cleanupResult = $this->backupService->cleanOldBackups($retentionDays);

            if ($cleanupResult['deleted_count'] > 0) {
                $this->info("✓ تم حذف {$cleanupResult['deleted_count']} نسخة احتياطية قديمة");
            }

            // تحديث آخر تشغيل
            $this->updateLastRun();

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("فشل النسخ الاحتياطي التلقائي: " . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * الحصول على إعدادات الجدولة
     */
    private function getScheduleConfig(): array
    {
        if (Storage::exists('backup_schedule.json')) {
            return json_decode(Storage::get('backup_schedule.json'), true);
        }

        return [
            'enabled' => false,
            'frequency' => 'weekly',
            'time' => '02:00',
            'database' => true,
            'files' => true,
            'compression' => true,
            'retention_days' => 30,
        ];
    }

    /**
     * التحقق من ضرورة التشغيل الآن
     */
    private function shouldRunNow(array $schedule): bool
    {
        $now = now();
        $scheduledTime = \Carbon\Carbon::createFromFormat('H:i', $schedule['time']);
        
        // التحقق من الوقت (مع هامش 30 دقيقة)
        $timeDiff = abs($now->diffInMinutes($scheduledTime->setDate($now->year, $now->month, $now->day)));
        if ($timeDiff > 30) {
            return false;
        }

        // التحقق من التكرار
        $lastRun = $this->getLastRun();
        
        switch ($schedule['frequency']) {
            case 'daily':
                return !$lastRun || $lastRun->diffInDays($now) >= 1;
            
            case 'weekly':
                return !$lastRun || $lastRun->diffInWeeks($now) >= 1;
            
            case 'monthly':
                return !$lastRun || $lastRun->diffInMonths($now) >= 1;
            
            default:
                return false;
        }
    }

    /**
     * الحصول على آخر تشغيل
     */
    private function getLastRun(): ?\Carbon\Carbon
    {
        if (Storage::exists('backup_last_run.txt')) {
            $lastRunStr = Storage::get('backup_last_run.txt');
            return \Carbon\Carbon::parse($lastRunStr);
        }

        return null;
    }

    /**
     * تحديث آخر تشغيل
     */
    private function updateLastRun(): void
    {
        Storage::put('backup_last_run.txt', now()->toISOString());
    }

    /**
     * تنسيق حجم الملف
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

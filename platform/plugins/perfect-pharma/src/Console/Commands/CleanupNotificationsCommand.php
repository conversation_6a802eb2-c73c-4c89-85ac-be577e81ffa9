<?php

namespace Botble\PerfectPharma\Console\Commands;

use Illuminate\Console\Command;
use Bo<PERSON><PERSON>\PerfectPharma\Models\Notification;
use Bot<PERSON>\PerfectPharma\Models\NotificationStatistic;
use Botble\PerfectPharma\Models\NotificationDelivery;

class CleanupNotificationsCommand extends Command
{
    /**
     * اسم الأمر
     */
    protected $signature = 'notifications:cleanup 
                            {--notifications-days=90 : عدد الأيام للاحتفاظ بالإشعارات}
                            {--statistics-days=365 : عدد الأيام للاحتفاظ بالإحصائيات}
                            {--deliveries-days=30 : عدد الأيام للاحتفاظ بسجلات التسليم}
                            {--dry-run : عرض ما سيتم حذفه دون تنفيذ الحذف}';

    /**
     * وصف الأمر
     */
    protected $description = 'تنظيف البيانات القديمة لنظام الإشعارات';

    /**
     * تنفيذ الأمر
     */
    public function handle(): int
    {
        $notificationsDays = (int) $this->option('notifications-days');
        $statisticsDays = (int) $this->option('statistics-days');
        $deliveriesDays = (int) $this->option('deliveries-days');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('تشغيل تجريبي - لن يتم حذف أي بيانات فعلياً');
        }

        $this->info('بدء عملية تنظيف البيانات القديمة...');

        try {
            // تنظيف الإشعارات القديمة
            $deletedNotifications = $this->cleanupNotifications($notificationsDays, $dryRun);
            
            // تنظيف الإحصائيات القديمة
            $deletedStatistics = $this->cleanupStatistics($statisticsDays, $dryRun);
            
            // تنظيف سجلات التسليم القديمة
            $deletedDeliveries = $this->cleanupDeliveries($deliveriesDays, $dryRun);

            $this->info('تم الانتهاء من عملية التنظيف:');
            $this->line("- الإشعارات المحذوفة: {$deletedNotifications}");
            $this->line("- الإحصائيات المحذوفة: {$deletedStatistics}");
            $this->line("- سجلات التسليم المحذوفة: {$deletedDeliveries}");

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("خطأ في عملية التنظيف: " . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * تنظيف الإشعارات القديمة
     */
    private function cleanupNotifications(int $days, bool $dryRun): int
    {
        $cutoffDate = now()->subDays($days);
        
        $query = Notification::where('created_at', '<', $cutoffDate)
                            ->whereIn('status', ['sent', 'failed', 'cancelled']);

        $count = $query->count();

        $this->info("الإشعارات المرشحة للحذف (أقدم من {$days} يوم): {$count}");

        if (!$dryRun && $count > 0) {
            if ($this->confirm("هل تريد حذف {$count} إشعار؟")) {
                $deleted = $query->delete();
                $this->info("تم حذف {$deleted} إشعار");
                return $deleted;
            }
        }

        return $dryRun ? $count : 0;
    }

    /**
     * تنظيف الإحصائيات القديمة
     */
    private function cleanupStatistics(int $days, bool $dryRun): int
    {
        $cutoffDate = now()->subDays($days);
        
        $query = NotificationStatistic::where('date', '<', $cutoffDate);
        $count = $query->count();

        $this->info("الإحصائيات المرشحة للحذف (أقدم من {$days} يوم): {$count}");

        if (!$dryRun && $count > 0) {
            if ($this->confirm("هل تريد حذف {$count} سجل إحصائي؟")) {
                $deleted = $query->delete();
                $this->info("تم حذف {$deleted} سجل إحصائي");
                return $deleted;
            }
        }

        return $dryRun ? $count : 0;
    }

    /**
     * تنظيف سجلات التسليم القديمة
     */
    private function cleanupDeliveries(int $days, bool $dryRun): int
    {
        $cutoffDate = now()->subDays($days);
        
        $query = NotificationDelivery::where('created_at', '<', $cutoffDate)
                                   ->whereIn('status', ['sent', 'delivered', 'failed']);

        $count = $query->count();

        $this->info("سجلات التسليم المرشحة للحذف (أقدم من {$days} يوم): {$count}");

        if (!$dryRun && $count > 0) {
            if ($this->confirm("هل تريد حذف {$count} سجل تسليم؟")) {
                $deleted = $query->delete();
                $this->info("تم حذف {$deleted} سجل تسليم");
                return $deleted;
            }
        }

        return $dryRun ? $count : 0;
    }
}

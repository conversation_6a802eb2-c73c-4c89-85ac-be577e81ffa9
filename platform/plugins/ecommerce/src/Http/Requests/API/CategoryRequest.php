<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Requests\API;

use Botble\Support\Http\Requests\Request;

class CategoryRequest extends Request
{
    public function rules(): array
    {
        return [
            'categories' => ['nullable', 'array'],
            'categories.*' => ['nullable', 'exists:ec_product_categories,id'],
            'is_featured' => ['nullable', 'boolean'],
        ];
    }

    public function attributes(): array
    {
        return [
            'categories.*' => trans('plugins/ecommerce::products.form.categories'),
        ];
    }
}

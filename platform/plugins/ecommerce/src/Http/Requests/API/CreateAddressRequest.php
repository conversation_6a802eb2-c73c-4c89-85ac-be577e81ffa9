<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Requests\API;

use Botble\Support\Http\Requests\Request;

class CreateAddressRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:191'],
            'email' => ['nullable', 'email', 'max:60'],
            'phone' => ['required', 'regex:/^([0-9\s\-\+\(\)]*)$/', 'min:9', 'max:15'],
            'country' => ['nullable', 'string', 'max:120'],
            'state' => ['nullable', 'string', 'max:120'],
            'city' => ['nullable', 'string', 'max:120'],
            'address' => ['nullable', 'string', 'max:191'],
            'is_default' => ['nullable', 'boolean'],
            'zip_code' => ['nullable', 'string', 'max:20'],

        ];
    }

}

<?php

namespace App\Services;

use App\Models\PharmaWallet;
use App\Models\WalletTransaction;
use Botble\ACL\Models\User;
use Botble\Payment\Models\Payment;
use Illuminate\Support\Facades\DB;

class WalletService
{
    /**
     * إنشاء محفظة جديدة للمستخدم
     */
    public function createWallet(User $user, string $currency = 'EGP'): PharmaWallet
    {
        // التحقق من عدم وجود محفظة مسبقاً
        $existingWallet = PharmaWallet::where('user_id', $user->id)->first();
        if ($existingWallet) {
            throw new \Exception('المستخدم يملك محفظة بالفعل');
        }

        return PharmaWallet::create([
            'user_id' => $user->id,
            'currency' => $currency,
            'is_active' => true,
            'is_verified' => false, // يحتاج تفعيل
        ]);
    }

    /**
     * الحصول على محفظة المستخدم أو إنشاؤها
     */
    public function getOrCreateWallet(User $user, string $currency = 'EGP'): PharmaWallet
    {
        $wallet = PharmaWallet::where('user_id', $user->id)->first();
        
        if (!$wallet) {
            $wallet = $this->createWallet($user, $currency);
        }

        return $wallet;
    }

    /**
     * شحن المحفظة عبر بوابة دفع
     */
    public function chargeWallet(PharmaWallet $wallet, float $amount, string $paymentMethod, array $paymentData = []): array
    {
        if (!$wallet->is_active) {
            throw new \Exception('المحفظة غير نشطة');
        }

        if ($amount <= 0) {
            throw new \Exception('المبلغ يجب أن يكون أكبر من صفر');
        }

        DB::beginTransaction();

        try {
            // إنشاء معاملة معلقة
            $transaction = $wallet->transactions()->create([
                'transaction_id' => $this->generateTransactionId(),
                'type' => WalletTransaction::TYPE_CREDIT,
                'amount' => $amount,
                'balance_before' => $wallet->balance,
                'balance_after' => $wallet->balance, // لم يتم التحديث بعد
                'description' => 'شحن المحفظة عبر ' . $paymentMethod,
                'status' => WalletTransaction::STATUS_PENDING,
                'metadata' => [
                    'payment_method' => $paymentMethod,
                    'payment_data' => $paymentData,
                ],
            ]);

            // معالجة الدفع حسب الطريقة
            $paymentResult = $this->processPayment($amount, $paymentMethod, $paymentData, $transaction);

            if ($paymentResult['success']) {
                // تحديث المحفظة
                $wallet->balance += $amount;
                $wallet->last_transaction_at = now();
                $wallet->save();

                // تحديث المعاملة
                $transaction->update([
                    'balance_after' => $wallet->balance,
                    'status' => WalletTransaction::STATUS_COMPLETED,
                    'metadata' => array_merge($transaction->metadata, [
                        'payment_id' => $paymentResult['payment_id'] ?? null,
                        'completed_at' => now()->toISOString(),
                    ]),
                ]);

                DB::commit();

                return [
                    'success' => true,
                    'transaction' => $transaction,
                    'new_balance' => $wallet->balance,
                    'payment_result' => $paymentResult,
                ];
            } else {
                // فشل الدفع
                $transaction->update([
                    'status' => WalletTransaction::STATUS_FAILED,
                    'metadata' => array_merge($transaction->metadata, [
                        'error' => $paymentResult['error'] ?? 'فشل في الدفع',
                        'failed_at' => now()->toISOString(),
                    ]),
                ]);

                DB::rollback();

                return [
                    'success' => false,
                    'error' => $paymentResult['error'] ?? 'فشل في عملية الدفع',
                    'transaction' => $transaction,
                ];
            }

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * دفع من المحفظة
     */
    public function payFromWallet(PharmaWallet $wallet, float $amount, string $description, string $referenceType = null, int $referenceId = null): WalletTransaction
    {
        if (!$wallet->is_active) {
            throw new \Exception('المحفظة غير نشطة');
        }

        if ($wallet->available_balance < $amount) {
            throw new \Exception('الرصيد غير كافي. الرصيد المتاح: ' . $wallet->available_balance);
        }

        return $wallet->debit($amount, $description, $referenceType, $referenceId);
    }

    /**
     * تحويل بين المحافظ
     */
    public function transferBetweenWallets(PharmaWallet $fromWallet, PharmaWallet $toWallet, float $amount, string $description = null): array
    {
        if (!$fromWallet->is_active || !$toWallet->is_active) {
            throw new \Exception('إحدى المحافظ غير نشطة');
        }

        if ($fromWallet->id === $toWallet->id) {
            throw new \Exception('لا يمكن التحويل لنفس المحفظة');
        }

        if ($fromWallet->available_balance < $amount) {
            throw new \Exception('الرصيد غير كافي');
        }

        return $fromWallet->transferTo($toWallet, $amount, $description);
    }

    /**
     * حجز مبلغ للدفع لاحقاً
     */
    public function holdAmount(PharmaWallet $wallet, float $amount, string $description, string $referenceType = null, int $referenceId = null): WalletTransaction
    {
        if (!$wallet->is_active) {
            throw new \Exception('المحفظة غير نشطة');
        }

        return $wallet->hold($amount, $description, $referenceType, $referenceId);
    }

    /**
     * تأكيد المبلغ المحجوز
     */
    public function confirmHeldAmount(WalletTransaction $holdTransaction): WalletTransaction
    {
        return $holdTransaction->wallet->confirmHold($holdTransaction);
    }

    /**
     * إلغاء المبلغ المحجوز
     */
    public function cancelHeldAmount(WalletTransaction $holdTransaction): WalletTransaction
    {
        return $holdTransaction->wallet->cancelHold($holdTransaction);
    }

    /**
     * استرداد مبلغ
     */
    public function refund(PharmaWallet $wallet, float $amount, string $description, string $referenceType = null, int $referenceId = null): WalletTransaction
    {
        if (!$wallet->is_active) {
            throw new \Exception('المحفظة غير نشطة');
        }

        return $wallet->refund($amount, $description, $referenceType, $referenceId);
    }

    /**
     * الحصول على تاريخ المعاملات
     */
    public function getTransactionHistory(PharmaWallet $wallet, array $filters = []): \Illuminate\Pagination\LengthAwarePaginator
    {
        $query = $wallet->transactions()->orderBy('created_at', 'desc');

        // تطبيق الفلاتر
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['from_date'])) {
            $query->whereDate('created_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->whereDate('created_at', '<=', $filters['to_date']);
        }

        if (isset($filters['min_amount'])) {
            $query->where('amount', '>=', $filters['min_amount']);
        }

        if (isset($filters['max_amount'])) {
            $query->where('amount', '<=', $filters['max_amount']);
        }

        return $query->paginate($filters['per_page'] ?? 20);
    }

    /**
     * الحصول على إحصائيات المحفظة
     */
    public function getWalletStatistics(PharmaWallet $wallet, \DateTime $from = null, \DateTime $to = null): array
    {
        $from = $from ?? now()->subMonth();
        $to = $to ?? now();

        $transactions = $wallet->getTransactionsByPeriod($from, $to);

        $stats = [
            'current_balance' => $wallet->balance,
            'available_balance' => $wallet->available_balance,
            'pending_balance' => $wallet->pending_balance,
            'total_transactions' => $transactions->count(),
            'total_credits' => $transactions->whereIn('type', ['credit', 'transfer_in', 'refund'])->sum('amount'),
            'total_debits' => $transactions->whereIn('type', ['debit', 'transfer_out'])->sum('amount'),
            'completed_transactions' => $transactions->where('status', 'completed')->count(),
            'pending_transactions' => $transactions->where('status', 'pending')->count(),
            'failed_transactions' => $transactions->where('status', 'failed')->count(),
        ];

        $stats['net_flow'] = $stats['total_credits'] - $stats['total_debits'];
        $stats['success_rate'] = $stats['total_transactions'] > 0 
            ? ($stats['completed_transactions'] / $stats['total_transactions']) * 100 
            : 0;

        return $stats;
    }

    /**
     * تفعيل المحفظة
     */
    public function activateWallet(PharmaWallet $wallet): bool
    {
        return $wallet->update([
            'is_active' => true,
            'is_verified' => true,
        ]);
    }

    /**
     * إلغاء تفعيل المحفظة
     */
    public function deactivateWallet(PharmaWallet $wallet): bool
    {
        return $wallet->update(['is_active' => false]);
    }

    /**
     * معالجة الدفع حسب الطريقة
     */
    private function processPayment(float $amount, string $paymentMethod, array $paymentData, WalletTransaction $transaction): array
    {
        // هنا يتم التكامل مع بوابات الدفع المختلفة
        // هذا مثال مبسط - في الواقع ستحتاج للتكامل مع APIs الحقيقية

        switch ($paymentMethod) {
            case 'credit_card':
                return $this->processCreditCardPayment($amount, $paymentData, $transaction);
            
            case 'fawry':
                return $this->processFawryPayment($amount, $paymentData, $transaction);
            
            case 'paymob':
                return $this->processPaymobPayment($amount, $paymentData, $transaction);
            
            case 'bank_transfer':
                return $this->processBankTransferPayment($amount, $paymentData, $transaction);
            
            default:
                return [
                    'success' => false,
                    'error' => 'طريقة دفع غير مدعومة',
                ];
        }
    }

    /**
     * معالجة دفع بطاقة ائتمان
     */
    private function processCreditCardPayment(float $amount, array $paymentData, WalletTransaction $transaction): array
    {
        // تكامل مع Stripe أو بوابة دفع أخرى
        // هذا مثال مبسط
        
        try {
            // محاكاة معالجة الدفع
            $success = rand(1, 10) > 2; // 80% نجاح
            
            if ($success) {
                return [
                    'success' => true,
                    'payment_id' => 'cc_' . uniqid(),
                    'transaction_id' => $transaction->transaction_id,
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'فشل في معالجة البطاقة الائتمانية',
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * معالجة دفع فوري
     */
    private function processFawryPayment(float $amount, array $paymentData, WalletTransaction $transaction): array
    {
        // تكامل مع Fawry API
        return [
            'success' => true,
            'payment_id' => 'fawry_' . uniqid(),
            'reference_number' => rand(100000, 999999),
        ];
    }

    /**
     * معالجة دفع PayMob
     */
    private function processPaymobPayment(float $amount, array $paymentData, WalletTransaction $transaction): array
    {
        // تكامل مع PayMob API
        return [
            'success' => true,
            'payment_id' => 'paymob_' . uniqid(),
        ];
    }

    /**
     * معالجة تحويل بنكي
     */
    private function processBankTransferPayment(float $amount, array $paymentData, WalletTransaction $transaction): array
    {
        // التحويل البنكي يحتاج تأكيد يدوي
        return [
            'success' => false,
            'pending' => true,
            'message' => 'يرجى إرسال إيصال التحويل البنكي للمراجعة',
        ];
    }

    /**
     * إنشاء رقم معاملة فريد
     */
    private function generateTransactionId(): string
    {
        do {
            $transactionId = 'TXN' . date('YmdHis') . str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        } while (WalletTransaction::where('transaction_id', $transactionId)->exists());

        return $transactionId;
    }
}

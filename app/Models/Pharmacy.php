<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Botble\ACL\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Pharmacy extends BaseModel
{
    use HasFactory;

    protected $table = 'pharmacies';

    protected $fillable = [
        'pharmacy_code',
        'user_id',
        'license_number',
        'name',
        'description',
        'address',
        'phone',
        'email',
        'latitude',
        'longitude',
        'opening_time',
        'closing_time',
        'is_24_hours',
        'accepts_insurance',
        'home_delivery',
        'delivery_fee',
        'delivery_radius_km',
        'is_verified',
        'is_active',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'opening_time' => 'datetime:H:i',
        'closing_time' => 'datetime:H:i',
        'is_24_hours' => 'boolean',
        'accepts_insurance' => 'boolean',
        'home_delivery' => 'boolean',
        'delivery_fee' => 'decimal:2',
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
    ];

    protected $appends = [
        'is_open_now',
        'distance_from_user',
    ];

    /**
     * العلاقة مع جدول المستخدمين
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع صرف الأدوية
     */
    public function medicationDispensings(): HasMany
    {
        return $this->hasMany(MedicationDispensing::class);
    }

    /**
     * العلاقة مع طلبات التوزيع (كطالب)
     */
    public function requestedOrders(): HasMany
    {
        return $this->hasMany(InterPharmacyOrder::class, 'requesting_pharmacy_id');
    }

    /**
     * العلاقة مع طلبات التوزيع (كمورد)
     */
    public function suppliedOrders(): HasMany
    {
        return $this->hasMany(InterPharmacyOrder::class, 'supplying_pharmacy_id');
    }

    /**
     * العلاقة مع التقييمات
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(ServiceReview::class, 'reviewable_id')
            ->where('reviewable_type', self::class);
    }

    /**
     * التحقق من كون الصيدلية مفتوحة الآن
     */
    public function getIsOpenNowAttribute(): bool
    {
        if (!$this->is_active || !$this->is_verified) {
            return false;
        }

        if ($this->is_24_hours) {
            return true;
        }

        if (!$this->opening_time || !$this->closing_time) {
            return true; // افتراض أنها مفتوحة إذا لم تحدد أوقات
        }

        $now = now()->format('H:i');
        $openTime = $this->opening_time->format('H:i');
        $closeTime = $this->closing_time->format('H:i');

        return $now >= $openTime && $now <= $closeTime;
    }

    /**
     * حساب المسافة من المستخدم (يحتاج إحداثيات المستخدم)
     */
    public function getDistanceFromUserAttribute(): ?float
    {
        // هذا يحتاج إحداثيات المستخدم الحالي
        // يمكن تمريرها من الكونترولر أو الخدمة
        return null;
    }

    /**
     * حساب المسافة بين نقطتين
     */
    public function calculateDistance(float $userLat, float $userLng): float
    {
        if (!$this->latitude || !$this->longitude) {
            return 0;
        }

        $earthRadius = 6371; // نصف قطر الأرض بالكيلومتر

        $latDelta = deg2rad($this->latitude - $userLat);
        $lngDelta = deg2rad($this->longitude - $userLng);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($userLat)) * cos(deg2rad($this->latitude)) *
             sin($lngDelta / 2) * sin($lngDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * التحقق من إمكانية التوصيل لموقع معين
     */
    public function canDeliverTo(float $userLat, float $userLng): bool
    {
        if (!$this->home_delivery) {
            return false;
        }

        if (!$this->delivery_radius_km) {
            return true; // لا يوجد حد للتوصيل
        }

        $distance = $this->calculateDistance($userLat, $userLng);
        return $distance <= $this->delivery_radius_km;
    }

    /**
     * الحصول على متوسط التقييم
     */
    public function getAverageRating(): float
    {
        return $this->reviews()->where('is_approved', true)->avg('rating') ?? 0;
    }

    /**
     * الحصول على عدد التقييمات
     */
    public function getReviewsCount(): int
    {
        return $this->reviews()->where('is_approved', true)->count();
    }

    /**
     * الحصول على المبيعات اليوم
     */
    public function getTodaySales()
    {
        return $this->medicationDispensings()
            ->whereDate('dispensing_date', today())
            ->with(['prescriptionMedication.product', 'prescriptionMedication.prescription.patient.customer'])
            ->get();
    }

    /**
     * الحصول على إجمالي المبيعات لفترة معينة
     */
    public function getSalesTotal(\DateTime $from, \DateTime $to): float
    {
        return $this->medicationDispensings()
            ->whereBetween('dispensing_date', [$from, $to])
            ->sum('final_price');
    }

    /**
     * الحصول على الأدوية الأكثر مبيعاً
     */
    public function getTopSellingMedications($limit = 10)
    {
        return $this->medicationDispensings()
            ->selectRaw('prescription_medications.product_id, SUM(quantity_dispensed) as total_quantity, SUM(final_price) as total_sales')
            ->join('prescription_medications', 'medication_dispensings.prescription_medication_id', '=', 'prescription_medications.id')
            ->groupBy('prescription_medications.product_id')
            ->orderBy('total_quantity', 'desc')
            ->limit($limit)
            ->with('prescriptionMedication.product')
            ->get();
    }

    /**
     * البحث عن الصيدليات القريبة
     */
    public static function findNearby(float $latitude, float $longitude, int $radiusKm = 10)
    {
        return self::selectRaw("
                *,
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
            ", [$latitude, $longitude, $latitude])
            ->where('is_active', true)
            ->where('is_verified', true)
            ->having('distance', '<', $radiusKm)
            ->orderBy('distance')
            ->get();
    }

    /**
     * البحث عن الصيدليات
     */
    public static function search(string $query)
    {
        return self::query()
            ->where('pharmacy_code', 'like', "%{$query}%")
            ->orWhere('name', 'like', "%{$query}%")
            ->orWhere('license_number', 'like', "%{$query}%")
            ->orWhere('address', 'like', "%{$query}%")
            ->orWhere('phone', 'like', "%{$query}%")
            ->orWhereHas('user', function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");
            })
            ->with('user')
            ->get();
    }

    /**
     * الحصول على إحصائيات الصيدلية
     */
    public function getStatistics(): array
    {
        $thisMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        return [
            'total_dispensings' => $this->medicationDispensings()->count(),
            'this_month_sales' => $this->getSalesTotal($thisMonth, now()),
            'last_month_sales' => $this->getSalesTotal($lastMonth, $thisMonth),
            'today_dispensings' => $this->medicationDispensings()->whereDate('dispensing_date', today())->count(),
            'average_rating' => $this->getAverageRating(),
            'total_reviews' => $this->getReviewsCount(),
            'requested_orders' => $this->requestedOrders()->count(),
            'supplied_orders' => $this->suppliedOrders()->count(),
        ];
    }

    /**
     * إنشاء رقم صيدلية فريد
     */
    public static function generatePharmacyCode(): string
    {
        do {
            $code = 'PH' . date('Y') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('pharmacy_code', $code)->exists());

        return $code;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($pharmacy) {
            if (!$pharmacy->pharmacy_code) {
                $pharmacy->pharmacy_code = self::generatePharmacyCode();
            }
        });
    }
}

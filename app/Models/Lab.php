<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Botble\ACL\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Lab extends BaseModel
{
    use HasFactory;

    protected $table = 'labs';

    protected $fillable = [
        'lab_code',
        'user_id',
        'license_number',
        'name',
        'description',
        'address',
        'phone',
        'email',
        'latitude',
        'longitude',
        'opening_time',
        'closing_time',
        'home_collection',
        'collection_fee',
        'available_tests',
        'is_verified',
        'is_active',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'opening_time' => 'datetime:H:i',
        'closing_time' => 'datetime:H:i',
        'home_collection' => 'boolean',
        'collection_fee' => 'decimal:2',
        'available_tests' => 'array',
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
    ];

    protected $appends = [
        'is_open_now',
    ];

    /**
     * العلاقة مع جدول المستخدمين
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع طلبات التحاليل
     */
    public function labTestRequests(): HasMany
    {
        return $this->hasMany(LabTestRequest::class);
    }

    /**
     * العلاقة مع التقييمات
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(ServiceReview::class, 'reviewable_id')
            ->where('reviewable_type', self::class);
    }

    /**
     * التحقق من كون المعمل مفتوح الآن
     */
    public function getIsOpenNowAttribute(): bool
    {
        if (!$this->is_active || !$this->is_verified) {
            return false;
        }

        if (!$this->opening_time || !$this->closing_time) {
            return true; // افتراض أنه مفتوح إذا لم تحدد أوقات
        }

        $now = now()->format('H:i');
        $openTime = $this->opening_time->format('H:i');
        $closeTime = $this->closing_time->format('H:i');

        return $now >= $openTime && $now <= $closeTime;
    }

    /**
     * حساب المسافة بين نقطتين
     */
    public function calculateDistance(float $userLat, float $userLng): float
    {
        if (!$this->latitude || !$this->longitude) {
            return 0;
        }

        $earthRadius = 6371; // نصف قطر الأرض بالكيلومتر

        $latDelta = deg2rad($this->latitude - $userLat);
        $lngDelta = deg2rad($this->longitude - $userLng);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($userLat)) * cos(deg2rad($this->latitude)) *
             sin($lngDelta / 2) * sin($lngDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * التحقق من توفر تحليل معين
     */
    public function hasTest(string $testName): bool
    {
        if (!$this->available_tests) {
            return false;
        }

        return in_array(strtolower($testName), array_map('strtolower', $this->available_tests));
    }

    /**
     * الحصول على متوسط التقييم
     */
    public function getAverageRating(): float
    {
        return $this->reviews()->where('is_approved', true)->avg('rating') ?? 0;
    }

    /**
     * الحصول على عدد التقييمات
     */
    public function getReviewsCount(): int
    {
        return $this->reviews()->where('is_approved', true)->count();
    }

    /**
     * الحصول على طلبات اليوم
     */
    public function getTodayRequests()
    {
        return $this->labTestRequests()
            ->whereDate('request_date', today())
            ->with(['patient.customer', 'doctor.user', 'testItems'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * الحصول على الطلبات المعلقة
     */
    public function getPendingRequests()
    {
        return $this->labTestRequests()
            ->where('status', 'pending')
            ->with(['patient.customer', 'doctor.user', 'testItems'])
            ->orderBy('request_date')
            ->get();
    }

    /**
     * الحصول على الطلبات قيد التنفيذ
     */
    public function getInProgressRequests()
    {
        return $this->labTestRequests()
            ->where('status', 'in_progress')
            ->with(['patient.customer', 'doctor.user', 'testItems'])
            ->orderBy('request_date')
            ->get();
    }

    /**
     * الحصول على إجمالي الإيرادات لفترة معينة
     */
    public function getRevenueTotal(\DateTime $from, \DateTime $to): float
    {
        return $this->labTestRequests()
            ->whereBetween('request_date', [$from, $to])
            ->where('is_paid', true)
            ->sum('total_cost');
    }

    /**
     * الحصول على التحاليل الأكثر طلباً
     */
    public function getTopRequestedTests($limit = 10)
    {
        return $this->labTestRequests()
            ->join('lab_test_items', 'lab_test_requests.id', '=', 'lab_test_items.request_id')
            ->selectRaw('lab_test_items.test_name, COUNT(*) as request_count, SUM(lab_test_items.price) as total_revenue')
            ->groupBy('lab_test_items.test_name')
            ->orderBy('request_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * البحث عن المعامل القريبة
     */
    public static function findNearby(float $latitude, float $longitude, int $radiusKm = 10)
    {
        return self::selectRaw("
                *,
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
            ", [$latitude, $longitude, $latitude])
            ->where('is_active', true)
            ->where('is_verified', true)
            ->having('distance', '<', $radiusKm)
            ->orderBy('distance')
            ->get();
    }

    /**
     * البحث عن المعامل التي تقدم تحليل معين
     */
    public static function findByTest(string $testName, float $latitude = null, float $longitude = null)
    {
        $query = self::where('is_active', true)
            ->where('is_verified', true)
            ->whereJsonContains('available_tests', $testName);

        if ($latitude && $longitude) {
            $query->selectRaw("
                *,
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
            ", [$latitude, $longitude, $latitude])
            ->orderBy('distance');
        }

        return $query->get();
    }

    /**
     * البحث عن المعامل
     */
    public static function search(string $query)
    {
        return self::query()
            ->where('lab_code', 'like', "%{$query}%")
            ->orWhere('name', 'like', "%{$query}%")
            ->orWhere('license_number', 'like', "%{$query}%")
            ->orWhere('address', 'like', "%{$query}%")
            ->orWhere('phone', 'like', "%{$query}%")
            ->orWhereJsonContains('available_tests', $query)
            ->orWhereHas('user', function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");
            })
            ->with('user')
            ->get();
    }

    /**
     * الحصول على إحصائيات المعمل
     */
    public function getStatistics(): array
    {
        $thisMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        return [
            'total_requests' => $this->labTestRequests()->count(),
            'pending_requests' => $this->labTestRequests()->where('status', 'pending')->count(),
            'in_progress_requests' => $this->labTestRequests()->where('status', 'in_progress')->count(),
            'completed_requests' => $this->labTestRequests()->where('status', 'completed')->count(),
            'this_month_revenue' => $this->getRevenueTotal($thisMonth, now()),
            'last_month_revenue' => $this->getRevenueTotal($lastMonth, $thisMonth),
            'today_requests' => $this->labTestRequests()->whereDate('request_date', today())->count(),
            'average_rating' => $this->getAverageRating(),
            'total_reviews' => $this->getReviewsCount(),
            'available_tests_count' => count($this->available_tests ?? []),
        ];
    }

    /**
     * إنشاء رقم معمل فريد
     */
    public static function generateLabCode(): string
    {
        do {
            $code = 'LAB' . date('Y') . str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        } while (self::where('lab_code', $code)->exists());

        return $code;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($lab) {
            if (!$lab->lab_code) {
                $lab->lab_code = self::generateLabCode();
            }
        });
    }
}

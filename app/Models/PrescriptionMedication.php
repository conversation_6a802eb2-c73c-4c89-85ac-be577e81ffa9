<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Product;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PrescriptionMedication extends BaseModel
{
    use HasFactory;

    protected $table = 'prescription_medications';

    protected $fillable = [
        'prescription_id',
        'product_id',
        'medication_name',
        'dosage',
        'frequency',
        'duration_days',
        'quantity',
        'dispensed_quantity',
        'instructions',
        'status',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_PARTIALLY_DISPENSED = 'partially_dispensed';
    const STATUS_COMPLETED = 'completed';

    /**
     * العلاقة مع الوصفة
     */
    public function prescription(): BelongsTo
    {
        return $this->belongsTo(Prescription::class);
    }

    /**
     * العلاقة مع المنتج (الدواء)
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * العلاقة مع عمليات الصرف
     */
    public function dispensings(): HasMany
    {
        return $this->hasMany(MedicationDispensing::class);
    }

    /**
     * الحصول على الكمية المتبقية
     */
    public function getRemainingQuantityAttribute(): int
    {
        return $this->quantity - $this->dispensed_quantity;
    }

    /**
     * الحصول على نسبة الصرف
     */
    public function getDispensedPercentageAttribute(): float
    {
        if ($this->quantity == 0) {
            return 0;
        }

        return ($this->dispensed_quantity / $this->quantity) * 100;
    }

    /**
     * التحقق من اكتمال الصرف
     */
    public function isFullyDispensed(): bool
    {
        return $this->dispensed_quantity >= $this->quantity;
    }

    /**
     * التحقق من إمكانية الصرف
     */
    public function canDispense(int $quantity = null): bool
    {
        $quantity = $quantity ?? $this->remaining_quantity;
        
        return $this->remaining_quantity >= $quantity && 
               $this->prescription->status !== Prescription::STATUS_EXPIRED &&
               $this->prescription->status !== Prescription::STATUS_CANCELLED;
    }

    /**
     * صرف كمية من الدواء
     */
    public function dispense(int $quantity, Pharmacy $pharmacy, User $pharmacist, array $dispensingData = []): MedicationDispensing
    {
        if (!$this->canDispense($quantity)) {
            throw new \Exception('لا يمكن صرف هذه الكمية');
        }

        if ($quantity > $this->remaining_quantity) {
            throw new \Exception('الكمية المطلوبة أكبر من المتبقي');
        }

        // التحقق من توفر الدواء في الصيدلية
        if ($this->product->quantity < $quantity) {
            throw new \Exception('الكمية غير متوفرة في المخزون');
        }

        \DB::beginTransaction();

        try {
            // إنشاء عملية صرف
            $dispensing = MedicationDispensing::create([
                'dispensing_number' => MedicationDispensing::generateDispensingNumber(),
                'prescription_medication_id' => $this->id,
                'pharmacy_id' => $pharmacy->id,
                'pharmacist_id' => $pharmacist->id,
                'quantity_dispensed' => $quantity,
                'unit_price' => $dispensingData['unit_price'] ?? $this->product->price,
                'total_price' => ($dispensingData['unit_price'] ?? $this->product->price) * $quantity,
                'discount_amount' => $dispensingData['discount_amount'] ?? 0,
                'final_price' => (($dispensingData['unit_price'] ?? $this->product->price) * $quantity) - ($dispensingData['discount_amount'] ?? 0),
                'dispensing_date' => now()->toDateString(),
                'pharmacist_notes' => $dispensingData['notes'] ?? null,
                'payment_method' => $dispensingData['payment_method'] ?? 'cash',
            ]);

            // تحديث الكمية المصروفة
            $this->dispensed_quantity += $quantity;
            
            // تحديث حالة الدواء
            if ($this->isFullyDispensed()) {
                $this->status = self::STATUS_COMPLETED;
            } else {
                $this->status = self::STATUS_PARTIALLY_DISPENSED;
            }
            
            $this->save();

            // تحديث مخزون المنتج
            $this->product->decrement('quantity', $quantity);

            // تحديث حالة الوصفة
            $this->prescription->updateStatusBasedOnDispensing();

            \DB::commit();

            return $dispensing;

        } catch (\Exception $e) {
            \DB::rollback();
            throw $e;
        }
    }

    /**
     * الحصول على إجمالي التكلفة
     */
    public function getTotalCost(): float
    {
        return $this->product->price * $this->quantity;
    }

    /**
     * الحصول على التكلفة المتبقية
     */
    public function getRemainingCost(): float
    {
        return $this->product->price * $this->remaining_quantity;
    }

    /**
     * الحصول على وصف الحالة
     */
    public function getStatusDescription(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'في الانتظار',
            self::STATUS_PARTIALLY_DISPENSED => 'مصروف جزئياً',
            self::STATUS_COMPLETED => 'مكتمل',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_PARTIALLY_DISPENSED => 'info',
            self::STATUS_COMPLETED => 'success',
            default => 'secondary',
        };
    }

    /**
     * الحصول على تعليمات الاستخدام المنسقة
     */
    public function getFormattedInstructions(): string
    {
        $instructions = [];
        
        if ($this->dosage) {
            $instructions[] = "الجرعة: {$this->dosage}";
        }
        
        if ($this->frequency) {
            $instructions[] = "التكرار: {$this->frequency}";
        }
        
        if ($this->duration_days) {
            $instructions[] = "المدة: {$this->duration_days} يوم";
        }
        
        if ($this->instructions) {
            $instructions[] = "تعليمات إضافية: {$this->instructions}";
        }

        return implode(' | ', $instructions);
    }

    /**
     * التحقق من التفاعلات الدوائية
     */
    public function checkDrugInteractions(): array
    {
        // هذه دالة مبسطة - في الواقع ستحتاج قاعدة بيانات للتفاعلات الدوائية
        $patientMedications = $this->prescription->patient->prescriptions()
            ->where('status', Prescription::STATUS_ACTIVE)
            ->with('medications.product')
            ->get()
            ->pluck('medications')
            ->flatten()
            ->where('id', '!=', $this->id);

        $interactions = [];
        
        foreach ($patientMedications as $medication) {
            // فحص التفاعلات (مبسط)
            if ($this->hasInteractionWith($medication)) {
                $interactions[] = [
                    'medication' => $medication->medication_name,
                    'severity' => 'moderate', // يمكن أن تكون mild, moderate, severe
                    'description' => 'قد يحدث تفاعل بين الدوائين',
                ];
            }
        }

        return $interactions;
    }

    /**
     * فحص التفاعل مع دواء آخر (مبسط)
     */
    private function hasInteractionWith(PrescriptionMedication $otherMedication): bool
    {
        // هذا مثال مبسط - في الواقع تحتاج قاعدة بيانات شاملة للتفاعلات
        $knownInteractions = [
            'warfarin' => ['aspirin', 'ibuprofen'],
            'metformin' => ['alcohol'],
            // إضافة المزيد من التفاعلات المعروفة
        ];

        $thisMedication = strtolower($this->medication_name);
        $otherMedicationName = strtolower($otherMedication->medication_name);

        foreach ($knownInteractions as $drug => $interactsWith) {
            if (($thisMedication === $drug && in_array($otherMedicationName, $interactsWith)) ||
                ($otherMedicationName === $drug && in_array($thisMedication, $interactsWith))) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على تاريخ الصرف
     */
    public function getDispensingHistory()
    {
        return $this->dispensings()
            ->with(['pharmacy', 'pharmacist'])
            ->orderBy('dispensing_date', 'desc')
            ->get();
    }

    protected static function boot()
    {
        parent::boot();

        static::updated(function ($medication) {
            // تحديث حالة الوصفة عند تغيير حالة الدواء
            $medication->prescription->updateStatusBasedOnDispensing();
        });
    }
}

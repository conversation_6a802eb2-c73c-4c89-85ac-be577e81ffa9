<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LabTestRequest extends BaseModel
{
    use HasFactory;

    protected $table = 'lab_test_requests';

    protected $fillable = [
        'request_number',
        'patient_id',
        'doctor_id',
        'lab_id',
        'clinical_data',
        'notes',
        'request_date',
        'required_date',
        'priority',
        'status',
        'total_cost',
        'is_paid',
    ];

    protected $casts = [
        'request_date' => 'date',
        'required_date' => 'date',
        'total_cost' => 'decimal:2',
        'is_paid' => 'boolean',
    ];

    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_URGENT = 'urgent';
    const PRIORITY_EMERGENCY = 'emergency';

    const STATUS_PENDING = 'pending';
    const STATUS_ACCEPTED = 'accepted';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * العلاقة مع المريض
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * العلاقة مع الطبيب
     */
    public function doctor(): BelongsTo
    {
        return $this->belongsTo(Doctor::class);
    }

    /**
     * العلاقة مع المعمل
     */
    public function lab(): BelongsTo
    {
        return $this->belongsTo(Lab::class);
    }

    /**
     * العلاقة مع تفاصيل التحاليل
     */
    public function testItems(): HasMany
    {
        return $this->hasMany(LabTestItem::class, 'request_id');
    }

    /**
     * التحقق من اكتمال التحاليل
     */
    public function isCompleted(): bool
    {
        return $this->testItems->every(function ($item) {
            return $item->status === LabTestItem::STATUS_COMPLETED;
        });
    }

    /**
     * الحصول على نسبة الإنجاز
     */
    public function getCompletionPercentage(): float
    {
        $totalItems = $this->testItems->count();
        $completedItems = $this->testItems->where('status', LabTestItem::STATUS_COMPLETED)->count();

        if ($totalItems == 0) {
            return 0;
        }

        return ($completedItems / $totalItems) * 100;
    }

    /**
     * الحصول على التحاليل المعلقة
     */
    public function getPendingTests()
    {
        return $this->testItems->where('status', LabTestItem::STATUS_PENDING);
    }

    /**
     * الحصول على التحاليل قيد التنفيذ
     */
    public function getInProgressTests()
    {
        return $this->testItems->where('status', LabTestItem::STATUS_IN_PROGRESS);
    }

    /**
     * الحصول على التحاليل المكتملة
     */
    public function getCompletedTests()
    {
        return $this->testItems->where('status', LabTestItem::STATUS_COMPLETED);
    }

    /**
     * قبول الطلب من المعمل
     */
    public function accept(Lab $lab, float $totalCost = null): bool
    {
        if ($this->status !== self::STATUS_PENDING) {
            throw new \Exception('لا يمكن قبول هذا الطلب');
        }

        $this->lab_id = $lab->id;
        $this->status = self::STATUS_ACCEPTED;
        
        if ($totalCost !== null) {
            $this->total_cost = $totalCost;
        }

        return $this->save();
    }

    /**
     * بدء تنفيذ التحاليل
     */
    public function startProcessing(): bool
    {
        if ($this->status !== self::STATUS_ACCEPTED) {
            throw new \Exception('يجب قبول الطلب أولاً');
        }

        $this->status = self::STATUS_IN_PROGRESS;
        return $this->save();
    }

    /**
     * إكمال جميع التحاليل
     */
    public function complete(): bool
    {
        if (!$this->isCompleted()) {
            throw new \Exception('لم يتم إكمال جميع التحاليل بعد');
        }

        $this->status = self::STATUS_COMPLETED;
        return $this->save();
    }

    /**
     * إلغاء الطلب
     */
    public function cancel(string $reason = null): bool
    {
        if (in_array($this->status, [self::STATUS_COMPLETED, self::STATUS_CANCELLED])) {
            throw new \Exception('لا يمكن إلغاء هذا الطلب');
        }

        $this->status = self::STATUS_CANCELLED;
        $this->notes = ($this->notes ? $this->notes . "\n" : '') . "ملغى: " . ($reason ?? 'بدون سبب');
        
        return $this->save();
    }

    /**
     * تحديث حالة الطلب بناءً على حالة التحاليل
     */
    public function updateStatusBasedOnTests(): void
    {
        if ($this->status === self::STATUS_CANCELLED) {
            return;
        }

        $completedCount = $this->testItems->where('status', LabTestItem::STATUS_COMPLETED)->count();
        $inProgressCount = $this->testItems->where('status', LabTestItem::STATUS_IN_PROGRESS)->count();
        $totalCount = $this->testItems->count();

        if ($completedCount === $totalCount && $totalCount > 0) {
            $this->status = self::STATUS_COMPLETED;
        } elseif ($inProgressCount > 0 || $completedCount > 0) {
            $this->status = self::STATUS_IN_PROGRESS;
        } elseif ($this->status === self::STATUS_PENDING && $this->lab_id) {
            $this->status = self::STATUS_ACCEPTED;
        }

        $this->save();
    }

    /**
     * الحصول على وصف الأولوية
     */
    public function getPriorityDescription(): string
    {
        return match($this->priority) {
            self::PRIORITY_NORMAL => 'عادي',
            self::PRIORITY_URGENT => 'عاجل',
            self::PRIORITY_EMERGENCY => 'طارئ',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على لون الأولوية
     */
    public function getPriorityColor(): string
    {
        return match($this->priority) {
            self::PRIORITY_NORMAL => 'success',
            self::PRIORITY_URGENT => 'warning',
            self::PRIORITY_EMERGENCY => 'danger',
            default => 'secondary',
        };
    }

    /**
     * الحصول على وصف الحالة
     */
    public function getStatusDescription(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'في الانتظار',
            self::STATUS_ACCEPTED => 'مقبول',
            self::STATUS_IN_PROGRESS => 'قيد التنفيذ',
            self::STATUS_COMPLETED => 'مكتمل',
            self::STATUS_CANCELLED => 'ملغى',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_ACCEPTED => 'info',
            self::STATUS_IN_PROGRESS => 'primary',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_CANCELLED => 'danger',
            default => 'secondary',
        };
    }

    /**
     * الحصول على النتائج الحرجة
     */
    public function getCriticalResults()
    {
        return $this->testItems()
            ->whereHas('results', function ($query) {
                $query->where('status', 'critical');
            })
            ->with('results')
            ->get();
    }

    /**
     * الحصول على النتائج غير الطبيعية
     */
    public function getAbnormalResults()
    {
        return $this->testItems()
            ->whereHas('results', function ($query) {
                $query->where('status', 'abnormal');
            })
            ->with('results')
            ->get();
    }

    /**
     * إنشاء تقرير شامل للنتائج
     */
    public function generateReport(): array
    {
        $report = [
            'request_info' => [
                'request_number' => $this->request_number,
                'patient_name' => $this->patient->customer->name,
                'patient_code' => $this->patient->patient_code,
                'doctor_name' => $this->doctor->user->name,
                'lab_name' => $this->lab->name,
                'request_date' => $this->request_date->format('Y-m-d'),
                'completion_date' => $this->updated_at->format('Y-m-d'),
            ],
            'clinical_data' => $this->clinical_data,
            'tests' => [],
            'summary' => [
                'total_tests' => $this->testItems->count(),
                'normal_results' => 0,
                'abnormal_results' => 0,
                'critical_results' => 0,
            ],
        ];

        foreach ($this->testItems as $testItem) {
            $testData = [
                'test_name' => $testItem->test_name,
                'test_description' => $testItem->test_description,
                'results' => [],
            ];

            foreach ($testItem->results as $result) {
                $testData['results'][] = [
                    'parameter' => $result->parameter_name,
                    'value' => $result->result_value,
                    'unit' => $result->unit,
                    'reference_range' => $result->reference_range,
                    'status' => $result->status,
                    'notes' => $result->notes,
                ];

                // تحديث الملخص
                switch ($result->status) {
                    case 'normal':
                        $report['summary']['normal_results']++;
                        break;
                    case 'abnormal':
                        $report['summary']['abnormal_results']++;
                        break;
                    case 'critical':
                        $report['summary']['critical_results']++;
                        break;
                }
            }

            $report['tests'][] = $testData;
        }

        return $report;
    }

    /**
     * البحث في طلبات التحاليل
     */
    public static function search(string $query)
    {
        return self::query()
            ->where('request_number', 'like', "%{$query}%")
            ->orWhere('clinical_data', 'like', "%{$query}%")
            ->orWhereHas('patient', function ($q) use ($query) {
                $q->where('patient_code', 'like', "%{$query}%")
                  ->orWhereHas('customer', function ($q2) use ($query) {
                      $q2->where('name', 'like', "%{$query}%");
                  });
            })
            ->orWhereHas('doctor', function ($q) use ($query) {
                $q->where('doctor_code', 'like', "%{$query}%")
                  ->orWhereHas('user', function ($q2) use ($query) {
                      $q2->where('name', 'like', "%{$query}%");
                  });
            })
            ->with(['patient.customer', 'doctor.user', 'lab', 'testItems'])
            ->orderBy('request_date', 'desc')
            ->get();
    }

    /**
     * إنشاء رقم طلب فريد
     */
    public static function generateRequestNumber(): string
    {
        do {
            $number = 'LAB' . date('Y') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('request_number', $number)->exists());

        return $number;
    }

    /**
     * الحصول على إحصائيات طلبات التحاليل
     */
    public static function getStatistics(\DateTime $from = null, \DateTime $to = null): array
    {
        $query = self::query();

        if ($from && $to) {
            $query->whereBetween('request_date', [$from, $to]);
        }

        $total = $query->count();
        $pending = (clone $query)->where('status', self::STATUS_PENDING)->count();
        $inProgress = (clone $query)->where('status', self::STATUS_IN_PROGRESS)->count();
        $completed = (clone $query)->where('status', self::STATUS_COMPLETED)->count();
        $cancelled = (clone $query)->where('status', self::STATUS_CANCELLED)->count();

        return [
            'total_requests' => $total,
            'pending_requests' => $pending,
            'in_progress_requests' => $inProgress,
            'completed_requests' => $completed,
            'cancelled_requests' => $cancelled,
            'completion_rate' => $total > 0 ? ($completed / $total) * 100 : 0,
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($request) {
            if (!$request->request_number) {
                $request->request_number = self::generateRequestNumber();
            }
        });
    }
}

<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Prescription extends BaseModel
{
    use HasFactory;

    protected $table = 'prescriptions';

    protected $fillable = [
        'prescription_number',
        'patient_id',
        'doctor_id',
        'diagnosis',
        'symptoms',
        'notes',
        'prescription_date',
        'expiry_date',
        'status',
        'is_chronic',
        'refill_count',
        'max_refills',
    ];

    protected $casts = [
        'prescription_date' => 'date',
        'expiry_date' => 'date',
        'is_chronic' => 'boolean',
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_PARTIALLY_FILLED = 'partially_filled';
    const STATUS_COMPLETED = 'completed';
    const STATUS_EXPIRED = 'expired';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * العلاقة مع المريض
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * العلاقة مع الطبيب
     */
    public function doctor(): BelongsTo
    {
        return $this->belongsTo(Doctor::class);
    }

    /**
     * العلاقة مع أدوية الوصفة
     */
    public function medications(): HasMany
    {
        return $this->hasMany(PrescriptionMedication::class);
    }

    /**
     * التحقق من انتهاء صلاحية الوصفة
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * التحقق من إمكانية التجديد
     */
    public function canRefill(): bool
    {
        return $this->is_chronic && 
               $this->refill_count < $this->max_refills && 
               !$this->isExpired() &&
               in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_PARTIALLY_FILLED]);
    }

    /**
     * التحقق من اكتمال صرف الوصفة
     */
    public function isFullyDispensed(): bool
    {
        return $this->medications->every(function ($medication) {
            return $medication->dispensed_quantity >= $medication->quantity;
        });
    }

    /**
     * الحصول على نسبة الصرف
     */
    public function getDispensedPercentage(): float
    {
        $totalQuantity = $this->medications->sum('quantity');
        $dispensedQuantity = $this->medications->sum('dispensed_quantity');

        if ($totalQuantity == 0) {
            return 0;
        }

        return ($dispensedQuantity / $totalQuantity) * 100;
    }

    /**
     * الحصول على الأدوية المتبقية
     */
    public function getRemainingMedications()
    {
        return $this->medications->filter(function ($medication) {
            return $medication->dispensed_quantity < $medication->quantity;
        });
    }

    /**
     * الحصول على إجمالي تكلفة الوصفة
     */
    public function getTotalCost(): float
    {
        return $this->medications->sum(function ($medication) {
            return $medication->product->price * $medication->quantity;
        });
    }

    /**
     * الحصول على التكلفة المتبقية
     */
    public function getRemainingCost(): float
    {
        return $this->medications->sum(function ($medication) {
            $remainingQuantity = $medication->quantity - $medication->dispensed_quantity;
            return $medication->product->price * $remainingQuantity;
        });
    }

    /**
     * تحديث حالة الوصفة بناءً على الصرف
     */
    public function updateStatusBasedOnDispensing(): void
    {
        if ($this->isExpired()) {
            $this->status = self::STATUS_EXPIRED;
        } elseif ($this->isFullyDispensed()) {
            $this->status = self::STATUS_COMPLETED;
        } elseif ($this->getDispensedPercentage() > 0) {
            $this->status = self::STATUS_PARTIALLY_FILLED;
        } else {
            $this->status = self::STATUS_ACTIVE;
        }

        $this->save();
    }

    /**
     * تجديد الوصفة
     */
    public function refill(): self
    {
        if (!$this->canRefill()) {
            throw new \Exception('لا يمكن تجديد هذه الوصفة');
        }

        // إنشاء وصفة جديدة
        $newPrescription = $this->replicate();
        $newPrescription->prescription_number = self::generatePrescriptionNumber();
        $newPrescription->prescription_date = now()->toDateString();
        $newPrescription->expiry_date = now()->addMonths(3)->toDateString();
        $newPrescription->status = self::STATUS_ACTIVE;
        $newPrescription->refill_count = $this->refill_count + 1;
        $newPrescription->save();

        // نسخ الأدوية
        foreach ($this->medications as $medication) {
            $newMedication = $medication->replicate();
            $newMedication->prescription_id = $newPrescription->id;
            $newMedication->dispensed_quantity = 0;
            $newMedication->status = PrescriptionMedication::STATUS_PENDING;
            $newMedication->save();
        }

        // تحديث الوصفة الحالية
        $this->refill_count++;
        $this->save();

        return $newPrescription;
    }

    /**
     * إلغاء الوصفة
     */
    public function cancel(string $reason = null): bool
    {
        if (in_array($this->status, [self::STATUS_COMPLETED, self::STATUS_CANCELLED])) {
            throw new \Exception('لا يمكن إلغاء هذه الوصفة');
        }

        $this->status = self::STATUS_CANCELLED;
        $this->notes = ($this->notes ? $this->notes . "\n" : '') . "ملغاة: " . ($reason ?? 'بدون سبب');
        
        return $this->save();
    }

    /**
     * الحصول على وصف الحالة
     */
    public function getStatusDescription(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'نشطة',
            self::STATUS_PARTIALLY_FILLED => 'مصروفة جزئياً',
            self::STATUS_COMPLETED => 'مكتملة',
            self::STATUS_EXPIRED => 'منتهية الصلاحية',
            self::STATUS_CANCELLED => 'ملغاة',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'success',
            self::STATUS_PARTIALLY_FILLED => 'warning',
            self::STATUS_COMPLETED => 'info',
            self::STATUS_EXPIRED => 'danger',
            self::STATUS_CANCELLED => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * البحث في الوصفات
     */
    public static function search(string $query)
    {
        return self::query()
            ->where('prescription_number', 'like', "%{$query}%")
            ->orWhere('diagnosis', 'like', "%{$query}%")
            ->orWhereHas('patient', function ($q) use ($query) {
                $q->where('patient_code', 'like', "%{$query}%")
                  ->orWhereHas('customer', function ($q2) use ($query) {
                      $q2->where('name', 'like', "%{$query}%");
                  });
            })
            ->orWhereHas('doctor', function ($q) use ($query) {
                $q->where('doctor_code', 'like', "%{$query}%")
                  ->orWhereHas('user', function ($q2) use ($query) {
                      $q2->where('name', 'like', "%{$query}%");
                  });
            })
            ->with(['patient.customer', 'doctor.user', 'medications.product'])
            ->orderBy('prescription_date', 'desc')
            ->get();
    }

    /**
     * الحصول على الوصفات النشطة
     */
    public static function getActive()
    {
        return self::whereIn('status', [self::STATUS_ACTIVE, self::STATUS_PARTIALLY_FILLED])
            ->where('expiry_date', '>', now())
            ->with(['patient.customer', 'doctor.user', 'medications.product'])
            ->orderBy('prescription_date', 'desc')
            ->get();
    }

    /**
     * الحصول على الوصفات المنتهية الصلاحية
     */
    public static function getExpired()
    {
        return self::where('expiry_date', '<=', now())
            ->where('status', '!=', self::STATUS_COMPLETED)
            ->with(['patient.customer', 'doctor.user'])
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * إنشاء رقم وصفة فريد
     */
    public static function generatePrescriptionNumber(): string
    {
        do {
            $number = 'RX' . date('Y') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('prescription_number', $number)->exists());

        return $number;
    }

    /**
     * الحصول على إحصائيات الوصفات
     */
    public static function getStatistics(\DateTime $from = null, \DateTime $to = null): array
    {
        $query = self::query();

        if ($from && $to) {
            $query->whereBetween('prescription_date', [$from, $to]);
        }

        $total = $query->count();
        $active = (clone $query)->where('status', self::STATUS_ACTIVE)->count();
        $completed = (clone $query)->where('status', self::STATUS_COMPLETED)->count();
        $expired = (clone $query)->where('status', self::STATUS_EXPIRED)->count();
        $chronic = (clone $query)->where('is_chronic', true)->count();

        return [
            'total_prescriptions' => $total,
            'active_prescriptions' => $active,
            'completed_prescriptions' => $completed,
            'expired_prescriptions' => $expired,
            'chronic_prescriptions' => $chronic,
            'completion_rate' => $total > 0 ? ($completed / $total) * 100 : 0,
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($prescription) {
            if (!$prescription->prescription_number) {
                $prescription->prescription_number = self::generatePrescriptionNumber();
            }

            if (!$prescription->expiry_date) {
                $prescription->expiry_date = now()->addMonths(3);
            }
        });

        static::updated(function ($prescription) {
            // تحديث حالة الأدوية المزمنة إذا تغيرت حالة الوصفة
            if ($prescription->is_chronic && $prescription->status === self::STATUS_COMPLETED) {
                ChronicMedication::where('patient_id', $prescription->patient_id)
                    ->whereIn('product_id', $prescription->medications->pluck('product_id'))
                    ->update(['last_refill_date' => now()]);
            }
        });
    }
}

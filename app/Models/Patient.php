<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Patient extends BaseModel
{
    use HasFactory;

    protected $table = 'patients';

    protected $fillable = [
        'patient_code',
        'user_id',
        'birth_date',
        'gender',
        'blood_type',
        'medical_history',
        'allergies',
        'chronic_diseases',
        'emergency_contact_name',
        'emergency_contact_phone',
        'national_id',
        'is_chronic_patient',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'is_chronic_patient' => 'boolean',
    ];

    protected $appends = [
        'age',
        'full_name',
    ];

    /**
     * العلاقة مع جدول العملاء
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'user_id');
    }

    /**
     * العلاقة مع الوصفات الطبية
     */
    public function prescriptions(): HasMany
    {
        return $this->hasMany(Prescription::class);
    }

    /**
     * العلاقة مع طلبات التحاليل
     */
    public function labTestRequests(): HasMany
    {
        return $this->hasMany(LabTestRequest::class);
    }

    /**
     * العلاقة مع الأدوية المزمنة
     */
    public function chronicMedications(): HasMany
    {
        return $this->hasMany(ChronicMedication::class);
    }

    /**
     * العلاقة مع المواعيد
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * حساب العمر
     */
    public function getAgeAttribute(): ?int
    {
        if (!$this->birth_date) {
            return null;
        }

        return $this->birth_date->diffInYears(now());
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        return $this->customer?->name ?? '';
    }

    /**
     * الحصول على الوصفات النشطة
     */
    public function getActivePrescriptions()
    {
        return $this->prescriptions()
            ->whereIn('status', ['active', 'partially_filled'])
            ->with(['medications.product', 'doctor'])
            ->orderBy('prescription_date', 'desc')
            ->get();
    }

    /**
     * الحصول على آخر التحاليل
     */
    public function getRecentLabTests($limit = 5)
    {
        return $this->labTestRequests()
            ->with(['testItems.results', 'doctor', 'lab'])
            ->orderBy('request_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * التحقق من وجود حساسية معينة
     */
    public function hasAllergy(string $allergen): bool
    {
        if (!$this->allergies) {
            return false;
        }

        return str_contains(strtolower($this->allergies), strtolower($allergen));
    }

    /**
     * التحقق من وجود مرض مزمن معين
     */
    public function hasChronicDisease(string $disease): bool
    {
        if (!$this->chronic_diseases) {
            return false;
        }

        return str_contains(strtolower($this->chronic_diseases), strtolower($disease));
    }

    /**
     * إنشاء رقم مريض فريد
     */
    public static function generatePatientCode(): string
    {
        do {
            $code = 'P' . date('Y') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('patient_code', $code)->exists());

        return $code;
    }

    /**
     * البحث عن المرضى
     */
    public static function search(string $query)
    {
        return self::query()
            ->where('patient_code', 'like', "%{$query}%")
            ->orWhereHas('customer', function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('phone', 'like', "%{$query}%");
            })
            ->orWhere('national_id', 'like', "%{$query}%")
            ->with('customer')
            ->get();
    }

    /**
     * الحصول على إحصائيات المريض
     */
    public function getStatistics(): array
    {
        return [
            'total_prescriptions' => $this->prescriptions()->count(),
            'active_prescriptions' => $this->prescriptions()->whereIn('status', ['active', 'partially_filled'])->count(),
            'total_lab_tests' => $this->labTestRequests()->count(),
            'pending_lab_tests' => $this->labTestRequests()->where('status', 'pending')->count(),
            'chronic_medications' => $this->chronicMedications()->where('is_active', true)->count(),
            'upcoming_appointments' => $this->appointments()->where('appointment_datetime', '>', now())->count(),
        ];
    }

    /**
     * الحصول على التاريخ الطبي المنظم
     */
    public function getMedicalTimelineAttribute(): array
    {
        $timeline = [];

        // إضافة الوصفات
        foreach ($this->prescriptions as $prescription) {
            $timeline[] = [
                'type' => 'prescription',
                'date' => $prescription->prescription_date,
                'title' => 'وصفة طبية من د. ' . $prescription->doctor->customer->name,
                'description' => $prescription->diagnosis,
                'data' => $prescription,
            ];
        }

        // إضافة التحاليل
        foreach ($this->labTestRequests as $labTest) {
            $timeline[] = [
                'type' => 'lab_test',
                'date' => $labTest->request_date,
                'title' => 'طلب تحليل من د. ' . $labTest->doctor->customer->name,
                'description' => $labTest->clinical_data,
                'data' => $labTest,
            ];
        }

        // ترتيب حسب التاريخ
        usort($timeline, function ($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        return $timeline;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($patient) {
            if (!$patient->patient_code) {
                $patient->patient_code = self::generatePatientCode();
            }
        });
    }
}

<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AcademyCourse extends BaseModel
{
    use HasFactory;

    protected $table = 'academy_courses';

    protected $fillable = [
        'title', 'description', 'content', 'category', 'type', 'instructor_name',
        'instructor_bio', 'price', 'duration_hours', 'difficulty_level', 'image',
        'video_url', 'pdf_url', 'learning_objectives', 'has_certificate',
        'passing_score', 'is_featured', 'status'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'learning_objectives' => 'array',
        'has_certificate' => 'boolean',
        'is_featured' => 'boolean',
    ];

    const STATUS_DRAFT = 'draft';
    const STATUS_PUBLISHED = 'published';
    const STATUS_ARCHIVED = 'archived';

    public function contents(): HasMany
    {
        return $this->hasMany(CourseContent::class, 'course_id');
    }

    public function progress(): HasMany
    {
        return $this->hasMany(CourseProgress::class, 'course_id');
    }

    public function certificates(): HasMany
    {
        return $this->hasMany(Certificate::class, 'course_id');
    }

    public function quizzes(): HasMany
    {
        return $this->hasMany(CourseQuiz::class, 'course_id');
    }

    public function getEnrollmentCount(): int
    {
        return $this->progress()->distinct('user_id')->count();
    }

    public function getCompletionRate(): float
    {
        $totalEnrollments = $this->getEnrollmentCount();
        if ($totalEnrollments === 0) return 0;

        $completions = $this->certificates()->count();
        return ($completions / $totalEnrollments) * 100;
    }

    public static function getFeatured()
    {
        return self::where('is_featured', true)
            ->where('status', self::STATUS_PUBLISHED)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public static function getByCategory(string $category)
    {
        return self::where('category', $category)
            ->where('status', self::STATUS_PUBLISHED)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}

<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Botble\ACL\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PharmaWallet extends BaseModel
{
    use HasFactory;

    protected $table = 'pharma_wallets';

    protected $fillable = [
        'user_id',
        'wallet_number',
        'balance',
        'pending_balance',
        'currency',
        'is_active',
        'is_verified',
        'last_transaction_at',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'is_verified' => 'boolean',
        'last_transaction_at' => 'datetime',
    ];

    protected $appends = [
        'available_balance',
        'total_balance',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع المعاملات
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class, 'wallet_id');
    }

    /**
     * الرصيد المتاح (الرصيد الحالي - الرصيد المعلق)
     */
    public function getAvailableBalanceAttribute(): float
    {
        return $this->balance - $this->pending_balance;
    }

    /**
     * إجمالي الرصيد
     */
    public function getTotalBalanceAttribute(): float
    {
        return $this->balance;
    }

    /**
     * شحن المحفظة
     */
    public function credit(float $amount, string $description = null, string $referenceType = null, int $referenceId = null): WalletTransaction
    {
        $balanceBefore = $this->balance;
        $this->balance += $amount;
        $this->last_transaction_at = now();
        $this->save();

        return $this->transactions()->create([
            'transaction_id' => $this->generateTransactionId(),
            'type' => 'credit',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->balance,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'description' => $description ?? 'شحن المحفظة',
            'status' => 'completed',
        ]);
    }

    /**
     * خصم من المحفظة
     */
    public function debit(float $amount, string $description = null, string $referenceType = null, int $referenceId = null): WalletTransaction
    {
        if ($this->available_balance < $amount) {
            throw new \Exception('الرصيد غير كافي');
        }

        $balanceBefore = $this->balance;
        $this->balance -= $amount;
        $this->last_transaction_at = now();
        $this->save();

        return $this->transactions()->create([
            'transaction_id' => $this->generateTransactionId(),
            'type' => 'debit',
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->balance,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'description' => $description ?? 'خصم من المحفظة',
            'status' => 'completed',
        ]);
    }

    /**
     * تحويل إلى محفظة أخرى
     */
    public function transferTo(PharmaWallet $toWallet, float $amount, string $description = null): array
    {
        if ($this->available_balance < $amount) {
            throw new \Exception('الرصيد غير كافي');
        }

        if (!$this->is_active || !$toWallet->is_active) {
            throw new \Exception('إحدى المحافظ غير نشطة');
        }

        \DB::beginTransaction();

        try {
            // خصم من المحفظة المرسلة
            $debitTransaction = $this->debit($amount, $description ?? 'تحويل إلى ' . $toWallet->user->name, 'transfer', $toWallet->id);
            $debitTransaction->update(['type' => 'transfer_out']);

            // إضافة للمحفظة المستقبلة
            $creditTransaction = $toWallet->credit($amount, $description ?? 'تحويل من ' . $this->user->name, 'transfer', $this->id);
            $creditTransaction->update(['type' => 'transfer_in']);

            \DB::commit();

            return [
                'debit_transaction' => $debitTransaction,
                'credit_transaction' => $creditTransaction,
            ];

        } catch (\Exception $e) {
            \DB::rollback();
            throw $e;
        }
    }

    /**
     * حجز مبلغ (إضافة للرصيد المعلق)
     */
    public function hold(float $amount, string $description = null, string $referenceType = null, int $referenceId = null): WalletTransaction
    {
        if ($this->available_balance < $amount) {
            throw new \Exception('الرصيد غير كافي للحجز');
        }

        $this->pending_balance += $amount;
        $this->save();

        return $this->transactions()->create([
            'transaction_id' => $this->generateTransactionId(),
            'type' => 'debit',
            'amount' => $amount,
            'balance_before' => $this->balance,
            'balance_after' => $this->balance,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'description' => $description ?? 'حجز مبلغ',
            'status' => 'pending',
            'metadata' => json_encode(['held_amount' => $amount]),
        ]);
    }

    /**
     * تأكيد المبلغ المحجوز (خصم نهائي)
     */
    public function confirmHold(WalletTransaction $holdTransaction): WalletTransaction
    {
        if ($holdTransaction->status !== 'pending') {
            throw new \Exception('المعاملة ليست معلقة');
        }

        $metadata = json_decode($holdTransaction->metadata, true);
        $heldAmount = $metadata['held_amount'] ?? $holdTransaction->amount;

        $balanceBefore = $this->balance;
        $this->balance -= $heldAmount;
        $this->pending_balance -= $heldAmount;
        $this->last_transaction_at = now();
        $this->save();

        $holdTransaction->update([
            'balance_before' => $balanceBefore,
            'balance_after' => $this->balance,
            'status' => 'completed',
        ]);

        return $holdTransaction;
    }

    /**
     * إلغاء المبلغ المحجوز
     */
    public function cancelHold(WalletTransaction $holdTransaction): WalletTransaction
    {
        if ($holdTransaction->status !== 'pending') {
            throw new \Exception('المعاملة ليست معلقة');
        }

        $metadata = json_decode($holdTransaction->metadata, true);
        $heldAmount = $metadata['held_amount'] ?? $holdTransaction->amount;

        $this->pending_balance -= $heldAmount;
        $this->save();

        $holdTransaction->update([
            'status' => 'cancelled',
        ]);

        return $holdTransaction;
    }

    /**
     * استرداد مبلغ
     */
    public function refund(float $amount, string $description = null, string $referenceType = null, int $referenceId = null): WalletTransaction
    {
        return $this->credit($amount, $description ?? 'استرداد', $referenceType, $referenceId)
            ->update(['type' => 'refund']);
    }

    /**
     * الحصول على آخر المعاملات
     */
    public function getRecentTransactions($limit = 10)
    {
        return $this->transactions()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * الحصول على معاملات فترة معينة
     */
    public function getTransactionsByPeriod(\DateTime $from, \DateTime $to)
    {
        return $this->transactions()
            ->whereBetween('created_at', [$from, $to])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * الحصول على إجمالي المبالغ المشحونة
     */
    public function getTotalCredits(\DateTime $from = null, \DateTime $to = null): float
    {
        $query = $this->transactions()->whereIn('type', ['credit', 'transfer_in', 'refund']);

        if ($from && $to) {
            $query->whereBetween('created_at', [$from, $to]);
        }

        return $query->sum('amount');
    }

    /**
     * الحصول على إجمالي المبالغ المخصومة
     */
    public function getTotalDebits(\DateTime $from = null, \DateTime $to = null): float
    {
        $query = $this->transactions()->whereIn('type', ['debit', 'transfer_out']);

        if ($from && $to) {
            $query->whereBetween('created_at', [$from, $to]);
        }

        return $query->sum('amount');
    }

    /**
     * إنشاء رقم معاملة فريد
     */
    private function generateTransactionId(): string
    {
        do {
            $transactionId = 'TXN' . date('YmdHis') . str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        } while (WalletTransaction::where('transaction_id', $transactionId)->exists());

        return $transactionId;
    }

    /**
     * إنشاء رقم محفظة فريد
     */
    public static function generateWalletNumber(): string
    {
        do {
            $walletNumber = 'PW' . date('Y') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('wallet_number', $walletNumber)->exists());

        return $walletNumber;
    }

    /**
     * البحث عن محفظة برقم المحفظة
     */
    public static function findByWalletNumber(string $walletNumber): ?self
    {
        return self::where('wallet_number', $walletNumber)->first();
    }

    /**
     * الحصول على إحصائيات المحفظة
     */
    public function getStatistics(): array
    {
        $thisMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        return [
            'current_balance' => $this->balance,
            'available_balance' => $this->available_balance,
            'pending_balance' => $this->pending_balance,
            'total_transactions' => $this->transactions()->count(),
            'this_month_credits' => $this->getTotalCredits($thisMonth, now()),
            'this_month_debits' => $this->getTotalDebits($thisMonth, now()),
            'last_month_credits' => $this->getTotalCredits($lastMonth, $thisMonth),
            'last_month_debits' => $this->getTotalDebits($lastMonth, $thisMonth),
            'last_transaction_date' => $this->last_transaction_at,
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($wallet) {
            if (!$wallet->wallet_number) {
                $wallet->wallet_number = self::generateWalletNumber();
            }
        });
    }
}

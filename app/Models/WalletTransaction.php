<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WalletTransaction extends BaseModel
{
    use HasFactory;

    protected $table = 'wallet_transactions';

    protected $fillable = [
        'wallet_id',
        'transaction_id',
        'type',
        'amount',
        'balance_before',
        'balance_after',
        'reference_type',
        'reference_id',
        'description',
        'status',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'metadata' => 'array',
    ];

    const TYPE_CREDIT = 'credit';
    const TYPE_DEBIT = 'debit';
    const TYPE_TRANSFER_IN = 'transfer_in';
    const TYPE_TRANSFER_OUT = 'transfer_out';
    const TYPE_REFUND = 'refund';

    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * العلاقة مع المحفظة
     */
    public function wallet(): BelongsTo
    {
        return $this->belongsTo(PharmaWallet::class, 'wallet_id');
    }

    /**
     * الحصول على نوع المرجع
     */
    public function getReference()
    {
        if (!$this->reference_type || !$this->reference_id) {
            return null;
        }

        return app($this->reference_type)->find($this->reference_id);
    }

    /**
     * التحقق من كون المعاملة مكتملة
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * التحقق من كون المعاملة معلقة
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * التحقق من كون المعاملة فاشلة
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * التحقق من كون المعاملة ملغاة
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * التحقق من كون المعاملة خصم
     */
    public function isDebit(): bool
    {
        return in_array($this->type, [self::TYPE_DEBIT, self::TYPE_TRANSFER_OUT]);
    }

    /**
     * التحقق من كون المعاملة إضافة
     */
    public function isCredit(): bool
    {
        return in_array($this->type, [self::TYPE_CREDIT, self::TYPE_TRANSFER_IN, self::TYPE_REFUND]);
    }

    /**
     * الحصول على وصف نوع المعاملة
     */
    public function getTypeDescription(): string
    {
        return match($this->type) {
            self::TYPE_CREDIT => 'شحن المحفظة',
            self::TYPE_DEBIT => 'خصم من المحفظة',
            self::TYPE_TRANSFER_IN => 'تحويل وارد',
            self::TYPE_TRANSFER_OUT => 'تحويل صادر',
            self::TYPE_REFUND => 'استرداد',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على وصف حالة المعاملة
     */
    public function getStatusDescription(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'معلقة',
            self::STATUS_COMPLETED => 'مكتملة',
            self::STATUS_FAILED => 'فاشلة',
            self::STATUS_CANCELLED => 'ملغاة',
            default => 'غير محدد',
        };
    }

    /**
     * الحصول على لون حالة المعاملة للعرض
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_CANCELLED => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * الحصول على أيقونة نوع المعاملة
     */
    public function getTypeIcon(): string
    {
        return match($this->type) {
            self::TYPE_CREDIT => 'fas fa-plus-circle text-success',
            self::TYPE_DEBIT => 'fas fa-minus-circle text-danger',
            self::TYPE_TRANSFER_IN => 'fas fa-arrow-down text-success',
            self::TYPE_TRANSFER_OUT => 'fas fa-arrow-up text-warning',
            self::TYPE_REFUND => 'fas fa-undo text-info',
            default => 'fas fa-circle',
        };
    }

    /**
     * البحث في المعاملات
     */
    public static function search(string $query)
    {
        return self::query()
            ->where('transaction_id', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->orWhere('reference_type', 'like', "%{$query}%")
            ->with('wallet.user')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * الحصول على المعاملات حسب النوع
     */
    public static function getByType(string $type, int $limit = null)
    {
        $query = self::where('type', $type)
            ->with('wallet.user')
            ->orderBy('created_at', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * الحصول على المعاملات حسب الحالة
     */
    public static function getByStatus(string $status, int $limit = null)
    {
        $query = self::where('status', $status)
            ->with('wallet.user')
            ->orderBy('created_at', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * الحصول على إحصائيات المعاملات
     */
    public static function getStatistics(\DateTime $from = null, \DateTime $to = null): array
    {
        $query = self::query();

        if ($from && $to) {
            $query->whereBetween('created_at', [$from, $to]);
        }

        $totalTransactions = $query->count();
        $completedTransactions = (clone $query)->where('status', self::STATUS_COMPLETED)->count();
        $pendingTransactions = (clone $query)->where('status', self::STATUS_PENDING)->count();
        $failedTransactions = (clone $query)->where('status', self::STATUS_FAILED)->count();

        $totalCredits = (clone $query)->whereIn('type', [self::TYPE_CREDIT, self::TYPE_TRANSFER_IN, self::TYPE_REFUND])
            ->where('status', self::STATUS_COMPLETED)
            ->sum('amount');

        $totalDebits = (clone $query)->whereIn('type', [self::TYPE_DEBIT, self::TYPE_TRANSFER_OUT])
            ->where('status', self::STATUS_COMPLETED)
            ->sum('amount');

        return [
            'total_transactions' => $totalTransactions,
            'completed_transactions' => $completedTransactions,
            'pending_transactions' => $pendingTransactions,
            'failed_transactions' => $failedTransactions,
            'success_rate' => $totalTransactions > 0 ? ($completedTransactions / $totalTransactions) * 100 : 0,
            'total_credits' => $totalCredits,
            'total_debits' => $totalDebits,
            'net_flow' => $totalCredits - $totalDebits,
        ];
    }

    /**
     * تحديث حالة المعاملة
     */
    public function updateStatus(string $status, array $metadata = null): bool
    {
        $updateData = ['status' => $status];

        if ($metadata) {
            $currentMetadata = $this->metadata ?? [];
            $updateData['metadata'] = array_merge($currentMetadata, $metadata);
        }

        return $this->update($updateData);
    }

    /**
     * إضافة ملاحظة للمعاملة
     */
    public function addNote(string $note): bool
    {
        $metadata = $this->metadata ?? [];
        $metadata['notes'] = $metadata['notes'] ?? [];
        $metadata['notes'][] = [
            'note' => $note,
            'timestamp' => now()->toISOString(),
        ];

        return $this->update(['metadata' => $metadata]);
    }

    /**
     * الحصول على الملاحظات
     */
    public function getNotes(): array
    {
        return $this->metadata['notes'] ?? [];
    }
}

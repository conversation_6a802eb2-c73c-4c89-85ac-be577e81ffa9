<?php

namespace App\Models;

use Botble\Base\Models\BaseModel;
use Botble\ACL\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Doctor extends BaseModel
{
    use HasFactory;

    protected $table = 'doctors';

    protected $fillable = [
        'doctor_code',
        'user_id',
        'license_number',
        'specialization',
        'qualification',
        'bio',
        'clinic_address',
        'clinic_phone',
        'work_start_time',
        'work_end_time',
        'work_days',
        'consultation_fee',
        'is_verified',
        'is_active',
    ];

    protected $casts = [
        'work_days' => 'array',
        'work_start_time' => 'datetime:H:i',
        'work_end_time' => 'datetime:H:i',
        'consultation_fee' => 'decimal:2',
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
    ];

    protected $appends = [
        'full_name',
        'is_available_now',
    ];

    /**
     * العلاقة مع جدول المستخدمين
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع الوصفات الطبية
     */
    public function prescriptions(): HasMany
    {
        return $this->hasMany(Prescription::class);
    }

    /**
     * العلاقة مع طلبات التحاليل
     */
    public function labTestRequests(): HasMany
    {
        return $this->hasMany(LabTestRequest::class);
    }

    /**
     * العلاقة مع المواعيد
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * العلاقة مع التقييمات
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(ServiceReview::class, 'reviewable_id')
            ->where('reviewable_type', self::class);
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        return $this->user?->name ?? '';
    }

    /**
     * التحقق من توفر الطبيب الآن
     */
    public function getIsAvailableNowAttribute(): bool
    {
        if (!$this->is_active || !$this->is_verified) {
            return false;
        }

        $now = now();
        $currentDay = strtolower($now->format('l')); // monday, tuesday, etc.
        $currentTime = $now->format('H:i');

        // التحقق من يوم العمل
        if (!in_array($currentDay, $this->work_days ?? [])) {
            return false;
        }

        // التحقق من ساعات العمل
        if ($this->work_start_time && $this->work_end_time) {
            $startTime = $this->work_start_time->format('H:i');
            $endTime = $this->work_end_time->format('H:i');

            return $currentTime >= $startTime && $currentTime <= $endTime;
        }

        return true;
    }

    /**
     * الحصول على متوسط التقييم
     */
    public function getAverageRating(): float
    {
        return $this->reviews()->where('is_approved', true)->avg('rating') ?? 0;
    }

    /**
     * الحصول على عدد التقييمات
     */
    public function getReviewsCount(): int
    {
        return $this->reviews()->where('is_approved', true)->count();
    }

    /**
     * الحصول على المواعيد اليوم
     */
    public function getTodayAppointments()
    {
        return $this->appointments()
            ->whereDate('appointment_datetime', today())
            ->with('patient.customer')
            ->orderBy('appointment_datetime')
            ->get();
    }

    /**
     * الحصول على المواعيد القادمة
     */
    public function getUpcomingAppointments($days = 7)
    {
        return $this->appointments()
            ->whereBetween('appointment_datetime', [now(), now()->addDays($days)])
            ->with('patient.customer')
            ->orderBy('appointment_datetime')
            ->get();
    }

    /**
     * الحصول على الوصفات الحديثة
     */
    public function getRecentPrescriptions($limit = 10)
    {
        return $this->prescriptions()
            ->with(['patient.customer', 'medications.product'])
            ->orderBy('prescription_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * التحقق من توفر موعد في وقت معين
     */
    public function isAvailableAt(\DateTime $datetime): bool
    {
        // التحقق من يوم العمل
        $dayName = strtolower($datetime->format('l'));
        if (!in_array($dayName, $this->work_days ?? [])) {
            return false;
        }

        // التحقق من ساعات العمل
        if ($this->work_start_time && $this->work_end_time) {
            $time = $datetime->format('H:i');
            $startTime = $this->work_start_time->format('H:i');
            $endTime = $this->work_end_time->format('H:i');

            if ($time < $startTime || $time > $endTime) {
                return false;
            }
        }

        // التحقق من عدم وجود موعد آخر في نفس الوقت
        return !$this->appointments()
            ->where('appointment_datetime', $datetime)
            ->whereIn('status', ['scheduled', 'confirmed'])
            ->exists();
    }

    /**
     * الحصول على الأوقات المتاحة في يوم معين
     */
    public function getAvailableSlots(\DateTime $date, int $slotDuration = 30): array
    {
        $dayName = strtolower($date->format('l'));
        
        // التحقق من يوم العمل
        if (!in_array($dayName, $this->work_days ?? [])) {
            return [];
        }

        $slots = [];
        $startTime = $this->work_start_time ? clone $this->work_start_time : new \DateTime('09:00');
        $endTime = $this->work_end_time ? clone $this->work_end_time : new \DateTime('17:00');

        // الحصول على المواعيد المحجوزة في هذا اليوم
        $bookedSlots = $this->appointments()
            ->whereDate('appointment_datetime', $date->format('Y-m-d'))
            ->whereIn('status', ['scheduled', 'confirmed'])
            ->pluck('appointment_datetime')
            ->map(function ($datetime) {
                return $datetime->format('H:i');
            })
            ->toArray();

        // إنشاء الفترات المتاحة
        $current = clone $startTime;
        while ($current < $endTime) {
            $timeSlot = $current->format('H:i');
            
            if (!in_array($timeSlot, $bookedSlots)) {
                $slots[] = [
                    'time' => $timeSlot,
                    'datetime' => $date->format('Y-m-d') . ' ' . $timeSlot,
                    'available' => true,
                ];
            }

            $current->add(new \DateInterval('PT' . $slotDuration . 'M'));
        }

        return $slots;
    }

    /**
     * إنشاء رقم طبيب فريد
     */
    public static function generateDoctorCode(): string
    {
        do {
            $code = 'DR' . date('Y') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('doctor_code', $code)->exists());

        return $code;
    }

    /**
     * البحث عن الأطباء
     */
    public static function search(string $query)
    {
        return self::query()
            ->where('doctor_code', 'like', "%{$query}%")
            ->orWhere('license_number', 'like', "%{$query}%")
            ->orWhere('specialization', 'like', "%{$query}%")
            ->orWhereHas('user', function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");
            })
            ->with('user')
            ->get();
    }

    /**
     * الحصول على إحصائيات الطبيب
     */
    public function getStatistics(): array
    {
        return [
            'total_patients' => $this->prescriptions()->distinct('patient_id')->count(),
            'total_prescriptions' => $this->prescriptions()->count(),
            'total_lab_requests' => $this->labTestRequests()->count(),
            'total_appointments' => $this->appointments()->count(),
            'completed_appointments' => $this->appointments()->where('status', 'completed')->count(),
            'average_rating' => $this->getAverageRating(),
            'total_reviews' => $this->getReviewsCount(),
            'today_appointments' => $this->appointments()->whereDate('appointment_datetime', today())->count(),
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($doctor) {
            if (!$doctor->doctor_code) {
                $doctor->doctor_code = self::generateDoctorCode();
            }
        });
    }
}

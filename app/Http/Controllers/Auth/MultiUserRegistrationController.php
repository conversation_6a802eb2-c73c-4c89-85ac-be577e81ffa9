<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Patient;
use App\Models\Doctor;
use App\Models\Pharmacy;
use App\Models\Lab;
use Botble\ACL\Models\User;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class MultiUserRegistrationController extends Controller
{
    /**
     * عرض صفحة اختيار نوع التسجيل
     */
    public function showRegistrationChoice()
    {
        return view('auth.registration-choice');
    }

    /**
     * عرض نموذج تسجيل المريض
     */
    public function showPatientRegistration()
    {
        return view('auth.patient-registration');
    }

    /**
     * عرض نموذج تسجيل الطبيب
     */
    public function showDoctorRegistration()
    {
        return view('auth.doctor-registration');
    }

    /**
     * عرض نموذج تسجيل الصيدلية
     */
    public function showPharmacyRegistration()
    {
        return view('auth.pharmacy-registration');
    }

    /**
     * عرض نموذج تسجيل المعمل
     */
    public function showLabRegistration()
    {
        return view('auth.lab-registration');
    }

    /**
     * تسجيل مريض جديد
     */
    public function registerPatient(Request $request)
    {
        $validator = $this->validatePatientRegistration($request);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // إنشاء حساب عميل
            $customer = Customer::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
            ]);

            // إنشاء ملف المريض
            $patient = Patient::create([
                'user_id' => $customer->id,
                'birth_date' => $request->birth_date,
                'gender' => $request->gender,
                'blood_type' => $request->blood_type,
                'medical_history' => $request->medical_history,
                'allergies' => $request->allergies,
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'national_id' => $request->national_id,
            ]);

            // تسجيل دخول تلقائي
            auth('customer')->login($customer);

            return redirect()->route('customer.overview')
                ->with('success', 'تم تسجيلك بنجاح! رقم المريض الخاص بك: ' . $patient->patient_code);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.')
                ->withInput();
        }
    }

    /**
     * تسجيل طبيب جديد
     */
    public function registerDoctor(Request $request)
    {
        $validator = $this->validateDoctorRegistration($request);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // إنشاء حساب مستخدم
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);

            // إنشاء ملف الطبيب
            $doctor = Doctor::create([
                'user_id' => $user->id,
                'license_number' => $request->license_number,
                'specialization' => $request->specialization,
                'qualification' => $request->qualification,
                'bio' => $request->bio,
                'clinic_address' => $request->clinic_address,
                'clinic_phone' => $request->clinic_phone,
                'work_start_time' => $request->work_start_time,
                'work_end_time' => $request->work_end_time,
                'work_days' => $request->work_days ?? [],
                'consultation_fee' => $request->consultation_fee,
                'is_verified' => false, // يحتاج موافقة المشرف
            ]);

            return redirect()->route('login')
                ->with('success', 'تم تسجيلك بنجاح! سيتم مراجعة طلبك والموافقة عليه قريباً. رقم الطبيب: ' . $doctor->doctor_code);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.')
                ->withInput();
        }
    }

    /**
     * تسجيل صيدلية جديدة
     */
    public function registerPharmacy(Request $request)
    {
        $validator = $this->validatePharmacyRegistration($request);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // إنشاء حساب مستخدم
            $user = User::create([
                'name' => $request->owner_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);

            // إنشاء ملف الصيدلية
            $pharmacy = Pharmacy::create([
                'user_id' => $user->id,
                'license_number' => $request->license_number,
                'name' => $request->pharmacy_name,
                'description' => $request->description,
                'address' => $request->address,
                'phone' => $request->phone,
                'email' => $request->email,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'opening_time' => $request->opening_time,
                'closing_time' => $request->closing_time,
                'is_24_hours' => $request->boolean('is_24_hours'),
                'accepts_insurance' => $request->boolean('accepts_insurance'),
                'home_delivery' => $request->boolean('home_delivery'),
                'delivery_fee' => $request->delivery_fee,
                'delivery_radius_km' => $request->delivery_radius_km,
                'is_verified' => false, // يحتاج موافقة المشرف
            ]);

            return redirect()->route('login')
                ->with('success', 'تم تسجيل الصيدلية بنجاح! سيتم مراجعة طلبك والموافقة عليه قريباً. رقم الصيدلية: ' . $pharmacy->pharmacy_code);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.')
                ->withInput();
        }
    }

    /**
     * تسجيل معمل جديد
     */
    public function registerLab(Request $request)
    {
        $validator = $this->validateLabRegistration($request);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // إنشاء حساب مستخدم
            $user = User::create([
                'name' => $request->owner_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);

            // إنشاء ملف المعمل
            $lab = Lab::create([
                'user_id' => $user->id,
                'license_number' => $request->license_number,
                'name' => $request->lab_name,
                'description' => $request->description,
                'address' => $request->address,
                'phone' => $request->phone,
                'email' => $request->email,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'opening_time' => $request->opening_time,
                'closing_time' => $request->closing_time,
                'home_collection' => $request->boolean('home_collection'),
                'collection_fee' => $request->collection_fee,
                'available_tests' => $request->available_tests ?? [],
                'is_verified' => false, // يحتاج موافقة المشرف
            ]);

            return redirect()->route('login')
                ->with('success', 'تم تسجيل المعمل بنجاح! سيتم مراجعة طلبك والموافقة عليه قريباً. رقم المعمل: ' . $lab->lab_code);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.')
                ->withInput();
        }
    }

    /**
     * التحقق من صحة بيانات تسجيل المريض
     */
    private function validatePatientRegistration(Request $request)
    {
        return Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:ec_customers',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'required|string|max:20',
            'birth_date' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'blood_type' => 'nullable|string|max:5',
            'medical_history' => 'nullable|string',
            'allergies' => 'nullable|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'national_id' => 'nullable|string|max:20|unique:patients',
        ]);
    }

    /**
     * التحقق من صحة بيانات تسجيل الطبيب
     */
    private function validateDoctorRegistration(Request $request)
    {
        return Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'license_number' => 'required|string|max:255|unique:doctors',
            'specialization' => 'required|string|max:255',
            'qualification' => 'nullable|string',
            'bio' => 'nullable|string',
            'clinic_address' => 'nullable|string',
            'clinic_phone' => 'nullable|string|max:20',
            'work_start_time' => 'nullable|date_format:H:i',
            'work_end_time' => 'nullable|date_format:H:i|after:work_start_time',
            'work_days' => 'nullable|array',
            'work_days.*' => 'string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'consultation_fee' => 'nullable|numeric|min:0',
        ]);
    }

    /**
     * التحقق من صحة بيانات تسجيل الصيدلية
     */
    private function validatePharmacyRegistration(Request $request)
    {
        return Validator::make($request->all(), [
            'owner_name' => 'required|string|max:255',
            'pharmacy_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'license_number' => 'required|string|max:255|unique:pharmacies',
            'description' => 'nullable|string',
            'address' => 'required|string',
            'phone' => 'required|string|max:20',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'opening_time' => 'nullable|date_format:H:i',
            'closing_time' => 'nullable|date_format:H:i|after:opening_time',
            'is_24_hours' => 'boolean',
            'accepts_insurance' => 'boolean',
            'home_delivery' => 'boolean',
            'delivery_fee' => 'nullable|numeric|min:0',
            'delivery_radius_km' => 'nullable|integer|min:1',
        ]);
    }

    /**
     * التحقق من صحة بيانات تسجيل المعمل
     */
    private function validateLabRegistration(Request $request)
    {
        return Validator::make($request->all(), [
            'owner_name' => 'required|string|max:255',
            'lab_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'license_number' => 'required|string|max:255|unique:labs',
            'description' => 'nullable|string',
            'address' => 'required|string',
            'phone' => 'required|string|max:20',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'opening_time' => 'nullable|date_format:H:i',
            'closing_time' => 'nullable|date_format:H:i|after:opening_time',
            'home_collection' => 'boolean',
            'collection_fee' => 'nullable|numeric|min:0',
            'available_tests' => 'nullable|array',
            'available_tests.*' => 'string|max:255',
        ]);
    }
}

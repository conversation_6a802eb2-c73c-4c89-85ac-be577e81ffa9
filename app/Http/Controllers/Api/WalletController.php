<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PharmaWallet;
use App\Services\WalletService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class WalletController extends Controller
{
    protected $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    /**
     * الحصول على بيانات المحفظة
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();
        $wallet = $this->walletService->getOrCreateWallet($user);

        return response()->json([
            'success' => true,
            'data' => [
                'wallet_number' => $wallet->wallet_number,
                'balance' => $wallet->balance,
                'available_balance' => $wallet->available_balance,
                'pending_balance' => $wallet->pending_balance,
                'currency' => $wallet->currency,
                'is_active' => $wallet->is_active,
                'is_verified' => $wallet->is_verified,
                'statistics' => $this->walletService->getWalletStatistics($wallet),
            ]
        ]);
    }

    /**
     * شحن المحفظة
     */
    public function charge(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'amount' => 'required|numeric|min:10|max:10000',
            'payment_method' => 'required|string|in:credit_card,fawry,paymob,bank_transfer',
            'payment_data' => 'nullable|array',
        ]);

        $user = $request->user();
        $wallet = $this->walletService->getOrCreateWallet($user);

        try {
            $result = $this->walletService->chargeWallet(
                $wallet,
                $validatedData['amount'],
                $validatedData['payment_method'],
                $validatedData['payment_data'] ?? []
            );

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'تم شحن المحفظة بنجاح' : 'فشل في شحن المحفظة',
                'data' => [
                    'transaction_id' => $result['transaction']->transaction_id,
                    'new_balance' => $result['new_balance'] ?? null,
                    'payment_result' => $result['payment_result'] ?? null,
                ],
                'error' => $result['error'] ?? null,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * تحويل إلى محفظة أخرى
     */
    public function transfer(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'to_wallet_number' => 'required|string|exists:pharma_wallets,wallet_number',
            'amount' => 'required|numeric|min:1',
            'description' => 'nullable|string|max:255',
        ]);

        $user = $request->user();
        $fromWallet = $this->walletService->getOrCreateWallet($user);
        $toWallet = PharmaWallet::findByWalletNumber($validatedData['to_wallet_number']);

        if (!$toWallet) {
            return response()->json(['error' => 'المحفظة المستهدفة غير موجودة'], 404);
        }

        try {
            $result = $this->walletService->transferBetweenWallets(
                $fromWallet,
                $toWallet,
                $validatedData['amount'],
                $validatedData['description'] ?? null
            );

            return response()->json([
                'success' => true,
                'message' => 'تم التحويل بنجاح',
                'data' => [
                    'debit_transaction_id' => $result['debit_transaction']->transaction_id,
                    'credit_transaction_id' => $result['credit_transaction']->transaction_id,
                    'new_balance' => $fromWallet->fresh()->balance,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * الحصول على تاريخ المعاملات
     */
    public function transactions(Request $request): JsonResponse
    {
        $user = $request->user();
        $wallet = $this->walletService->getOrCreateWallet($user);

        $filters = $request->only(['type', 'status', 'from_date', 'to_date', 'per_page']);
        $transactions = $this->walletService->getTransactionHistory($wallet, $filters);

        return response()->json([
            'success' => true,
            'data' => $transactions->items(),
            'pagination' => [
                'current_page' => $transactions->currentPage(),
                'last_page' => $transactions->lastPage(),
                'per_page' => $transactions->perPage(),
                'total' => $transactions->total(),
            ]
        ]);
    }

    /**
     * الحصول على إحصائيات المحفظة
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->user();
        $wallet = $this->walletService->getOrCreateWallet($user);

        $from = $request->get('from_date') ? new \DateTime($request->get('from_date')) : null;
        $to = $request->get('to_date') ? new \DateTime($request->get('to_date')) : null;

        $statistics = $this->walletService->getWalletStatistics($wallet, $from, $to);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Patient;
use App\Models\Prescription;
use App\Models\LabTestRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PatientController extends Controller
{
    /**
     * الحصول على ملف المريض
     */
    public function show(string $patientCode): JsonResponse
    {
        $patient = Patient::where('patient_code', $patientCode)
            ->with(['customer', 'prescriptions.medications.product', 'labTestRequests.testItems.results'])
            ->first();

        if (!$patient) {
            return response()->json(['error' => 'المريض غير موجود'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'patient_info' => [
                    'patient_code' => $patient->patient_code,
                    'name' => $patient->customer->name,
                    'email' => $patient->customer->email,
                    'phone' => $patient->customer->phone,
                    'birth_date' => $patient->birth_date,
                    'age' => $patient->age,
                    'gender' => $patient->gender,
                    'blood_type' => $patient->blood_type,
                    'medical_history' => $patient->medical_history,
                    'allergies' => $patient->allergies,
                    'chronic_diseases' => $patient->chronic_diseases,
                    'is_chronic_patient' => $patient->is_chronic_patient,
                ],
                'statistics' => $patient->getStatistics(),
                'active_prescriptions' => $patient->getActivePrescriptions(),
                'recent_lab_tests' => $patient->getRecentLabTests(),
                'medical_timeline' => $patient->medical_timeline,
            ]
        ]);
    }

    /**
     * الحصول على الوصفات النشطة للمريض
     */
    public function activePrescriptions(string $patientCode): JsonResponse
    {
        $patient = Patient::where('patient_code', $patientCode)->first();
        
        if (!$patient) {
            return response()->json(['error' => 'المريض غير موجود'], 404);
        }

        $prescriptions = $patient->getActivePrescriptions();

        return response()->json([
            'success' => true,
            'data' => $prescriptions->map(function ($prescription) {
                return [
                    'prescription_number' => $prescription->prescription_number,
                    'doctor_name' => $prescription->doctor->user->name,
                    'diagnosis' => $prescription->diagnosis,
                    'prescription_date' => $prescription->prescription_date,
                    'expiry_date' => $prescription->expiry_date,
                    'status' => $prescription->status,
                    'medications' => $prescription->medications->map(function ($med) {
                        return [
                            'medication_name' => $med->medication_name,
                            'dosage' => $med->dosage,
                            'frequency' => $med->frequency,
                            'quantity' => $med->quantity,
                            'dispensed_quantity' => $med->dispensed_quantity,
                            'remaining_quantity' => $med->remaining_quantity,
                            'instructions' => $med->instructions,
                            'status' => $med->status,
                        ];
                    }),
                ];
            })
        ]);
    }

    /**
     * الحصول على نتائج التحاليل للمريض
     */
    public function labResults(string $patientCode): JsonResponse
    {
        $patient = Patient::where('patient_code', $patientCode)->first();
        
        if (!$patient) {
            return response()->json(['error' => 'المريض غير موجود'], 404);
        }

        $labTests = $patient->getRecentLabTests(10);

        return response()->json([
            'success' => true,
            'data' => $labTests->map(function ($test) {
                return [
                    'request_number' => $test->request_number,
                    'doctor_name' => $test->doctor->user->name,
                    'lab_name' => $test->lab->name,
                    'request_date' => $test->request_date,
                    'status' => $test->status,
                    'clinical_data' => $test->clinical_data,
                    'tests' => $test->testItems->map(function ($item) {
                        return [
                            'test_name' => $item->test_name,
                            'status' => $item->status,
                            'results' => $item->results->map(function ($result) {
                                return [
                                    'parameter' => $result->parameter_name,
                                    'value' => $result->result_value,
                                    'unit' => $result->unit,
                                    'reference_range' => $result->reference_range,
                                    'status' => $result->status,
                                    'notes' => $result->notes,
                                ];
                            }),
                        ];
                    }),
                ];
            })
        ]);
    }

    /**
     * البحث عن المرضى
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q');
        
        if (!$query) {
            return response()->json(['error' => 'يرجى إدخال كلمة البحث'], 400);
        }

        $patients = Patient::search($query);

        return response()->json([
            'success' => true,
            'data' => $patients->map(function ($patient) {
                return [
                    'patient_code' => $patient->patient_code,
                    'name' => $patient->customer->name,
                    'phone' => $patient->customer->phone,
                    'age' => $patient->age,
                    'gender' => $patient->gender,
                    'is_chronic_patient' => $patient->is_chronic_patient,
                ];
            })
        ]);
    }

    /**
     * تحديث بيانات المريض
     */
    public function update(Request $request, string $patientCode): JsonResponse
    {
        $patient = Patient::where('patient_code', $patientCode)->first();
        
        if (!$patient) {
            return response()->json(['error' => 'المريض غير موجود'], 404);
        }

        $validatedData = $request->validate([
            'medical_history' => 'nullable|string',
            'allergies' => 'nullable|string',
            'chronic_diseases' => 'nullable|string',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
        ]);

        $patient->update($validatedData);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث بيانات المريض بنجاح',
            'data' => $patient
        ]);
    }
}
